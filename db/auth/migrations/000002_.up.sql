CREATE INDEX IF NOT EXISTS actor_id_deleted_at_idx ON token_stores (actor_id DESC, deleted_at ASC);
CREATE INDEX IF NOT EXISTS token_stores_updated_at_idx ON token_stores (updated_at ASC);
CREATE INDEX IF NOT EXISTS token_stores_phone_number_token_type_created_at_idx ON token_stores  (computed_phone_number ASC, token_type ASC, created_at DESC);
CREATE INDEX IF NOT EXISTS token_stores_actor_id_token_type_created_at_idx ON token_stores (actor_id ASC, token_type ASC, created_at DESC)
