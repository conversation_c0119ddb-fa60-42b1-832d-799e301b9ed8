CREATE TABLE IF NOT EXISTS public.biometrics (
	 id TEXT NOT NULL,
	 actor_id TEXT NOT NULL,
	 status TEXT NOT NULL,
	 biometrics_info JSONB NOT NULL,
	 verified_at TIMESTAMPTZ NULL,
	 created_at TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
	 updated_at TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
	 deleted_at TIMESTAMPTZ NULL,
	 PRIMARY KEY (id)
);

COMMENT ON TABLE public.biometrics IS 'table to store biometrics related data for a user';
COMMENT ON COLUMN public.biometrics.biometrics_info IS 'hold data like biometric identifier, app platform, etc of the user';
COMMENT ON COLUMN public.biometrics.verified_at IS 'timestamp at which the user has verified the biometric changes';
