CREATE TABLE public.token_stores (
									 id TEXT NOT NULL,
									 status TEXT NOT NULL,
									 device JSONB NOT NULL,
									 token TEXT NULL,
									 token_type TEXT NULL,
									 phone_number JSONB NOT NULL,
									 email TEXT NULL,
									 last_activity TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
									 device_registration_status TEXT NULL,
									 auth_factor_update_id UUID NULL,
									 actor_id TEXT NOT NULL,
									 computed_phone_number TEXT NULL GENERATED ALWAYS AS (CASE WHEN ((phone_number->>'country_code') IS NOT NULL) AND ((phone_number->>'national_number') IS NOT NULL) THEN ((phone_number->>'country_code') || (phone_number->>'national_number')) ELSE NULL END) STORED,
									 device_integrity_id TEXT NULL,
									 device_integrity_result TEXT NULL,
									 created_at TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
									 updated_at TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
									 deleted_at TIMESTAMPTZ NULL,
									 PRIMARY KEY (id)
);
