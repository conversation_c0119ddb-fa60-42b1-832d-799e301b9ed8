CREATE TABLE public.biometrics (
    id text NOT NULL,
    actor_id text NOT NULL,
    status text NOT NULL,
    biometrics_info jsonb NOT NULL,
    verified_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone,
    sub_status text
);
COMMENT ON TABLE public.biometrics IS 'table to store biometrics related data for a user';
COMMENT ON COLUMN public.biometrics.biometrics_info IS 'hold data like biometric identifier, app platform, etc of the user';
COMMENT ON COLUMN public.biometrics.verified_at IS 'timestamp at which the user has verified the biometric changes';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.token_stores (
    id text NOT NULL,
    status text NOT NULL,
    device jsonb NOT NULL,
    token text,
    token_type text,
    phone_number jsonb NOT NULL,
    email text,
    last_activity timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    device_registration_status text,
    auth_factor_update_id uuid,
    actor_id text NOT NULL,
    computed_phone_number text GENERATED ALWAYS AS (
CASE
    WHEN (((phone_number ->> 'country_code'::text) IS NOT NULL) AND ((phone_number ->> 'national_number'::text) IS NOT NULL)) THEN ((phone_number ->> 'country_code'::text) || (phone_number ->> 'national_number'::text))
    ELSE NULL::text
END) STORED,
    device_integrity_id text,
    device_integrity_result text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at timestamp with time zone,
    token_deletion_reason text,
    subject_value text,
    subject_type text
);
ALTER TABLE ONLY public.biometrics
    ADD CONSTRAINT biometrics_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.token_stores
    ADD CONSTRAINT token_stores_pkey PRIMARY KEY (id);
CREATE INDEX actor_id_deleted_at_idx ON public.token_stores USING btree (actor_id DESC, deleted_at);
CREATE INDEX biometrics_actor_id_idx ON public.biometrics USING btree (actor_id);
CREATE INDEX biometrics_updated_at_idx ON public.biometrics USING btree (updated_at);
CREATE INDEX token_stores_actor_id_token_type_created_at_idx ON public.token_stores USING btree (actor_id, token_type, created_at DESC);
CREATE INDEX token_stores_phone_number_token_type_created_at_idx ON public.token_stores USING btree (computed_phone_number, token_type, created_at DESC);
CREATE INDEX token_stores_updated_at_idx ON public.token_stores USING btree (updated_at);
