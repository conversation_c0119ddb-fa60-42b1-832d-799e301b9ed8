-- (version_id), removing since previous migration is making version_id as primary key
drop index if exists rule_subscription_version_id_idx;
-- (actor_id, execution_state, version_state) - was added temporarily to fix load
-- changing key order in new index (actor_id, version_state, execution_state)
drop index if exists rule_subscriptions_actor_execution_state_idx;
-- (rule_id) not required as already existing (rule_id, actor_id) will serve similar queries
drop index if exists rule_subscriptions_rule_id_idx;
-- (id, version_state) - replacing with partial index `rule_subscriptions_current_version_idx`
drop index if exists rule_subscription_version_state_composite_idx;

-- unique index on (id, version_state) to ensure only 1 current version is present at a time --
CREATE UNIQUE INDEX if not exists rule_subscriptions_current_version_idx on rule_subscriptions(id, version_state) where version_state = 'CURRENT';
-- changing order of fields from (actor_id, execution_state, version_state) to (actor_id, version_state, execution_state) to serve more queries
CREATE INDEX if not exists rule_subscriptions_actor_version_execution_state_idx ON rule_subscriptions USING btree (actor_id, version_state, execution_state);
