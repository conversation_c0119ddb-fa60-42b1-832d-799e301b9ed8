ALTER TABLE rules add column version_support_info jsonb;
ALTER TABLE collections add column version_support_info jsonb;

-- investment recurring rules --
update rules set version_support_info = '{"min_supported_android_app_version":129, "min_supported_ios_app_version":133}' where id in ('0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25');
-- zen mode --
update rules set version_support_info = '{"min_supported_android_app_version":87}' where id = '046b769b-c6e9-4edb-9d7e-0b20f06ad50d';
-- investment collections --
update collections set version_support_info = '{"min_supported_android_app_version":129, "min_supported_ios_app_version":133}' where id in ('e29f99de-1b78-4594-97d7-ddffc2689a99', 'bf595b5c-54c8-42b3-930e-ac2d71e6b35b');
