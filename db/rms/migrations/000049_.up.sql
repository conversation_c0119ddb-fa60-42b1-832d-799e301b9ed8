-- using version_id as primary key --
alter table rule_subscriptions drop constraint rule_subscriptions_id_version_id_idx;
ALTER TABLE rule_subscriptions ADD CONSTRAINT rule_subscriptions_pkey PRIMARY KEY (version_id);

-- (version_id, rule_id) - not useful as version Id is primary key so only 1 record will be returned --
drop index if exists rule_subscription_composite_version_id_rule_id_idx;
-- (id, version_valid_from, version_valid_till) - not very useful as no more than 100 versions of a subscription is expected --
drop index if exists rule_subscription_version_valid_time_composite_idx;

-- for queries to get subscriptions for an actor during execution of an rms event --
CREATE INDEX if not exists rule_subscriptions_actor_specific_subs_for_executions_idx ON rule_subscriptions USING btree (actor_id, state, version_valid_from, version_valid_till);
-- for queries to get subscriptions for subscriptions across rules during execution of an rms event --
CREATE INDEX if not exists rule_subscriptions_across_actors_for_execution_idx ON rule_subscriptions USING btree (version_valid_from, version_valid_till, rule_id);
-- index to be used for getting subscriptions in a particular execution state --
CREATE INDEX if not exists rule_subscriptions_actor_execution_state_idx ON rule_subscriptions USING btree (actor_id, execution_state, version_state);
