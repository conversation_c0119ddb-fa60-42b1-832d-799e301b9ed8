-- Possible param values --
create table possible_param_values(
    id UUID default uuid_generate_v4() not null constraint possible_param_values_pkey PRIMARY KEY,
    tags varchar[],
    param_value_type varchar not null,
    value jsonb,
    is_default_value boolean,
    state varchar not null
);

-- Rule display info --
create table rule_display_infos(
    id UUID default uuid_generate_v4() not null constraint rule_display_infos_pkey PRIMARY KEY,
    name varchar not null,
    formatted_name varchar not null,
    home_page_text varchar not null,
    home_page_img_url varchar not null,
    landing_page_text varchar not null,
    landing_page_img_url varchar not null,
    background_color varchar not null,
    tags_bg_color varchar not null
);

-- Tag display info --
create table tag_display_infos(
    tag_id varchar not null,
    icon_urls varchar[],
    CONSTRAINT tag_display_info_tag_id_ref_rule_tags FOREIGN KEY (tag_id) REFERENCES rule_tags(id)
);

comment on column possible_param_values.id is '{"proto_type":"rms.rule.PossibleParamValues", "comment": "Id of the player from vendor"}';
comment on column possible_param_values.tags is '{"proto_type":"rms.rule.PossibleParamValues", "comment": "tags to categorize a param for using in different rules (eg: Shopping, Food etc)"}';
comment on column possible_param_values.param_value_type is '{"proto_type":"rms.rule.PossibleParamValues", "comment": "Type of value `value` column holds"}';
comment on column possible_param_values.value is '{"proto_type":"rms.rule.PossibleParamValues", "comment": "Json form of param value"}';
comment on column possible_param_values.is_default_value is '{"proto_type":"rms.rule.PossibleParamValues", "comment": "Flag to define whether the value can be used as default among all selected ones"}';

comment on column rule_display_infos.name is '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Rule name"}';
comment on column rule_display_infos.formatted_name is '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Formatted in a way that can be used on my rules page cards (eg: Healthy, Wealthy,\n Wise (%s))"}';
comment on column rule_display_infos.home_page_text is '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Text to be displayed on home page"}';
comment on column rule_display_infos.home_page_img_url is '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "S3 URL for rule image to be displayed on home page"}';
comment on column rule_display_infos.landing_page_text is '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Text to be displayed on landing page"}';
comment on column rule_display_infos.landing_page_img_url is '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "S3 URL for rule image to be displayed on landing page"}';
comment on column rule_display_infos.background_color is '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Background color for card"}';
comment on column rule_display_infos.tags_bg_color is '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Background color for tags"}';

comment on column tag_display_infos.tag_id is '{"proto_type":"rms.rule.SportsTeam", "comment": "Id of a rule tag"}';
comment on column tag_display_infos.icon_urls is '{"proto_type":"rms.rule.SportsTeam", "comment": "List of icons urls for a tag"}';
