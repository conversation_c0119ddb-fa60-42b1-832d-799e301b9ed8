-- schema changes --
CREATE TABLE rms_profiles (
  id uuid default uuid_generate_v4() not null constraint user_profile_pkey PRIMARY KEY,
  actor_id varchar not null,
  client varchar not null,
  profile_data jsonb not null,
  created_at timestamp with time zone default now() not null,
  updated_at timestamp with time zone default now() not null,
  deleted_at timestamp with time zone,
  UNIQUE (actor_id, client)
);

-- comments --
COMMENT ON COLUMN rms_profiles.actor_id is '{"proto_type":"RmsProfile.ActorId", "comment": "Actor Id identifies the original actor"}';
COMMENT ON COLUMN rms_profiles.client is '{"proto_type":"RmsProfile.Client", "comment": "Client specifies which rms agent updated the value i.e fittt or rewards"}';
COMMENT ON COLUMN rms_profiles.profile_data is '{"proto_type":"RmsProfile.ProfileData", "comment": "Actual Json data that contains various fields such as last accessed, etc"}';
