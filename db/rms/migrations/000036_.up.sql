create table param_value_selector_ctas (
	id UUID default uuid_generate_v4() NOT NULL constraint param_value_selector_ctas_pkey PRIMARY KEY,
	param_value_type character varying NOT NULL,
	cta jsonb,
	state character varying NOT NULL,
	rule_id uuid NOT NULL,
	created_at timestamp with time zone DEFAULT now() NOT NULL,
	updated_at timestamp with time zone DEFAULT now() NOT NULL,
	deleted_at timestamp with time zone
);

CREATE INDEX IF NOT EXISTS param_value_selector_ctas_rule_id_idx ON param_value_selector_ctas USING BTREE(rule_id);
CREATE INDEX param_value_selector_ctas_updated_at_idx ON param_value_selector_ctas USING btree (updated_at);
CREATE INDEX param_value_selector_ctas_created_at_idx ON param_value_selector_ctas USING btree (created_at);
