

CREATE TABLE do_once_tasks (
                               task_name varchar not null,
                               deleted_at_unix integer default 0,
                               created_at timestamp with time zone default now() not null,
                               updated_at timestamp with time zone default now() not null,
                               primary key (task_name, deleted_at_unix)
);

CREATE INDEX do_once_tasks_updated_at_idx ON do_once_tasks(updated_at);

comment on table do_once_tasks is 'table to identify tasks by their unique names that should be done exactly once';
