CREATE INDEX IF NOT EXISTS rule_subscriptions_composite_idx ON rule_subscriptions(rule_id, state, execution_state, version_valid_from, created_at);

-- SAMPLE QUERY:
--
-- select
--   *
-- from
--   rule_subscriptions
-- where
--   rule_id in (
--     '1c44b98f-0b75-4568-a06d-7474b00e4c55',
--     '7b251ad6-a653-4c8a-88fa-a3714b45b480',
--     '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0',
--     'd157d5c9-8b32-4f1b-9185-a60a8263ea25',
--     '95d7aa89-f081-4a07-90bc-7950775ce1b4',
--     '43dd9b50-786b-47c0-bf0b-61401bc8da42',
--     'e31c6513-cadd-4e8a-8eb4-2ec696a7be75',
--     'bb093acb-fb94-4aa4-abe6-1d85a91feba2',
--     '42286516-f4d1-421f-9c1c-1a7f65612e66'
--   )
--   and state = 'ACTIVE'
--   and version_valid_from <= '2023-10-27 05:00:00'
--   and (
--     version_valid_till is null
--     or version_valid_till > '2023-10-27 05:00:00'
--   )
--   and execution_state in (
--     'SUBSCRIPTION_EXECUTION_ALLOWED',
--     'SUBSCRIPTION_EXECUTION_NOT_SPECIFIED'
--   )
--   and created_at <= '2022-07-19T13:31:44.263863Z'
-- order by
--   created_at desc offset 24615
-- limit
--   101;
