alter table rule_executions drop constraint  rule_executions_subscription_id_ref_rule_subscriptions;
alter table rule_subscriptions drop constraint  rule_subscriptions_pkey;

alter table rule_subscriptions add column version_id UUID default uuid_generate_v4() not null;
comment on column rule_subscriptions.version_id is '{"proto_type":"rms.rule.RuleSubscription", "comment": "A version is a snapshot of subscription. There can be multiple versions of subscription in cases of updates, however only one version will be Active at any time"';

alter table rule_subscriptions add column version_valid_from timestamp with time zone;
comment on column rule_subscriptions.version_valid_from is '{"proto_type":"rms.rule.RuleSubscription", "comment": "Defines timestamp from which this particular subscription version is/was valid"';
-- initializing version_valid_from with valid_from timestamp
update rule_subscriptions set version_valid_from = valid_from where version_valid_from is null;
-- defining constraints for version_valid_from
alter table rule_subscriptions alter column version_valid_from set not null ;
alter table rule_subscriptions alter column version_valid_from set default now() ;
-- defining constraints for cutoff_param_updated_at
alter table rule_subscriptions alter column cutoff_param_updated_at set default now() ;

alter table rule_subscriptions add column version_valid_till timestamp with time zone;
comment on column rule_subscriptions.version_valid_from is '{"proto_type":"rms.rule.RuleSubscription", "comment": "Defines the version validity end time. version_valid_till is the time when subscription was updated"';

alter table rule_subscriptions add column version_state varchar not null default 'CURRENT';
comment on column rule_subscriptions.version_state is '{"proto_type":"rms.rule.SubscriptionVersionState", "comment": "Defines state of the version. Only one among all the versions will be RUNTIME rest will be in EXPIRED state"';
-- adding primary key
alter table rule_subscriptions add constraint rule_subscriptions_id_version_id_idx primary key (id, version_id);
-- creating indexes
CREATE INDEX rule_subscription_version_state_composite_idx ON rule_subscriptions(id, version_state);
CREATE INDEX rule_subscription_version_valid_time_composite_idx ON rule_subscriptions(id, version_valid_from, version_valid_till);
