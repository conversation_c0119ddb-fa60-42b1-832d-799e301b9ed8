ALTER TABLE rules ADD COLUMN subscriptions_aggregation_type varchar default 'AGGREGATE_ON_RULE';
COMMENT ON COLUMN rules.subscriptions_aggregation_type is '{"proto_type":"rule.SubscriptionsAggregationType", "comment": "My rules page displays subscriptions aggregated based on rules/tags, subscriptions_aggregation_type defines the type of aggregation that should be applied to subscriptions of this rule"}';
UPDATE rules set subscriptions_aggregation_type = 'AGGREGATE_ON_TAG' where event_type in ('CRICKET_BATTING_STATS', 'CRICKET_BOWLING_STATS',
        'CRICKET_MATCH_RESULT', 'CRICKET_MAIDEN_OVERS', 'CRICKET_BATSMAN_STATS', 'CRICKET_BOWLER_STATS', 'CRICKET_MAIDEN_OVER_STATS',
        'CRICKET_PARTNERSHIP_STATS', 'CRICKET_TEAM_PARTNERSHIP_STATS', 'FOOTBALL_GOAL_STATS', 'FOOTBALL_PLAYER_GOAL_STATS', 'FOOTBALL_MATCH_RESULT');

CREATE TABLE subscription_runtime_infos (
  id uuid constraint subscription_runtime_infos_pkey PRIMARY KEY,
  rule_id uuid not null references rules(id),
  actor_id varchar not null,
  state varchar not null,
  aggregation_id varchar not null,
  aggregation_type varchar not null,
  rule_param_values jsonb not null,
  last_executed_at timestamp with time zone,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  deleted_at timestamp with time zone
);

COMMENT ON COLUMN subscription_runtime_infos.id is '{"proto_type":"SubscriptionRuntimeInfo.Id", "comment": "Id is the Rule subscription id"}';
COMMENT ON COLUMN subscription_runtime_infos.state is '{"proto_type":"rule.RuleSubscriptionState", "comment": "Defines current state of subscription (ACTIVE/INACTIVE/CLOSED)"}';
COMMENT ON COLUMN subscription_runtime_infos.rule_param_values is '{"proto_type":"rule.RuleParamValues", "comment": "Defines current param values for the subscription"}';
COMMENT ON COLUMN subscription_runtime_infos.aggregation_id is '{"proto_type":"SubscriptionRuntimeInfo.AggregationId", "comment": "My rules page displays subscriptions aggregated based on rules/tags, aggregation_id defines the id of aggregation that should be applied this subscription"}';
COMMENT ON COLUMN subscription_runtime_infos.aggregation_type is '{"proto_type":"SubscriptionRuntimeInfo.SubscriptionsAggregationType", "comment": "My rules page displays subscriptions aggregated based on rules/tags, subscriptions_aggregation_type defines the type of aggregation that should be applied to this subscription"}';
COMMENT ON COLUMN subscription_runtime_infos.last_executed_at is '{"proto_type":"SubscriptionRuntimeInfo.LastExecutedAt", "comment": "timestamp for last successful execution of subscription. i.e. Execution resulted in state ACTION_PROCESSING_SUCCESS"}';

CREATE INDEX subscription_runtime_infos_composite_idx ON subscription_runtime_infos(aggregation_id, actor_id, state);

-- APP --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredApp,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'APP' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredApp' -> 'appVal' ->> 'appName' = possible_param_values.value -> 'app_val' ->> 'app_name';

-- DURATION --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDuration,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'DURATION' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredDuration' -> 'duration' ->> 'value' = possible_param_values.value -> 'duration' ->> 'value';

-- MERCHANT --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredMerchant,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'MERCHANT' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredMerchant' -> 'merchantVal' ->> 'name' = possible_param_values.value -> 'merchant_val' ->> 'merchant_name';

-- CRICKET PLAYER --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredCricketPlayer,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'CRICKET_PLAYER' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredCricketPlayer' -> 'playerVal' ->> 'id' = possible_param_values.value -> 'cricketPlayerVal' ->> 'playerId';

-- CRICKET TEAM --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredCricketTeam,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'CRICKET_TEAM' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredCricketTeam' -> 'teamVal' ->> 'id' = possible_param_values.value -> 'cricketTeamVal' ->> 'teamId';

-- FOOTBALL PLAYER --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredFootballPlayer,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'FOOTBALL_PLAYER' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredFootballPlayer' -> 'footballPlayerVal' ->> 'id' = possible_param_values.value -> 'football_player_val' ->> 'id';

-- FOOTBALL TEAM --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredFootballTeam,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'FOOTBALL_TEAM' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredFootballTeam' -> 'footballTeamVal' ->> 'id' = possible_param_values.value -> 'football_team_val' ->> 'id';

-- DAY OF WEEK (STRING VALUE) --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDayOfWeek,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'STRING_INPUT' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredDayOfWeek' ->> 'strVal' = possible_param_values.value ->> 'str_val';

-- DATE OF MONTH (INT VALUE) --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDateOfMonth,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'INT_INPUT' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredDateOfMonth' ->> 'intVal' = possible_param_values.value ->> 'int_val';

-- MONEY --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDepositAmount,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'MONEY' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredDepositAmount' -> 'moneyVal' ->> 'units' = possible_param_values.value -> 'money_val' ->> 'units';

-- MONEY --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,depositAmount,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'MONEY' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'depositAmount' -> 'moneyVal' ->> 'units' = possible_param_values.value -> 'money_val' ->> 'units';

-- MONEY --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredRoundAmount,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'MONEY' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredRoundAmount' -> 'moneyVal' ->> 'units' = possible_param_values.value -> 'money_val' ->> 'units';

-- adding current states from rule_subscriptions table --
INSERT INTO subscription_runtime_infos (
  SELECT
    id, rule_id, actor_id, state, rule_id, 'AGGREGATE_ON_RULE', rule_param_values, null, valid_from, updated_at, deleted_at
  FROM
    rule_subscriptions
  WHERE
    version_state = 'CURRENT'
);

-- update subscriptions with aggregation type tag --
UPDATE
  subscription_runtime_infos
SET
  aggregation_id = tag_id,
  aggregation_type = 'AGGREGATE_ON_TAG'
FROM
  (
    SELECT
      tag_id, rule_id
    FROM
      rule_tag_mappings
    WHERE
      tag_id IN (
        SELECT
          id
        FROM
          rule_tags
        WHERE
          ('Sports.Cricket' @> path
          OR 'Sports.Football' @> path)
          AND type = 'TERMINAL'
      )
  ) AS a
WHERE
  subscription_runtime_infos.rule_id = a.rule_id;

-- update last_executed_at for all subscriptions from rule_executions table --
UPDATE
  subscription_runtime_infos
SET
  last_executed_at = re.last_executed_at
FROM
  (
    SELECT
      rule_subscription_id, MAX(created_at) AS last_executed_at
    FROM
      rule_executions
    WHERE
      STATE = 'ACTION_PROCESSING_SUCCESS'
    GROUP BY
      rule_subscription_id
  ) AS re
WHERE
  subscription_runtime_infos.id = re.rule_subscription_id;

UPDATE rule_tags set display_info = jsonb_set(display_info, '{"bgColor"}', '"#FAD0D0"', true) where id = 'WC2021';
UPDATE rule_tags set display_info = jsonb_set(display_info, '{"bgColor"}', '"#FAD0D0"', true) where id = 'EPL2021';
UPDATE rule_tags set display_info = jsonb_set(display_info, '{"bgColor"}', '"#F4E7BF"', true) where id = 'NZVsIND2021Test';
