drop index if exists rule_subscriptions_current_version_idx;
drop index if exists rule_subscriptions_actor_version_execution_state_idx;

CREATE INDEX if not exists rule_subscription_version_id_idx ON rule_subscriptions(version_id);
CREATE INDEX if not exists rule_subscriptions_actor_execution_state_idx ON rule_subscriptions USING btree (actor_id, execution_state, version_state);
CREATE INDEX if not exists rule_subscriptions_rule_id_idx ON rule_subscriptions (rule_id);
CREATE INDEX if not exists rule_subscription_version_state_composite_idx ON rule_subscriptions(id, version_state);
