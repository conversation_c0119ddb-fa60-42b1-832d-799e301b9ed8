CREATE MATERIALIZED VIEW rule_executions_count_view AS (
select
	name,
	count(*)
from
	rule_executions,
	rule_subscriptions,
	rules
where
  rule_executions.subscription_version_id = rule_subscriptions.version_id
  and rule_executions.state = 'ACTION_PROCESSING_SUCCESS'
  and rule_id = rules.id
  group by name );

CREATE UNIQUE INDEX rule_executions_count_view_name_idx on rule_executions_count_view(name);

REFRESH MATERIALIZED VIEW CONCURRENTLY rule_executions_count_view ;
