-- schema changes --
ALTER TABLE rule_tags add column state varchar not null default 'TAG_STATE_ACTIVE';

CREATE TABLE collections (
    id uuid default uuid_generate_v4() not null constraint collections_pkey PRIMARY KEY,
    state varchar not null,
    weight integer not null default 1,
    display_info json not null,
    child_ids json not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone
);

-- comments --
COMMENT ON COLUMN collections.state is '{"proto_type":"rule.Collection.state", "comment": "State can be Active/Inactive"}';
COMMENT ON COLUMN collections.display_info is '{"proto_type":"rule.CollectionDisplayInfo", "comment": "Contains all UI specific information required to render collection on UI"}';
COMMENT ON COLUMN collections.child_ids is '{"proto_type":"rule.ChildIds", "comment": "contains list of either tagIds or ruleIds associated with collection"}';
COMMENT ON COLUMN collections.weight is '{"proto_type":"rule.Collection.weight", "comment": "weight will help in ordering cards on UI"}';

-- indexes --
CREATE INDEX collection_state_idx ON collections (state);
CREATE INDEX collection_created_at_idx ON collections (created_at);
CREATE INDEX collection_deleted_at_idx ON collections (deleted_at);
