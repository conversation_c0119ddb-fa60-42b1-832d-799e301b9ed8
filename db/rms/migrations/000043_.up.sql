ALTER TABLE rules ADD COLUMN aggregation_info jsonb;
ALTER TABLE rules ADD COLUMN rule_type_for_special_handling varchar not null default 'RULE_TYPE_UNSPECIFIED';

comment on column rules.aggregation_info is '{"proto_type":"rms.rule.AggregationInfo", "comment": "storing if the rule contains aggregation related info"}';
comment on column rules.rule_type_for_special_handling is '{"proto_type":"rms.rule.RuleTypeForSpecialHandling", "comment": "there are specific handling required for certain set of rules at FE layer mostly. Instead of identifying the rule based on name, type should be used for identification"}';

