create extension if not exists ltree;

create table rule_tags (
        id varchar not null constraint rule_tags_pkey PRIMARY KEY, -- name of the tag, should be unique
        name varchar,  -- name to be used for display purposes
        type varchar not null, -- defines whether the tag is terminal or a intermediate
        path ltree, -- defines complete node path from root
        created_at timestamp with time zone default now() not null,
        updated_at timestamp with time zone default now() not null,
        deleted_at timestamp with time zone
);

create index rule_tags_path_idx on rule_tags using gist(path);

comment on column rule_tags.id is '{"proto_type":"rms.rule.RuleTag", "comment": "name of the tag, value to identify tag uniquely"}';
comment on column rule_tags.name is '{"proto_type":"rms.rule.RuleTag", "comment": "tag name to be used for display purposes"}';
comment on column rule_tags.type is '{"proto_type":"rms.rule.RuleTag", "comment": "defines nature of tag node in tree (possible values Terminal, intermediate, root)"}';
comment on column rule_tags.path is '{"proto_type":"rms.rule.RuleTag", "comment": " defines complete node path from root"}';

-- since there can be one to many relation ship between rule and tag
-- maintaining a foreign key relation as a form of array is not possible in postgres
-- using a separate mapping table for same
create table rule_tag_mappings (
    id  UUID default uuid_generate_v4() not null constraint rule_tag_mappings_pkey PRIMARY KEY,
    rule_id UUID not null,
    tag_id varchar not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone,
    CONSTRAINT rule_tag_mappings_rule_id_ref_rules FOREIGN KEY (rule_id) REFERENCES rules(id),
    CONSTRAINT rule_tag_mappings_tag_id_ref_rule_tags FOREIGN KEY (tag_id) REFERENCES rule_tags(id)
);
