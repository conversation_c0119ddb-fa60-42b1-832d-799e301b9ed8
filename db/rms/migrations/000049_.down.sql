alter table rule_subscriptions drop constraint rule_subscriptions_pkey;
alter table rule_subscriptions add constraint rule_subscriptions_id_version_id_idx primary key (id, version_id);

drop index if exists rule_subscriptions_actor_specific_subs_for_executions_idx;
drop index if exists rule_subscriptions_across_actors_for_execution_idx;
drop index if exists rule_subscriptions_actor_execution_state_idx;

CREATE INDEX if not exists rule_subscription_composite_version_id_rule_id_idx ON rule_subscriptions USING btree (version_id, rule_id);
CREATE INDEX if not exists rule_subscription_version_valid_time_composite_idx ON rule_subscriptions(id, version_valid_from, version_valid_till);
