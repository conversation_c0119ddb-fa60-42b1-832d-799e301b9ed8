CREATE INDEX IF NOT EXISTS subscription_runtime_infos_mutual_fund_id_idx ON rms.public.subscription_runtime_infos
    USING BTREE((rule_param_values->'ruleParamValues'->'mutualFundVal'->'mutualFundVal'->>'mfId'), actor_id, rule_id);
CREATE INDEX IF NOT EXISTS subscription_runtime_infos_smart_deposit_id_idx ON rms.public.subscription_runtime_infos
    USING BTREE((rule_param_values->'ruleParamValues'->'depositAccountId'->'sdValue'->>'accountId'), rule_id);
