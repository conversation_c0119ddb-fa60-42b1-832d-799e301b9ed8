CREATE TABLE IF NOT EXISTS workflow_requests
(
	id            VARCHAR     NOT NULL,
	actor_id      VARCHAR     NULL,
	stage         VARCHAR     NOT NULL,
	status        VARCHAR     NOT NULL,
	version       VARCHAR     NOT NULL,
	type          VARCHAR     NOT NULL,
	payload       VARCHAR     NULL,
	client_req_id VARCHAR     NOT NULL,
	ownership     VARCHAR     NOT NULL,
	next_action   JSONB       NULL,
	created_at    TIMESTAMPTZ NULL DEFAULT now(),
	updated_at    TIMESTAMPTZ NULL DEFAULT now(),
	deleted_at    TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id)
	);
COMMENT ON TABLE workflow_requests IS 'workflow_requests maintains all us stocks orders which went through orchestration';
COMMENT ON COLUMN workflow_requests.stage IS 'defines current stage of workflow execution';
COMMENT ON COLUMN workflow_requests.status IS 'defines current status of the ongoing stage of workflow execution';
COMMENT ON COLUMN workflow_requests.type IS 'defines type of workflow under execution';
COMMENT ON COLUMN workflow_requests.payload IS 'json format of request payload';

CREATE UNIQUE INDEX IF NOT EXISTS workflow_requests_client_req_id_key ON workflow_requests (client_req_id ASC);
CREATE INDEX IF NOT EXISTS workflow_requests_updated_at_idx ON workflow_requests (updated_at ASC);
CREATE INDEX IF NOT EXISTS workflow_requests_type_stage_status_created_at_idx ON workflow_requests (type ASC, stage ASC, status ASC, created_at ASC);


CREATE TABLE IF NOT EXISTS workflow_histories
(
	id                  UUID        NOT NULL DEFAULT gen_random_uuid(),
	wf_req_id           VARCHAR     NOT NULL,
	ext_req_id          VARCHAR     NULL,
	payload             VARCHAR     NULL,
	stage               VARCHAR     NOT NULL,
	status              VARCHAR     NOT NULL,
	created_at          TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at          TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at          TIMESTAMPTZ NULL,
	completed_at        TIMESTAMPTZ NULL,
	attempts            INT8        NOT NULL DEFAULT 1,
	failure_description VARCHAR     NULL,
	CONSTRAINT workflow_histories_pkey PRIMARY KEY (id)
	);
COMMENT ON TABLE workflow_histories IS 'workflow_histories tracks status of all stages for a given workflow';
COMMENT ON COLUMN workflow_histories.wf_req_id IS 'corresponding workflow Id for which the stage is being executed';
COMMENT ON COLUMN workflow_histories.ext_req_id IS 'If a stage is required to raise request with other services, ext_req_id will store the Identifier received. It can be mainly used for reverse lookups';
COMMENT ON COLUMN workflow_histories.payload IS 'payload is a json field used to store stage specific payload';

CREATE INDEX IF NOT EXISTS workflow_histories_updated_at_idx ON workflow_histories (updated_at ASC);
CREATE INDEX IF NOT EXISTS workflow_histories_wf_req_id_stage_idx ON workflow_histories (wf_req_id ASC, stage ASC);
