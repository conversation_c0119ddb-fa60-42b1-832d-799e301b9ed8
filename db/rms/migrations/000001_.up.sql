-- PostgreSQL allows you store and compare UUID values but it does not include functions for generating the UUID values in its core.
-- Instead, it relies on the third-party modules that provide specific algorithms to generate UUIDs. For example the uuid-ossp module provides
-- some handy functions that implement standard algorithms for generating UUIDs.
-- To install the uuid-ossp module, you use the CREATE EXTENSION statement as follows:
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE rules (
           id UUID default uuid_generate_v4() not null constraint rules_pkey PRIMARY KEY,
           name varchar not null,
           description jsonb not null,
           event_type varchar NOT NULL,
           category varchar NOT NULL,
           client varchar not null,
           condition jsonb,
           actions jsonb not null,
           created_at timestamp with time zone default now() not null,
           updated_at timestamp with time zone,
           deleted_at timestamp with time zone
);

-- adding indexes on event-type and client considering the nature of queries acc to API requirements
CREATE INDEX event_type_idx ON rules (event_type);
CREATE INDEX client_idx ON rules (client);

CREATE TABLE rule_subscriptions(
           id UUID default uuid_generate_v4() not null constraint rule_subscriptions_pkey PRIMARY KEY,
           rule_id UUID NOT NULL,
           state varchar NOT NULL,
           actor_id varchar NOT NULL,
           valid_from timestamp with time zone default now() not null,
           valid_till timestamp with time zone,
           rule_param_values jsonb,
           created_at timestamp with time zone default now() not null,
           updated_at timestamp with time zone,
           deleted_at timestamp with time zone,
           CONSTRAINT rule_subscription_id_ref_rules FOREIGN KEY (rule_id) REFERENCES rules(id)
);

-- not adding index on rule_id separately
-- because in cases where only rule_id is used in where clause, below index will be used by postgres
CREATE INDEX rule_actor_composite_idx ON rule_subscriptions(rule_id, actor_id);
