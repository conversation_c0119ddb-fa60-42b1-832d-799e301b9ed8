CREATE TABLE rule_executions(
    id UUID default uuid_generate_v4() not null constraint rule_executions_pkey PRIMARY KEY,
    rule_subscription_id UUID NOT NULL,
    state varchar NOT NULL,
    description varchar,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone,
    CONSTRAINT rule_executions_subscription_id_ref_rule_subscriptions FOREIGN KEY (rule_subscription_id) REFERENCES rule_subscriptions(id)
)
