-- update subscription which has already sms and what app to decrease cost
UPDATE rule_subscriptions
SET updated_at = now(),rule_param_values = jsonb_set(
	COALESCE(rule_param_values, '{}'::jsonb),
	'{ruleParamValues, commsMedium, commsMediums, mediumList}',
	COALESCE(
		(SELECT jsonb_agg(value)
		 FROM jsonb_array_elements(rule_param_values->'ruleParamValues'->'commsMedium'->'commsMediums'->'mediumList') AS value
		WHERE value::text NOT IN ('"SMS"', '"WHATSAPP"')),
                                '[]'::jsonb
                            )
						)
WHERE rule_id in ('d889238f-a21b-4bda-b51d-155075071119','d81b1323-0700-4eaa-adad-a72b5effeaf1') and updated_at<= '2024-05-03 10:57:18.379';
