-- Set the default notification for the empty communication medium list as part of the remaining backfill process
UPDATE rule_subscriptions
SET updated_at = now(),rule_param_values = jsonb_set(
	rule_param_values,
	'{ruleParamValues, commsMedium, commsMediums, mediumList}',
	CASE
		WHEN jsonb_array_length(rule_param_values->'ruleParamValues'->'commsMedium'->'commsMediums'->'mediumList') = 0 THEN jsonb_build_array('NOTIFICATION')
		ELSE rule_param_values->'ruleParamValues'->'commsMedium'->'commsMediums'->'mediumList'
		END
						)
WHERE rule_id in ('d889238f-a21b-4bda-b51d-155075071119','d81b1323-0700-4eaa-adad-a72b5effeaf1') and updated_at<= '2024-05-03 10:57:18.379';
