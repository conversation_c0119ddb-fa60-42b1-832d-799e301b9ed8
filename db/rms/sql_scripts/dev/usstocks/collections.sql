INSERT INTO collections (id, state, display_info, child_ids, is_featured, type)
VALUES ('7d70ba29-d8ff-420c-9252-52c81ef7f037', 'COLLECTION_ACTIVE', '{
	"name": {
		"text": "SIP",
		"font_color": "#333333"
	},
	"cardDisplayInfo": {
		"imgUrl": "https://epifi-icons.s3.ap-south-1.amazonaws.com/fittt-images/collections/3D/SystematicInvestments_Explore.png",
		"homePageImgUrl": "https://epifi-icons.s3.ap-south-1.amazonaws.com/fittt-images/collections/3D/SystematicInvestments_Home.png",
		"bgColor": "#D9F2CC"
	},
	"description": {
		"text": "Diversify your portfolio with an SIP in a US stock or ETF today",
		"fontColor": "#333333"
	}
}', '{
	"ruleIds": {
		"list": [
			"26550dcd-01a0-466c-86e0-801aba9538af"
		]
	}
}', false, 'COLLECTION_TYPE_US_STOCKS_SIP')
ON CONFLICT (id) DO UPDATE SET state        = EXCLUDED.state,
							   display_info = EXCLUDED.display_info,
							   child_ids    = EXCLUDED.child_ids,
							   is_featured  = EXCLUDED.is_featured,
							   type         = EXCLUDED.type;
