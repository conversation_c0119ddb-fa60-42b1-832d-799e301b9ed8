-- US Stocks Monthly SIP Rule
INSERT INTO rules (id, name, description, event_type, client,
				   category, condition, actions, max_subscriptions_per_actor,
				   max_subscriptions,
				   state, allowed_user_groups, weight,
				   subscriptions_aggregation_type, rule_type_for_special_handling,
				   version_support_info)
VALUES ('26550dcd-01a0-466c-86e0-801aba9538af', 'US STOCKS MONTHLY SIP',
		'{
			"displayStr": "Make an investment in {usStockId} for {purchaseAmount} on {configuredDateOfMonth} of every month",
			"inputParams": [
				{
					"name": "usStockId",
					"inputType": "US_STOCK_ID"
				},
				{
					"name": "purchaseAmount",
					"inputType": "MONEY"
				},
				{
					"name": "configuredDateOfMonth",
					"inputType": "INT_INPUT"
				}
			]
		}',
		'DAILY_EVENT', 'FITTT',
		'US_STOCKS_SIP',
		'{
			"condition": "configuredDateOfMonth == getDateFromDay(day)",
			"valuePath": {
				"day": {
					"path": [
						"Data",
						"DailyEvent",
						"Day"
					]
				}
			}
		}',
		'{
			"actionArr": [
				{
					"data": {},
					"type": "EXECUTE_US_STOCKS_SIP_ACTION"
				}
			]
		}',
		   -- check how many days it is assuming 100 subs per month per user at scale
		'2147483647', '2147483647',
		   -- check if special handling rule type should be changed
		'RULE_STATE_ACTIVE', '{"INTERNAL"}', 1, 'AGGREGATE_ON_RULE', 'RULE_TYPE_US_STOCKS_MONTHLY_SIP',
		'{
			"min_supported_android_app_version": 129,
			"min_supported_ios_app_version": 133
		}')
ON CONFLICT (id)
	DO UPDATE SET name                           = EXCLUDED.name,
				  description                    = EXCLUDED.description,
				  event_type                     = EXCLUDED.event_type,
				  client                         = EXCLUDED.client,
				  category                       = EXCLUDED.category,
				  condition                      = EXCLUDED.condition,
				  actions                        = EXCLUDED.actions,
				  max_subscriptions_per_actor    = EXCLUDED.max_subscriptions_per_actor,
				  max_subscriptions              = EXCLUDED.max_subscriptions,
				  state                          = EXCLUDED.state,
				  allowed_user_groups            = EXCLUDED.allowed_user_groups,
				  weight                         = EXCLUDED.weight,
				  subscriptions_aggregation_type = EXCLUDED.subscriptions_aggregation_type,
				  rule_type_for_special_handling = EXCLUDED.rule_type_for_special_handling,
				  version_support_info           = EXCLUDED.version_support_info;

-- Possible param values of US Stocks Monthly SIP rule
INSERT INTO possible_param_values (id, rule_id, param_value_type, value, is_default_value, state, weight)
VALUES
-- Date of month
('2564e111-935e-4a02-8c4d-15de5bffc2d0', '26550dcd-01a0-466c-86e0-801aba9538af', 'INT_INPUT',
 '{
	 "int_val": 1,
	 "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/01.png"
 }', false, 'PARAM_STATE_ACTIVE', 10),
('7af64c59-c50b-439a-b881-18da5c3034ec', '26550dcd-01a0-466c-86e0-801aba9538af', 'INT_INPUT',
 '{
	 "int_val": 5,
	 "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/05.png"
 }', true, 'PARAM_STATE_ACTIVE', 20),
('440b04cc-177a-4c8a-b1b7-be607ec0f4fc', '26550dcd-01a0-466c-86e0-801aba9538af', 'INT_INPUT',
 '{
	 "int_val": 10,
	 "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/10.png"
 }', false, 'PARAM_STATE_ACTIVE', 30),
('9317a505-b71e-433a-a9db-d4e3fd62c782', '26550dcd-01a0-466c-86e0-801aba9538af', 'INT_INPUT',
 '{
	 "int_val": 25,
	 "icon_url": "https://epifi-icons.pointz.in/fittt-images/icons/25.png"
 }', false, 'PARAM_STATE_ACTIVE', 40),
-- Preset amounts
('f3fcd1d4-657f-4dc9-9d8f-6c1cad0000c4', '26550dcd-01a0-466c-86e0-801aba9538af', 'MONEY',
 '{
	 "money_val": {
		 "currency_code": "INR",
		 "units": "1000"
	 }
 }', false, 'PARAM_STATE_ACTIVE', 10),
('6f98dbc5-1c23-44cc-ad94-d836b6a8c601', '26550dcd-01a0-466c-86e0-801aba9538af', 'MONEY',
 '{
	 "money_val": {
		 "currency_code": "INR",
		 "units": "5000"
	 }
 }', true, 'PARAM_STATE_ACTIVE', 20),
('9c98e1e9-f479-4201-a47e-d81735e3f931', '26550dcd-01a0-466c-86e0-801aba9538af', 'MONEY',
 '{
	 "money_val": {
		 "currency_code": "INR",
		 "units": "10000"
	 }
 }', false, 'PARAM_STATE_ACTIVE', 30),
('893e7dd7-0ec0-4b29-a0c3-f14095281240', '26550dcd-01a0-466c-86e0-801aba9538af', 'MONEY',
 '{
	 "money_val": {
		 "currency_code": "INR",
		 "units": "25000"
	 }
 }', false, 'PARAM_STATE_ACTIVE', 40),
('53b24955-f759-407e-8a13-e732e4711a8b', '26550dcd-01a0-466c-86e0-801aba9538af', 'MONEY',
 '{
	 "money_val": {
		 "currency_code": "INR",
		 "units": "100000"
	 }
 }', false, 'PARAM_STATE_ACTIVE', 50)
ON CONFLICT (id) DO UPDATE SET rule_id          = EXCLUDED.rule_id,
							   param_value_type = EXCLUDED.param_value_type,
							   value            = EXCLUDED.value,
							   is_default_value = EXCLUDED.is_default_value,
							   state            = EXCLUDED.state,
							   weight           = EXCLUDED.weight;


INSERT INTO param_value_selector_ctas (id, param_value_type, cta, state, rule_id)
VALUES ('48fd02e9-4132-471c-b52a-1e4dc215b925', 'MONEY', '{
	"customAmtCta": {
		"textForRuleDesc": "Select Amount",
		"textForPossibleValues": "Custom"
	}
}', 'CTA_STATE_ACTIVE', '26550dcd-01a0-466c-86e0-801aba9538af')
ON CONFLICT (id) DO UPDATE SET id               = EXCLUDED.id,
							   param_value_type = EXCLUDED.param_value_type,
							   cta              = EXCLUDED.cta,
							   state            = EXCLUDED.state,
							   rule_id          = EXCLUDED.rule_id;


-- Display info for US Stocks Monthly SIP rule
INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name, home_page_text,
								home_page_img_url, landing_page_text, landing_page_img_url, background_color,
								tags_bg_color, stats_bg_color)
VALUES ('e61620bb-9abb-42ec-8168-8992884dfd67', 'US_STOCKS_SIP', 'US STOCKS MONTHLY SIP', 'US STOCKS MONTHLY SIP',
		'Monthly SIP',
		'On 1st of every month, invest ₹3,000',
		'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead_Home.png',
		'On 1st of every month, invest ₹3,000',
		'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead.png', '#FAD0D0', '#CF8888',
		'#EFC0C0')
ON CONFLICT (id) DO UPDATE SET category            = EXCLUDED.category,
							   name                = EXCLUDED.name,
							   formatted_name      = EXCLUDED.formatted_name,
							   sentence_case_name  = EXCLUDED.sentence_case_name,
							   home_page_text      = EXCLUDED.home_page_text,
							   home_page_img_url   = EXCLUDED.home_page_img_url,
							   landing_page_text   = EXCLUDED.landing_page_text,
							   landing_page_img_url= EXCLUDED.landing_page_img_url,
							   background_color    = EXCLUDED.background_color,
							   tags_bg_color       = EXCLUDED.tags_bg_color,
							   stats_bg_color      = EXCLUDED.stats_bg_color;

INSERT INTO rule_tags (id, name, type, path, is_display_tag, state)
VALUES ('US_STOCKS', 'US STOCKS', 'TERMINAL', 'US_STOCKS', true, 'TAG_ACTIVE')
ON CONFLICT (id) DO UPDATE SET name           = EXCLUDED.name,
							   type           = EXCLUDED.type,
							   path           = EXCLUDED.path,
							   is_display_tag = EXCLUDED.is_display_tag,
							   state          = EXCLUDED.state;

INSERT INTO rule_tag_mappings (id, rule_id, tag_id)
VALUES ('b4429d42-780a-4c44-9777-9883d328b141', '26550dcd-01a0-466c-86e0-801aba9538af', 'US_STOCKS')
ON CONFLICT (id) DO UPDATE SET rule_id = EXCLUDED.rule_id,
							   tag_id  = EXCLUDED.tag_id;
