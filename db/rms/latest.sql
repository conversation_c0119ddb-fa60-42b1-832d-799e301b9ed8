CREATE EXTENSION IF NOT EXISTS ltree WITH SCHEMA public;
COMMENT ON EXTENSION ltree IS 'data type for hierarchical tree-like structures';
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.collections (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    state character varying NOT NULL,
    weight integer DEFAULT 1 NOT NULL,
    display_info json NOT NULL,
    child_ids json NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    is_featured boolean DEFAULT false,
    type character varying,
    allowed_user_groups character varying[],
    version_support_info jsonb
);
COMMENT ON COLUMN public.collections.state IS '{"proto_type":"rule.Collection.state", "comment": "State can be Active/Inactive"}';
COMMENT ON COLUMN public.collections.weight IS '{"proto_type":"rule.Collection.weight", "comment": "weight will help in ordering cards on UI"}';
COMMENT ON COLUMN public.collections.display_info IS '{"proto_type":"rule.CollectionDisplayInfo", "comment": "Contains all UI specific information required to render collection on UI"}';
COMMENT ON COLUMN public.collections.child_ids IS '{"proto_type":"rule.ChildIds", "comment": "contains list of either tagIds or ruleIds associated with collection"}';
CREATE TABLE public.do_once_tasks (
    task_name character varying NOT NULL,
    deleted_at_unix integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.do_once_tasks IS 'table to identify tasks by their unique names that should be done exactly once';
CREATE TABLE public.home_cards (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    state character varying NOT NULL,
    deeplink jsonb,
    weight integer DEFAULT 1 NOT NULL,
    card_data jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    allowed_user_groups character varying[],
    version_support_info jsonb
);
COMMENT ON COLUMN public.home_cards.state IS '{"proto_type":"rule.HomeCard.state", "comment": "State can be Active/Inactive"}';
COMMENT ON COLUMN public.home_cards.deeplink IS '{"proto_type":"frontend.deeplink.Deeplink", "comment": "provides data which the redirected screen would use to invoke API and load the page"}';
COMMENT ON COLUMN public.home_cards.weight IS '{"proto_type":"rule.HomeCard.weight", "comment": "weight will help in ordering cards"}';
COMMENT ON COLUMN public.home_cards.card_data IS '{"proto_type":"rule.HomeCardData", "comment": "Contains all information required to construct description text of the card"}';
CREATE TABLE public.param_value_selector_ctas (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    param_value_type character varying NOT NULL,
    cta jsonb,
    state character varying NOT NULL,
    rule_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.possible_param_values (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    param_value_type character varying NOT NULL,
    value jsonb,
    is_default_value boolean,
    state character varying NOT NULL,
    rule_id uuid NOT NULL,
    weight integer DEFAULT 1 NOT NULL
);
COMMENT ON COLUMN public.possible_param_values.id IS '{"proto_type":"rms.rule.PossibleParamValues", "comment": "Id of the player from vendor"}';
COMMENT ON COLUMN public.possible_param_values.param_value_type IS '{"proto_type":"rms.rule.PossibleParamValues", "comment": "Type of value `value` column holds"}';
COMMENT ON COLUMN public.possible_param_values.value IS '{"proto_type":"rms.rule.PossibleParamValues", "comment": "Json form of param value"}';
COMMENT ON COLUMN public.possible_param_values.is_default_value IS '{"proto_type":"rms.rule.PossibleParamValues", "comment": "Flag to define whether the value can be used as default among all selected ones"}';
CREATE TABLE public.rms_profiles (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    client character varying NOT NULL,
    profile_data jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON COLUMN public.rms_profiles.actor_id IS '{"proto_type":"RmsProfile.ActorId", "comment": "Actor Id identifies the original actor"}';
COMMENT ON COLUMN public.rms_profiles.client IS '{"proto_type":"RmsProfile.Client", "comment": "Client specifies which rms agent updated the value i.e fittt or rewards"}';
COMMENT ON COLUMN public.rms_profiles.profile_data IS '{"proto_type":"RmsProfile.ProfileData", "comment": "Actual Json data that contains various fields such as last accessed, etc"}';
CREATE TABLE public.rule_display_infos (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying NOT NULL,
    formatted_name character varying NOT NULL,
    home_page_text character varying NOT NULL,
    home_page_img_url character varying NOT NULL,
    landing_page_text character varying NOT NULL,
    landing_page_img_url character varying NOT NULL,
    background_color character varying NOT NULL,
    tags_bg_color character varying NOT NULL,
    sentence_case_name character varying NOT NULL,
    category character varying NOT NULL,
    stats_bg_color character varying,
    display_info jsonb
);
COMMENT ON COLUMN public.rule_display_infos.name IS '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Rule name"}';
COMMENT ON COLUMN public.rule_display_infos.formatted_name IS '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Formatted in a way that can be used on my rules page cards (eg: Healthy, Wealthy,\n Wise (%s))"}';
COMMENT ON COLUMN public.rule_display_infos.home_page_text IS '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Text to be displayed on home page"}';
COMMENT ON COLUMN public.rule_display_infos.home_page_img_url IS '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "S3 URL for rule image to be displayed on home page"}';
COMMENT ON COLUMN public.rule_display_infos.landing_page_text IS '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Text to be displayed on landing page"}';
COMMENT ON COLUMN public.rule_display_infos.landing_page_img_url IS '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "S3 URL for rule image to be displayed on landing page"}';
COMMENT ON COLUMN public.rule_display_infos.background_color IS '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Background color for card"}';
COMMENT ON COLUMN public.rule_display_infos.tags_bg_color IS '{"proto_type":"rms.rule.RuleDisplayInfo", "comment": "Background color for tags"}';
COMMENT ON COLUMN public.rule_display_infos.display_info IS '{"proto_type":"rms.DisplayInfo", "comment": "description of how the rule works"}';
CREATE TABLE public.rule_executions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    rule_subscription_id uuid NOT NULL,
    state character varying NOT NULL,
    description character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    client_event_id character varying,
    subscription_version_id uuid NOT NULL,
    event_unique_key character varying NOT NULL,
    batch_id character varying
);
CREATE TABLE public.rule_subscriptions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    rule_id uuid NOT NULL,
    state character varying NOT NULL,
    actor_id character varying NOT NULL,
    valid_from timestamp with time zone DEFAULT now() NOT NULL,
    valid_till timestamp with time zone,
    rule_param_values jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    subscription_expiry_data jsonb,
    cutoff_param_updated_at timestamp with time zone DEFAULT now() NOT NULL,
    version_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    version_valid_from timestamp with time zone DEFAULT now() NOT NULL,
    version_valid_till timestamp with time zone,
    version_state character varying DEFAULT 'CURRENT'::character varying NOT NULL,
    state_change_reason character varying DEFAULT 'NEW_SUBSCRIPTION'::character varying NOT NULL,
    state_change_provenance character varying DEFAULT 'USER_APP'::character varying NOT NULL,
    execution_state character varying DEFAULT 'SUBSCRIPTION_EXECUTION_ALLOWED'::character varying NOT NULL,
    client_request_id character varying
);
COMMENT ON COLUMN public.rule_subscriptions.subscription_expiry_data IS '{"proto_type":"rms.SubscriptionExpiryData", "comment": "Defines expiry for a particular subscription. This has higher priority to the rules.default_subscription_expiry_data"}';
COMMENT ON COLUMN public.rule_subscriptions.version_id IS '{"proto_type":"rms.rule.RuleSubscription", "comment": "A version is a snapshot of subscription. There can be multiple versions of subscription in cases of updates, however only one version will be Active at any time"';
COMMENT ON COLUMN public.rule_subscriptions.version_valid_from IS '{"proto_type":"rms.rule.RuleSubscription", "comment": "Defines the version validity end time. version_valid_till is the time when subscription was updated"';
COMMENT ON COLUMN public.rule_subscriptions.version_state IS '{"proto_type":"rms.rule.SubscriptionVersionState", "comment": "Defines state of the version. Only one among all the versions will be RUNTIME rest will be in EXPIRED state"';
CREATE TABLE public.rules (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying NOT NULL,
    description jsonb,
    event_type character varying NOT NULL,
    category character varying NOT NULL,
    client character varying NOT NULL,
    condition jsonb,
    actions jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    max_subscriptions_per_actor integer DEFAULT 2147483647 NOT NULL,
    max_subscriptions integer DEFAULT 2147483647 NOT NULL,
    default_subscription_expiry_data jsonb,
    state character varying DEFAULT 'RULE_STATE_ACTIVE'::character varying NOT NULL,
    allowed_user_groups character varying[],
    weight integer DEFAULT 1 NOT NULL,
    subscriptions_aggregation_type character varying DEFAULT 'AGGREGATE_ON_RULE'::character varying,
    version_support_info jsonb,
    aggregation_info jsonb,
    rule_type_for_special_handling character varying DEFAULT 'RULE_TYPE_UNSPECIFIED'::character varying NOT NULL
);
COMMENT ON COLUMN public.rules.default_subscription_expiry_data IS '{"proto_type":"rms.SubscriptionExpiryData", "comment": "Defines default expiry for all subscriptions to this rule"}';
COMMENT ON COLUMN public.rules.state IS '{"proto_type":"rms.rule.RuleSubscription", "comment": "cutoff params are those params which affects execution of subscription eg: player name, deposit amount, state of subscription (Active/Inactive). SD Name is not a cutoff param"}';
COMMENT ON COLUMN public.rules.subscriptions_aggregation_type IS '{"proto_type":"rule.SubscriptionsAggregationType", "comment": "My rules page displays subscriptions aggregated based on rules/tags, subscriptions_aggregation_type defines the type of aggregation that should be applied to subscriptions of this rule"}';
COMMENT ON COLUMN public.rules.aggregation_info IS '{"proto_type":"rms.rule.AggregationInfo", "comment": "storing if the rule contains aggregation related info"}';
COMMENT ON COLUMN public.rules.rule_type_for_special_handling IS '{"proto_type":"rms.rule.RuleTypeForSpecialHandling", "comment": "there are specific handling required for certain set of rules at FE layer mostly. Instead of identifying the rule based on name, type should be used for identification"}';
CREATE MATERIALIZED VIEW public.rule_executions_count_view AS
 SELECT rules.name,
    count(*) AS count
   FROM public.rule_executions,
    public.rule_subscriptions,
    public.rules
  WHERE ((rule_executions.subscription_version_id = rule_subscriptions.version_id) AND ((rule_executions.state)::text = 'ACTION_PROCESSING_SUCCESS'::text) AND (rule_subscriptions.rule_id = rules.id))
  GROUP BY rules.name
  WITH NO DATA;
CREATE TABLE public.rule_tag_mappings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    rule_id uuid NOT NULL,
    tag_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.rule_tags (
    id character varying NOT NULL,
    name character varying,
    type character varying NOT NULL,
    path public.ltree,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    is_display_tag boolean DEFAULT false NOT NULL,
    state character varying DEFAULT 'TAG_ACTIVE'::character varying NOT NULL,
    display_info jsonb
);
COMMENT ON COLUMN public.rule_tags.id IS '{"proto_type":"rms.rule.RuleTag", "comment": "name of the tag, value to identify tag uniquely"}';
COMMENT ON COLUMN public.rule_tags.name IS '{"proto_type":"rms.rule.RuleTag", "comment": "tag name to be used for display purposes"}';
COMMENT ON COLUMN public.rule_tags.type IS '{"proto_type":"rms.rule.RuleTag", "comment": "defines nature of tag node in tree (possible values Terminal, intermediate, root)"}';
COMMENT ON COLUMN public.rule_tags.path IS '{"proto_type":"rms.rule.RuleTag", "comment": " defines complete node path from root"}';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.subscription_runtime_infos (
    id uuid NOT NULL,
    rule_id uuid NOT NULL,
    actor_id character varying NOT NULL,
    state character varying NOT NULL,
    aggregation_id character varying NOT NULL,
    aggregation_type character varying NOT NULL,
    rule_param_values jsonb,
    last_executed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    client_request_id character varying
);
COMMENT ON COLUMN public.subscription_runtime_infos.id IS '{"proto_type":"SubscriptionRuntimeInfo.Id", "comment": "Id is the Rule subscription id"}';
COMMENT ON COLUMN public.subscription_runtime_infos.state IS '{"proto_type":"rule.RuleSubscriptionState", "comment": "Defines current state of subscription (ACTIVE/INACTIVE/CLOSED)"}';
COMMENT ON COLUMN public.subscription_runtime_infos.aggregation_id IS '{"proto_type":"SubscriptionRuntimeInfo.AggregationId", "comment": "My rules page displays subscriptions aggregated based on rules/tags, aggregation_id defines the id of aggregation that should be applied this subscription"}';
COMMENT ON COLUMN public.subscription_runtime_infos.aggregation_type IS '{"proto_type":"SubscriptionRuntimeInfo.SubscriptionsAggregationType", "comment": "My rules page displays subscriptions aggregated based on rules/tags, subscriptions_aggregation_type defines the type of aggregation that should be applied to this subscription"}';
COMMENT ON COLUMN public.subscription_runtime_infos.rule_param_values IS '{"proto_type":"rule.RuleParamValues", "comment": "Defines current param values for the subscription"}';
COMMENT ON COLUMN public.subscription_runtime_infos.last_executed_at IS '{"proto_type":"SubscriptionRuntimeInfo.LastExecutedAt", "comment": "timestamp for last successful execution of subscription. i.e. Execution resulted in state ACTION_PROCESSING_SUCCESS"}';
CREATE TABLE public.tag_display_infos (
    tag_id character varying NOT NULL,
    icon_urls character varying[]
);
COMMENT ON COLUMN public.tag_display_infos.tag_id IS '{"proto_type":"rms.rule.SportsTeam", "comment": "Id of a rule tag"}';
COMMENT ON COLUMN public.tag_display_infos.icon_urls IS '{"proto_type":"rms.rule.SportsTeam", "comment": "List of icons urls for a tag"}';
CREATE TABLE public.workflow_histories (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    wf_req_id character varying NOT NULL,
    ext_req_id character varying,
    payload character varying,
    stage character varying NOT NULL,
    status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    completed_at timestamp with time zone,
    attempts bigint DEFAULT 1 NOT NULL,
    failure_description character varying
);
COMMENT ON TABLE public.workflow_histories IS 'workflow_histories tracks status of all stages for a given workflow';
COMMENT ON COLUMN public.workflow_histories.wf_req_id IS 'corresponding workflow Id for which the stage is being executed';
COMMENT ON COLUMN public.workflow_histories.ext_req_id IS 'If a stage is required to raise request with other services, ext_req_id will store the Identifier received. It can be mainly used for reverse lookups';
COMMENT ON COLUMN public.workflow_histories.payload IS 'payload is a json field used to store stage specific payload';
CREATE TABLE public.workflow_requests (
    id character varying NOT NULL,
    actor_id character varying,
    stage character varying NOT NULL,
    status character varying NOT NULL,
    version character varying NOT NULL,
    type character varying NOT NULL,
    payload character varying,
    client_req_id character varying NOT NULL,
    ownership character varying NOT NULL,
    next_action jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.workflow_requests IS 'workflow_requests maintains all us stocks orders which went through orchestration';
COMMENT ON COLUMN public.workflow_requests.stage IS 'defines current stage of workflow execution';
COMMENT ON COLUMN public.workflow_requests.status IS 'defines current status of the ongoing stage of workflow execution';
COMMENT ON COLUMN public.workflow_requests.type IS 'defines type of workflow under execution';
COMMENT ON COLUMN public.workflow_requests.payload IS 'json format of request payload';
ALTER TABLE ONLY public.collections
    ADD CONSTRAINT collections_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.do_once_tasks
    ADD CONSTRAINT do_once_tasks_pkey PRIMARY KEY (task_name, deleted_at_unix);
ALTER TABLE ONLY public.home_cards
    ADD CONSTRAINT home_cards_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.param_value_selector_ctas
    ADD CONSTRAINT param_value_selector_ctas_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.possible_param_values
    ADD CONSTRAINT possible_param_values_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT "primary" PRIMARY KEY (id);
ALTER TABLE ONLY public.rms_profiles
    ADD CONSTRAINT rms_profiles_actor_id_client_key UNIQUE (actor_id, client);
ALTER TABLE ONLY public.rule_display_infos
    ADD CONSTRAINT rule_display_infos_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.rule_executions
    ADD CONSTRAINT rule_executions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.rule_subscriptions
    ADD CONSTRAINT rule_subscriptions_pkey PRIMARY KEY (version_id);
ALTER TABLE ONLY public.rule_tag_mappings
    ADD CONSTRAINT rule_tag_mappings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.rule_tags
    ADD CONSTRAINT rule_tags_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.rules
    ADD CONSTRAINT rules_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.rule_executions
    ADD CONSTRAINT subscription_event_id_unique_key UNIQUE (rule_subscription_id, client_event_id, event_unique_key);
ALTER TABLE ONLY public.subscription_runtime_infos
    ADD CONSTRAINT subscription_runtime_infos_client_request_id_key UNIQUE (client_request_id);
ALTER TABLE ONLY public.subscription_runtime_infos
    ADD CONSTRAINT subscription_runtime_infos_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.rms_profiles
    ADD CONSTRAINT user_profile_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.workflow_histories
    ADD CONSTRAINT workflow_histories_pkey PRIMARY KEY (id);
CREATE INDEX client_idx ON public.rules USING btree (client);
CREATE INDEX collection_created_at_idx ON public.collections USING btree (created_at);
CREATE INDEX collection_deleted_at_idx ON public.collections USING btree (deleted_at);
CREATE INDEX collection_state_idx ON public.collections USING btree (state);
CREATE INDEX collection_state_type_comp_idx ON public.collections USING btree (state, type);
CREATE INDEX collections_updated_at_idx ON public.collections USING btree (updated_at);
CREATE INDEX do_once_tasks_updated_at_idx ON public.do_once_tasks USING btree (updated_at);
CREATE INDEX event_type_idx ON public.rules USING btree (event_type);
CREATE INDEX param_value_selector_ctas_created_at_idx ON public.param_value_selector_ctas USING btree (created_at);
CREATE INDEX param_value_selector_ctas_rule_id_idx ON public.param_value_selector_ctas USING btree (rule_id);
CREATE INDEX param_value_selector_ctas_updated_at_idx ON public.param_value_selector_ctas USING btree (updated_at);
CREATE INDEX possible_param_values_rule_id_idx ON public.possible_param_values USING btree (rule_id);
CREATE INDEX rms_re_updated_at_index ON public.rule_executions USING btree (updated_at);
CREATE INDEX rms_rs_updated_at_index ON public.rule_subscriptions USING btree (updated_at);
CREATE INDEX rms_ru_updated_at_index ON public.rules USING btree (updated_at);
CREATE INDEX rule_actor_composite_idx ON public.rule_subscriptions USING btree (rule_id, actor_id);
CREATE UNIQUE INDEX rule_display_infos_category_name_unique_index ON public.rule_display_infos USING btree (category, name);
CREATE INDEX rule_display_infos_name_idx ON public.rule_display_infos USING btree (name);
CREATE INDEX rule_executions_batch_id_idx ON public.rule_executions USING btree (batch_id);
CREATE INDEX rule_executions_composite_state_subscription_vers_id_idx ON public.rule_executions USING btree (state, subscription_version_id);
CREATE UNIQUE INDEX rule_executions_count_view_name_idx ON public.rule_executions_count_view USING btree (name);
CREATE INDEX rule_subscriptions_across_actors_for_execution_idx ON public.rule_subscriptions USING btree (version_valid_from, version_valid_till, rule_id);
CREATE INDEX rule_subscriptions_actor_specific_subs_for_executions_idx ON public.rule_subscriptions USING btree (actor_id, state, version_valid_from, version_valid_till);
CREATE INDEX rule_subscriptions_actor_version_execution_state_idx ON public.rule_subscriptions USING btree (actor_id, version_state, execution_state);
CREATE UNIQUE INDEX rule_subscriptions_client_request_id_idx ON public.rule_subscriptions USING btree (client_request_id) WHERE ((version_state)::text = 'CURRENT'::text);
CREATE INDEX rule_subscriptions_composite_idx ON public.rule_subscriptions USING btree (rule_id, state, execution_state, version_valid_from, created_at);
CREATE UNIQUE INDEX rule_subscriptions_current_version_idx ON public.rule_subscriptions USING btree (id, version_state) WHERE ((version_state)::text = 'CURRENT'::text);
CREATE INDEX rule_tag_mappings_updated_at_idx ON public.rule_tag_mappings USING btree (updated_at);
CREATE INDEX rule_tags_path_idx ON public.rule_tags USING gist (path);
CREATE INDEX rule_tags_updated_at_idx ON public.rule_tags USING btree (updated_at);
CREATE INDEX state_idx ON public.home_cards USING btree (state);
CREATE INDEX subscription_runtime_infos_actor_rule_composite_idx ON public.subscription_runtime_infos USING btree (actor_id, rule_id);
CREATE INDEX subscription_runtime_infos_composite_idx ON public.subscription_runtime_infos USING btree (aggregation_id, actor_id, state);
CREATE INDEX subscription_runtime_infos_mutual_fund_id_idx ON public.subscription_runtime_infos USING btree ((((((rule_param_values -> 'ruleParamValues'::text) -> 'mutualFundVal'::text) -> 'mutualFundVal'::text) ->> 'mfId'::text)), actor_id, rule_id);
CREATE INDEX subscription_runtime_infos_rule_state_actor_composite_idx ON public.subscription_runtime_infos USING btree (rule_id, state, actor_id);
CREATE INDEX subscription_runtime_infos_smart_deposit_id_idx ON public.subscription_runtime_infos USING btree ((((((rule_param_values -> 'ruleParamValues'::text) -> 'depositAccountId'::text) -> 'sdValue'::text) ->> 'accountId'::text)), rule_id);
CREATE INDEX workflow_histories_updated_at_idx ON public.workflow_histories USING btree (updated_at);
CREATE INDEX workflow_histories_wf_req_id_stage_idx ON public.workflow_histories USING btree (wf_req_id, stage);
CREATE UNIQUE INDEX workflow_requests_client_req_id_key ON public.workflow_requests USING btree (client_req_id);
CREATE INDEX workflow_requests_type_stage_status_created_at_idx ON public.workflow_requests USING btree (type, stage, status, created_at);
CREATE INDEX workflow_requests_updated_at_idx ON public.workflow_requests USING btree (updated_at);
ALTER TABLE ONLY public.possible_param_values
    ADD CONSTRAINT rule_id_ref_rules FOREIGN KEY (rule_id) REFERENCES public.rules(id);
ALTER TABLE ONLY public.rule_subscriptions
    ADD CONSTRAINT rule_subscription_id_ref_rules FOREIGN KEY (rule_id) REFERENCES public.rules(id);
ALTER TABLE ONLY public.rule_tag_mappings
    ADD CONSTRAINT rule_tag_mappings_rule_id_ref_rules FOREIGN KEY (rule_id) REFERENCES public.rules(id);
ALTER TABLE ONLY public.rule_tag_mappings
    ADD CONSTRAINT rule_tag_mappings_tag_id_ref_rule_tags FOREIGN KEY (tag_id) REFERENCES public.rule_tags(id);
ALTER TABLE ONLY public.subscription_runtime_infos
    ADD CONSTRAINT subscription_runtime_infos_rule_id_fkey FOREIGN KEY (rule_id) REFERENCES public.rules(id);
ALTER TABLE ONLY public.tag_display_infos
    ADD CONSTRAINT tag_display_info_tag_id_ref_rule_tags FOREIGN KEY (tag_id) REFERENCES public.rule_tags(id);
