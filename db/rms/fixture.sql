-- above and beyond --
INSERT INTO rules (
    id, name, description, event_type, client,
    category, condition, actions, max_subscriptions_per_actor,
    max_subscriptions,
    state
)
VALUES
(
    'eeddf029-cdf5-413a-8634-b8561b0e6aca','ABOVE AND BEYOND', '{"displayStr": "When {configuredCricketPlayer} hits a boundary, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BATTING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && ( noOfSixes+noOfFours) > 0 ", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "noOfFours": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Fours"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}',
	'{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "(noOfSixes + noOfFours)*configuredDepositAmount", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "noOfFours": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Fours"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********',
    'RULE_STATE_ACTIVE'
);

-- howzaaattt --
INSERT INTO rules (
    id, name, description, event_type, client,
    category, condition, actions, max_subscriptions_per_actor,
    max_subscriptions,
    state
)
VALUES
(
     'eeddf029-cdf5-413a-8634-b8561b0e6acc', 'HOWZAAATTT', '{"displayStr": "When {configuredCricketPlayer} takes a wicket, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BOWLING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfWickets>0 ", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}',
	 '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression":"noOfWickets*configuredDepositAmount", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}}}]}, "type": "DEPOSIT"}]}',
    '2', '**********',
    'RULE_STATE_ACTIVE'
);


-- one team one dream --
INSERT INTO rules (
    name, description, event_type, client,
    category, condition, actions, max_subscriptions_per_actor,
    max_subscriptions,
    state
)
VALUES
(
    'ONE TEAM, ONE DREAM', '{"displayStr": "When {configuredCricketTeam} wins a match, put aside {depositAmount} in {depositAccountId}", "inputParams": [{"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition": "configuredCricketTeam == winningTeam ", "valuePath": {"winningTeam" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE'
);


-- maiden maiden! --
INSERT INTO rules (
    name, description, event_type, client,
    category, condition, actions, max_subscriptions_per_actor,
    max_subscriptions,
    state
)
VALUES
(
    'MAIDEN MAIDEN!', '{"displayStr": "When {configuredCricketPlayer} bowls a maiden, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BOWLING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfMaidenByPlayer > 0 ", "valuePath": {"noOfMaidenByPlayer" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "MaidenOvers"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}',
	'{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "noOfMaidenByPlayer*configuredDepositAmount", "valuePath": {"noOfMaidenByPlayer" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "MaidenOvers"]}}}]}, "type": "DEPOSIT"}]}',
    '2', '**********',
    'RULE_STATE_ACTIVE'
);

-- Dont Shop till you drop --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    '8f0bf6ea-bd23-48e1-ab39-bbd36baf1d7a','DON''T SHOP TILL YOU DROP', '{ "display_str": "When I order from {configuredMerchant} with Fi, put aside {depositAmount} in {depositAccountId}", "input_params": [ { "name":"configuredMerchant", "input_type":4 }, { "name":"depositAmount", "input_type":8 }, { "name":"depositAccountId", "input_type":7 } ] }',
    'PAYMENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '5', '**********',
    'RULE_STATE_ACTIVE'
  );

-- HEALTHY WEALTHY WISE --
  INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    'adf3b3b7-c866-4a76-881b-733167b5ec05','HEALTHY, WEALTHY, WISE', '{ "display_str": "When I order food from {configuredMerchant} with Fi, put aside {depositAmount} in {depositAccountId}", "input_params": [ { "name":"configuredMerchant", "input_type": "MERCHANT" }, { "name":"depositAmount", "input_type":"MONEY" }, { "name":"depositAccountId", "input_type": "SMART_DEPOSIT"}] }',
    'PAYMENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '5', '**********',
    'RULE_STATE_ACTIVE'
  );

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state) values
('7b6b8d59-07e2-4944-ae11-b83a7f03a104','eeddf029-cdf5-413a-8634-b8561b0e6aca', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ms_dhoni", "playerName":"MS Dhoni", "team":{"teamId":"csk", "teamName":"Chennai Super Kings", "teamLogo":"https://www.google.com/img/sample.jpg", "abbreviatedName":"CSK"}, "playerType":"BATSMAN"}}' ,true, 'PARAM_STATE_ACTIVE'),
('76281fa8-27ac-4018-8097-3ba5649ea557','eeddf029-cdf5-413a-8634-b8561b0e6aca', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"csk", "teamName":"Chennai Super Kings", "teamLogo":"https://www.google.com/img/sample.jpg", "abbreviatedName":"CSK"}, "playerType":"BATSMAN"}}' ,true, 'PARAM_STATE_ACTIVE'),
('76281fa9-27ac-4018-8097-3ba5649ea557','eeddf029-cdf5-413a-8634-b8561b0e6aca', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_tendulkar", "playerName":"Sachin Tendulkar", "team":{"teamId":"mi", "teamName":"Mumbai Indians", "teamLogo":"https://www.google.com/img/sample.jpg", "abbreviatedName":"MI"}, "playerType":"BATSMAN"}}' ,true, 'PARAM_STATE_ACTIVE'),
('76281fa1-27ac-4018-8097-3ba5649ea557','eeddf029-cdf5-413a-8634-b8561b0e6aca', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"v_kohli", "playerName":"Virat Kohli", "team":{"teamId":"rcb", "teamName":"Royal Challengers Bangalore", "teamLogo":"https://www.google.com/img/sample.jpg", "abbreviatedName":"RCB"}, "playerType":"BATSMAN"}}' ,true, 'PARAM_STATE_ACTIVE'),
('d0b628ad-80bc-418c-9020-59c5859ff6fb','adf3b3b7-c866-4a76-881b-733167b5ec05', 'MERCHANT',' {"merchantVal":{"merchantName":"Swiggy", "iconUrl":"https://www.google.com/img/sample.jpg"}}' ,true, 'PARAM_STATE_ACTIVE'),
('5a870aab-8d79-4e6e-a7fe-0371b45f894b','8f0bf6ea-bd23-48e1-ab39-bbd36baf1d7a', 'MERCHANT',' {"merchantVal":{"merchantName":"Amazon", "iconUrl":"https://www.google.com/img/sample.jpg"}}' ,true, 'PARAM_STATE_ACTIVE');
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state) values
('7a2a1ea2-1eb4-45c3-8c49-aee0e3ada2c7','8f0bf6ea-bd23-48e1-ab39-bbd36baf1d7a', 'MERCHANT',' {"merchantVal":{"merchantName":"Flipkart", "iconUrl":"https://www.google.com/img/sample.jpg"}}' ,true, 'PARAM_STATE_ACTIVE');


insert into rule_tags (id, name, type, path, is_display_tag, display_info) values
('Sports','Sports', 'ROOT' ,'Sports', false, '{}'),
('Cricket','Cricket', 'INTERMEDIATE' , 'Sports.Cricket', false, '{}'),
('T20','T20', 'INTERMEDIATE' , 'Sports.Cricket.T20', false, '{}'),
('OneDay','One Day', 'INTERMEDIATE' , 'Sports.Cricket.OneDay', false, '{}'),
('Test','Test', 'INTERMEDIATE' , 'Sports.Cricket.Test', false, '{}'),
('ICCWorldTestChampionship','ICC World Test Championship', 'TERMINAL' , 'Sports.Cricket.Test.ICCWorldTestChampionship', true, '{"chipColor":"#CF8888", "imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/Cricket.png", "bgColor": "#F4E7BF", "iconUrls": ["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"]}'),
('IPL2021','IPL 2021', 'TERMINAL' , 'Sports.Cricket.T20.IPL2021', true, '{"chipColor":"#CF8888", "imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/Cricket.png", "bgColor": "#F4E7BF", "iconUrls": ["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"]}'),
('Football','Football', 'INTERMEDIATE' , 'Sports.Football', false, '{}'),
('EURO2021','EURO 2021', 'TERMINAL' , 'Sports.Football.EURO2021', true, '{"chipColor":"#CF8888", "imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/Cricket.png", "bgColor": "#F4E7BF", "iconUrls": ["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"]}'),
('Merchant','Merchant', 'ROOT' , 'Merchant', false, '{}'),
('Shopping','Shopping', 'TERMINAL' , 'Merchant.Shopping', true, '{"chipColor":"#CF8888", "imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/Cricket.png", "bgColor": "#F4E7BF", "iconUrls": ["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"]}'),
('Food', 'Food', 'TERMINAL' , 'Merchant.Food', true, '{"chipColor":"#CF8888", "imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/Cricket.png", "bgColor": "#F4E7BF", "iconUrls": ["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"]}');

insert into rule_tag_mappings (tag_id, rule_id) values ('IPL2021', 'eeddf029-cdf5-413a-8634-b8561b0e6aca');

INSERT INTO rule_display_infos (category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color) values
    ('AUTO_SAVE', 'TIP YOURSELF', E'TIP \nYOURSELF', 'Tip Yourself', 'When I make a payment, round up and save','https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Home.png','When I make a payment, round up to nearest <u>defaultMoneyValue</u> and <u>save</u> the difference','https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Landing.png','#CDC6E8','#9183C7'),
    -- merchant rules --
    ('AUTO_SAVE', 'HEALTHY, WEALTHY, WISE', E'HEALTHY, WEALTHY,\n WISE', 'Healthy, Wealthy, Wise', 'When I order takeout, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/HWW-Home.png', 'When I order food from <u>defaultUniqueValue</u> with Fi, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/HWW-Landing.png', '#F4E7BF', '#C8AB52'),
    ('AUTO_SAVE', 'DON''T SHOP TILL YOU DROP', E'DON''T SHOP TILL\nYOU DROP', 'Don''t Shop till you Drop','When I order on <u>subscribedUniqueValue</u>, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Home.png', 'When I order from <u>defaultUniqueValue</u> with Fi, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Landing.png', '#D1DAF1', '#768CC3'),
    -- cricket rules --
    ('AUTO_SAVE', 'ABOVE AND BEYOND', E'ABOVE AND BEYOND\n','Above and Beyond', 'When <u>subscribedUniqueValue</u> hits a boundary, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/SuperSixer-Home.png', 'When <u>defaultUniqueValue</u> hits a boundary, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Sixer_Landing.png', '#EABEBE', '#BC6B6B'),
    ('AUTO_SAVE', 'MAIDEN MAIDEN!', E'MAIDEN MAIDEN!\n','Maiden Maiden!', 'When <u>subscribedUniqueValue</u> bowls a maiden, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Maiden-Home.png', 'When <u>defaultUniqueValue</u> bowls a maiden, save <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Maiden_Landing.png', '#CDE5C1', '#75AA5B'),
    ('AUTO_SAVE', 'CAN’T STOP, WON’T STOP', E'CAN’T STOP, WON’T STOP\n', 'CAN’T STOP, WON’T STOP','For every <u>subscribedIntVal</u> runs <u>subscribedUniqueValue</u> scores, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/IPLBatsman.png', 'For every <u>defaultIntVal</u> runs <u>defaultUniqueValue</u> scores, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/IPLBatsman.png', '#D1DAF1', '#768CC3'),
    ('AUTO_SAVE', 'DYNAMIC DUO', E'DYNAMIC DUO\n', 'Dynamic Duo','When partnership in team <u>subscribedUniqueValue</u> scores <u>subscribedIntVal</u> runs together, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/Partnership.png', 'When partnership in team <u>defaultUniqueValue</u> scores <u>defaultIntVal</u> runs together, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/Partnership.png', '#CDC6E8', '#9183C7'),
    ('AUTO_SAVE', 'DRAWN OUT', E'DRAWN OUT\n', 'Drawn Out','If a test match ends in a draw, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner-Home.png', 'If a test match ends in a draw, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner_Landing.png', '#F4E7BF', '#C8AB52'),
    ('AUTO_SAVE', 'SUPER SIXER', E'SUPER SIXER\n', 'Super Sixer','When <u>subscribedUniqueValue</u> hits a 6, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/SuperSixer-Home.png','When <u>defaultUniqueValue</u> hits a 6, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Sixer_Landing.png','#EABEBE','#BC6B6B'),
    ('AUTO_SAVE', 'HOWZAAATTT', E'HOWZAAATTT\n', 'Howzaaattt', 'When <u>subscribedUniqueValue</u> takes a wicket, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Wicket-Home.png','When <u>defaultUniqueValue</u> takes a wicket, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Wicket_Landing.png','#DEEEF2','#72BACC'),
    ('AUTO_SAVE', 'ONE TEAM, ONE DREAM', E'ONE TEAM,\n ONE DREAM', 'One Team, One Dream', 'When <u>subscribedUniqueValue</u> wins, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner-Home.png','When <u>defaultUniqueValue</u> wins, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner_Landing.png','#F4E7BF','#C8AB52'),
    ('AUTO_SAVE', 'MAIDEN MAIDEN', E'MAIDEN MAIDEN\n', 'Maiden Maiden', 'When <u>subscribedUniqueValue</u> bowl a maiden, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Maiden-Home.png','When any bowler from <u>defaultUniqueValue</u> bowls a maiden, save <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Maiden_Landing.png','#CDE5C1','#75AA5B'),
    -- football rules --
    ('AUTO_SAVE', 'EYES ON THE TROPHY', E'EYES ON THE TROPHY\n', 'Eyes on the Trophy', 'When <u>subscribedUniqueValue</u> wins, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/EyesOnTheTrophy_Home.png','When <u>defaultUniqueValue</u> wins, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/EyesOnTheTrophy.png','#CDC6E8','#9287BD'),
    ('AUTO_SAVE', 'GOOOOOOAL', E'GOOOOOOAL\n', 'Gooooooal', 'When <u>subscribedUniqueValue</u> scores a goal, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Goal-Home.png','When <u>defaultUniqueValue</u> scores a goal, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Gooooooal.png','#EABEBE','#CF8888'),
    ('AUTO_SAVE', 'OWNING THE GAME', E'OWNING THE GAME\n', 'Owning the Game', 'When <u>subscribedUniqueValue</u> keeps a clean sheet, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/OwningTheGame_Home.png','When <u>defaultUniqueValue</u> keeps a clean sheet, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/OwningTheGame.png','#CDE5C1','#A8CF92'),
    -- olympic rule --
    ('AUTO_SAVE', 'PEDAL TO THE MEDAL', E'PEDAL TO THE MEDAL\n', 'Pedal to the Medal', 'When India wins an Olympic medal, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/PedalToTheMedal_Home.png','When India wins an Olympic medal, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/PedalToTheMedal.png','#D1DAF1','#7C91C9'),
    -- app rules --
    ('AUTO_SAVE', 'ZEN MODE', E'ZEN MODE\n', 'Zen Mode', 'When I spend less than <u>subscribedDuration</u> on <u>subscribedUniqueValue</u>, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Zen_Home.png','When I spend less than <u>defaultDuration</u> on <u>defaultUniqueValue</u>, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Zen.png','#CDE5C1','#87BA6B');
