-- daily sip home_card --
insert into home_cards (id, state, deeplink, card_data, weight)
values ('5103f5ce-e7a2-44f4-ba2c-6cbe6a330a2f',
		'HOME_CARD_ACTIVE', '{
			"screen": "FIT_CUSTOMISE_RULE_SCREEN",
			"fitCustomiseRuleScreenOptions": {
				"ruleId": "d157d5c9-8b32-4f1b-9185-a60a8263ea25",
				"pageType": "SUBSCRIPTION_PAGE_NEW"
			}
		}',
        '{
			"tags": [
				{
					"text": "NEW",
					"bgColor": "#80869EDB"
				}
			],
			"title": {
				"text": "Daily SIP \nin Mutual Funds",
				"fontColor": "#333333"
			},
			"description": {
				"descStr": "SIPs starting at INR 100",
				"displayInfo": {
					"fontColor": "#646464"
				},
				"replaceableParamMap": {}
			},
			"displayInfo": {
				"imgUrl": "https://epifi-icons.pointz.in/fittt-images/home-card/daily-sip-home-card.png",
				"bgColor": "#D1DAF1"
			},
			"idForEventLogging": "dailySIPHomeCard"
		}',
		60);

-- updating monthly sip home_card --
update home_cards
set card_data=('{
  "tags": [
    {
      "text": "NEW",
      "bgColor": "#809E5A57"
    }
  ],
  "title": {
    "text": "Monthly SIP \nin Mutual Funds",
    "fontColor": "#333333"
  },
  "description": {
    "descStr": "Invest regularly in top fund houses",
    "displayInfo": {
      "fontColor": "#646464"
    },
    "replaceableParamMap": {}
  },
  "displayInfo": {
    "imgUrl": "https://epifi-icons.pointz.in/fittt-images/home-card/monthly-sip-home-card.png",
    "bgColor": "#FAD0D0"
  },
  "idForEventLogging": "monthlySIPHomeCard"
}')::jsonb
where id = 'cc730ef0-eec9-4ff4-abf2-bedccf03ea9b';

-- updating monthly sip weight --
update home_cards set weight=40 where id = 'cc730ef0-eec9-4ff4-abf2-bedccf03ea9b';
-- updating round up weight --
update home_cards set weight=50 where id = 'ab389f8e-4308-47f2-8435-ea5d41089826';

-- setting only some collections to be featured collections --
update collections set is_featured=false where id not in ('c8e66cbe-3c4a-4ea0-9686-5ccf9ba8cbe2',
                                                          '2bebb285-8883-40e3-8839-f09c8e87a89b',
                                                          'd67c8498-26a6-4cc1-a089-42a860eb0cf0');
