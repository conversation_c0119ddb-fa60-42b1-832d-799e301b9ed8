-- (version_id, rule_id) - not useful as version Id is primary key so only 1 record will be returned --
drop index if exists rule_subscription_composite_version_id_rule_id_idx;
-- (id, version_valid_from, version_valid_till) - not very useful as no more than 100 versions of a subscription is expected --
drop index if exists rule_subscription_version_valid_time_composite_idx;
-- (id, version_state) - not very useful as primary key index is already present for `id` and one subscription cannot have a huge number of versions
drop index if exists rule_subscription_version_state_composite_idx;

-- for queries to get subscriptions for an actor during execution of an rms event --
CREATE INDEX if not exists rule_subscriptions_actor_specific_subs_for_executions_idx ON rule_subscriptions USING btree (actor_id, state, version_valid_from, version_valid_till);
-- index to be used for getting subscriptions in a particular execution state --
CREATE INDEX if not exists rule_subscriptions_actor_execution_state_idx ON rule_subscriptions USING btree (actor_id, execution_state, version_state);
-- version_id is unique for rule_subscriptions --
CREATE INDEX if not exists rule_subscription_version_id_idx ON rule_subscriptions(version_id);
