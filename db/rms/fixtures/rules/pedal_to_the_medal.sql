-- PEDAL TO THE MEDAL --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    'd5d58aff-f45f-4e0a-99fb-634d13a82f3c','PEDAL TO THE MEDAL', '{"displayStr": "When India wins an Olympic medal, put aside {configuredDepositAmount} in {depositAccountId}", "inputParams": [{"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'OLYMPIC_INDIA_MEDAL_EVENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "true"}',
	'{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "noOfMedals*configuredDepositAmount"}]}, "valuePath": {"noOfMedals": {"path": ["Data", "OlympicsMedalsEvent", "Stats", "MedalsCount"]}}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE'
  );
