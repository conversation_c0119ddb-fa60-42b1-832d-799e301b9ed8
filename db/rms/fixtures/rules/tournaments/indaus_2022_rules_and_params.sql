
-- super sixer --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '********-5fd0-4707-85fa-73c16be9f062','SUPER SIXER', '{"displayStr": "When {configuredCricketPlayer} hits a 6, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BATTING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfSixes > 0 ", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}',
    '{"actionArr": [{"type": "DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "noOfSixes*configuredDepositAmount", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}}}]}}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 70
  );

-- howzaaattt --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '802d0767-333e-4285-86e9-6c6a2c6a7c21','HOWZAAATTT', '{"displayStr": "When {configuredCricketPlayer} takes a wicket, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BOWLING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfWickets>0 ", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression":"noOfWickets*configuredDepositAmount", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 60
  );

-- one team, one dream --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    'ef38780c-4492-493a-8602-f0f5818ee238','ONE TEAM, ONE DREAM', '{"displayStr": "When {configuredCricketTeam} wins a match, put aside {depositAmount} in {depositAccountId}", "inputParams": [{"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition": "configuredCricketTeam == winningTeam ", "valuePath": {"winningTeam" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 50
  );

-- creating rule tags --
insert into rule_tags (id, name, type, path, is_display_tag, display_info) values('INDVSAUS_T20_SEP2022','IND VS AUS', 'TERMINAL' , 'Sports.Cricket.T20.INDVSAUS_T20_SEP2022', true, '{"imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/INDvsRSA_T20_JUN2022.png","bgColor": "#FAD0D0","iconUrls":["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"], "chipColor": "#F4E7BF"}');

insert into home_cards (id, state, deeplink, card_data, weight) values
	('f2b3429b-38e6-44a3-b433-62299d6f7386', 'HOME_CARD_ACTIVE', '{"screen": "FIT_COLLECTION_PAGE", "fitCollectionPageScreenOptions": {"collectionId": "9544aeac-697e-4c61-b3f9-78f9d547d280", "selectedTagId": "INDVSAUS_T20_SEP2022"}}',
	 '{"title":{"text" : "IND vs AUS", "fontColor": "#333333"}, "tags": [{"text": "NEW", "bgColor": "#D3B250"}], "description": {"descStr": "It''s a tie, who will win?", "displayInfo": {"fontColor": "#333333"}, "replaceableParamMap": {}}, "displayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/CricketCraze_Home.png", "homeCardOldVersionImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/CricketCraze_Home.png", "bgColor": "#F4E7BF"}}', 80);

-- make the cricket mania collection state to active --
update collections set state='COLLECTION_ACTIVE' where id='9544aeac-697e-4c61-b3f9-78f9d547d280';

-- append a new tag to child ids --
update collections set child_ids=jsonb_set(child_ids::jsonb, '{tagIds, list}'::text[], ((child_ids->'tagIds'->'list')::jsonb || '"INDVSAUS_T20_SEP2022"')::jsonb, true) where id='9544aeac-697e-4c61-b3f9-78f9d547d280'  and not((child_ids->'tagIds'->'list')::jsonb ? 'INDVSAUS_T20_SEP2022');

INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
('d3d27e28-5286-472b-95d6-d988594c89a0', '********-5fd0-4707-85fa-73c16be9f062', 'INDVSAUS_T20_SEP2022'),
('760733b3-d4fc-4d84-8edb-725c827bd09c', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'INDVSAUS_T20_SEP2022'),
('a47171fb-19c9-4f9c-bf3e-34137623ee5e', 'ef38780c-4492-493a-8602-f0f5818ee238', 'INDVSAUS_T20_SEP2022');

-- super sixer values --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('14ed95ae-5fb7-4773-b8ed-e7f967a7bdfe', '********-5fd0-4707-85fa-73c16be9f062', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"75"}}', false, 'PARAM_STATE_ACTIVE', 20),
('b3a17560-d65d-4fcb-b6f2-2200bc67f7e0', '********-5fd0-4707-85fa-73c16be9f062', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 15),
('db75a03e-8ea9-46b9-bc82-6c0b79543a9d', '********-5fd0-4707-85fa-73c16be9f062', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"125"}}', true, 'PARAM_STATE_ACTIVE', 10),
('e6700790-2850-40fd-8a6c-482ba5c919a3', '********-5fd0-4707-85fa-73c16be9f062', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 5);

-- howzat values --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('30aab6c2-44bf-4f87-9c33-00d7325b9b20', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 20),
('d54f9538-99e8-4a3d-979d-d467443f91f3', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"75"}}', false, 'PARAM_STATE_ACTIVE', 15),
('fd7c4845-37e7-45aa-9722-2b1a4e415dd4', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', true, 'PARAM_STATE_ACTIVE', 10),
('bc30e0b0-7a51-477b-b384-06762975e6ee', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"125"}}', false, 'PARAM_STATE_ACTIVE', 5);

-- one team, one dream values --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('2b485d90-e940-4648-89b4-fe63b7fe4882', 'ef38780c-4492-493a-8602-f0f5818ee238', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('d125931d-340f-4ad2-a541-8024e38155bc', 'ef38780c-4492-493a-8602-f0f5818ee238', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
('06d8f072-d849-445b-a243-e5ffac5d1beb', 'ef38780c-4492-493a-8602-f0f5818ee238', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('e1a2e856-f90a-40e3-bfd1-eb3d91f71c56', 'ef38780c-4492-493a-8602-f0f5818ee238', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('543ba8b3-2c1f-4978-9173-cd2379581d7e', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"p_cummins", "playerName":"Pat Cummins", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 7),
('9a970817-131d-4f0d-a024-d51f2f1ec07a', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"g_maxwell", "playerName":"Glenn Maxwell", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 7),
('e5ec5b5f-6a26-41f8-bdf8-cd8a500d6ac7', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"g_maxwell", "playerName":"Glenn Maxwell", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b788caf9-6a38-4956-9401-3952475f7667', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_richardson", "playerName":"Kane Richardson", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3b78a45f-b3bb-4183-ba60-c264d6b7a32f', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"u_yadav", "playerName":"Umesh Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('7fd01aee-9eb5-4e2c-a6d0-8e0a0f2666ac', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"l_rahul", "playerName":"Lokesh Rahul", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('6602aade-9659-4094-bff9-9f77c7a7036c', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"y_chahal", "playerName":"YS Chahal", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 5),
('ac66c891-dc8d-4c34-be9f-3138419fefde', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_bumrah", "playerName":"Jasprit Bumrah", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,true, 'PARAM_STATE_ACTIVE', 10),
('d066646c-410b-4648-90fc-bcf08201dffb', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_wade", "playerName":"Matthew Wade", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('28994512-495c-4a91-b5ed-7cba269b4533', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"tim_david", "playerName":"Timothy David", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('1a2f8408-c0ff-4207-96d4-206b94efad77', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"cam_green", "playerName":"Cameron Green", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 6),
('d3b5de73-b674-439b-909b-f5d9507127ad', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"cam_green", "playerName":"Cameron Green", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 6),
('8cab21e9-6a9d-4aac-ac87-99bdfc3b9485', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_hazlewood", "playerName":"Josh Hazlewood", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('72998fdb-4652-42aa-a601-08982a8f0627', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_karthik", "playerName":"Dinesh Karthik", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 8),
('bb958b7b-396c-42dd-b60f-a164a6b55679', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('f4376937-4d01-4753-8b90-c984259437a3', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_pant", "playerName":"Rishabh Pant", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 5),
('dc3dd545-b954-4285-9d88-df21753ef653', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"h_pandya", "playerName":"Hardik Pandya", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 7),
('aad48dc1-bf5c-4f5d-83b1-3d0dd17775d6', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"h_pandya", "playerName":"Hardik Pandya", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 9),
('a10dc0d5-9ade-4f1a-8cac-6df4d40c6cad', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_ahmed", "playerName":"Mohammed Shami", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8218fc68-7170-4e7c-9b38-942e02697897', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_chahar", "playerName":"Deepak Chahar", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('83a2d354-ff4c-40bd-acb8-d134e51f1cee', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('abddcaf3-2254-497f-9ad7-17d844cf3c21', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('ae196612-3c3a-459b-a908-dfd5693f5318', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"h_patel", "playerName":"Harshal Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('7498608c-ac39-4b7a-98ec-266c64212f02', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"b_kumar", "playerName":"Bhuvneshwar Kumar", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('de3859f0-82a4-4ac4-b84b-f05ea63e939f', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_zampa", "playerName":"Adam Zampa", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('752d30ee-bc32-44ca-a119-c78ad2e001aa', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"rg_sharma", "playerName":"Rohit Sharma", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,true, 'PARAM_STATE_ACTIVE', 10),
('10a8db44-4e9f-44f4-89f2-c4933d235da6', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_finch", "playerName":"Aaron Finch", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 5),
('8a9412b7-45f4-46f9-9c14-9976b7c41532', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_abbott", "playerName":"Sean Abbott", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('754e1193-0b43-4bdc-b75d-96f141900f18', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"v_kohli", "playerName":"Virat Kohli", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('2315d049-8c74-4158-be29-296d558c6578', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_yadav", "playerName":"Suryakumar Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8238f958-0147-440e-9539-ca0268369723', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_sams", "playerName":"Daniel Sams", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('7eb744ca-8c77-4a25-85e7-b0bc62d763d8', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_sams", "playerName":"Daniel Sams", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('90c422f6-6b41-498e-b530-c9abea0ab4f8', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_smith", "playerName":"Steven Smith", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('469df0ec-4596-48ca-ad89-4397978b17b3', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__nathan_ellis__1a190", "playerName":"Nathan Ellis", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('48035f42-0fdb-4b6f-ab20-77db4d3c4275', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_hooda", "playerName":"Deepak Hooda", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('11c0cd67-05f8-4992-beca-3c10989a6a65', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_hooda", "playerName":"Deepak Hooda", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('81b41263-53e1-4321-b63f-dc45d544e5ac', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"b_dwarshuis", "playerName":"Ben Dwarshuis", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('11a79280-3661-4750-8f37-d273093a6c3c', '********-5fd0-4707-85fa-73c16be9f062', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"jo_inglis", "playerName":"Josh Inglis", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('2d786cea-996b-4f9f-aca4-73977cfa568a', '802d0767-333e-4285-86e9-6c6a2c6a7c21', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_agar", "playerName":"Ashton Agar", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('e4bbcae6-9c9d-442f-a47b-4f76b5a43cf6', 'ef38780c-4492-493a-8602-f0f5818ee238', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}}', true, 'PARAM_STATE_ACTIVE', 20),
('f71747e2-74da-45af-bfd0-5dea8bdecac9', 'ef38780c-4492-493a-8602-f0f5818ee238', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}}', false, 'PARAM_STATE_ACTIVE', 10);
