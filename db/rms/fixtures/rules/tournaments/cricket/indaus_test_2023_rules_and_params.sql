
-- above and beyond --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '7bf17a5b-b344-4a44-8096-c654c45b508b','ABOVE AND BEYOND', '{"displayStr": "When {configuredCricketPlayer} hits a boundary, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BATTING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && ( noOfSixes+noOfFours) > 0 ", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "noOfFours": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Fours"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "(noOfSixes + noOfFours)*configuredDepositAmount", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "noOfFours": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Fours"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 70
  );

-- howzaaattt --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7','HOWZAAATTT', '{"displayStr": "When {configuredCricketPlayer} takes a wicket, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BOWLING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfWickets>0 ", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression":"noOfWickets*configuredDepositAmount", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 60
  );

-- one team one dream --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '72d32eb9-7fb4-4ca3-a698-df73dea50fcb','ONE TEAM, ONE DREAM', '{"displayStr": "When {configuredCricketTeam} wins a match, put aside {depositAmount} in {depositAccountId}", "inputParams": [{"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition": "configuredCricketTeam == winningTeam ", "valuePath": {"winningTeam" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 50
  );

-- maiden maiden! --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '6abbee6c-f39d-4b53-bd16-3eb354611209','MAIDEN MAIDEN!', '{"displayStr": "When {configuredCricketPlayer} bowls a maiden, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BOWLING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfMaidenByPlayer > 0 ", "valuePath": {"noOfMaidenByPlayer" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "MaidenOvers"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "noOfMaidenByPlayer*configuredDepositAmount", "valuePath": {"noOfMaidenByPlayer" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "MaidenOvers"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 40
  );

-- can't stop won't stop--
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
   '689bad9b-2247-4232-ac27-26a276b6ca26', 'CAN’T STOP, WON’T STOP', '{"displayStr": "For every {configuredBatchRuns} runs {configuredCricketPlayer} scores, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredBatchRuns", "inputType": "INT_INPUT"},{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BATTING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && runsScored >= configuredBatchRuns ", "valuePath": {"runsScored": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "RunsScored"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "((runsScored/configuredBatchRuns) | 0)*configuredDepositAmount"}]}, "valuePath": {"runsScored": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "RunsScored"]}}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 30
  );

-- dynamic duo --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    'bef9f54f-**************-b5a088288357','DYNAMIC DUO', '{"displayStr": "When partnership in team {configuredCricketTeam} scores {configuredBatchRuns} runs together, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"},{"name": "configuredBatchRuns", "inputType": "INT_INPUT"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_PARTNERSHIP_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketTeam == configuredCricketTeam", "valuePath": {"cricketTeam" : {"path": ["Data", "TeamPartnershipEvent", "TeamId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"var_name": "depositAmount", "expression": "getNumOfPartnershipGreaterThanV2(partnershipStats,configuredBatchRuns)*configuredDepositAmount"}]}, "valuePath": {"partnershipStats" : {"path": ["Data", "TeamPartnershipEvent", "Stats"]}}, "type": "DEPOSIT"}]}',
    '1', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 20
  );

-- drawn out --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '556122a8-a188-40bc-baf1-33870066cdb5','DRAWN OUT', '{"displayStr": "If a test match ends in a draw, put aside {depositAmount} into {depositAccountId}", "inputParams": [ {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition": "matchDrawn(matchResult)", "valuePath": {"matchResult" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "ResultType"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 10
  );

insert into rule_tags (id, name, type, path, is_display_tag, display_info) values('ind_aus_feb_2023_test','IND v AUS 2023 TEST', 'TERMINAL' , 'Sports.Cricket.Test.ind_aus_feb_2023_test', true, '{"imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/INDvsRSA_OCT2022.png","bgColor": "#FAD0D0","iconUrls":["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"], "chipColor": "#CF8888"}');

update collections set child_ids=jsonb_set(child_ids::jsonb, '{tagIds, list}'::text[], ((child_ids->'tagIds'->'list')::jsonb || '"ind_aus_feb_2023_test"')::jsonb, true) where id='9544aeac-697e-4c61-b3f9-78f9d547d280' and not((child_ids->'tagIds'->'list')::jsonb ? 'ind_aus_feb_2023_test');

INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
('b41f8f6e-b553-438d-a7d9-4822a14686d5', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'ind_aus_feb_2023_test'),
('e722e727-f9f2-4cf9-9e79-495c10d18571', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'ind_aus_feb_2023_test'),
('22f1718f-4e1c-4069-b1eb-ee0de64e3e51', '72d32eb9-7fb4-4ca3-a698-df73dea50fcb', 'ind_aus_feb_2023_test'),
('8450b6f7-33c0-4293-9de3-ae2f075d12a8', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'ind_aus_feb_2023_test'),
('5d5b5765-da96-4a68-ac8b-b6f44ac4dd50', '689bad9b-2247-4232-ac27-26a276b6ca26', 'ind_aus_feb_2023_test'),
('9704633a-e593-4b25-9648-7edca1a876f4', 'bef9f54f-**************-b5a088288357', 'ind_aus_feb_2023_test'),
('12ab0c5f-6cc0-4123-a063-e039cd529021', '556122a8-a188-40bc-baf1-33870066cdb5', 'ind_aus_feb_2023_test');

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('0d403cfe-144c-4f2c-acc6-ce7b0bc1e1b2', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 20),
('3fd9cefa-8ad4-42e0-8984-fac951c07253', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 15),
('1297a0c9-349f-4bc2-846f-a67bb6c61696', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', true, 'PARAM_STATE_ACTIVE', 10),
('4462b7cf-9a79-4c57-809f-ab653327c5a1', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('f774bf0e-b4c0-49a6-a901-246d582b2372', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 20),
('a0f72288-f970-405e-a3fd-ddb9f1086adf', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"75"}}', false, 'PARAM_STATE_ACTIVE', 15),
('ec3fa759-6560-4ad6-a9c5-9f1e3d7970fa', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', true, 'PARAM_STATE_ACTIVE', 10),
('6d318871-4fab-4f06-9a65-ef4306a090c6', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"125"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('ba88dc75-ea1a-4828-92f0-7eab292c80c5', '72d32eb9-7fb4-4ca3-a698-df73dea50fcb', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('8f40b9bd-3f2f-49d6-97de-bfde726b5bd9', '72d32eb9-7fb4-4ca3-a698-df73dea50fcb', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
('6bf2b676-0a77-4c2d-bd75-c8471f1d88ea', '72d32eb9-7fb4-4ca3-a698-df73dea50fcb', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('4a35a9ae-7227-4c18-b77e-73bd8ad88e0b', '72d32eb9-7fb4-4ca3-a698-df73dea50fcb', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('dd2b303b-9870-4f4c-945d-bec2abd41c08', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('fe0ce59f-6cd6-4172-9964-9bc19d6264ae', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
('fc9ed571-265e-4fc9-849a-d7b9e701cc01', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('be3fed9c-f49b-4597-a97f-ed3ca1448651', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('1263e1e3-3335-4f77-a478-c5aa17adcd99', '689bad9b-2247-4232-ac27-26a276b6ca26', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 20),
('d8f7b179-d8e1-4828-9f49-af3d6b769e1f', '689bad9b-2247-4232-ac27-26a276b6ca26', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"75"}}', false, 'PARAM_STATE_ACTIVE', 15),
('d4c064e9-7a7c-44ad-8999-31d9e25af187', '689bad9b-2247-4232-ac27-26a276b6ca26', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', true, 'PARAM_STATE_ACTIVE', 10),
('e0a8c4a2-8419-470d-aad8-7fcf71cc39f8', '689bad9b-2247-4232-ac27-26a276b6ca26', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"125"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('59e4ea46-928f-4983-a0a3-45521a4d8f37', 'bef9f54f-**************-b5a088288357', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('8301be6e-a0b7-4413-835e-6a867fdee24b', 'bef9f54f-**************-b5a088288357', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
('54971ce7-63ea-49ab-bd72-3231ccf77110', 'bef9f54f-**************-b5a088288357', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('a4817917-a73a-4a39-a411-56cb9f07f4dd', 'bef9f54f-**************-b5a088288357', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('3b22d97f-59ef-409a-938b-730611fd1a90', '556122a8-a188-40bc-baf1-33870066cdb5', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('788bb79a-c734-46c9-b825-22ac088a0ac6', '556122a8-a188-40bc-baf1-33870066cdb5', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 15),
('70088bdc-46bd-4be4-a1c5-905d378cb52e', '556122a8-a188-40bc-baf1-33870066cdb5', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"500"}}', true, 'PARAM_STATE_ACTIVE', 10),
('6403e6e4-2c3a-43cb-a29b-8b0ef2373d19', '556122a8-a188-40bc-baf1-33870066cdb5', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"750"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(rule_id, param_value_type, value, is_default_value, state) values
('689bad9b-2247-4232-ac27-26a276b6ca26', 'INT_INPUT', '{"int_val":30}', false, 'PARAM_STATE_ACTIVE'),
('689bad9b-2247-4232-ac27-26a276b6ca26', 'INT_INPUT', '{"int_val":50}', true, 'PARAM_STATE_ACTIVE'),
('689bad9b-2247-4232-ac27-26a276b6ca26', 'INT_INPUT', '{"int_val":100}', false, 'PARAM_STATE_ACTIVE'),
('bef9f54f-**************-b5a088288357', 'INT_INPUT', '{"int_val":100}', true, 'PARAM_STATE_ACTIVE'),
('bef9f54f-**************-b5a088288357', 'INT_INPUT', '{"int_val":150}', false, 'PARAM_STATE_ACTIVE'),
('bef9f54f-**************-b5a088288357', 'INT_INPUT', '{"int_val":200}', false, 'PARAM_STATE_ACTIVE');

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('fd9ac930-00e5-4dbd-94d2-4673c1bc85a3', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3552fa66-4f6f-4b16-aaf3-5831b50541dc', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('e78e895e-b288-443a-9a5e-16814014ed13', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a2cdd4d2-3aed-4b29-8ce8-9f8f5f6fefce', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('302ef65f-4f5d-49f1-8cd9-faab639c7c17', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_swepson", "playerName":"Mitchell Swepson", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('49d1364b-fa62-4b07-a6bb-dd9295184c79', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_swepson", "playerName":"Mitchell Swepson", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('d1d6403f-66c1-4147-8c7f-46a570f14d0b', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"lan_morris", "playerName":"Lance Morris", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('5f9bd29e-307c-4fef-a481-a9c9ba5178f8', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"lan_morris", "playerName":"Lance Morris", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('7a927abb-5533-428e-8132-d5cd273450e6', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('966482a0-d7a0-4f8a-a614-9fa6290b66aa', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c58cd463-13e9-43ca-92eb-b9908d537db1', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('251d9800-b61f-4816-a78f-626edeba0d67', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('634c9558-1313-4394-ab84-048c1489eef3', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_yadav", "playerName":"Suryakumar Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 8),
('4b16eeef-36fe-4c8d-afbf-ac27f9b126a2', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_yadav", "playerName":"Suryakumar Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 8),
('9ba609d8-75a5-44cf-8a1c-c9e2de11ef8c', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"cam_green", "playerName":"Cameron Green", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('4e935fac-4fbb-4af0-b8fc-09babee04470', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"cam_green", "playerName":"Cameron Green", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('d58c37a8-e006-4b58-b46d-70fbf5d86a48', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ale_carey", "playerName":"Alex Carey", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('03b2bd98-e712-45df-b09a-a0cf8946faaf', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ale_carey", "playerName":"Alex Carey", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a987823c-f263-4c9d-9366-a21ca278f116', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"l_rahul", "playerName":"Lokesh Rahul", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('119ba74a-8da8-429c-9046-957de2e2350d', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"l_rahul", "playerName":"Lokesh Rahul", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('6cf7736c-5d5b-406f-b2ba-4c344596e6ad', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mar_labuschagne", "playerName":"Marnus Labuschagne", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c7a0b515-1ee5-4d6a-bdf9-5f7aa8484382', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mar_labuschagne", "playerName":"Marnus Labuschagne", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('4b502f12-06fa-4808-ba90-c3121fae2f6d', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mar_labuschagne", "playerName":"Marnus Labuschagne", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('795067fc-54f3-4d84-89a3-9e33cb84331e', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mar_labuschagne", "playerName":"Marnus Labuschagne", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('58dae616-a1c0-4916-bc97-181df4fbfc7d', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_ahmed", "playerName":"Mohammed Shami", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('84b46dcb-b3a9-4e95-8679-5f478b9fcbb7', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_ahmed", "playerName":"Mohammed Shami", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('507b26c8-e601-481d-8999-46aa6cfbd60f', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_warner", "playerName":"David Warner", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b3ee085a-41fa-410b-9999-6332f62a6d93', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_warner", "playerName":"David Warner", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b299bc14-3d7c-4051-87b4-731e5d20e154', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"v_kohli", "playerName":"Virat Kohli", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 9),
('6daeedaf-4cc9-49ef-9c8b-a17c70c37233', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"v_kohli", "playerName":"Virat Kohli", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 9),
('c76b7834-225e-4918-a384-84c618e59359', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_iyer", "playerName":"Shreyas Iyer", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('9b1ef0cd-3bd0-432c-96e5-c9d5f826444f', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_iyer", "playerName":"Shreyas Iyer", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('649838aa-b764-44b1-93c7-a9daa10d0587', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_yadav", "playerName":"Kuldeep Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('00a69d93-84f2-43a1-9770-8db0d4998b48', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_yadav", "playerName":"Kuldeep Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b11c4877-763c-4625-a89d-d9306afd3e89', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"p_cummins", "playerName":"Pat Cummins", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('ac0631eb-3f41-479b-86ac-4e664205a5d2', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"p_cummins", "playerName":"Pat Cummins", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('4f8be7d9-ed44-4a16-95c3-f7ece3ad5df6', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"n_lyon", "playerName":"Nathan Lyon", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b8bf3739-62db-4f67-a49e-2f2a56c08694', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"n_lyon", "playerName":"Nathan Lyon", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('03960f08-d4f2-4980-8e49-35adeb98e2f6', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_hazlewood", "playerName":"Josh Hazlewood", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c6091ef9-9bc0-4b32-9ebe-dd7cae01e419', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_hazlewood", "playerName":"Josh Hazlewood", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('11c47d7b-502e-4b55-adb4-9e4f10a9f894', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"t_head", "playerName":"Travis Head", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('cc73d4c5-cd4e-44ee-a1c4-b9edbad63c07', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"t_head", "playerName":"Travis Head", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('f80fef55-f340-489d-a378-3a7324e1e62e', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"t_head", "playerName":"Travis Head", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('28afb549-d046-4c0a-be06-73e614502026', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"t_head", "playerName":"Travis Head", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c9cc7c89-eb13-4977-b5d4-ab2436993f03', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ks_bharat", "playerName":"KS Bharat", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8344dbbe-b6dd-4f0d-9a18-cd0c922fb1c0', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ks_bharat", "playerName":"KS Bharat", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('154eddcd-e2fd-445c-bc77-5a39159e01bc', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c_pujara", "playerName":"Cheteshwar Pujara", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('530fd0b9-c0b1-42b8-8a97-319d9787033d', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c_pujara", "playerName":"Cheteshwar Pujara", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('fbeae2ae-749c-424c-9431-a57fe7283baf', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_siraj", "playerName":"Mohammed Siraj", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('eb3326eb-63ac-488f-8c28-7876665bbabb', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_siraj", "playerName":"Mohammed Siraj", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('6d4e97af-7918-407d-bea0-812007df2f09', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"i_kishan", "playerName":"Ishan Kishan", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('252f9ee6-aef8-4414-bf1e-8e551708b79f', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"i_kishan", "playerName":"Ishan Kishan", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('0450a7a4-2369-4401-b954-acd5f04a005f', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_boland", "playerName":"Scott Boland", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('589b2f3a-2344-4df8-9a52-e5dfd9f75ae8', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_boland", "playerName":"Scott Boland", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('98729978-aada-4b02-abe6-39a062c386bb', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_agar", "playerName":"Ashton Agar", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('0b9c7720-f719-4bba-bbb0-ed9f9f4d9e39', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_agar", "playerName":"Ashton Agar", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('e38f02f4-4a10-412c-899d-2e5e747ce37d', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_agar", "playerName":"Ashton Agar", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('944f95e5-4dba-4337-83d6-514742eb34a8', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_agar", "playerName":"Ashton Agar", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('fe0bc58f-b2c4-410f-8cec-b4ed7199e343', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"u_khawaja", "playerName":"Usman Khawaja", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('2ced25b7-028a-490a-babb-09d80e756b9e', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"u_khawaja", "playerName":"Usman Khawaja", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c0510ffa-005d-4989-b75c-e1a82c630e24', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"p_handscomb", "playerName":"Peter Handscomb", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c3397696-194f-4a7d-84d7-23c66d5f2f47', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"p_handscomb", "playerName":"Peter Handscomb", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('6e9b5608-3973-4f7b-b4e3-4a54487780d4', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3852c2f3-837d-49bf-8347-9c24a193cfa2', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c17ce95f-a9be-4490-8128-14129efa2eaf', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('eb73ecbf-e2e8-43d4-b4fd-1b4607d1098d', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('4bad151a-6655-43ba-bb84-928745c5c391', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mat_renshaw", "playerName":"Matt Renshaw", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3a86c55a-38e2-4f35-adda-b2f10a4125a0', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mat_renshaw", "playerName":"Matt Renshaw", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('dad7140a-5ce4-4331-9d63-b95b15a173e3', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mat_renshaw", "playerName":"Matt Renshaw", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('9e5386f8-ff47-44f1-8989-3a02a5aa4fe8', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mat_renshaw", "playerName":"Matt Renshaw", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('bc157cee-4d06-4fb3-a4d2-cf043872a269', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__todd_murphy__cb5f6", "playerName":"Todd Murphy", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('e06051df-809c-48dc-b541-8b49a6f3f823', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__todd_murphy__cb5f6", "playerName":"Todd Murphy", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('746522e7-018b-4277-bfab-b6b55a226084', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"u_yadav", "playerName":"Umesh Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('608e05df-1af2-47e1-84d5-e9b9d0c0297a', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"u_yadav", "playerName":"Umesh Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8a828f60-fea0-4809-8363-b73e132511df', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"rg_sharma", "playerName":"Rohit Sharma", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 10),
('680ada5e-40f5-4210-8480-6863dc6853bd', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"rg_sharma", "playerName":"Rohit Sharma", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 10),
('2d2759ff-f591-4e65-91af-c24f0912f79d', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_smith", "playerName":"Steven Smith", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b9de551a-1e36-4268-b305-c5755601ecb8', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_smith", "playerName":"Steven Smith", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('f0f9eaf0-b7a4-4575-89c3-572078fe7209', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_unadkat", "playerName":"Jaydev Unadkat", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('bb33c062-adad-4224-98e9-1c541ec02a0b', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_unadkat", "playerName":"Jaydev Unadkat", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('9d427d9f-0bea-4328-ae8b-42a2cde16cb2', '2a7d1fd5-e0f6-4ca9-8c72-50c6159af1b7', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_starc", "playerName":"Mitchell Starc", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('ee31a27a-1ce6-4a36-972f-df70fa3e2886', '6abbee6c-f39d-4b53-bd16-3eb354611209', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_starc", "playerName":"Mitchell Starc", "team":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('9be1fe69-2ec0-4c9a-824f-db8115fc3ad1', '7bf17a5b-b344-4a44-8096-c654c45b508b', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"su_gill", "playerName":"Shubman Gill", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('edd4a4f1-046c-462d-8847-898a34c0d1ae', '689bad9b-2247-4232-ac27-26a276b6ca26', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"su_gill", "playerName":"Shubman Gill", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1);
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('4a02a8ef-0183-4bb3-8f6d-daec023f400d', 'bef9f54f-**************-b5a088288357', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}}', false, 'PARAM_STATE_ACTIVE', 1),
('a4fb0b80-d556-4051-a18d-d5312fed4a76', '72d32eb9-7fb4-4ca3-a698-df73dea50fcb', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}}', false, 'PARAM_STATE_ACTIVE', 1),
('a72123a3-b2a2-4c7b-a46d-075f7efd5201', 'bef9f54f-**************-b5a088288357', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}}', false, 'PARAM_STATE_ACTIVE', 20),
('efeadd10-15ff-4897-b8c0-07e6375c1680', '72d32eb9-7fb4-4ca3-a698-df73dea50fcb', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}}', false, 'PARAM_STATE_ACTIVE', 20);
