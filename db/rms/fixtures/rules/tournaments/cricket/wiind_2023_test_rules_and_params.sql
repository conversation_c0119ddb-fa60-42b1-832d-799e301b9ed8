
-- above and beyond --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    'a865d7ed-84f3-4eb0-812c-c885166f1841','ABOVE AND BEYOND', '{"displayStr": "When {configuredCricketPlayer} hits a boundary, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BATTING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && ( noOfSixes+noOfFours) > 0 ", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "noOfFours": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Fours"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "(noOfSixes + noOfFours)*configuredDepositAmount", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "noOfFours": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Fours"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 70
  );

-- howzaaattt --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8','HOWZAAATTT', '{"displayStr": "When {configuredCricketPlayer} takes a wicket, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BOWLING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfWickets>0 ", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression":"noOfWickets*configuredDepositAmount", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 60
  );

-- one team one dream --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    'f1ba69e6-abad-4674-8672-614bddc1e357','ONE TEAM, ONE DREAM', '{"displayStr": "When {configuredCricketTeam} wins a match, put aside {depositAmount} in {depositAccountId}", "inputParams": [{"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition": "configuredCricketTeam == winningTeam ", "valuePath": {"winningTeam" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 50
  );

-- maiden maiden! --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '6ef7c9df-5e10-4447-8cd7-4080c5ca7114','MAIDEN MAIDEN!', '{"displayStr": "When {configuredCricketPlayer} bowls a maiden, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BOWLING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfMaidenByPlayer > 0 ", "valuePath": {"noOfMaidenByPlayer" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "MaidenOvers"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "noOfMaidenByPlayer*configuredDepositAmount", "valuePath": {"noOfMaidenByPlayer" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "MaidenOvers"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 40
  );

-- can't stop won't stop--
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
   '********-b002-4b4a-ac21-d070749d461d', 'CAN’T STOP, WON’T STOP', '{"displayStr": "For every {configuredBatchRuns} runs {configuredCricketPlayer} scores, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredBatchRuns", "inputType": "INT_INPUT"},{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BATTING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && runsScored >= configuredBatchRuns ", "valuePath": {"runsScored": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "RunsScored"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "((runsScored/configuredBatchRuns) | 0)*configuredDepositAmount"}]}, "valuePath": {"runsScored": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "RunsScored"]}}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 30
  );

-- dynamic duo --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    'bbd441eb-9547-474b-a962-3bf4f84ca11c','DYNAMIC DUO', '{"displayStr": "When partnership in team {configuredCricketTeam} scores {configuredBatchRuns} runs together, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"},{"name": "configuredBatchRuns", "inputType": "INT_INPUT"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_PARTNERSHIP_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketTeam == configuredCricketTeam", "valuePath": {"cricketTeam" : {"path": ["Data", "TeamPartnershipEvent", "TeamId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"var_name": "depositAmount", "expression": "getNumOfPartnershipGreaterThanV2(partnershipStats, configuredBatchRuns)*configuredDepositAmount"}]}, "valuePath": {"partnershipStats" : {"path": ["Data", "TeamPartnershipEvent", "Stats"]}}, "type": "DEPOSIT"}]}',
    '1', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 20
  );

-- drawn out --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '082c2b82-2b63-400a-b2a3-e6ac4455d839','DRAWN OUT', '{"displayStr": "If a test match ends in a draw, put aside {depositAmount} into {depositAccountId}", "inputParams": [ {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition": "matchDrawn(matchResult)", "valuePath": {"matchResult" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "ResultType"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 10
  );

insert into rule_tags (id, name, type, path, is_display_tag, display_info) values('wiind_2023_test','WI v IND 2023 TEST', 'TERMINAL' , 'Sports.Cricket.Test.wiind_2023_test', true, '{"imgUrl": "https://epifi-icons.pointz.in/fittt-images/tournaments/india_west_indies_cup.png","bgColor": "#FAD0D0","iconUrls":["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"], "chipColor": "#CF8888"}');

update collections set child_ids=jsonb_set(child_ids::jsonb, '{tagIds, list}'::text[], ((child_ids->'tagIds'->'list')::jsonb || '"wiind_2023_test"')::jsonb, true) where id='9544aeac-697e-4c61-b3f9-78f9d547d280' and not((child_ids->'tagIds'->'list')::jsonb ? 'wiind_2023_test');

INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
('e1001363-07d4-4198-9cf2-c3620169f671', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'wiind_2023_test'),
('edb294d3-4c5c-4228-a42c-4cc4ee437a01', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'wiind_2023_test'),
('01bf4946-4e4f-46cd-8aca-100e898fc51a', 'f1ba69e6-abad-4674-8672-614bddc1e357', 'wiind_2023_test'),
('0e4cbffa-2b80-4d1b-b795-48151382c095', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'wiind_2023_test'),
('123f578b-0c61-419b-8068-8a52ecc6edea', '********-b002-4b4a-ac21-d070749d461d', 'wiind_2023_test'),
('11989dd3-e57b-4372-a5bb-eb1eb199cde9', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'wiind_2023_test'),
('fe1061f6-7244-49b7-bade-227a77ab5141', '082c2b82-2b63-400a-b2a3-e6ac4455d839', 'wiind_2023_test');

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('5df89a99-4348-4967-b84f-a8dc8afa4640', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 20),
('4766cb72-cc68-480a-bc6b-9a1f445fff19', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 15),
('5c0f0cad-ff3f-4aa9-94d0-4d409f811d7b', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', true, 'PARAM_STATE_ACTIVE', 10),
('2886ce65-fdd4-42a4-8f63-85995cf69b6f', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('e0139b91-0a65-45f3-8113-90f6c9a1df0a', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 20),
('0a274b55-20ee-40b6-9531-d88198a00f0e', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"75"}}', false, 'PARAM_STATE_ACTIVE', 15),
('2fc344ce-9138-4714-8969-3152ff0e59a0', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', true, 'PARAM_STATE_ACTIVE', 10),
('ca13f038-24e5-49d7-b6ae-c4b0ffad67ce', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"125"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('0f5a7b98-bdd8-46f4-8a5e-7cf41fadfa7a', 'f1ba69e6-abad-4674-8672-614bddc1e357', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('b8d96a83-2cff-44d0-9c12-c8c56a2a7bb5', 'f1ba69e6-abad-4674-8672-614bddc1e357', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
('4b1283a2-e7c8-4643-b836-c92c9cd3ee18', 'f1ba69e6-abad-4674-8672-614bddc1e357', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('2b7ae147-7394-4311-83b5-c83766a6c897', 'f1ba69e6-abad-4674-8672-614bddc1e357', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('b2da9367-d87c-4727-a89d-57e52b3a1232', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('890919bd-4308-4b70-906e-769c42f7098a', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
('30ce0b92-bb20-4944-918d-805efedcefcf', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('1952beac-bad9-41c7-b143-0e0b46db2d41', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('7f608b75-2707-4da5-ae6f-ca6aa0dd0d78', '********-b002-4b4a-ac21-d070749d461d', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 20),
('863b848e-42dd-4a63-aa5c-c8825e639de5', '********-b002-4b4a-ac21-d070749d461d', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"75"}}', false, 'PARAM_STATE_ACTIVE', 15),
('93287718-78b1-45d4-8810-25dd11fd8e58', '********-b002-4b4a-ac21-d070749d461d', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', true, 'PARAM_STATE_ACTIVE', 10),
('4a938bc4-3334-408d-961d-edd15f5db19c', '********-b002-4b4a-ac21-d070749d461d', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"125"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('ecd4d0fd-9e51-4961-9610-6b1748ea44cb', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('c15a5d11-69dc-4977-90c8-1956c0409604', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
('d3355925-20d0-43c0-8894-a3c4cc88b781', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('104e8e60-08c5-4fdc-9e57-a102aeb850dd', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('cffb2579-2140-4733-be2c-f03e83c9a82f', '082c2b82-2b63-400a-b2a3-e6ac4455d839', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('601fddec-7392-4caf-bbbf-4386a4cd9081', '082c2b82-2b63-400a-b2a3-e6ac4455d839', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 15),
('37a086c6-d916-4306-a6d7-60dd8fb2cde3', '082c2b82-2b63-400a-b2a3-e6ac4455d839', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"500"}}', true, 'PARAM_STATE_ACTIVE', 10),
('b77ca73b-d114-4670-8684-5989e55405aa', '082c2b82-2b63-400a-b2a3-e6ac4455d839', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"750"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state) values
('97577ac0-4bd6-4015-b99f-5e6eb8064aeb', '********-b002-4b4a-ac21-d070749d461d', 'INT_INPUT', '{"int_val":30}', false, 'PARAM_STATE_ACTIVE'),
('1abd27dd-6e60-4206-8e8b-f89c159d15fb', '********-b002-4b4a-ac21-d070749d461d', 'INT_INPUT', '{"int_val":50}', true, 'PARAM_STATE_ACTIVE'),
('8da8b8e8-b9a0-491e-94ce-4702dc7afb67', '********-b002-4b4a-ac21-d070749d461d', 'INT_INPUT', '{"int_val":100}', false, 'PARAM_STATE_ACTIVE'),
('9dea6f4a-14cd-4082-80d1-8869c0233f1c', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'INT_INPUT', '{"int_val":100}', true, 'PARAM_STATE_ACTIVE'),
('c4525d52-46ea-4c6e-9b26-9b01116217e7', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'INT_INPUT', '{"int_val":150}', false, 'PARAM_STATE_ACTIVE'),
('b94eb2a9-b0a9-4e23-b1c8-630371a52b72', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'INT_INPUT', '{"int_val":200}', false, 'PARAM_STATE_ACTIVE');

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('9b3bc7e6-caa9-4290-a54a-db4d7467f935', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_warrican", "playerName":"Jomel Warrican", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b5a44c48-c6ff-4528-9880-86c953a51335', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_warrican", "playerName":"Jomel Warrican", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('099390bb-8a5d-43fd-8161-5816299e5425', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"rd_gaikwad", "playerName":"Ruturaj Gaikwad", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b6da6ddc-6895-4cb7-aef8-928a48b9ea22', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"rd_gaikwad", "playerName":"Ruturaj Gaikwad", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('4e2392f1-9df2-4415-85f6-babbe9fb82b9', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('9a70de3d-ad69-4d3f-a8b6-18e83065cfe9', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('5f56a056-ab4f-44ea-a8b2-1cfd8ec6246f', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8216caea-af06-4583-bab5-b7df1cd18b06', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_jadeja", "playerName":"Ravindra Jadeja", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('73ceb6c1-3e19-46db-9baa-fbd6eedbaa44', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__kona_srikar_bharat__00e3a", "playerName":"Kona Srikar Bharat", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('48af5aac-4ff1-49e3-b162-4e22135bad97', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__kona_srikar_bharat__00e3a", "playerName":"Kona Srikar Bharat", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('5e2ad9b4-2a0b-483d-81aa-4a1d2caf042d', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_yadav", "playerName":"Suryakumar Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 8),
('8c6ce77f-d7cf-438f-b917-70170e8b6140', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_yadav", "playerName":"Suryakumar Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 8),
('1bd730b1-4e23-4a75-9a5e-57654e2b82ea', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"na_saini", "playerName":"NA Saini", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('79f01d96-fd64-4b9b-b95d-1244714232e0', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"na_saini", "playerName":"NA Saini", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('eb52f6fe-8c7f-43e6-bcfb-22eb4279cd6a', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"al_joseph", "playerName":"Alzarri Joseph", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('e1905635-fe7f-48d4-83f8-b476ee838327', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"al_joseph", "playerName":"Alzarri Joseph", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3a929889-d206-410e-bb1d-f0b7cd69096b', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_athanaze", "playerName":"Alick Athanaze", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('daa75554-7c73-48d4-8a96-f089d1aef584', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_athanaze", "playerName":"Alick Athanaze", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('952245c4-c216-4392-99ea-a3f8adfa24d2', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__tevin_imlach__27ff9", "playerName":"Tevin Imlach", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('1195dd4e-842f-443b-bc75-2c9e30e71451', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__tevin_imlach__27ff9", "playerName":"Tevin Imlach", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('9be405b8-1bea-421b-b3cb-17962559bedd', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_rahane", "playerName":"Ajinkya Rahane", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('751a63b1-d2a0-45df-bb39-c9d0f7a7c41f', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_rahane", "playerName":"Ajinkya Rahane", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('1acc2b15-d675-47f5-94c8-86e183f1f3e6', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_yadav", "playerName":"Kuldeep Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('1b41dd82-07da-419b-a2a2-2f996159703d', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_yadav", "playerName":"Kuldeep Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('290675ff-d852-49c1-ae08-bb41ff4f4ac7', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_unadkat", "playerName":"Jaydev Unadkat", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a2fa7b9c-7b6e-4ec8-b398-d82163a9a2ac', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_unadkat", "playerName":"Jaydev Unadkat", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('16c15fe1-**************-9da6424c3bdf', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"y_chahal", "playerName":"YS Chahal", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('9cbe0a14-8c71-48c4-843f-8be9222ed487', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"y_chahal", "playerName":"YS Chahal", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('31dc69cb-1316-4f4c-933a-35cbec483880', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"av_khan", "playerName":"Avesh Khan", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('9dc8831e-78ff-43f1-9dbe-8fe0ce8e700b', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"av_khan", "playerName":"Avesh Khan", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a2e2b7be-bcba-4d47-823c-03d7f8d8853d', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"h_pandya", "playerName":"Hardik Pandya", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 7),
('8b030b6d-c379-4e7f-be90-8ac9ea285ada', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"h_pandya", "playerName":"Hardik Pandya", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 7),
('75992301-6b25-4b7d-8654-dd56644adc9e', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"h_pandya", "playerName":"Hardik Pandya", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 9),
('5da09776-0b63-4948-b422-c02675999240', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"h_pandya", "playerName":"Hardik Pandya", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 9),
('2e1d0cf5-6901-40f7-8a22-59ecde511013', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__kirk_mckenzie__1163e", "playerName":"Kirk McKenzie", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('add341de-4709-49ec-855a-c50ad82d59dc', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__kirk_mckenzie__1163e", "playerName":"Kirk McKenzie", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('ee2a022a-03fa-491c-aede-e30048c77e60', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"y_jaiswal", "playerName":"Yashasvi Jaiswal", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('00a61554-8331-4af6-9267-46b0b148448e', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"y_jaiswal", "playerName":"Yashasvi Jaiswal", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('cd86bf50-2194-4db2-9c51-eabb27d64cf9', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ard_singh", "playerName":"Arshdeep Singh", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c0efbace-a1a1-47aa-86f5-6e69a20b3737', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ard_singh", "playerName":"Arshdeep Singh", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('91721041-986f-4b78-8217-8e0d659d0163', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_thakur", "playerName":"Shardul Thakur", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('375756d7-a821-4ce0-a8d7-e7af3a0955eb', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_thakur", "playerName":"Shardul Thakur", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('27c3c5d3-e26a-4887-bd18-298d1a7b5ecb', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"josh_de_silva", "playerName":"Joshua Da Silva", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('4791c138-5f21-4d70-a5e7-b17f0f212fc8', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"josh_de_silva", "playerName":"Joshua Da Silva", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c8badda3-b716-460f-9bac-d6fab624c8ce', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_samson", "playerName":"Sanju Samson", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('1203c00e-e498-4edf-9d7a-2e1dfddda459', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_samson", "playerName":"Sanju Samson", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('424bb743-22b7-4651-8a21-61b25add1a26', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b9578f32-da19-4337-a30a-9c47dab421e6', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('647b1d4d-8c39-46d2-8308-1907899aed09', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8af245d4-d27e-491c-846f-fea216c378dc', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('5cda207b-e5c0-4c9e-b376-00d90d0b91ca', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_roach", "playerName":"Kemar Roach", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('7b981519-7361-4422-b734-d1f28b1a16d6', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_roach", "playerName":"Kemar Roach", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('e04ee1be-c3c7-4ec8-b050-ac17a5864466', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_holder", "playerName":"Jason Holder", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8758add9-f532-4425-ade2-c7708f6f0910', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_holder", "playerName":"Jason Holder", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a7d2b0e7-dcab-4fc4-b258-c11beee95c27', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_holder", "playerName":"Jason Holder", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8aa4a0c2-b084-42a9-a3f3-5e1bf92afba5', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_holder", "playerName":"Jason Holder", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('96406f5c-5032-4ffa-9458-eb91393109b9', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__tilak_varma__90ac7", "playerName":"Tilak Varma", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8ac84456-0b3a-4264-b5a8-56be7044d33e', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__tilak_varma__90ac7", "playerName":"Tilak Varma", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3697e3e2-f2af-4a49-b5c9-c94b3624615e', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_siraj", "playerName":"Mohammed Siraj", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('cbeef5fa-7271-4ee8-b98c-3ce6a4394fbd', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"m_siraj", "playerName":"Mohammed Siraj", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3ef31b1a-f1d3-4feb-8563-33ac9a99ad6b', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"su_gill", "playerName":"Shubman Gill", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('7a42fbe4-8ae5-4de4-87d1-c4f4ae46b6af', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"su_gill", "playerName":"Shubman Gill", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('449485bc-789a-4db4-adbd-7fbd5d406f13', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"i_kishan", "playerName":"Ishan Kishan", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('5e194081-dc61-4e59-b3d0-e970bea97835', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"i_kishan", "playerName":"Ishan Kishan", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('042cb48d-c4b2-4911-ab28-aa66182f6b3d', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"rg_sharma", "playerName":"Rohit Sharma", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 10),
('0533817a-8a9a-4e0c-be55-223fe2ddded6', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"rg_sharma", "playerName":"Rohit Sharma", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 10),
('*************-46a5-b396-d22658170423', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_gabriel", "playerName":"Shannon Gabriel", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('2c263867-7a7e-4177-8a14-d7c3987adcf9', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_gabriel", "playerName":"Shannon Gabriel", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('208c344d-acf7-49ce-bcf5-a16b83f44c07', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__umran_malik__14eed", "playerName":"Umran Malik", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a1609969-b4dd-436a-9031-4eda70e4534f', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__umran_malik__14eed", "playerName":"Umran Malik", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('0af93fa7-c193-436a-b35f-8bbb216820b3', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"v_kohli", "playerName":"Virat Kohli", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 9),
('9ac0dab8-d003-4a32-a22a-6d3e76a8535f', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"v_kohli", "playerName":"Virat Kohli", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 9),
('ae62bb90-91a6-4d8b-b236-1e458b6ccf7b', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_reifer", "playerName":"Raymon Reifer", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('e187af5a-ca9c-4087-b20e-ca95adabb5b8', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_reifer", "playerName":"Raymon Reifer", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('892f6eec-**************-6faded297094', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_reifer", "playerName":"Raymon Reifer", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('85052bd1-088c-4987-bdfa-41b4d301cb36', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_reifer", "playerName":"Raymon Reifer", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c9781a13-cad2-489e-a43c-2d6adb191c64', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_cornwall", "playerName":"Rahkeem Cornwall", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('d1ca8b6e-f19f-43c4-8528-bc2d45e8a576', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_cornwall", "playerName":"Rahkeem Cornwall", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('5ecd7f7a-5c58-45ee-9ad0-28d3bd21c567', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_cornwall", "playerName":"Rahkeem Cornwall", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a24c1d81-7f58-488c-9b4f-00b13748a41f', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_cornwall", "playerName":"Rahkeem Cornwall", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('624d440d-9b50-403b-9665-b7bbcaecae7c', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_blackwood", "playerName":"Jermaine Blackwood", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3a37e02e-3428-4f6a-a6d8-f83d2d005394', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_blackwood", "playerName":"Jermaine Blackwood", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('2303ffb7-40a3-49ee-8dc7-c2822c5daf21', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"t_Chanderpaul", "playerName":"Tagenarine Chanderpaul", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('ecb180da-2a02-4532-b40c-ac98108d5ac3', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"t_Chanderpaul", "playerName":"Tagenarine Chanderpaul", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('145cd45c-f528-4fca-a6cd-7252b574914f', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a059c849-f9a8-4e4f-bcfe-a3871ac70158', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('2acff672-8ef9-4dbe-b048-5b8e30ff453e', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('a4aa2d9c-b69c-44a7-a049-40d9ae4afdb7', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('2fc0a7ff-8675-4af2-b79f-71fd55044bcb', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__ravi_bishnoi__8a9ae", "playerName":"Ravi Bishnoi", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('43bf8ac6-acae-4042-9464-4d9232174b11', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__ravi_bishnoi__8a9ae", "playerName":"Ravi Bishnoi", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3d28225b-6d2f-4292-8084-be5b344963ed', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__mukesh_kumar__744ac", "playerName":"Mukesh Kumar", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('8048dad8-4dc9-48d2-bcda-4d301a68baeb', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__mukesh_kumar__744ac", "playerName":"Mukesh Kumar", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('14f8dc9b-7864-44d5-aa69-3b6b2c1f2c1a', 'e0876dd2-6969-4f6e-8835-b2ed3c9c4df8', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ake_jordan", "playerName":"Akeem Jordan", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('725104ce-de66-4a0b-ba2d-11115c6b6c0f', '6ef7c9df-5e10-4447-8cd7-4080c5ca7114', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ake_jordan", "playerName":"Akeem Jordan", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('5a34a41d-12f1-4ccb-9066-2fb35e27dcbf', 'a865d7ed-84f3-4eb0-812c-c885166f1841', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_brathwaite", "playerName":"Kraigg Brathwaite", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('1d886867-6a6d-4b8b-8be1-2e4936a692ed', '********-b002-4b4a-ac21-d070749d461d', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_brathwaite", "playerName":"Kraigg Brathwaite", "team":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1);
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('7567bd9d-73c1-42bd-8986-8998563973de', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}}', false, 'PARAM_STATE_ACTIVE', 20),
('7321cb1f-d321-46ee-bba6-5ec54de9f61e', 'f1ba69e6-abad-4674-8672-614bddc1e357', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}}', false, 'PARAM_STATE_ACTIVE', 20),
('351c7f76-51bf-4cc3-8d51-005f877e83fe', 'bbd441eb-9547-474b-a962-3bf4f84ca11c', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}}', false, 'PARAM_STATE_ACTIVE', 1),
('97d7ac0f-c58a-4758-b3a1-6861d8186c84', 'f1ba69e6-abad-4674-8672-614bddc1e357', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}}', false, 'PARAM_STATE_ACTIVE', 1);
