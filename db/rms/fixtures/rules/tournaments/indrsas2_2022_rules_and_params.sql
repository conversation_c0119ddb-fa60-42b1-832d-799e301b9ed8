
-- super sixer --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    'fc0153a3-9a4a-4af0-8776-92472a59fdcd','SUPER SIXER', '{"displayStr": "When {configuredCricketPlayer} hits a 6, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BATTING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfSixes > 0 ", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}',
    '{"actionArr": [{"type": "DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "noOfSixes*configuredDepositAmount", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}}}]}}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 70
  );

-- howzaaattt --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815','HOWZAAATTT', '{"displayStr": "When {configuredCricketPlayer} takes a wicket, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredCricketPlayer", "inputType": "CRICKET_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_BOWLING_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition": "cricketPlayer == configuredCricketPlayer && noOfWickets>0 ", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}',
    '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression":"noOfWickets*configuredDepositAmount", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}}}]}, "type": "DEPOSIT"}]}',
    '3', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 60
  );

-- one team, one dream --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions, subscriptions_aggregation_type,
  state, weight
)
VALUES
  (
    'b09ed9b8-ede9-460f-9dfa-a9f12cd2babe','ONE TEAM, ONE DREAM', '{"displayStr": "When {configuredCricketTeam} wins a match, put aside {depositAmount} in {depositAccountId}", "inputParams": [{"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'CRICKET_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition": "configuredCricketTeam == winningTeam ", "valuePath": {"winningTeam" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********', 'AGGREGATE_ON_TAG',
    'RULE_STATE_ACTIVE', 50
  );

-- creating rule tags --
insert into rule_tags (id, name, type, path, is_display_tag, display_info) values('INDVSRSA_OCT2022','IND VS RSA', 'TERMINAL' , 'Sports.Cricket.T20.INDVSRSA_OCT2022', true, '{"imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/INDvsRSA_OCT2022.png","bgColor": "#FAD0D0","iconUrls":["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"], "chipColor": "#F4E7BF"}');

-- creating home card --
insert into home_cards (id, state, deeplink, card_data, weight) values
	('f98ba0e4-318e-41b4-87ca-0969db12e6bb', 'HOME_CARD_INACTIVE', '{"screen": "FIT_COLLECTION_PAGE", "fitCollectionPageScreenOptions": {"collectionId": "9544aeac-697e-4c61-b3f9-78f9d547d280", "selectedTagId": "INDVSRSA_OCT2022"}}',
	 '{"title":{"text" : "IND vs SA", "fontColor": "#333333"}, "tags": [{"text": "NEW", "bgColor": "#D3B250"}], "description": {"descStr": "Cheer for India against South Africa!", "displayInfo": {"fontColor": "#333333"}, "replaceableParamMap": {}}, "displayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/CricketCraze_Home.png", "homeCardOldVersionImgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/3D/CricketCraze_Home.png", "bgColor": "#F4E7BF"}}', 80);

-- make the cricket mania collection state to active --
update collections set state='COLLECTION_ACTIVE' where id='9544aeac-697e-4c61-b3f9-78f9d547d280';

-- append a new tag to child ids --
update collections set child_ids=jsonb_set(child_ids::jsonb, '{tagIds, list}'::text[], ((child_ids->'tagIds'->'list')::jsonb || '"INDVSRSA_OCT2022"')::jsonb, true) where id='9544aeac-697e-4c61-b3f9-78f9d547d280'  and not((child_ids->'tagIds'->'list')::jsonb ? 'INDVSRSA_OCT2022');

INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
('e903f1b4-02f8-4a61-b087-c6b1ee8975e8', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'INDVSRSA_OCT2022'),
('3652286c-7f6d-4b1c-848d-c667c537e447', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'INDVSRSA_OCT2022'),
('df80f4d5-9890-4a2c-8247-137c4e697bd6', 'b09ed9b8-ede9-460f-9dfa-a9f12cd2babe', 'INDVSRSA_OCT2022');

-- super sixer values --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('4cb37159-5f70-44ba-8440-f6a37e471643', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"75"}}', false, 'PARAM_STATE_ACTIVE', 20),
('d87e5f91-1832-4e01-88d7-63b322619a44', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 15),
('f7c2d769-6d7d-4c25-8bf8-0c93b78cd527', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"125"}}', true, 'PARAM_STATE_ACTIVE', 10),
('85bfea65-b731-4ac9-b0d4-1d1a4392b9a8', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 5);

-- howzat values --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('6e129eaf-96b2-43dc-bf31-2b1450dcf6da', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 20),
('35c2847b-b99e-42f3-a94b-6661132be3a5', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"75"}}', false, 'PARAM_STATE_ACTIVE', 15),
('f4ac023c-1263-4695-9a89-859aace9fb74', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', true, 'PARAM_STATE_ACTIVE', 10),
('211f4426-0cee-475a-abff-aac137412adc', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"125"}}', false, 'PARAM_STATE_ACTIVE', 5);

-- one team, one dream values --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('d3e35deb-66b3-4336-95ac-16907f95a08b', 'b09ed9b8-ede9-460f-9dfa-a9f12cd2babe', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
('ec18148d-b788-4ae8-aa49-c8db1e28c906', 'b09ed9b8-ede9-460f-9dfa-a9f12cd2babe', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
('f7cb190b-3321-4a73-b582-c5d8c35f479b', 'b09ed9b8-ede9-460f-9dfa-a9f12cd2babe', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('43c11940-b62e-454e-84d3-a8fca8a91949', 'b09ed9b8-ede9-460f-9dfa-a9f12cd2babe', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('5283c5c5-b527-4405-ac22-e745a3e07912', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_ahmed", "playerName":"Mohammed Shami", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('74627166-7c8f-4ae1-82a0-ba8ff764d3f6', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_malan", "playerName":"Janneman Malan", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 2),
('4a3b43f0-eaad-4be7-9cfa-021a10c75a82', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_hooda", "playerName":"Deepak Hooda", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('daed90f9-4b5f-43bd-bb7e-43a0e16b5003', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_rabada", "playerName":"Kagiso Rabada", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c8b8ad14-c7df-4ac8-b4a2-30f97b264a23', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__tristan_stubbs__ff381", "playerName":"Tristan Stubbs", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c4df5653-52c3-4f1e-a8d4-1731a4d2e075', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"c__player__tristan_stubbs__ff381", "playerName":"Tristan Stubbs", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('30db9794-31a8-4b8b-b739-c11e2dc15720', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"h_klaasen", "playerName":"Heinrich Klaasen", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('6a52caa2-ad92-4583-888d-6c8a76906d61', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"l_rahul", "playerName":"Lokesh Rahul", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('4dc72264-bd73-4cde-81aa-c9629f9da822', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"v_kohli", "playerName":"Virat Kohli", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 9),
('cd53e593-86fe-4f1e-88eb-c6d8368993b6', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"rg_sharma", "playerName":"Rohit Sharma", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 10),
('47fb63af-43ec-4913-a0de-b5f5c1857ad5', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_ashwin", "playerName":"Ravichandran Ashwin", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('04d35631-9e5f-4b18-b964-416829d19a6f', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"b_fortuin", "playerName":"Bjorn Fortuin", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('6352583f-1c38-46b9-9dc0-79a81e00bc91', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"b_fortuin", "playerName":"Bjorn Fortuin", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('02c0fe59-5f57-4297-84cf-d5f2304f5e63', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_hendricks", "playerName":"Reeza Hendricks", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('be56cee3-d676-45cc-9c91-334a86108fea', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_phehlukwayo", "playerName":"Andile Phehlukwayo", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('bf4e25c3-72ea-47b7-a6d6-a64d7a78cbb9', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_phehlukwayo", "playerName":"Andile Phehlukwayo", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('df27174d-7b61-4992-972a-c1299d684091', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('c1f6a6b7-8246-49b4-8f29-92ce98628d9c', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_patel", "playerName":"Axar Patel", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('3e560676-d6ad-4ee9-9e81-1082587dc6a7', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"mar_jansen", "playerName":"Marco Jansen", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('6c5c8d14-9689-4bf4-af7f-fb97c43119cf', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"ard_singh", "playerName":"Arshdeep Singh", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b3c50527-4e95-499b-ab25-3edac5ecebf6', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_pant", "playerName":"Rishabh Pant", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 6),
('f179f5f1-cc5e-410a-b7df-2e76d223d663', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_maharaj", "playerName":"Keshav Maharaj", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 3),
('8e472699-db81-4829-8045-92ebe088df3b', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"k_maharaj", "playerName":"Keshav Maharaj", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 4),
('74af69e3-f31e-4867-becc-482384e60abd', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"s_yadav", "playerName":"Suryakumar Yadav", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 8),
('559d8cbd-94f1-43af-9205-e759f2a6bcd1', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_karthik", "playerName":"Dinesh Karthik", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('1052b2eb-370f-4d1d-86c1-4bea72821aa9', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"t_shamsi", "playerName":"Tabraiz Shamsi", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('34ee10a2-7443-470e-a759-05e979660d4f', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"t_bavuma", "playerName":"Temba Bavuma", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('fd2712ba-e813-4ec0-ad19-1d60db10d483', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_pretorius", "playerName":"Dwaine Pretorius", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('47cc25f4-bb4c-439c-b098-17f971f8b311', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_pretorius", "playerName":"Dwaine Pretorius", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 4),
('b8ae7c21-62cc-4184-ba72-66e9f0435bd0', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_chahar", "playerName":"Deepak Chahar", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('0723fe4a-a34a-47e8-a8bb-07b385e7c260', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"j_bumrah", "playerName":"Jasprit Bumrah", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 10),
('19226c11-6959-4053-a4f1-a3146ba5c469', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"y_chahal", "playerName":"YS Chahal", "team":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('cc504fc2-1f1b-47a0-bfd6-7605fa9a3025', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"w_parnell", "playerName":"Wayne Parnell", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('b0a85747-a08e-4431-ae7c-56c6400e33b4', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"d_miller", "playerName":"David Miller", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 3),
('02227ec0-dd40-471c-b53d-6f03095576bf', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"l_ngidi", "playerName":"Lungi Ngidi", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('355bec1b-ba3b-450b-94d4-51a2a007d34d', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"r_rossouw", "playerName":"RR Rossouw", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 1),
('276d9ec0-21c7-4cda-9009-0d0f53c2caa0', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"de_kock", "playerName":"Quinton de Kock", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 5),
('db12769c-1691-4712-900d-01362c116658', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"anr_nortje", "playerName":"Anrich Nortje", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 4),
('9e0d267e-6c1d-4da7-8a46-58c4ea3063de', 'fc0153a3-9a4a-4af0-8776-92472a59fdcd', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_markram", "playerName":"Aiden Markram", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', 3),
('768a40c6-ee6b-4128-b181-a409be2d5205', '1eeb30ab-b74a-4cbd-83b7-d23ca3d01815', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"a_markram", "playerName":"Aiden Markram", "team":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', 5);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
('0962972b-5b1f-4edc-9a20-c5adc9a85d4f', 'b09ed9b8-ede9-460f-9dfa-a9f12cd2babe', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}}', false, 'PARAM_STATE_ACTIVE', 20),
('1fe5e288-702e-4097-aac1-517bd3b62531', 'b09ed9b8-ede9-460f-9dfa-a9f12cd2babe', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}}', false, 'PARAM_STATE_ACTIVE', 10);
