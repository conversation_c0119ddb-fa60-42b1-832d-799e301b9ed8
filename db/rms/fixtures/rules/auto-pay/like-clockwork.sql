-- RECURRING PAYMENT --
INSERT INTO rules (id, name, description, event_type, client,
				   category, condition, actions, max_subscriptions_per_actor,
				   max_subscriptions,
				   state, weight, allowed_user_groups, version_support_info)
VALUES ('1c44b98f-0b75-4568-a06d-7474b00e4c55', 'RECURRING PAYMENT',
		'{"displayStr": "Send {recurringPaymentInfo} {paymentAmount} {frequency} starting from {startDate} till {endDate}", "input_params": [{"name": "recurringPaymentInfo", "input_type": "PAYMENT_RECIPIENT"}, {"name": "paymentAmount", "inputType": "MONEY"}, {"name": "frequency", "inputType": "FREQUENCY"}, {"name": "startDate", "inputType": "VALID_FROM_DATE"}, {"name": "endDate", "inputType": "VALID_TILL_DATE"}]}',
		'DAILY_EVENT', 'FITTT',
		'AUTO_PAY', '{"condition": "shouldExecuteBasedOnFrequency(startDate, endDate, frequency)", "valuePath": {}}',
		'{"actionArr": [{"data": {}, "type": "PAYMENT"}]}',
		'2147483647', '2147483647',
		'RULE_STATE_ACTIVE', 100, '{"INTERNAL"}',
		'{"min_supported_android_app_version":500, "min_supported_ios_app_version":500}');

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name, home_page_text,
								home_page_img_url, landing_page_text, landing_page_img_url, background_color,
								tags_bg_color, stats_bg_color)
values ('b112306e-aadd-438a-9e99-e7c41288ed26', 'AUTO_PAY', 'RECURRING PAYMENT', 'RECURRING PAYMENT',
		'Recurring Payment',
		'Send my landlord ₹10,000 every month',
		'https://epifi-icons.pointz.in/fittt-images/rules/recurring-payment-home.png',
		'Send my landlord ₹10,000 every month',
		'https://epifi-icons.pointz.in/fittt-images/rules/recurring-payment-landing.png', '#D1DAF1',
		'#768CC3', '#BBC8E9');

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight)
values ('d3c0f95e-102f-4171-a14b-6b2bab5ff017', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 'FREQUENCY',
		'{"frequency_val":{"display_text":"Weekly", "frequency":"FREQUENCY_WEEKLY"}}', false, 'PARAM_STATE_ACTIVE', 5),
	   ('4ed1b5cf-386b-4ae4-a578-ae99e9ccd6d4', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 'FREQUENCY',
		'{"frequency_val":{"display_text":"Fortnightly (every 15 days)", "frequency":"FREQUENCY_FORTNIGHTLY"}}', false,
		'PARAM_STATE_ACTIVE', 1),
	   ('0232a881-db07-4f49-9e36-ea2ab2117c66', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 'FREQUENCY',
		'{"frequency_val":{"display_text":"Monthly", "frequency":"FREQUENCY_MONTHLY"}}', true, 'PARAM_STATE_ACTIVE', 3),
	   ('4c908674-69bd-437a-8ed5-866c67ee8431', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 'FREQUENCY',
		'{"frequency_val":{"display_text":"Quarterly", "frequency":"FREQUENCY_QUARTERLY"}}', false,
		'PARAM_STATE_ACTIVE', 1),
	   ('9416af15-9b24-46ae-8b09-ce0152ce4f66', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 'FREQUENCY',
		'{"frequency_val":{"display_text":"Half-Yearly", "frequency":"FREQUENCY_HALF_YEARLY"}}', false,
		'PARAM_STATE_ACTIVE', 1),
	   ('2ecb79fe-819a-4812-9bed-6f97653863cc', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 'FREQUENCY',
		'{"frequency_val":{"display_text":"Yearly", "frequency":"FREQUENCY_YEARLY"}}', false, 'PARAM_STATE_ACTIVE', 2);

-- param value selector for custom amount value --
INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('ae65b7aa-0e76-4ebc-a64b-a96c6955b334', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 'MONEY',
		'{"customAmtCta":{"type":"CUSTOM_AMOUNT_SELECTOR","textForPossibleValues":"Add Amount","textForRuleDesc":"amount",
		"constraint": {"maxAmount": {"units": "200000", "currencyCode": "INR"},"minAmount": {"units": "100", "currencyCode": "INR"}, "stepAmount": {"units": "1","currencyCode": "INR"}},
		"iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png",
		"currencyIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/recurring_payment_selector_currency.png",
		"editIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/pencil.png"}}',
		'CTA_STATE_ACTIVE'),
	   ('3532e0a2-6192-42ea-9504-f5b9286b5a61', '1c44b98f-0b75-4568-a06d-7474b00e4c55', 'PAYMENT_RECIPIENT',
		'{"recurringPaymentCta":{"text":"Select a payee","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "editIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "payeeBgColor":"#AC7C44"}}',
		'CTA_STATE_ACTIVE');

----------------- ----------------- ----------------- -----------------
-- ONE TIME PAYMENT --
INSERT INTO rules (id, name, description, event_type, client,
				   category, condition, actions, max_subscriptions_per_actor,
				   max_subscriptions,
				   state, weight, allowed_user_groups, version_support_info)
VALUES ('95d7aa89-f081-4a07-90bc-7950775ce1b4', 'ONE-TIME PAYMENT',
		'{"displayStr": "Send {recurringPaymentInfo} {paymentAmount} on {date}", "input_params": [{"name": "recurringPaymentInfo", "input_type": "PAYMENT_RECIPIENT"}, {"name": "paymentAmount", "inputType": "MONEY"}, {"name": "date", "inputType": "VALID_FROM_DATE"}]}',
		'DAILY_EVENT', 'FITTT',
		'AUTO_PAY', '{"condition": "isCurrentDate(date)", "valuePath": {}}',
		'{"actionArr": [{"data": {}, "type": "PAYMENT"}]}',
		'2147483647', '2147483647',
		'RULE_STATE_ACTIVE', 90, '{"INTERNAL"}',
		'{"min_supported_android_app_version":500, "min_supported_ios_app_version":500}');

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name, home_page_text,
								home_page_img_url, landing_page_text, landing_page_img_url, background_color,
								tags_bg_color, stats_bg_color)
values ('0e78c2ed-4b43-4a8e-a47e-b2686a9debf4', 'AUTO_PAY', 'ONE-TIME PAYMENT', 'ONE-TIME PAYMENT', 'One-Time Payment',
		'Send mom ₹3,000 on July 07',
		'https://epifi-icons.pointz.in/fittt-images/rules/one-time-payment-home.png',
		'Send mom ₹3,000 on July 07',
		'https://epifi-icons.pointz.in/fittt-images/rules/one-time-payment-landing.png', '#F4E7BF',
		'#D3B250', '#EAD8A3');

-- param value selector for custom amount value --
INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('63460c74-45f6-40aa-a975-af0dfcc16cec', '95d7aa89-f081-4a07-90bc-7950775ce1b4', 'MONEY',
		'{"customAmtCta":{"type":"CUSTOM_AMOUNT_SELECTOR","textForPossibleValues":"Add Amount","textForRuleDesc":"amount",
		"constraint": {"maxAmount": {"units": "200000", "currencyCode": "INR"},"minAmount": {"units": "100", "currencyCode": "INR"}, "stepAmount": {"units": "1","currencyCode": "INR"}},
		"iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png",
    	"currencyIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/onetime_payment_selector_currency.png",
		"editIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/pencil.png"}}',
		'CTA_STATE_ACTIVE'),
	   ('f347914c-b1a7-4b89-b11f-c25a20b5b505', '95d7aa89-f081-4a07-90bc-7950775ce1b4', 'PAYMENT_RECIPIENT',
		'{"recurringPaymentCta":{"text":"Select a payee","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "editIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "payeeBgColor":"#2A496F"}}',
		'CTA_STATE_ACTIVE');


----------------- ----------------- ----------------- -----------------
-- home_card deeplink to the rule --
insert into home_cards (id, state, deeplink, card_data, weight, allowed_user_groups, version_support_info)
values ('477b7009-ef52-42df-a7cf-9e48089eb60e', 'HOME_CARD_ACTIVE',
		'{"screen": "FIT_COLLECTION_PAGE", "fitCollectionPageScreenOptions": {"collectionId": "d67c8498-26a6-4cc1-a089-42a860eb0cf0"}}',
			'{"idForEventLogging":"timelyPaymentsHomeCard", "tags": [{"text": "NEW", "bgColor": "#7FBECE"}], "title": {"text": "Timely Payments", "fontColor": "#333333"}, "description": {"descStr": "Pay rent, EMIs, send money home, and more", "displayInfo": {"fontColor": "#646464"}, "replaceableParamMap": {}}, "displayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/home-card/auto-pay-home-card.png", "bgColor": "#DEEEF2"}}',
		50, '{"INTERNAL"}', '{"minSupportedAndroidAppVersion":169, "minSupportedIosAppVersion":255}');

-- updating the child ids of the collection to reflect a new rule addition
UPDATE collections
set child_ids=jsonb_set(child_ids::jsonb, '{ruleIds, list}'::text[],
((child_ids -> 'ruleIds' -> 'list')::jsonb || '"95d7aa89-f081-4a07-90bc-7950775ce1b4"' || '"1c44b98f-0b75-4568-a06d-7474b00e4c55"')::jsonb, true)
where id = 'd67c8498-26a6-4cc1-a089-42a860eb0cf0';

-- updating the dynamic tag of the collection to reflect a new rule addition
update collections
SET display_info=jsonb_set(display_info::jsonb, '{dynamicTag}'::text[],
						   ('{"tag":{"text":"NEW", "bg_color":"#80478295"}, "expiry":"' ||
							to_char(((Now() + '24 days 00 hours 00 minutes'):: timestamp), 'YYYY-MM-DD"T"HH24:MI:SS"Z"') ||
							'"}')::jsonb,
						   true)
where id = 'd67c8498-26a6-4cc1-a089-42a860eb0cf0';
