update rules set version_support_info = '{"min_supported_android_app_version":170, "min_supported_ios_app_version":255}', state = 'RULE_STATE_ACTIVE' where id = '1c44b98f-0b75-4568-a06d-7474b00e4c55';
update rules set version_support_info = '{"min_supported_android_app_version":170, "min_supported_ios_app_version":255}', state = 'RULE_STATE_ACTIVE' where id = '95d7aa89-f081-4a07-90bc-7950775ce1b4';
update rules set version_support_info = '{"min_supported_android_app_version":170, "min_supported_ios_app_version":255}', state = 'RULE_STATE_ACTIVE' where id = 'c61fe3d4-80ff-4908-bc56-9ea042d3c046';
update collections set version_support_info  = '{"min_supported_android_app_version":170, "min_supported_ios_app_version":255}', state = 'COLLECTION_ACTIVE' where id = 'd67c8498-26a6-4cc1-a089-42a860eb0cf0';
update home_cards set version_support_info  = '{"min_supported_android_app_version":170, "min_supported_ios_app_version":255}', state = 'HOME_CARD_ACTIVE' where id = '477b7009-ef52-42df-a7cf-9e48089eb60e';
