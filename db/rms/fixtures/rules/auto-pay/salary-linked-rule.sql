-- MAINTAIN BALANCE --
INSERT INTO rules (id, name, description, event_type, client,
                   category, condition, actions, max_subscriptions_per_actor,
                   max_subscriptions,
                   state, weight, allowed_user_groups, version_support_info)
VALUES ('c61fe3d4-80ff-4908-bc56-9ea042d3c046', 'MAINTAIN BALANCE',
        '{"displayStr": "When my salary is credited to <PERSON>, send {paymentAmount} to {recurringPaymentInfo}", "input_params": [{"name": "paymentAmount", "inputType": "MONEY"}, {"name": "recurringPaymentInfo", "input_type": "PAYMENT_RECIPIENT"}]}',
        'SALARY_DETECTED_EVENT', 'FITTT',
        'AUTO_PAY', '{"condition": "true", "valuePath": {}}',
        '{"actionArr": [{"data": {}, "type": "PAYMENT"}]}',
        '2147483647', '2147483647',
        'RULE_STATE_ACTIVE', 100, '{"INTERNAL"}',
        '{"min_supported_android_app_version":500, "min_supported_ios_app_version":500}');

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name, home_page_text,
                                home_page_img_url, landing_page_text, landing_page_img_url, background_color,
                                tags_bg_color, stats_bg_color)
values ('1c817dba-cf9f-41f6-b34b-4b06feddff9b', 'AUTO_PAY', 'MAINTAIN BALANCE', 'MAINTAIN BALANCE',
        'Maintain Balance',
        'When my salary is credited to Fi, send ₹10,000 to other account',
        'https://epifi-icons.pointz.in/fittt-images/rules/maintain-balance-home.png',
        'When my salary is credited to Fi, send ₹10,000 to other account',
        'https://epifi-icons.pointz.in/fittt-images/rules/maintain-balance-landing.png', '#DEEEF2',
        '#7FBECE', '#C0DAE0');

-- param value selector for custom amount value --
INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('7320a075-0916-4d16-9c79-ce7dca92537c', 'c61fe3d4-80ff-4908-bc56-9ea042d3c046', 'MONEY',
        '{"customAmtCta":{"type":"CUSTOM_AMOUNT_SELECTOR","textForPossibleValues":"Add Amount","textForRuleDesc":"amount",
        "constraint": {"maxAmount": {"units": "100000", "currencyCode": "INR"},"minAmount": {"units": "1000", "currencyCode": "INR"}, "stepAmount": {"units": "1","currencyCode": "INR"}},
        "iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png",
        "currencyIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/recurring_payment_selector_currency.png",
        "editIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/pencil.png"}}',
        'CTA_STATE_ACTIVE'),
       ('c34bdc09-c7d3-4829-9bfc-19c178a8ab18', 'c61fe3d4-80ff-4908-bc56-9ea042d3c046', 'PAYMENT_RECIPIENT',
        '{"recurringPaymentCta":{"text":"Select a payee","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png", "editIconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/pencil.png", "payeeBgColor":"#AC7C44"}}',
        'CTA_STATE_ACTIVE');

-- updating the child ids of the collection to reflect a new rule addition
UPDATE collections
set child_ids=jsonb_set(child_ids::jsonb, '{ruleIds, list}'::text[],
                        ((child_ids -> 'ruleIds' -> 'list')::jsonb || '"c61fe3d4-80ff-4908-bc56-9ea042d3c046"')::jsonb, true)
where id = 'd67c8498-26a6-4cc1-a089-42a860eb0cf0';
