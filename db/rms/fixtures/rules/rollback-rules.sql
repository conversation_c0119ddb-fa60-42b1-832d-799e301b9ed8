-- consistency is key --
UPDATE rules set condition = '{"condition": "configuredDayOfWeek == dayOfWeek"}'
where name in ('CONSISTENCY IS KEY');

-- thinking ahead --
UPDATE rules set condition = '{"condition": "configuredDateOfMonth == dateOfMonth"}'
where name in ('THINKING AHEAD');

-- Healthy, wealthy and wise --
UPDATE rules set condition = '{"condition": "prefixMatch(merchantName, configuredMerchant)"}'
where name in ('HEALTHY, WEALTHY, WISE');

-- don't shop till you drop --
UPDATE rules set condition = '{"condition": "prefixMatch(merchantName, configuredMerchant)"}'
where name in ('DON''T SHOP TILL YOU DROP');

-- DYNAMIC DUO --
UPDATE rules set condition = '{"condition": "cricketTeam == configuredCricketTeam"}',
				 actions = '{"actionArr": [{"data": {"expressions": [{"var_name": "depositAmount", "expression": "getNumOfPartnershipGreaterThan(partnershipRunsArr, configuredBatchRuns)*configuredDepositAmount"}]}, "type": "DEPOSIT"}]}'
where name in ('DYNAMIC DUO');

-- DRAWN OUT --
UPDATE rules set condition = '{"condition": "isMatchDrawn"}'
where name in ('DRAWN OUT');

-- above and beyond --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && ( noOfSixes+noOfFours) > 0 "}',
				 actions = '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "(noOfSixes + noOfFours)*configuredDepositAmount"}]}, "type": "DEPOSIT"}]}'
where name in ('ABOVE AND BEYOND');

-- super sixer --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && noOfSixes > 0 "}',
				 actions = '{"actionArr": [{"type": "DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "noOfSixes*configuredDepositAmount"}]}}]}'
where name in ('SUPER SIXER');

-- howzaaattt --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && noOfWickets>0 "}',
				 actions = '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression":"noOfWickets*configuredDepositAmount"}]}, "type": "DEPOSIT"}]}'
where name in ('HOWZAAATTT');

-- one team one dream --
UPDATE rules set condition = '{"condition": "configuredCricketTeam == winningTeam "}'
where name in ('ONE TEAM, ONE DREAM');

-- maiden maiden! --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && noOfMaidenByPlayer > 0 "}',
				 actions = '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "noOfMaidenByPlayer*configuredDepositAmount"}]}, "type": "DEPOSIT"}]}'
where name in ('MAIDEN MAIDEN!');

-- GOOOOOOAL --
UPDATE rules set condition = '{"condition":"footballPlayer == configuredFootballPlayer && noOfGoalsByPlayer > 0 "}',
				 actions = '{"actionArr": [{"type":"DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "noOfGoalsByPlayer*configuredDepositAmount"}]}}]}'
where name in ('GOOOOOOAL');

-- EYES ON THE TROPHY --
UPDATE rules set condition = '{"condition":"winnerFootballTeam == configuredFootballTeam"}'
where name in ('EYES ON THE TROPHY');

-- keep the change --
UPDATE rules set condition = '{"condition": "paymentAmount.Units >= 50"}',
				 actions = '{"actionArr": [{"type": "DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "calculateChangeAmountV2(paymentAmount, configuredRoundAmount)"}]}}]}'
where name in ('KEEP THE CHANGE');

-- DRAWN OUT --
UPDATE rules set condition = '{"condition": "matchDrawn(matchResult)"}'
where name in ('DRAWN OUT');

-- TIP YOURSELF --
UPDATE rules set condition = '{"condition": "paymentAmount.Units >= 50"}',
				 actions = '{"actionArr": [{"data": {"expressions": [ { "var_name": "depositAmount", "expression": "differenceFromNearestMultiple(paymentAmount, configuredRoundAmount)" }]}, "type": "DEPOSIT"}]}'
where name in ('TIP YOURSELF');

-- PEDAL TO THE MEDAL --
UPDATE rules set actions = '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "noOfMedals*configuredDepositAmount"}]}, "type": "DEPOSIT"}]}'
where name in ('PEDAL TO THE MEDAL');

-- MAIDEN MAIDEN --
UPDATE rules set condition = '{"condition": "cricketTeam == configuredCricketTeam && noOfMaidenOvers > 0"}',
				 actions = '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "noOfMaidenOvers*configuredDepositAmount"}]}, "type": "DEPOSIT"}]}'
where name in ('MAIDEN MAIDEN');

