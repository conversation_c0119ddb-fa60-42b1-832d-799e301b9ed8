DELETE from param_value_selector_ctas where id in ('78c54f44-2d28-4d3c-9f90-ddaaa7ba0f03', '6586307c-3d10-4492-bdb5-4a900128bb41',
                                                  '5eca8fa7-a972-415a-ad08-3d5819512d92', 'a4528fbd-b2e6-4bf0-a324-7447a6c85d8c',
                                                  '05dca7f2-e49f-4366-a363-b6e057bd68e5', 'f241ca37-64eb-4477-a319-51b62beb1a6d');

INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state) values
--- DON'T SHOP TILL YOU DROP
('78c54f44-2d28-4d3c-9f90-ddaaa7ba0f03', 'bc925bcb-ff12-43f9-8396-b6383242d939' , 'MONEY',	'{"customAmtCta": {"constraint": {"minAmount": {"units": "10", "currencyCode": "INR"}, "maxAmount": {"units": "10000", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}','CTA_STATE_ACTIVE'),
--- CONSISTENCY IS KEY
('6586307c-3d10-4492-bdb5-4a900128bb41', '7b251ad6-a653-4c8a-88fa-a3714b45b480' , 'MONEY',	'{"customAmtCta": {"constraint": {"minAmount": {"units": "50", "currencyCode": "INR"}, "maxAmount": {"units": "10000", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}','CTA_STATE_ACTIVE'),
--- THINKING AHEAD
('5eca8fa7-a972-415a-ad08-3d5819512d92', 'bb093acb-fb94-4aa4-abe6-1d85a91feba2' , 'MONEY',	'{"customAmtCta": {"constraint": {"minAmount": {"units": "100", "currencyCode": "INR"}, "maxAmount": {"units": "10000", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}','CTA_STATE_ACTIVE'),
--- HEALTHY, WEALTHY, WISE
('a4528fbd-b2e6-4bf0-a324-7447a6c85d8c', '79bf2744-5781-4667-8b7f-7056a1e34a9c' , 'MONEY',	'{"customAmtCta": {"constraint": {"minAmount": {"units": "10", "currencyCode": "INR"}, "maxAmount": {"units": "10000", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}','CTA_STATE_ACTIVE'),
--- DAILY DEEDS
('05dca7f2-e49f-4366-a363-b6e057bd68e5', '43dd9b50-786b-47c0-bf0b-61401bc8da42' , 'MONEY',	'{"customAmtCta": {"constraint": {"minAmount": {"units": "10", "currencyCode": "INR"}, "maxAmount": {"units": "10000", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}','CTA_STATE_ACTIVE'),
--- KEEP THE CHANGE
('f241ca37-64eb-4477-a319-51b62beb1a6d', '78d18f6c-03bf-499a-aed7-f49eef104ba8' , 'MONEY',	'{"customAmtCta": {"constraint": {"minAmount": {"units": "10", "currencyCode": "INR"}, "maxAmount": {"units": "10000", "currencyCode": "INR"}, "stepAmount": {"units": "1", "currencyCode": "INR"}}, "textForRuleDesc": "Select Amount", "textForPossibleValues": "Custom"}}','CTA_STATE_ACTIVE');
