-- Keep the change --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state, weight
)
VALUES
  (
    '78d18f6c-03bf-499a-aed7-f49eef104ba8','KEEP THE CHANGE', '{ "display_str": "When I spend with Fi, round-up to the next {configuredRoundAmount} and save the difference in {depositAccountId}", "input_params": [ { "name": "configuredRoundAmount", "input_type": "MONEY" }, { "name": "depositAccountId", "input_type": "SMART_DEPOSIT" } ] }',
    'PAYMENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "paymentAmount.Units >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}',
    '{"actionArr": [{"type": "DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "calculateChangeAmountV2(paymentAmount, configuredRoundAmount)", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}]}}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE', 90
  );

INSERT INTO possible_param_values(rule_id, param_value_type, value, is_default_value, state) values
    ('78d18f6c-03bf-499a-aed7-f49eef104ba8', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"20"}}', false, 'PARAM_STATE_ACTIVE'),
    ('78d18f6c-03bf-499a-aed7-f49eef104ba8', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', true, 'PARAM_STATE_ACTIVE'),
    ('78d18f6c-03bf-499a-aed7-f49eef104ba8', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE'),
    ('78d18f6c-03bf-499a-aed7-f49eef104ba8', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', false, 'PARAM_STATE_ACTIVE');

INSERT INTO rule_display_infos (name, category, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color) values
    ('KEEP THE CHANGE', 'AUTO_SAVE', 'KEEP THE CHANGE', 'Keep the Change', 'When I spend with Fi, round-up and save','https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Home.png','When I spend with Fi, round-up to the next <u>defaultMoneyValue</u> and save the difference','https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Landing.png','#CDC6E8','#9183C7');

INSERT  into rule_tag_mappings (rule_id, tag_id) values ('78d18f6c-03bf-499a-aed7-f49eef104ba8', 'Shopping');

insert into home_cards (id, state, deeplink, card_data, weight) values
	('5ac98e16-559d-4560-aba7-c7a800af09d0', 'HOME_CARD_ACTIVE', '{"screen": "FIT_COLLECTION_PAGE", "fitCollectionPageScreenOptions": {"collectionId": "c61c89bc-1828-40e4-a734-e29e10a8ad36"}}',
	 '{"tags": [{"text": "NEW", "bgColor": "#9183C7"}], "description": {"descStr": "When I spend with Fi, \nround-up and save", "displayInfo": {"fontColor": "#282828"}, "replaceableParamMap": {}}, "displayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/RetailTherapy_Explore.jpg", "bgColor": "#CDC6E8", "homeCardOldVersionImgUrl": "https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Home.png"}}', 15);
