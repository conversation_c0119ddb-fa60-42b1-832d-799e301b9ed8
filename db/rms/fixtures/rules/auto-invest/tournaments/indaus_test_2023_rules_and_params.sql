-- one team, one dream --
INSERT INTO rules (
    id, name, description, event_type, client,
    category, condition, actions, max_subscriptions_per_actor,
    max_subscriptions, subscriptions_aggregation_type,
    state, weight, version_support_info
)
VALUES
    (
        '2204b1c9-8447-4444-a341-9f9793541648','INVEST IN THE TEAM', '{"displayStr": "Make an Investment in {mutualFundVal} for {purchaseAmount} whenever team {configuredCricketTeam} wins", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}, {"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"}]}',
        'CRICKET_MATCH_RESULT', 'FITTT',
        'AUTO_INVEST', '{"condition": "configuredCricketTeam == winningTeam ", "valuePath": {"winningTeam" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
        '{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}',
        '1', '2147483647', 'AGGREGATE_ON_TAG',
        'RULE_STATE_ACTIVE', 50, '{"min_supported_android_app_version":155, "min_supported_ios_app_version":1000}'
    );

-- creating rule tags --
insert into rule_tags (id, name, type, path, is_display_tag, display_info) values('ind_aus_feb_2023_test_invest','IND v AUS 2023 TEST', 'TERMINAL' , 'Sports.Cricket.Test.ind_aus_feb_2023_test_invest', true, '{"imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/INDvsRSA_OCT2022.png","bgColor": "#FAD0D0","iconUrls":["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"], "chipColor": "#CF8888"}');

-- make the cricket mania collection state to active --
update collections set child_ids=jsonb_set(child_ids::jsonb, '{tagIds, list}'::text[], ((child_ids->'tagIds'->'list')::jsonb || '"ind_aus_feb_2023_test_invest"')::jsonb, true) where id='f4c94fce-7498-4000-a1da-15a60eeff379' and not((child_ids->'tagIds'->'list')::jsonb ? 'ind_aus_feb_2023_test_invest');

INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
    ('0f678bda-7b47-42a9-b7c5-c4abe7514b45', '2204b1c9-8447-4444-a341-9f9793541648', 'ind_aus_feb_2023_test'),
    ('0f678bda-7b47-42a9-b7c5-c4abe7514b46', '2204b1c9-8447-4444-a341-9f9793541648', 'ind_aus_feb_2023_test_invest');

INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('afa8f1a2-daa0-4630-8513-69d0ac77a9e3', '2204b1c9-8447-4444-a341-9f9793541648', 'MUTUAL_FUND',
       ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
       'CTA_STATE_ACTIVE'),
       ('4b6e2c66-222a-49d5-96fb-565748114768', '2204b1c9-8447-4444-a341-9f9793541648', 'MONEY',
        '{"customAmtCta":{"textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('aef90737-2d40-40e8-9435-15a69836bc42', '2204b1c9-8447-4444-a341-9f9793541648', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"rsaw", "teamName":"South Africa Women", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSAW"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('090833a0-ce32-4cec-a9ab-f17e87263435', '2204b1c9-8447-4444-a341-9f9793541648', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"banw", "teamName":"Bangladesh Women", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Bangladesh.png", "abbreviatedName":"BANW"}}', false, 'PARAM_STATE_ACTIVE', 1);
