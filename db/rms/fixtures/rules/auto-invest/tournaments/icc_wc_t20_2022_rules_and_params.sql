-- one team, one dream --
INSERT INTO rules (
    id, name, description, event_type, client,
    category, condition, actions, max_subscriptions_per_actor,
    max_subscriptions, subscriptions_aggregation_type,
    state, weight, version_support_info
)
VALUES
    (
        'f3ccf7fe-785c-48d7-9020-428e20c5c287','INVEST IN THE TEAM', '{"displayStr": "Make an Investment in {mutualFundVal} for {purchaseAmount} whenever team {configuredCricketTeam} wins", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}, {"name": "configuredCricketTeam", "inputType": "CRICKET_TEAM"}]}',
        'CRICKET_MATCH_RESULT', 'FITTT',
        'AUTO_INVEST', '{"condition": "configuredCricketTeam == winningTeam ", "valuePath": {"winningTeam" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
        '{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}',
        '1', '2147483647', 'AGGREGATE_ON_TAG',
        'RULE_STATE_ACTIVE', 50, '{"min_supported_android_app_version":155, "min_supported_ios_app_version":1000}'
    );

-- creating rule tags --
insert into rule_tags (id, name, type, path, is_display_tag, display_info) values('icc_wc_t20_2022_invest','ICC T20 World Cup 2022', 'TERMINAL' , 'Sports.Cricket.T20.icc_wc_t20_2022', true, '{"imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/INDvsRSA_OCT2022.png","bgColor": "#FAD0D0","iconUrls":["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"], "chipColor": "#BC6B6B"}');

-- make the cricket mania collection state to active --
update collections set state='COLLECTION_ACTIVE' where id='f4c94fce-7498-4000-a1da-15a60eeff379';

-- append a new tag to child ids --
update collections set child_ids='{"tagIds": {"list": ["icc_wc_t20_2022_invest"]}}', version_support_info = '{"minSupportedIosAppVersion": 1000, "minSupportedAndroidAppVersion": 169}' where id='f4c94fce-7498-4000-a1da-15a60eeff379';

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color) values
    ('c6540f96-16e1-4d58-80a9-e6a41eb6933c','AUTO_INVEST', 'INVEST IN THE TEAM', 'INVEST IN THE TEAM', 'Invest in the team', 'When <u>subscribedUniqueValue</u> wins, invest <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner-Home.png','When <u>defaultUniqueValue</u> wins, invest <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner_Landing.png','#F4E7BF','#C8AB52', '#EAD8A3');

INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
    ('8a0c2528-b13c-4642-b5cc-871af034a352', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'icc_wc_t20_2022_invest');

INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('c71b2d34-3b81-48b6-9301-d3e9c420a992', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'MUTUAL_FUND',
        ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
        'CTA_STATE_ACTIVE');

-- one team, one dream values --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('e27a628c-8212-4442-8c7b-1723ddebd393', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 20),
     ('f49908c4-d8b9-4c4b-9ccd-561f12f09df9', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"150"}}', false, 'PARAM_STATE_ACTIVE', 15),
     ('716d6614-6603-4116-8de9-daf89fb8fd54', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
     ('3a17db93-91d8-4853-be96-90020dc130b9', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"250"}}', false, 'PARAM_STATE_ACTIVE', 5),
-- gold fund --
     ('d347554d-9993-44bf-a199-29fe05fee5f4', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220613uDAc4K+7QzaKEp8SnDIHeg=="}}', true, 'PARAM_STATE_ACTIVE', 3),
-- nifty fifty index
     ('dcbe21e1-6853-4cc8-8937-1c76ed81c340', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220131w8sIXk4+Qsa9IC1NWgitaQ=="}}', false, 'PARAM_STATE_ACTIVE', 2),
-- debt fund --
     ('bc6a61c5-3c4a-455c-982f-ca1672c7b63e', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220505AGpkU2bDRM6m6FWch1RzsA=="}}', false, 'PARAM_STATE_ACTIVE', 1);

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('ef57d08b-533b-4542-baff-89da502df854', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"sl", "teamName":"Sri Lanka", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SriLanka.png", "abbreviatedName":"SL"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('23b60d62-3008-4c69-bacf-1f40f177c169', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"pak", "teamName":"Pakistan", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Pakistan.png", "abbreviatedName":"PAK"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('318ded16-aaf0-4611-9380-8b94592a79b2', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ind", "teamName":"India", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png", "abbreviatedName":"IND"}}', false, 'PARAM_STATE_ACTIVE', 20),
     ('e6281fef-1367-4627-8061-5cf9cdd11a78', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"aus", "teamName":"Australia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png", "abbreviatedName":"AUS"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('8b8741c8-5e68-407b-beac-199acdaa78f0', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"wi", "teamName":"West Indies", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png", "abbreviatedName":"WI"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('03bef997-a2c9-4d36-aebf-9fdc92fc6513', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"uae", "teamName":"United Arab Emirates", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/UAE.png", "abbreviatedName":"UAE"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('32ad7d39-ae50-45b4-aa54-553cc1b928df', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"afg", "teamName":"Afghanistan", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Afghanistan.png", "abbreviatedName":"AFG"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('a921a0c8-3d34-46d2-8443-42ac874cd672', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"nz", "teamName":"New Zealand", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/NewZealand.png", "abbreviatedName":"NZ"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('a959877e-5bdd-4a8d-9a35-9c88f98c3d83', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"nl", "teamName":"Netherlands", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Netherlands.png", "abbreviatedName":"NL"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('61eebcfc-86ad-49dd-99fc-b17f78acf3c2', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"sct", "teamName":"Scotland", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Scotland.png", "abbreviatedName":"SCT"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('5458041a-4720-404d-bebc-c46ead737a59', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"rsa", "teamName":"South Africa", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png", "abbreviatedName":"RSA"}}', false, 'PARAM_STATE_ACTIVE', 10),
     ('837dab05-c810-4df1-8330-56aebc939d66', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"eng", "teamName":"England", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/England.png", "abbreviatedName":"ENG"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('9f5ab4d4-fa4c-4985-974a-4c6678179f60', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ban", "teamName":"Bangladesh", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Bangladesh.png", "abbreviatedName":"BAN"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('d85723cb-8fe7-4d79-9b63-49ab53dcb80c', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"zim", "teamName":"Zimbabwe", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Zimbabwe.png", "abbreviatedName":"ZIM"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('ef5f46ca-5bd9-43e1-800f-89acd466032a', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"nam", "teamName":"Namibia", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Namibia.png", "abbreviatedName":"NAM"}}', false, 'PARAM_STATE_ACTIVE', 1),
     ('f99b9ba2-07ea-47f6-88ba-9794bfd6a445', 'f3ccf7fe-785c-48d7-9020-428e20c5c287', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"ire", "teamName":"Ireland", "teamLogo":"https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Ireland.png", "abbreviatedName":"IRE"}}', false, 'PARAM_STATE_ACTIVE', 1);
