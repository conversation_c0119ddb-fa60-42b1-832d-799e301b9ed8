-- Don't Shop till you Drop --
INSERT INTO rules (id, name, description, event_type, client,
				   category, condition, actions, max_subscriptions_per_actor,
				   max_subscriptions,
				   state, weight, allowed_user_groups, version_support_info)
VALUES ('5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'DON''T SHOP TILL YOU DROP',
		'{"display_str": "When I order from {configuredMerchant} with Fi, \nin {mutualFundVal} \ninvest {purchaseAmount}", "input_params": [{"name": "configuredMerchant", "input_type": "MERCHANT"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}',
		'PAYMENT', 'FITTT',
		'AUTO_INVEST', '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}',
		'{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}',
		'5', '2147483647',
		'RULE_STATE_ACTIVE', 80, '{"FIT_INVESTMENT"}',
		'{"min_supported_android_app_version":129, "min_supported_ios_app_version":133}');

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name, home_page_text,
								home_page_img_url, landing_page_text, landing_page_img_url, background_color,
								tags_bg_color)
values ('cc0565e0-0cb2-4362-8da2-fcb95001e1bb', 'AUTO_INVEST', 'DON''T SHOP TILL YOU DROP',
		'DON''T SHOP TILL \nYOU DROP', 'Don''t Shop till you Drop',
		'When I order on <u>subscribedUniqueValue</u>, invest <u>subscribedMoneyValue</u>',
		'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Home.png',
		'When I order from <u>defaultUniqueValue</u> with Fi, invest <u>defaultMoneyValue</u>',
		'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Landing.png',
		'#D1DAF1', '#768CC3');

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight)
values
-- merchant values --
('e09873be-e9ae-4f9c-a822-9eed103c3b8c', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MERCHANT',
 '{"merchant_val":{"merchant_name":"Amazon", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/amazon.png"}}',
 true, 'PARAM_STATE_ACTIVE', 20),
('af09bc80-a638-4adf-bca5-3e467da7553b', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MERCHANT',
 '{"merchant_val":{"merchant_name":"Flipkart", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/flipkart.png"}}',
 false, 'PARAM_STATE_ACTIVE', 15),
('e5af5826-0795-4b2a-afee-47f2d7ebad49', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MERCHANT',
 '{"merchant_val":{"merchant_name":"Big Basket", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/big-basket.png"}}',
 false, 'PARAM_STATE_ACTIVE', 1),
('d931752a-256b-48e6-8bda-ef5969f7a138', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MERCHANT',
 '{"merchant_val":{"merchant_name":"Myntra", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/myntra.png"}}',
 false, 'PARAM_STATE_ACTIVE', 1),
('a38e7b98-31c4-48e9-9e84-282876bbab2a', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MERCHANT',
 '{"merchant_val":{"merchant_name":"Nykaa", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/nykaa.png"}}',
 false, 'PARAM_STATE_ACTIVE', 1),
('ee02c681-6c5a-4006-ab18-1bbc6ada607d', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MERCHANT',
 '{"merchant_val":{"merchant_name":"Dunzo", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/dunzo.png"}}',
 false, 'PARAM_STATE_ACTIVE', 1),
-- money values --
('d236c2f7-6e6d-48e4-bcf5-3b88686fe051', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MONEY',
 '{"money_val":{"currency_code":"INR", "units":"100"}}', false,
 'PARAM_STATE_ACTIVE', 1),
('4c385afe-af9f-4c4a-a60b-7fe9a5cff0db', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MONEY',
 '{"money_val":{"currency_code":"INR", "units":"150"}}', true,
 'PARAM_STATE_ACTIVE', 1),
('e0ec358b-ea38-4ba0-a2ec-0b14d1c46b45', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MONEY',
 '{"money_val":{"currency_code":"INR", "units":"200"}}', false,
 'PARAM_STATE_ACTIVE', 1),
('98e1db20-93cb-4a93-bd94-a15ba46b5032', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MONEY',
 '{"money_val":{"currency_code":"INR", "units":"250"}}', false,
 'PARAM_STATE_ACTIVE', 1);

INSERT into rule_tag_mappings (id, rule_id, tag_id)
values ('e69ac3e8-c3dd-45ec-8783-b28f9056eca4', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'Shopping');

INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('ec715c11-4a6e-4c3a-b399-951a0b395653', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MUTUAL_FUND',
		' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
		'CTA_STATE_ACTIVE'),
		('d827e3f6-f9b2-4c56-ab7a-39b2f6654a47', '5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'MONEY',
		'{"customAmtCta":{"textForPossibleValues":"Custom","textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');

-- updating the dynamic tag of the retail therapy collection to reflect a new rule addition
update collections
SET display_info=jsonb_set(display_info::jsonb, '{dynamicTag}'::text[],
						   ('{"tag":{"text":"New Rules Launched", "bg_color":"#9E5A5780"}, "expiry":"' ||
							to_char(((Now() + '7 days 00 hours 00 minutes'):: timestamp), 'YYYY-MM-DD"T"HH24:MI:SS"Z"') ||
							'"}')::jsonb,
						   true)
WHERE id = 'c8e66cbe-3c4a-4ea0-9686-5ccf9ba8cbe2';
