-- Monthly SIP --
update rules set description = '{"displayStr": "Make an investment in {mutualFundVal} for {purchaseAmount} on {configuredDateOfMonth}th of every month", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}, {"name": "configuredDateOfMonth", "inputType": "INT_INPUT"}]}' where id = '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0';
-- Weekly SIP --
update rules set description = '{"displayStr": "Make an investment in {mutualFundVal} for {purchaseAmount} on {configuredDayOfWeek} every week", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}, {"name": "configuredDayOfWeek", "inputType": "STRING_INPUT"}]}' where id = '42286516-f4d1-421f-9c1c-1a7f65612e66';

-- Daily SIP: execute only on week days --
update rules set description = '{"displayStr": "Make an investment in {mutualFundVal} for {purchaseAmount} every weekday", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}', condition = '{"condition": "currentWeekdayOneOf(day, \"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\")", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}' where id = 'd157d5c9-8b32-4f1b-9185-a60a8263ea25';

-- Deactivating Saturday and Sunday options for Weekly SIP rule --
update possible_param_values set state = 'PARAM_STATE_INACTIVE' where id in ('dddc83d5-9f5d-440d-abf6-b205bd3e7f19', 'ed98b262-7ee7-4897-8c0e-45d39a381aa3');
