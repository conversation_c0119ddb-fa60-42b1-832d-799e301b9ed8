update rules set description = '{"displayStr": "At the end of every day, \nin {mutualFundVal} \ninvest {purchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}', updated_at=now() where id='d157d5c9-8b32-4f1b-9185-a60a8263ea25';
update rules set description = '{"displayStr": "On {configuredDayOfWeek} every week, \nin {mutualFundVal} \ninvest {purchaseAmount}", "inputParams": [{"name": "configuredDayOfWeek", "inputType": "STRING_INPUT"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}', updated_at=now() where id='42286516-f4d1-421f-9c1c-1a7f65612e66';
update rules set description = '{"display_str": "When I order food from {configuredMerchant} with Fi, \nin {mutualFundVal} \ninvest {purchaseAmount}", "input_params": [{"name": "configuredMerchant", "input_type": "MERCHANT"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}', updated_at=now() where id='52a3048a-b3ff-473a-a4f0-8f2086564713';
update rules set description = '{"display_str": "When I order from {configuredMerchant} with Fi, \nin {mutualFundVal} \ninvest {purchaseAmount}", "input_params": [{"name": "configuredMerchant", "input_type": "MERCHANT"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}', updated_at=now() where id='5cdbbe08-257d-44e2-853b-9b0a3c3e4dda';
update rules set description = '{"displayStr": "On the {configuredDateOfMonth}th every month, \nin {mutualFundVal} \ninvest {purchaseAmount}", "inputParams": [{"name": "configuredDateOfMonth", "inputType": "INT_INPUT"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}', updated_at=now() where id='0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0';
