update rules set condition='{"condition": "configuredDateOfMonth == dateOfMonth"}' where id = '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0';
update rules set condition='{"condition": "configuredDayOfWeek == dayOfWeek"}' where id = '42286516-f4d1-421f-9c1c-1a7f65612e66';
update rules set condition='{"condition": "true"}' where id = 'd157d5c9-8b32-4f1b-9185-a60a8263ea25';
update rules set condition='{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}' where id in ('5cdbbe08-257d-44e2-853b-9b0a3c3e4dda', 'cc0565e0-0cb2-4362-8da2-fcb95001e1bb');
