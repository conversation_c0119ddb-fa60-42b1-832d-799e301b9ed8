-- HEALTHY WEALTHY WISE --
-- note: no need to add this rule to collection as it has already been added in previous PR
INSERT INTO rules (id, name, description, event_type, client,
				   category, condition, actions, max_subscriptions_per_actor,
				   max_subscriptions,
				   state, weight, allowed_user_groups, version_support_info)
VALUES ('52a3048a-b3ff-473a-a4f0-8f2086564713', 'HEALTHY, WEALTHY, WISE',
		'{"display_str": "When I order food from {configuredMerchant} with Fi, \nin {mutualFundVal} \ninvest {purchaseAmount}", "input_params": [{"name": "configuredMerchant", "input_type": "MERCHANT"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}',
		'PAYMENT', 'FITTT',
		'AUTO_INVEST', '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configured<PERSON>erchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}',
		'{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}',
		'2', '**********',
		'RULE_STATE_ACTIVE', 100, '{"FIT_INVESTMENT"}', '{"min_supported_android_app_version":129, "min_supported_ios_app_version":133}');

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name, home_page_text,
								home_page_img_url, landing_page_text, landing_page_img_url, background_color,
								tags_bg_color)
values ('8dc5e1f0-4566-44e1-b1bb-8e5ba66810a6', 'AUTO_INVEST', 'HEALTHY, WEALTHY, WISE', 'HEALTHY, WEALTHY, \nWISE',
		'Healthy, Wealthy, Wise',
		'When I order takeout, invest <u>subscribedMoneyValue</u> in mutual funds',
		'https://epifi-icons.pointz.in/fittt-images/png-illustrations/HWW-Home.png',
		'When I order food from <u>defaultUniqueValue</u> with Fi, invest <u>defaultMoneyValue</u>',
		'https://epifi-icons.pointz.in/fittt-images/png-illustrations/HWW-Landing.png', '#F4E7BF',
		'#C8AB52');

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state)
values
-- merchant values --
('3e929379-8c81-4661-a13e-5b788bc92da3', '52a3048a-b3ff-473a-a4f0-8f2086564713', 'MERCHANT',
 '{"merchant_val":{"merchant_name":"Swiggy", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/swiggy.png"}}',
 true, 'PARAM_STATE_ACTIVE'),
('e120ff9a-90af-46c0-8bcb-c6476b511456', '52a3048a-b3ff-473a-a4f0-8f2086564713', 'MERCHANT',
 '{"merchant_val":{"merchant_name":"Zomato", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/zomato.png"}}',
 false, 'PARAM_STATE_ACTIVE'),
-- money values --
('5f095696-b1a8-4c50-89d0-d6a0f5d7b2da', '52a3048a-b3ff-473a-a4f0-8f2086564713', 'MONEY',
 '{"money_val":{"currency_code":"INR", "units":"100"}}', false,
 'PARAM_STATE_ACTIVE'),
('3e0cbbd9-a620-47f1-a57c-0debe554ea39', '52a3048a-b3ff-473a-a4f0-8f2086564713', 'MONEY',
 '{"money_val":{"currency_code":"INR", "units":"150"}}', true,
 'PARAM_STATE_ACTIVE'),
('b5e15b0e-6c19-4f56-b01b-8881bc53c9c1', '52a3048a-b3ff-473a-a4f0-8f2086564713', 'MONEY',
 '{"money_val":{"currency_code":"INR", "units":"200"}}', false,
 'PARAM_STATE_ACTIVE'),
('9ce2c672-1509-4966-a5e0-61c32af0e327', '52a3048a-b3ff-473a-a4f0-8f2086564713', 'MONEY',
 '{"money_val":{"currency_code":"INR", "units":"250"}}', false,
 'PARAM_STATE_ACTIVE');

INSERT into rule_tag_mappings (id, rule_id, tag_id)
values ('fcb67696-8ced-46e1-9f66-1d45b54cc385', '52a3048a-b3ff-473a-a4f0-8f2086564713', 'Food');

INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('53e646f3-a463-4612-a11e-cbc8884c4a44', '52a3048a-b3ff-473a-a4f0-8f2086564713', 'MUTUAL_FUND',
		' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
		'CTA_STATE_ACTIVE');

-- updating the dynamic tag of the retail therapy collection to reflect a new rule addition
update collections
SET display_info=jsonb_set(display_info::jsonb, '{dynamicTag}'::text[],
						   ('{"tag":{"text":"New Rules Launched", "bg_color":"#9E5A5780"}, "expiry":"' ||
							to_char(((Now() + '7 days 00 hours 00 minutes'):: timestamp), 'YYYY-MM-DD"T"HH24:MI:SS"Z"') ||
							'"}')::jsonb,
						   true)
WHERE id = 'eaf09b9b-df61-4cb6-ad87-b5344c2d2add';
