INSERT INTO rules (
	id, name, description,
    event_type, client, category,
    condition,
    actions,
    max_subscriptions_per_actor, max_subscriptions,
	state, weight, allowed_user_groups, version_support_info, aggregation_info, rule_type_for_special_handling
)
-- INVEST THE CHANGE --
VALUES
	(
		'f49d1c45-708f-49c7-988e-131050dbfe88', 'ROUND-UP & INVEST', '{"displayStr": "When I spend with Fi, round-up to the next {configuredRoundAmount} and invest the change in {mutualFundVal}", "inputParams": [{"name": "configuredRoundAmount", "inputType": "MONEY"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}]}',
		'PAYMENT', 'FITTT', 'AUTO_INVEST',
		'{"condition": "paymentAmount > 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}',
		'{"actionArr": [{"type": "AGGREGATE_PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "purchaseAmount", "expression": "calculateChangeAmountV2(paymentAmount, configuredRoundAmount)", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}]}}]}',
		'2147483647', '2147483647',
		'RULE_STATE_ACTIVE', 100, '{}', '{"minSupportedAndroidAppVersion":153, "minSupportedIosAppVersion":209}',
	 	'{"condition": "aggregatedAmount >= minMFAmount"}', 'RULE_TYPE_INVEST_THE_CHANGE'
	);

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color, display_info) values
	('978c1714-4d6d-4b29-983a-af6fb638eed2', 'AUTO_INVEST', 'ROUND-UP & INVEST', 'ROUND-UP & \nINVEST', 'Round-up & Invest',
	 'When I spend with Fi, round-up and invest',
	 'https://epifi-icons.pointz.in/fittt-images/rules/invest-the-change-home.png',
	 'When I spend with Fi, round-up to the next <u>defaultMoneyValue</u> and invest the change',
	 'https://epifi-icons.pointz.in/fittt-images/rules/invest-the-change-landing.png', '#CDC6E8', '#9287BD', '#C0B7E1',
	 '{"howRuleWorks":{"title": "How this rule works", "info":["After every payment, we round-up your change to the chosen value",
		"We’ll collect your change till it crosses ₹100; the minimum investment amount", "And then, we’ll invest the amount in the fund that you’ve chosen"]}}');

-- param values for the rule --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
			('a74978d8-e251-4550-afad-36cf4269dd59' ,'f49d1c45-708f-49c7-988e-131050dbfe88', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"10"}}', false, 'PARAM_STATE_ACTIVE', 3),
			('7bac6c9f-2dcc-47d5-82b9-b5c666677f29' ,'f49d1c45-708f-49c7-988e-131050dbfe88', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', false, 'PARAM_STATE_ACTIVE', 2),
			('7f3bce18-cb7a-40c1-98d2-7632c9b84c98' ,'f49d1c45-708f-49c7-988e-131050dbfe88', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', true, 'PARAM_STATE_ACTIVE', 1),
            -- gold fund --
            ('ab784dd6-a36b-4ead-9145-87a8f3c972dd', 'f49d1c45-708f-49c7-988e-131050dbfe88', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220613uDAc4K+7QzaKEp8SnDIHeg=="}}', true, 'PARAM_STATE_ACTIVE', 3),
            -- nifty fifty index
            ('2b109837-047a-4795-a60a-870ec7342080', 'f49d1c45-708f-49c7-988e-131050dbfe88', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220131w8sIXk4+Qsa9IC1NWgitaQ=="}}', false, 'PARAM_STATE_ACTIVE', 2),
            -- debt fund --
            ('b0fb0be6-9928-4e34-bd2f-bc89eaeef6ed', 'f49d1c45-708f-49c7-988e-131050dbfe88', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220505AGpkU2bDRM6m6FWch1RzsA=="}}', false, 'PARAM_STATE_ACTIVE', 1);

INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('9f70f881-af33-4694-bfce-3eb9ceec8dd5', 'f49d1c45-708f-49c7-988e-131050dbfe88', 'MUTUAL_FUND',
		' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
		'CTA_STATE_ACTIVE'),
	   ('ef7fb8b3-e4d3-47a1-b2d6-d6d3a77afb11', 'f49d1c45-708f-49c7-988e-131050dbfe88', 'MONEY',
		'{"customAmtCta":{"textForPossibleValues":"Custom","textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');

-- homecard deeplink to the rule --
insert into home_cards (id, state, deeplink, card_data, weight, allowed_user_groups, version_support_info) values
	('ab389f8e-4308-47f2-8435-ea5d41089826', 'HOME_CARD_ACTIVE',
	 '{"screen": "FIT_CUSTOMISE_RULE_SCREEN", "fitCustomiseRuleScreenOptions": {"ruleId": "f49d1c45-708f-49c7-988e-131050dbfe88", "pageType":"SUBSCRIPTION_PAGE_NEW"}}',
	 '{"idForEventLogging":"roundUpAndInvestHomeCard", "tags": [{"text": "NEW RULE", "bgColor": "#80478295"}], "title": {"text": "Round-up & Invest", "fontColor": "#333333"}, "description": {"descStr": "Round-up and invest \nyour spare change", "displayInfo": {"fontColor": "#333333"}, "replaceableParamMap": {}}, "displayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/home-card/invest-the-change-home-card.png", "bgColor": "#DEEEF2", "homeCardOldVersionImgUrl": "https://epifi-icons.pointz.in/fittt-images/home-card/invest-the-change-home-card-old.png"}}',
	 50, '{}', '{"minSupportedAndroidAppVersion":153, "minSupportedIosAppVersion":209}');

INSERT into rule_tag_mappings (id, rule_id, tag_id) values
            ('82ac8090-2184-4b7e-bddc-cc46dd770941', 'f49d1c45-708f-49c7-988e-131050dbfe88', 'Shopping');

-- updating the child ids of the good buys collection to reflect a new rule addition
UPDATE collections
set child_ids=jsonb_set(child_ids::jsonb, '{ruleIds, list}'::text[],
						((child_ids -> 'ruleIds' -> 'list')::jsonb || '"f49d1c45-708f-49c7-988e-131050dbfe88"')::jsonb, true)
where id='eaf09b9b-df61-4cb6-ad87-b5344c2d2add';

-- updating the dynamic tag of the good buys collection to reflect a new rule addition
update collections
SET display_info=jsonb_set(display_info::jsonb, '{dynamicTag}'::text[],
						   ('{"tag":{"text":"NEW RULE", "bg_color":"#80478295"}, "expiry":"' ||
							to_char(((Now() + '14 days 00 hours 00 minutes'):: timestamp), 'YYYY-MM-DD"T"HH24:MI:SS"Z"') ||
							'"}')::jsonb,
						   true)
where id='eaf09b9b-df61-4cb6-ad87-b5344c2d2add';
