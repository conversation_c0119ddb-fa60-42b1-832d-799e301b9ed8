INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state, allowed_user_groups
)
  -- THINKING AHEAD --
VALUES
  (
   -- bb093acb-fb94-4aa4-abe6-1d85a91feba2
    '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0','THINKING AHEAD', '{"displayStr": "On the {configuredDateOfMonth}th every month, \nin {mutualFundVal} \ninvest {purchaseAmount}", "inputParams": [{"name": "configuredDateOfMonth", "inputType": "INT_INPUT"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}',
    'DAILY_EVENT', 'FITTT',
    'AUTO_INVEST', '{"condition": "configuredDateOfMonth == getDateFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}',
    '{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}',
    '1', '2147483647',
    'RULE_STATE_ACTIVE', '{"FIT_INVESTMENT"}'
  ),
-- CONSISTENCY IS KEY --
  (
	-- 7b251ad6-a653-4c8a-88fa-a3714b45b480
    '42286516-f4d1-421f-9c1c-1a7f65612e66','CONSISTENCY IS KEY', '{"displayStr": "On {configuredDayOfWeek} every week, \nin {mutualFundVal} \ninvest {purchaseAmount}", "inputParams": [{"name": "configuredDayOfWeek", "inputType": "STRING_INPUT"}, {"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}',
    'DAILY_EVENT', 'FITTT',
    'AUTO_INVEST', '{"condition": "configuredDayOfWeek == getWeekdayFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}',
    '{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}',
    '1', '2147483647',
    'RULE_STATE_ACTIVE', '{"FIT_INVESTMENT"}'
  ),
-- DAILY DEEDS --
	(
	-- 43dd9b50-786b-47c0-bf0b-61401bc8da42
	'd157d5c9-8b32-4f1b-9185-a60a8263ea25','DAILY DEEDS', '{"displayStr": "Every day, \nin {mutualFundVal} \ninvest {purchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "purchaseAmount", "inputType": "MONEY"}]}',
		'DAILY_EVENT', 'FITTT',
		'AUTO_INVEST', '{"condition": "true"}',
		'{"actionArr": [{"data": {}, "type": "PURCHASE_MUTUAL_FUND"}]}',
		'1', '2147483647',
		'RULE_STATE_ACTIVE', '{"FIT_INVESTMENT"}'
	);

--- Rule Display Entries --
INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color) values
    ('9108c557-07f8-4791-bad9-c9d758ab4df6', 'AUTO_INVEST', 'THINKING AHEAD', 'THINKING AHEAD', 'Thinking Ahead', 'On the <u>subscribedIntVal</u>th every month, invest <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead_Home.png','On the <u>defaultIntVal</u>th every month, invest <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/ThinkingAhead.png','#F9D0D0','#BF7D7D'),
    ('e7e27cb6-3ee3-4f59-baa6-c40854be830b', 'AUTO_INVEST', 'CONSISTENCY IS KEY', 'CONSISTENCY IS KEY', 'Consistency is Key', 'On <u>subscribedStrVal</u> every week, invest <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ConsistencyIsKey_Home.png', 'On <u>defaultStrVal</u> every week, invest <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/ConsistencyIsKey.png', '#D9F2CB', '#87BA6B'),
	('dd42c626-13e5-4184-b1ad-3d7c668850ab', 'AUTO_INVEST', 'DAILY DEEDS', 'DAILY DEEDS', 'Daily Deeds', 'At the end of every day, invest <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/DailyDeeds_Home2.png','At the end of every day, invest <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/DailyDeedsV2.png','#DEEEF2','#7FBECE');

insert into collections (id, state, display_info, child_ids, is_featured, type, allowed_user_groups) values
('bf595b5c-54c8-42b3-930e-ac2d71e6b35b', 'COLLECTION_ACTIVE', '{"name":{"text":"Make your\nmoney grow"},"cardDisplayInfo":{"imgUrl":"https://epifi-icons.pointz.in/fittt-images/collections/MakeYourMoneyGrow_Explore.jpg", "homePageImgUrl":"https://epifi-icons.pointz.in/fittt-images/collections/MakeYourMoneyGrow_Home.jpg","bgColor":""},"description":{"text":"Get higher returns by putting aside money in a Mutual Fund and diversify your portfolio today","fontColor":""}}', '{"ruleIds":{"list":["0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0","42286516-f4d1-421f-9c1c-1a7f65612e66", "d157d5c9-8b32-4f1b-9185-a60a8263ea25"]}}', true, 'COLLECTION_TYPE_AUTO_INVEST', '{"FIT_INVESTMENT"}');


-- THINKING AHEAD --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
-- Date of month --
('443c26fe-bc29-4db4-9c65-bf1044c062bc', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'INT_INPUT', '{"int_val": 5, "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/05.png"}', true, 'PARAM_STATE_ACTIVE', 5),
('4e619a2f-80e9-4f41-9b5d-106fac30fcdc', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'INT_INPUT', '{"int_val": 10, "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/10.png"}', false, 'PARAM_STATE_ACTIVE', 10),
('c9332f58-caf5-4e90-b86d-c2611a84c6b9', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'INT_INPUT', '{"int_val": 15, "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/15.png"}', false, 'PARAM_STATE_ACTIVE', 15),
('a5a10ad1-6790-40ef-b705-c3c50f0f7daf', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'INT_INPUT', '{"int_val": 20, "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/20.png"}', false, 'PARAM_STATE_ACTIVE', 20),
-- money values --
('5b836e7a-8c15-41e9-8848-292f7ed4e8f8', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"2500"}}', false, 'PARAM_STATE_ACTIVE', 5),
('e705fcbd-7eb4-4e97-b52d-4fb9a27a83e4', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"5000"}}', true, 'PARAM_STATE_ACTIVE', 10),
('ea0c89ca-f167-43bf-95e6-5b441796d34c', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"7500"}}', false, 'PARAM_STATE_ACTIVE', 15),
('e0006f9a-90b8-45bf-a655-cf87b603e410', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"10000"}}', false, 'PARAM_STATE_ACTIVE', 20),

-- CONSISTENCY IS KEY --
-- day values --
('529c4fb3-abdf-4833-80f3-bb2419f2a495', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'STRING_INPUT', '{"str_val": "Monday", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/Monday.png"}', false, 'PARAM_STATE_ACTIVE', 5),
('77cc573c-b4e8-442d-99f2-1e10ede3177a', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'STRING_INPUT', '{"str_val": "Tuesday", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/Tuesday.png"}', false, 'PARAM_STATE_ACTIVE', 10),
('71746c64-e816-4a2e-87a6-e9ce253f1565', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'STRING_INPUT', '{"str_val": "Wednesday", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/Wednesday.png"}', false, 'PARAM_STATE_ACTIVE', 15),
('4499f41d-5f7e-4f51-af4b-b021ad8981b2', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'STRING_INPUT', '{"str_val": "Thursday", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/Thursday.png"}', false, 'PARAM_STATE_ACTIVE', 20),
('194fcd74-13bd-41b9-9665-b06c7219ce11', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'STRING_INPUT', '{"str_val": "Friday", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/Friday.png"}', true, 'PARAM_STATE_ACTIVE', 25),
('dddc83d5-9f5d-440d-abf6-b205bd3e7f19', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'STRING_INPUT', '{"str_val": "Saturday", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/Saturday.png"}', false, 'PARAM_STATE_ACTIVE', 30),
('ed98b262-7ee7-4897-8c0e-45d39a381aa3', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'STRING_INPUT', '{"str_val": "Sunday", "icon_url":"https://epifi-icons.pointz.in/fittt-images/icons/Sunday.png"}', false, 'PARAM_STATE_ACTIVE', 35),
-- money values --
('5dd1ba73-3379-4767-99cb-a81a5f5f781b', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"500"}}', false, 'PARAM_STATE_ACTIVE', 5),
('4e46419b-67e8-44eb-a882-1a3c5965c028', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"1000"}}', true, 'PARAM_STATE_ACTIVE', 10),
('fecae468-e087-4ac4-bcf5-fcf87c13cac8', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"2000"}}', false, 'PARAM_STATE_ACTIVE', 15),
('fc7cae1d-890b-4d3f-9ffa-a2788aca1313', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"2500"}}', false, 'PARAM_STATE_ACTIVE', 20),
-- DAILY DEEDS --
-- money values --
('bfffc9a4-099b-48b6-baf3-3996a5223c53', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE', 5),
('82186042-a0b2-4708-ae45-02765b04a341', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', true, 'PARAM_STATE_ACTIVE', 10),
('6495d20e-0d2c-4274-8f9f-8d142d3961ed', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"400"}}', false, 'PARAM_STATE_ACTIVE', 15),
('d9cfccb2-5452-4cd3-b4fb-c721b950917d', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"500"}}', false, 'PARAM_STATE_ACTIVE', 20);

INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state) values
('3b90ef1c-96dc-4ca9-8bf1-820e8ee49cce','0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'MUTUAL_FUND',' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}' , 'CTA_STATE_ACTIVE'),
('b36d66c9-2900-4165-a5da-0e44c3515c07','42286516-f4d1-421f-9c1c-1a7f65612e66', 'MUTUAL_FUND',' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}' , 'CTA_STATE_ACTIVE'),
('9fd4a047-2b12-489a-8dcf-154eca04c301','d157d5c9-8b32-4f1b-9185-a60a8263ea25', 'MUTUAL_FUND',' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}' , 'CTA_STATE_ACTIVE'),
('b07b54e1-eba4-4601-833b-14aebf06e504', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0', 'MONEY', '{"customAmtCta":{"textForPossibleValues":"Custom","textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE'),
('6d4a10c9-2a9e-474b-bc1c-32e57d423af2', '42286516-f4d1-421f-9c1c-1a7f65612e66', 'MONEY', '{"customAmtCta":{"textForPossibleValues":"Custom","textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE'),
('94cc0bdf-f7c4-4aa4-bf8e-c451e8be28e3', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25', 'MONEY', '{"customAmtCta":{"textForPossibleValues":"Custom","textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');


insert into rule_tags (id, name, type, path, is_display_tag) values
('INVESTMENTS', 'INVESTMENTS', 'TERMINAL' , 'INVESTMENTS', true);

insert into rule_tag_mappings (tag_id, rule_id) values
	('INVESTMENTS', '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0'),
	('INVESTMENTS', '42286516-f4d1-421f-9c1c-1a7f65612e66'),
	('INVESTMENTS', 'd157d5c9-8b32-4f1b-9185-a60a8263ea25');
