-- EYES ON THE TROPHY --
INSERT INTO rules (
  name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    'EYES ON THE TROPHY', '{"displayStr": "When {configuredFootballTeam} wins, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredFootballTeam", "inputType": "FOOTBALL_TEAM"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'FOOTBALL_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition":"winnerFootballTeam == configuredFootballTeam", "valuePath": {"winnerFootballTeam": {"path": ["Data", "FootballMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE'
  );


-- Goooooal --
INSERT INTO rules (
  name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    'GOOOOOOAL', '{"displayStr": "When {configuredFootballPlayer} scores a goal, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredFootballPlayer", "inputType": "FOOTBALL_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'FOOTBALL_GOAL_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition":"footballPlayer == configuredFootballPlayer && noOfGoalsByPlayer > 0 ", "valuePath": {"noOfGoalsByPlayer": {"path": ["Data", "FootballPlayerEvent", "PlayerStat", "GoalsScored"]}, "footballPlayer": {"path": ["Data", "FootballPlayerEvent", "PlayerStat", "Player", "PlayerId"]}}}',
	'{"actionArr": [{"type":"DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "noOfGoalsByPlayer*configuredDepositAmount", "valuePath": {"noOfGoalsByPlayer": {"path": ["Data", "FootballPlayerEvent", "PlayerStat", "GoalsScored"]}}}]}}]}',
    '5', '**********',
    'RULE_STATE_ACTIVE'
  );

-- OWNING THE GAME --
INSERT INTO rules (
  name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    'OWNING THE GAME', '{"displayStr": "When {configuredFootballTeam} keeps a clean sheet, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredFootballTeam", "inputType": "FOOTBALL_TEAM"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'FOOTBALL_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition": "teamHavingCleanSheet1 == configuredFootballTeam || teamHavingCleanSheet2 == configuredFootballTeam"}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE'
  );
