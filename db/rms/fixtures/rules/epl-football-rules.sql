-- EYES ON THE TROPHY --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    '0b8db9b8-c5a8-4671-9395-7f819dc88673','EYES ON THE TROPHY', '{"displayStr": "When {configuredFootballTeam} wins, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredFootballTeam", "inputType": "FOOTBALL_TEAM"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'FOOTBALL_MATCH_RESULT', 'FITTT',
    'AUTO_SAVE', '{"condition":"winnerFootballTeam == configuredFootballTeam", "valuePath": {"winnerFootballTeam": {"path": ["Data", "FootballMatchResultEvent", "MatchResult", "WinningTeamId"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE'
  );


-- Goooooal --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    '1780f970-783b-458f-b558-26eed1236c7e','GOOOOOOAL', '{"displayStr": "When {configuredFootballPlayer} scores a goal, put aside {configuredDepositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredFootballPlayer", "inputType": "FOOTBALL_PLAYER"}, {"name": "configuredDepositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'FOOTBALL_GOAL_STATS', 'FITTT',
    'AUTO_SAVE', '{"condition":"footballPlayer == configuredFootballPlayer && noOfGoalsByPlayer > 0 ", "valuePath": {"noOfGoalsByPlayer": {"path": ["Data", "FootballPlayerEvent", "PlayerStat", "GoalsScored"]}, "footballPlayer": {"path": ["Data", "FootballPlayerEvent", "PlayerStat", "Player", "PlayerId"]}}}',
    '{"actionArr": [{"type":"DEPOSIT", "data": {"expressions": [{"varName": "depositAmount", "expression": "noOfGoalsByPlayer*configuredDepositAmount", "valuePath": {"noOfGoalsByPlayer": {"path": ["Data", "FootballPlayerEvent", "PlayerStat", "GoalsScored"]}}}]}}]}',
    '5', '**********',
    'RULE_STATE_ACTIVE'
  );

insert into rule_tags (id, name, type, path, is_display_tag) values('EPL2021','EPL 2021-22', 'TERMINAL' , 'Sports.Football.EPL2021', true);

insert into rule_tag_mappings (tag_id, rule_id) values ('EPL2021', '0b8db9b8-c5a8-4671-9395-7f819dc88673');
insert into rule_tag_mappings (tag_id, rule_id) values ('EPL2021', '1780f970-783b-458f-b558-26eed1236c7e');
