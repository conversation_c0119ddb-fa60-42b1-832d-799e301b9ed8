-- above and beyond --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && ( noOfSixes+noOfFours) > 0 ", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "noOfFours": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Fours"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}'
where name in ('ABOVE AND BEYOND');

-- super sixer --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && noOfSixes > 0 ", "valuePath": {"noOfSixes": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Sixes"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "<PERSON>sman", "PlayerId"]}}}'
where name in ('SUPER SIXER');

-- howzaaattt --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && noOfWickets>0 ", "valuePath": {"noOfWickets" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Wickets"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}'
where name in ('HOWZAAATTT');

-- one team one dream --
UPDATE rules set condition = '{"condition": "configuredCricketTeam == winningTeam ", "valuePath": {"winningTeam" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "WinningTeamId"]}}}'
where name in ('ONE TEAM, ONE DREAM');

-- maiden maiden! --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && noOfMaidenByPlayer > 0 ", "valuePath": {"noOfMaidenByPlayer" : {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "MaidenOvers"]}, "cricketPlayer": {"path": ["Data", "CricketBowlerEvent", "BowlerStats", "Bowler", "PlayerId"]}}}'
where name in ('MAIDEN MAIDEN!');

-- GOOOOOOAL --
UPDATE rules set condition = '{"condition":"footballPlayer == configuredFootballPlayer && noOfGoalsByPlayer > 0 ", "valuePath": {"noOfGoalsByPlayer": {"path": ["Data", "FootballPlayerEvent", "PlayerStat", "GoalsScored"]}, "footballPlayer": {"path": ["Data", "FootballPlayerEvent", "PlayerStat", "Player", "PlayerId"]}}}'
where name in ('GOOOOOOAL');

-- EYES ON THE TROPHY --
UPDATE rules set condition = '{"condition":"winnerFootballTeam == configuredFootballTeam", "valuePath": {"winnerFootballTeam": {"path": ["Data", "FootballMatchResultEvent", "MatchResult", "WinningTeamId"]}}}'
where name in ('EYES ON THE TROPHY');

-- keep the change --
UPDATE rules set condition = '{"condition": "paymentAmount.Units >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}'
where name in ('KEEP THE CHANGE');

-- consistency is key --
UPDATE rules set condition = '{"condition": "configuredDayOfWeek == getWeekdayFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}'
where name in ('CONSISTENCY IS KEY', 'WEEKLY SIP');

-- thinking ahead --
UPDATE rules set condition = '{"condition": "configuredDateOfMonth == getDateFromDay(day)", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}'
where name in ('THINKING AHEAD', 'MONTHLY SIP');

-- Healthy, wealthy and wise --
UPDATE rules set condition = '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}'
where name in ('HEALTHY, WEALTHY, WISE');

-- don't shop till you drop --
UPDATE rules set condition = '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}'
where name in ('DON''T SHOP TILL YOU DROP');

-- CAN'T STOP WON'T STOP --
UPDATE rules set condition = '{"condition": "cricketPlayer == configuredCricketPlayer && runsScored >= configuredBatchRuns ", "valuePath": {"runsScored": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "RunsScored"]}, "cricketPlayer": {"path": ["Data", "CricketBatsmanEvent", "BattingStats", "Batsman", "PlayerId"]}}}'
where name in ('CAN’T STOP, WON’T STOP');

-- DYNAMIC DUO --
UPDATE rules set condition = '{"condition": "cricketTeam == configuredCricketTeam", "valuePath": {"cricketTeam" : {"path": ["Data", "TeamPartnershipEvent", "TeamId"]}}}',
				 actions = '{"actionArr": [{"data": {"expressions": [{"var_name": "depositAmount", "expression": "getNumOfPartnershipGreaterThanV2(partnershipStats, configuredBatchRuns)*configuredDepositAmount"}]}, "valuePath": {"partnershipStats" : {"path": ["Data", "TeamPartnershipEvent", "Stats"]}}, "type": "DEPOSIT"}]}'
where name in ('DYNAMIC DUO');

-- DRAWN OUT --
UPDATE rules set condition = '{"condition": "matchDrawn(matchResult)", "valuePath": {"matchResult" : {"path": ["Data", "CricketMatchResultEvent", "MatchResult", "ResultType"]}}}'
where name in ('DRAWN OUT');

-- TIP YOURSELF --
UPDATE rules set condition = '{"condition": "paymentAmount.Units >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}'
where name in ('TIP YOURSELF');

-- PEDAL TO THE MEDAL --
UPDATE rules set actions = '{"actionArr": [{"data": {"expressions": [{"varName": "depositAmount", "expression": "noOfMedals*configuredDepositAmount"}]}, "valuePath": {"noOfMedals": {"path": ["Data", "OlympicsMedalsEvent", "Stats", "MedalsCount"]}}, "type": "DEPOSIT"}]}'
where name in ('PEDAL TO THE MEDAL');

-- MAIDEN MAIDEN --
UPDATE rules set condition = '{"condition": "cricketTeam == configuredCricketTeam && noOfMaidenOvers > 0", "valuePath": {"cricketTeam" : {"path": ["Data", "CricketTeamMaidenOverEvent", "TeamMaiden", "Team", "TeamId"]}, "noOfMaidenOvers": {"path": ["Data", "CricketTeamMaidenOverEvent", "TeamMaiden", "TotalMaidenOvers"]}}}'
where name in ('MAIDEN MAIDEN');
