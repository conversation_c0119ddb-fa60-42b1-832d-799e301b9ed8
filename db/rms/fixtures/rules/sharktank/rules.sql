insert into collections (id, state, weight, display_info, child_ids, is_featured, type, allowed_user_groups, version_support_info)
values
-- Invest along with Shark Tank India --
('54a44408-62cb-4914-aae8-c3026bb4a3a4', 'COLLECTION_ACTIVE', 100,
 '{ "name":{ "text":"Shark Tank" }, "cardDisplayInfo":{ "imgUrl":"https://epifi-icons.pointz.in/fittt-images/collections/shark-tank-explore.png", "homePageImgUrl":"https://epifi-icons.pointz.in/fittt-images/collections/shark-tank-home.png", "bgColor":"#DEEEF2" }, "description":{ "text":"Build wealth every time a Shark offers a deal", "fontColor":"" }, "disclaimer":{ "text":"Fi: Official Money Partner on Shark Tank India S2", "fontColor":"#606265" } }',
 '{"ruleIds":{"list":["e2535f52-1ac1-41cb-ab94-e3bada5746ed","b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c", "de166023-fc3f-4d99-b7e2-e3ec1f1daa42", "6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7", "9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea", "dbc2347b-3068-412e-b38f-de42f2817a5e", "a8c118a5-5aa3-4c00-8e79-90c45d8fed55"]}}', true, 'COLLECTION_TYPE_AUTO_INVEST',
 '{"INTERNAL"}', '{"minSupportedAndroidAppVersion":210, "minSupportedIosAppVersion":295}');


-- creating rule tags --
insert into rule_tags (id, name, type, path, is_display_tag, display_info)
values ('SHARK_TANK_2023', 'Shark Tank', 'TERMINAL', 'SharkTank2023', true,
        '{"imgUrl": "https://epifi-icons.pointz.in/fittt-images/icons/IndVsSLFeb2022.png","bgColor": "#D1DAF1","iconUrls":["https://epifi-icons.pointz.in/fittt-images/Cricket.svg"], "chipColor": "#BBC8E9"}'),
       ('EQUITY', 'Equity', 'TERMINAL', 'Equity', false , null),
       ('VERY_HIGH_RISK', 'Very High Risk', 'TERMINAL', 'VeryHighRisk', false , null),
       ('ESG', 'ESG', 'TERMINAL', 'ESG', false , null),
       ('SECTORAL', 'Sectoral', 'TERMINAL', 'Sectoral', false , null),
       ('IT_AND_TECH', 'IT & Tech', 'TERMINAL', 'ITAndTech', false , null),
       ('MODERATELY_HIGH_RISK', 'Moderately High Risk', 'TERMINAL', 'ModeratelyHighRisk', false , null),
       ('GLOBAL', 'Global', 'TERMINAL', 'Global', false , null),
       ('CONSUMPTION', 'Consumption', 'TERMINAL', 'Consumption', false , null),
       ('PHARMA', 'Pharma', 'TERMINAL', 'Pharma', false , null),
       ('INDEX', 'Index', 'TERMINAL', 'Index', false , null),
       ('MANUFACTURING', 'Manufacturing', 'TERMINAL', 'Manufacturing', false , null),
       ('MID_CAP', 'Mid Cap', 'TERMINAL', 'MidCap', false , null),
       ('SMALL_CAP', 'Small Cap', 'TERMINAL', 'SmallCap', false , null),
       ('GROWTH', 'Growth', 'TERMINAL', 'Growth', false , null);

-- INVEST WITH SHARK ANUPAM --
INSERT INTO rules (
    id, name, description,
    event_type, client, category,
    condition,
    actions,
    max_subscriptions_per_actor, max_subscriptions,
    state, weight, allowed_user_groups, subscriptions_aggregation_type, version_support_info
)
VALUES
    (
        'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'INVEST WITH SHARK ANUPAM', '{"displayStr": "Every time Anupam offers a deal, make an investment in {mutualFundVal} for {configuredPurchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "configuredPurchaseAmount", "inputType": "MONEY"}]}',
        'SHARK_TANK_INDIVIDUAL_JUDGE_EVENT', 'FITTT', 'AUTO_INVEST',
        '{"condition": "\"Anupam\" == judge && noOfCompaniesInvestedIn > 0", "valuePath": {"judge": {"path": ["Data", "CommonEvent", "Key"]}, "noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}',
        '{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "purchaseAmount", "expression": "noOfCompaniesInvestedIn*configuredPurchaseAmount", "valuePath": {"noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}]}}]}',
        '1', '2147483647',
        'RULE_STATE_ACTIVE', 100, '{"INTERNAL"}', 'AGGREGATE_ON_TAG', '{"minSupportedAndroidAppVersion":210, "minSupportedIosAppVersion":295}'
    );

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color) values
    ('2b8790ef-7e80-4c1c-b36d-608738c4fead', 'AUTO_INVEST', 'INVEST WITH SHARK ANUPAM', 'INVEST WITH SHARK ANUPAM', 'Invest with Shark Anupam',
     'Invest every time Anupam offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-anupam.png',
     'Invest every time Anupam offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-anupam.png', '#CDC6E8', '#9287BD', '#C0B7E1');


-- param values for the rule --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('429d2ed0-deae-47fc-be7e-32d7e4681729', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220530VOdN8yLdThSwoD+9llNb0A=="}}', true, 'PARAM_STATE_ACTIVE', 3),
     ('3fa39450-bedd-4034-af6d-ad3c7bf6d917', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220613vsI9f1owShGcnUDi2uc34g=="}}', false, 'PARAM_STATE_ACTIVE', 2);


INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('4e6bf123-743a-47bc-b837-a839d352140e', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'MUTUAL_FUND',
        ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
        'CTA_STATE_ACTIVE'),
       ('1e735df0-a4eb-488d-8fb0-94b48806a630', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'MONEY',
        '{"customAmtCta":{"textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');

-- Equity; Sectoral; IT & Tech; Moderately High Risk; Global
INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
    ('523f72e2-f0da-44fd-adc6-6e1d67aafcdc', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'SHARK_TANK_2023'),
    ('d122143a-dc36-4866-b455-0df30a54f5bd', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'SECTORAL'),
    ('a1bc7e7c-ef92-4bcc-876a-b099098a0056', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'IT_AND_TECH'),
    ('01ba2cc8-e549-4e5a-9a9e-a6f94c5a6167', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'MODERATELY_HIGH_RISK'),
    ('bb269451-9ca3-474d-a57f-2bf5495d42d0', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'EQUITY'),
    ('cb5cf72a-bc64-4ee2-a796-ac0f7e0f358d', 'b9f19ba9-ab94-4bf7-85c3-38f1f0ad0b6c', 'GLOBAL');

-- INVEST WITH SHARK AMAN --
INSERT INTO rules (
    id, name, description,
    event_type, client, category,
    condition,
    actions,
    max_subscriptions_per_actor, max_subscriptions,
    state, weight, allowed_user_groups, subscriptions_aggregation_type, version_support_info
)
VALUES
    (
        'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'INVEST WITH SHARK AMAN', '{"displayStr": "Every time Aman offers a deal, make an investment in {mutualFundVal} for {configuredPurchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "configuredPurchaseAmount", "inputType": "MONEY"}]}',
        'SHARK_TANK_INDIVIDUAL_JUDGE_EVENT', 'FITTT', 'AUTO_INVEST',
        '{"condition": "\"Aman\" == judge && noOfCompaniesInvestedIn > 0", "valuePath": {"judge": {"path": ["Data", "CommonEvent", "Key"]}, "noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}',
        '{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "purchaseAmount", "expression": "noOfCompaniesInvestedIn*configuredPurchaseAmount", "valuePath": {"noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}]}}]}',
        '1', '2147483647',
        'RULE_STATE_ACTIVE', 100, '{"INTERNAL"}', 'AGGREGATE_ON_TAG', '{"minSupportedAndroidAppVersion":210, "minSupportedIosAppVersion":295}'
    );

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color) values
    ('22ef1435-0f3f-438c-ae65-29fcbbae2c66', 'AUTO_INVEST', 'INVEST WITH SHARK AMAN', 'INVEST WITH SHARK AMAN', 'Invest with Shark Aman',
     'Invest every time Aman offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-aman.png',
     'Invest every time Aman offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-aman.png', '#DEEEF2', '#7FBECE', '#C0DAE0');


-- param values for the rule --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('70171777-277e-4c1f-9d33-9c3826256e60', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220628OSPney6ORlC24uB7Ej6h9g=="}}', true, 'PARAM_STATE_ACTIVE', 3),
     ('73e95b02-6856-42a6-af53-3b26b36669f3', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF221109wIOxwUgJT+W+HDW7CXk/8A=="}}', false, 'PARAM_STATE_ACTIVE', 2);


INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('876399d7-dac6-4f22-8e32-676678d279b9', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'MUTUAL_FUND',
        ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
        'CTA_STATE_ACTIVE'),
       ('7fbc52d2-caed-4ac9-9d19-3387771601fb', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'MONEY',
        '{"customAmtCta":{"textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');


INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
        ('f6144aa3-a860-4bf6-a87e-86e936e6a4aa', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'SHARK_TANK_2023'),
        ('a902f3c9-b30e-4ae1-ba1f-ca9633e4ee8b', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'MID_CAP'),
        ('7284b4dd-8763-46d5-bea2-e8b8111e95e4', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'SMALL_CAP'),
        ('4e45e701-ba94-4d98-9b13-11e560c62968', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'VERY_HIGH_RISK'),
        ('27f6bdf1-3dc3-43d5-908f-410914260f51', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'EQUITY'),
        ('b69754c2-5ffe-4296-93bc-d79c66629954', 'de166023-fc3f-4d99-b7e2-e3ec1f1daa42', 'GROWTH');

-- INVEST WITH SHARK AMIT --
INSERT INTO rules (
    id, name, description,
    event_type, client, category,
    condition,
    actions,
    max_subscriptions_per_actor, max_subscriptions,
    state, weight, allowed_user_groups, subscriptions_aggregation_type, version_support_info
)
VALUES
    (
        '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'INVEST WITH SHARK AMIT', '{"displayStr": "Every time Amit offers a deal, make an investment in {mutualFundVal} for {configuredPurchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "configuredPurchaseAmount", "inputType": "MONEY"}]}',
        'SHARK_TANK_INDIVIDUAL_JUDGE_EVENT', 'FITTT', 'AUTO_INVEST',
        '{"condition": "\"Amit\" == judge && noOfCompaniesInvestedIn > 0", "valuePath": {"judge": {"path": ["Data", "CommonEvent", "Key"]}, "noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}',
        '{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "purchaseAmount", "expression": "noOfCompaniesInvestedIn*configuredPurchaseAmount", "valuePath": {"noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}]}}]}',
        '1', '2147483647',
        'RULE_STATE_ACTIVE', 100, '{"INTERNAL"}', 'AGGREGATE_ON_TAG', '{"minSupportedAndroidAppVersion":210, "minSupportedIosAppVersion":295}'
    );

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color) values
    ('e76a4930-8f4e-4609-b430-b80c61c88c1e', 'AUTO_INVEST', 'INVEST WITH SHARK AMIT', 'INVEST WITH SHARK AMIT', 'Invest with Shark Amit',
     'Invest every time Amit offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-amit.png',
     'Invest every time Amit offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-amit.png', '#D1DAF1', '#879EDB', '#BBC8E9');


-- param values for the rule --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('2cb45fe3-c0e0-4291-aa32-a6dd59abfc99', '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220613uDAc4K+7QzaKEp8SnDIHeg=="}}', true, 'PARAM_STATE_ACTIVE', 3),
     ('42022225-0aed-4e33-8a56-df9b27ad11be', '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220131w8sIXk4+Qsa9IC1NWgitaQ=="}}', false, 'PARAM_STATE_ACTIVE', 2);


INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('658c8ae4-cfc8-4e51-bb43-0fc1cadbcf07', '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'MUTUAL_FUND',
        ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
        'CTA_STATE_ACTIVE'),
       ('356b33e9-5a86-4ee9-a113-aebf2fef42ed', '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'MONEY',
        '{"customAmtCta":{"textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');


INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
    ('7d7181ba-3b2f-41dc-8026-958687e234f1', '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'SHARK_TANK_2023'),
    ('e92deb0b-9c83-4ece-ac69-d2796387ad48', '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'VERY_HIGH_RISK'),
    ('fe00e74d-45b0-4936-a3b3-eda25283f875', '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'EQUITY'),
    ('17bfddd2-dfd2-4c7c-9e19-b1e51cf10d4b', '6b0a69a5-ca57-47a3-b9c1-75e4cc15f3d7', 'MANUFACTURING');

-- INVEST WITH SHARK NAMITA --
INSERT INTO rules (
    id, name, description,
    event_type, client, category,
    condition,
    actions,
    max_subscriptions_per_actor, max_subscriptions,
    state, weight, allowed_user_groups, subscriptions_aggregation_type, version_support_info
)
-- INVEST WITH A SHARK --
VALUES
    (
        '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'INVEST WITH SHARK NAMITA', '{"displayStr": "Every time Namita offers a deal, make an investment in {mutualFundVal} for {configuredPurchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "configuredPurchaseAmount", "inputType": "MONEY"}]}',
        'SHARK_TANK_INDIVIDUAL_JUDGE_EVENT', 'FITTT', 'AUTO_INVEST',
        '{"condition": "\"Namita\" == judge && noOfCompaniesInvestedIn > 0", "valuePath": {"judge": {"path": ["Data", "CommonEvent", "Key"]}, "noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}',
        '{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "purchaseAmount", "expression": "noOfCompaniesInvestedIn*configuredPurchaseAmount", "valuePath": {"noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}]}}]}',
        '1', '2147483647',
        'RULE_STATE_ACTIVE', 100, '{"INTERNAL"}', 'AGGREGATE_ON_TAG', '{"minSupportedAndroidAppVersion":210, "minSupportedIosAppVersion":295}'
    );

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color) values
    ('4a4d87a8-d43e-4c9d-b791-9e128e124202', 'AUTO_INVEST', 'INVEST WITH SHARK NAMITA', 'INVEST WITH SHARK NAMITA', 'Invest with Shark Namita',
     'Invest every time Namita offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-namita.png',
     'Invest every time Namita offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-namita.png', '#F4E7BF', '#D3B250', '#EAD8A3');


-- param values for the rule --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('063d51e6-67c9-438e-a99d-4e3b36bc6ccf', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220613VcktFKnpRYC3Aon78Fntag=="}}', true, 'PARAM_STATE_ACTIVE', 3),
     ('b71c0bd3-c89e-4f3d-a8f8-68accb062f3c', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220531kD2RLGQ/TBGbfSyKaW+gZA=="}}', false, 'PARAM_STATE_ACTIVE', 2);


INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('e676d645-dcea-4552-b90b-0eaf31006e0d', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'MUTUAL_FUND',
        ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
        'CTA_STATE_ACTIVE'),
       ('57e3556c-cc53-4e45-838a-1b19b7d65158', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'MONEY',
        '{"customAmtCta":{"textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');


INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
        ('785f9d1b-5aa5-44f0-bc53-c2dac31f2c6c', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'SHARK_TANK_2023'),
        ('2d7949c2-8039-4549-92a5-14b71051c0a9', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'VERY_HIGH_RISK'),
        ('96f920ff-9f25-4a48-bcee-ab114433a551', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'EQUITY'),
        ('18791624-ba3a-4ce9-90a7-27df9c35950c', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'SECTORAL'),
        ('cebc5c4a-5136-4ace-8267-40a5f667411d', '9d4a2ac0-c9ad-4d5c-99b3-9c68d35c52ea', 'PHARMA');


-- INVEST WITH SHARK PEYUSH --
INSERT INTO rules (
    id, name, description,
    event_type, client, category,
    condition,
    actions,
    max_subscriptions_per_actor, max_subscriptions,
    state, weight, allowed_user_groups, subscriptions_aggregation_type, version_support_info
)
VALUES
    (
        'dbc2347b-3068-412e-b38f-de42f2817a5e', 'INVEST WITH SHARK PEYUSH', '{"displayStr": "Every time Peyush offers a deal, make an investment in {mutualFundVal} for {configuredPurchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "configuredPurchaseAmount", "inputType": "MONEY"}]}',
        'SHARK_TANK_INDIVIDUAL_JUDGE_EVENT', 'FITTT', 'AUTO_INVEST',
        '{"condition": "\"Peyush\" == judge && noOfCompaniesInvestedIn > 0", "valuePath": {"judge": {"path": ["Data", "CommonEvent", "Key"]}, "noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}',
        '{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "purchaseAmount", "expression": "noOfCompaniesInvestedIn*configuredPurchaseAmount", "valuePath": {"noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}]}}]}',
        '1', '2147483647',
        'RULE_STATE_ACTIVE', 100, '{"INTERNAL"}', 'AGGREGATE_ON_TAG', '{"minSupportedAndroidAppVersion":210, "minSupportedIosAppVersion":295}'
    );


INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color) values
    ('c33c7219-2418-4db1-8ee4-134bc5c25eab', 'AUTO_INVEST', 'INVEST WITH SHARK PEYUSH', 'INVEST WITH SHARK PEYUSH', 'Invest with Shark Peyush',
     'Invest every time Peyush offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-peyush.png',
     'Invest every time Peyush offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-peyush.png', '#D9F2CC', '#87BA6B', '#C5E9B2');


-- param values for the rule --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('a051a90d-9cf0-4998-8f2e-1c568efa3549', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220628RCM4gGxmRUKmBfl8dig4kg=="}}', true, 'PARAM_STATE_ACTIVE', 3),
     ('ca1f8ffe-3320-497d-ab9e-40ee48656a4a', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF221109wIOxwUgJT+W+HDW7CXk/8A=="}}', false, 'PARAM_STATE_ACTIVE', 2);


INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('71257ddb-7ae1-4062-8822-fbfbefc83e32', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'MUTUAL_FUND',
        ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
        'CTA_STATE_ACTIVE'),
       ('8d46853d-398b-453e-b781-78f9360cf429', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'MONEY',
        '{"customAmtCta":{"textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');


INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
    ('f420f8e3-f506-4316-a9c7-cace0d98bf02', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'SHARK_TANK_2023'),
    ('e4ba3f93-3d43-49da-96d9-e8ab1e0763c6', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'VERY_HIGH_RISK'),
    ('530d7389-62e9-419e-a435-bbe59e925ea2', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'EQUITY'),
    ('42f4acb7-82dd-47f8-b002-2abbb7ca51ef', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'SECTORAL'),
    ('dc9a9bb0-18d6-419c-9692-ebdb55452d08', 'dbc2347b-3068-412e-b38f-de42f2817a5e', 'ESG');


-- INVEST WITH SHARK VINEETA --
INSERT INTO rules (
    id, name, description,
    event_type, client, category,
    condition,
    actions,
    max_subscriptions_per_actor, max_subscriptions,
    state, weight, allowed_user_groups, subscriptions_aggregation_type, version_support_info
)
VALUES
    (
        'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'INVEST WITH SHARK VINEETA', '{"displayStr": "Every time Vineeta offers a deal, make an investment in {mutualFundVal} for {configuredPurchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "configuredPurchaseAmount", "inputType": "MONEY"}]}',
        'SHARK_TANK_INDIVIDUAL_JUDGE_EVENT', 'FITTT', 'AUTO_INVEST',
        '{"condition": "\"Vineeta\" == judge && noOfCompaniesInvestedIn > 0", "valuePath": {"judge": {"path": ["Data", "CommonEvent", "Key"]}, "noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}',
        '{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "purchaseAmount", "expression": "noOfCompaniesInvestedIn*configuredPurchaseAmount", "valuePath": {"noOfCompaniesInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}]}}]}',
        '1', '2147483647',
        'RULE_STATE_ACTIVE', 100, '{"INTERNAL"}', 'AGGREGATE_ON_TAG',  '{"minSupportedAndroidAppVersion":210, "minSupportedIosAppVersion":295}'
    );

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color) values
    ('9b11e7c4-c053-4c4b-93f9-671ef8e679f7', 'AUTO_INVEST', 'INVEST WITH SHARK VINEETA', 'INVEST WITH SHARK VINEETA', 'Invest with Shark Vineeta',
     'Invest every time Vineeta offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-vineeta.png',
     'Invest every time Vineeta offers a deal',
     'https://epifi-icons.pointz.in/fittt-images/rules/shark-tank-vineeta.png', '#FAD0D0', '#CF8888', '#EFC0C0');


-- param values for the rule --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     ('f765ed70-1599-4e38-b60b-232eb65cc579', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220617m5cEEAUeSUadK9CUUH7dGg=="}}', true, 'PARAM_STATE_ACTIVE', 3),
     ('998d5dce-097f-4113-9fb9-5cd66c42cc3a', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220613MEujKQvaQXa+A2Ov1psibA=="}}', false, 'PARAM_STATE_ACTIVE', 2);


INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('86bd5347-18bb-4d79-8a96-a6ca646bb333', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'MUTUAL_FUND',
        ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
        'CTA_STATE_ACTIVE'),
       ('fd2835c6-065b-47b1-bbc3-bea213e6368b', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'MONEY',
        '{"customAmtCta":{"textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');


INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
    ('a290af4e-2b95-4c29-ab32-fd411f7b148a', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'SHARK_TANK_2023'),
    ('46f7bc81-f746-4c7e-b6ee-4c026ba38b44', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'VERY_HIGH_RISK'),
    ('e77d9426-30ad-45d2-9eae-5a9bc2cdd139', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'EQUITY'),
    ('78b375ca-0074-4bc5-9ddd-0c11df2e432c', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'SECTORAL'),
    ('9569b9f0-7ae6-4def-9836-fd12f27a8fb5', 'a8c118a5-5aa3-4c00-8e79-90c45d8fed55', 'CONSUMPTION');


INSERT INTO rules (
    id, name, description,
    event_type, client, category,
    condition,
    actions,
    max_subscriptions_per_actor, max_subscriptions,
    state, weight, allowed_user_groups, subscriptions_aggregation_type, version_support_info
)
-- SHARKS ALL IN --
VALUES
    (
        'e2535f52-1ac1-41cb-ab94-e3bada5746ed', 'SHARKS ALL IN', '{"displayStr": "When all the Sharks offer a deal together, make an investment in {mutualFundVal} for {configuredPurchaseAmount}", "inputParams": [{"name": "mutualFundVal", "inputType": "MUTUAL_FUND"}, {"name": "configuredPurchaseAmount", "inputType": "MONEY"}]}',
        'SHARK_TANK_ALL_JUDGE_EVENT', 'FITTT', 'AUTO_INVEST',
        '{"condition": "noOfCompaniesAllSharksInvestedIn > 0", "valuePath": {"noOfCompaniesAllSharksInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}',
        '{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "purchaseAmount", "expression": "noOfCompaniesAllSharksInvestedIn*configuredPurchaseAmount", "valuePath": {"noOfCompaniesAllSharksInvestedIn": {"path": ["Data", "CommonEvent", "IntVal"]}}}]}}]}',
        '1', '2147483647',
        'RULE_STATE_ACTIVE', 90, '{"INTERNAL"}', 'AGGREGATE_ON_TAG',  '{"minSupportedAndroidAppVersion":210, "minSupportedIosAppVersion":295}'
    );

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color, stats_bg_color) values
    ('7df76ed4-94e1-46c9-b50e-39507a9bf95b', 'AUTO_INVEST', 'SHARKS ALL IN', 'SHARKS ALL IN', 'Sharks all In',
     'Invest when all the Sharks offer a deal together',
     'https://epifi-icons.pointz.in/fittt-images/rules/all-shark.png',
     'Invest when all the Sharks offer a deal together',
     'https://epifi-icons.pointz.in/fittt-images/rules/all-shark.png', '#DEEEF2', '#7FBECE', '#C0DAE0');

-- param values for the rule --
INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values
     -- gold fund --
     ('009cae93-f0e9-4f75-a9a2-c2372109a09a', 'e2535f52-1ac1-41cb-ab94-e3bada5746ed', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220531pswSy2pNRRq1eSUC/2mvuQ=="}}', true, 'PARAM_STATE_ACTIVE', 3),
     -- nifty fifty index --
     ('ca26196b-80cc-4652-ad8c-6dd0f285260c', 'e2535f52-1ac1-41cb-ab94-e3bada5746ed', 'MUTUAL_FUND', '{"mf_val":{"mf_id":"MF220124cyjwnXcXSHKtiDdlltiKFw=="}}', false, 'PARAM_STATE_ACTIVE', 2);

INSERT INTO param_value_selector_ctas(id, rule_id, param_value_type, cta, state)
values ('818f8b1c-ca86-4170-a19e-5be93fb02d01', 'e2535f52-1ac1-41cb-ab94-e3bada5746ed', 'MUTUAL_FUND',
        ' {"mfSelectorCta":{"text":"Select a mutual fund","iconUrl":"https://epifi-icons.pointz.in/fittt-images/icons/plus.png"}}',
        'CTA_STATE_ACTIVE'),
       ('9cf8682a-dc70-4ce9-a7e9-e172128dc6be', 'e2535f52-1ac1-41cb-ab94-e3bada5746ed', 'MONEY',
        '{"customAmtCta":{"textForRuleDesc":"Select Amount"}}','CTA_STATE_ACTIVE');

INSERT INTO rule_tag_mappings (id, rule_id, tag_id) values
    ('c3927474-b251-4d23-920f-cfa0815dae1f', 'e2535f52-1ac1-41cb-ab94-e3bada5746ed', 'SHARK_TANK_2023'),
    ('7434ea10-58db-4de9-8159-47061f8cd7b7', 'e2535f52-1ac1-41cb-ab94-e3bada5746ed', 'EQUITY'),
    ('77b3e2a2-c79d-4197-9bd5-3143969248a5', 'e2535f52-1ac1-41cb-ab94-e3bada5746ed', 'SECTORAL');
