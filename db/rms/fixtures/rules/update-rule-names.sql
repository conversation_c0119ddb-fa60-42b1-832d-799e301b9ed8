update rules set name = 'DA<PERSON>Y SIP' where id = 'd157d5c9-8b32-4f1b-9185-a60a8263ea25';
update rules set name = 'WEEKLY SIP' where id = '42286516-f4d1-421f-9c1c-1a7f65612e66';
update rules set name = 'MONTHLY SIP' where id = '0db377e1-c5ca-45f3-b0e1-99ff5b8b2ae0';

update rule_display_infos set name = 'DAILY SIP', formatted_name = 'DAILY SIP', sentence_case_name = 'Daily SIP' where id = 'dd42c626-13e5-4184-b1ad-3d7c668850ab';
update rule_display_infos set name = 'WEEKLY SIP', formatted_name = 'WEEKLY SIP', sentence_case_name = 'Weekly SIP' where id = 'e7e27cb6-3ee3-4f59-baa6-c40854be830b';
update rule_display_infos set name = '<PERSON><PERSON><PERSON><PERSON><PERSON> SIP', formatted_name = 'MONTHLY SIP', sentence_case_name = 'Monthly SIP' where id = '9108c557-07f8-4791-bad9-c9d758ab4df6';

update collections set display_info = jsonb_set(display_info::jsonb, '{name,text}', '"SIP"') where id = 'c8e66cbe-3c4a-4ea0-9686-5ccf9ba8cbe2';
update collections set display_info = jsonb_set(display_info::jsonb, '{description,text}', '"SIP for your Mutual Fund portfolio"') where id = 'c8e66cbe-3c4a-4ea0-9686-5ccf9ba8cbe2';
update collections set display_info = jsonb_set(display_info::jsonb, '{name,text}', '"Good Buys"') where id = 'eaf09b9b-df61-4cb6-ad87-b5344c2d2add';
update collections set display_info = jsonb_set(display_info::jsonb, '{description,text}', '"Invest in Mutual Funds each time you shop"') where id = 'eaf09b9b-df61-4cb6-ad87-b5344c2d2add';

update home_cards set card_data = jsonb_set(card_data, '{description,descStr}', '"Invest spare change in Mutual Funds"') where id = 'ab389f8e-4308-47f2-8435-ea5d41089826';
