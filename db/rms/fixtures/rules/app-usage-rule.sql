-- <PERSON><PERSON> MODE --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    '046b769b-c6e9-4edb-9d7e-0b20f06ad50d','ZEN MODE', '{"displayStr": "When I spend less than {configuredDuration} on {configuredApp} daily, put aside {depositAmount} into {depositAccountId}", "inputParams": [{"name": "configuredDuration", "inputType": "DURATION"}, {"name": "configuredApp", "inputType": "APP"}, {"name": "depositAmount", "inputType": "MONEY"}, {"name": "depositAccountId", "inputType": "SMART_DEPOSIT"}]}',
    'APP_USAGE_RULE_INITIATE_EXECUTION_EVENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "true"}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '20', '**********',
    'RULE_STATE_ACTIVE'
  );
