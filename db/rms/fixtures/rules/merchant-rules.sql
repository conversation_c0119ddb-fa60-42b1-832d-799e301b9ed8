-- HEALTHY WEALTHY WISE --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    '79bf2744-5781-4667-8b7f-7056a1e34a9c','HEALTHY, WEALTHY, WISE', '{ "display_str": "When I order food from {configuredMerchant} with Fi, put aside {depositAmount} in {depositAccountId}", "input_params": [ { "name":"configuredMerchant", "input_type": "MERCHANT" }, { "name":"depositAmount", "input_type":"MONEY" }, { "name":"depositAccountId", "input_type": "SMART_DEPOSIT"}] }',
    'PAYMENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '5', '**********',
    'RULE_STATE_ACTIVE'
  );

-- Dont Shop till you drop --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    'bc925bcb-ff12-43f9-8396-b6383242d939','DON''T SHOP TILL YOU DROP', '{ "display_str": "When I order from {configuredMerchant} with Fi, put aside {depositAmount} in {depositAccountId}", "input_params": [ { "name":"configuredMerchant", "input_type":4 }, { "name":"depositAmount", "input_type":8 }, { "name":"depositAccountId", "input_type":7 } ] }',
    'PAYMENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "prefixMatch(getMerchantNameFromOrder(order), configuredMerchant)", "valuePath": {"order": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order"]}}}',
    '{"actionArr": [{"data": {}, "type": "DEPOSIT"}]}',
    '5', '**********',
    'RULE_STATE_ACTIVE'
  );


-- Tip Yourself --
INSERT INTO rules (
  id, name, description, event_type, client,
  category, condition, actions, max_subscriptions_per_actor,
  max_subscriptions,
  state
)
VALUES
  (
    'e52fb0c5-8a07-4f59-a70b-18c6f2669163','TIP YOURSELF', '{ "display_str": "When I make a payment, round up to the nearest {configuredRoundAmount} and set aside the difference in {depositAccountId}", "input_params": [ { "name": "configuredRoundAmount", "input_type": 8 }, { "name": "depositAccountId", "input_type": 7 } ] }',
    'PAYMENT', 'FITTT',
    'AUTO_SAVE', '{"condition": "paymentAmount.Units >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}',
	'{"actionArr": [{"data": {"expressions": [ { "var_name": "depositAmount", "expression": "differenceFromNearestMultiple(paymentAmount, configuredRoundAmount)" }]}, "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}, "type": "DEPOSIT"}]}',
    '1', '**********',
    'RULE_STATE_ACTIVE'
  );
