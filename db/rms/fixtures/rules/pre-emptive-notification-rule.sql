-- pre emptive notification --
INSERT INTO rules (
	id, name,     event_type, client, category, condition, actions, max_subscriptions_per_actor, max_subscriptions,state, rule_type_for_special_handling
)
VALUES
	(
		'e31c6513-cadd-4e8a-8eb4-2ec696a7be75','PRE-EMPTIVE NOTIFICATION', 'DAILY_EVENT', 'FITTT', 'NOTIFY',
	    '{"condition": "shouldSendPreEmptiveNotification(1, 3)"}','{"actionArr": [{"data": {}, "type": "NOTIFY"}]}', '1', '2147483647', 'RULE_STATE_ACTIVE', 'RULE_TYPE_PRE_EMPTIVE_NOTIFICATIONS'
	);

-- adding subscriptions for all existing FIT users for pre-emptive notification --
INSERT INTO rule_subscriptions (
    actor_id, rule_id, state,
    state_change_provenance
)
select
    distinct(actor_id), 'e31c6513-cadd-4e8a-8eb4-2ec696a7be75' :: uuid, 'ACTIVE', 'INTERNAL'
from
    rule_subscriptions;

-- adding current states from rule_subscriptions table --
INSERT INTO subscription_runtime_infos (
    SELECT
        id, rule_id, actor_id, state, rule_id, 'AGGREGATE_ON_RULE', rule_param_values, null, valid_from, updated_at, deleted_at
    FROM
        rule_subscriptions
    WHERE
            version_state = 'CURRENT' and rule_id = 'e31c6513-cadd-4e8a-8eb4-2ec696a7be75'
);
