INSERT INTO rules (
	id, name, description, event_type, client,
	category, condition, actions, max_subscriptions_per_actor,
	max_subscriptions,
	state,rule_type_for_special_handling
)
VALUES
	(
		'b86d96a3-d0a8-4558-a180-d64c4d875a1e','CREDIT CARD DUE DATE REMINDER', '{"displayStr": "Remind me to pay my 5x credit card bill on the  {configuredDateOfMonth} of every month", "inputParams": [{"name": "configuredDateOfMonth", "inputType": "INT_INPUT"}]}',
		'DAILY_EVENT', 'BUDGETING',
		'REMINDER', '{"condition": "configuredDateOfMonth == getDateFromDay(day) && getCreditCardOutstandingBalance() > 0", "valuePath": {"day": {"path": ["Data", "DailyEvent", "Day"]}}}',
		'{"actionArr": [{"data": {}, "type": "ACTION_TYPE_REMINDER"}]}',
		'1', '2147483647',
		'RULE_STATE_ACTIVE','RULE_TYPE_CREDIT_CARD_DUE_DATE_REMINDER'
	);
