INSERT INTO rules (
	id, name, description, event_type, client,
	category, condition, actions, max_subscriptions_per_actor,
	max_subscriptions,
	state,rule_type_for_special_handling
)
VALUES
	(
		'd889238f-a21b-4bda-b51d-155075071119','AMOUNT SPENDS REMINDER', '{"displayStr":"Remind me if I spend more than {configuredAmount} in {configuredDuration}","inputParams":[{"name":"configuredDuration","inputType":"DURATION"},{"name":"configuredAmount","inputType":"MONEY"}]}',
		'AMOUNT_SPENDS_REMINDER', 'BUDGETING',
		'REMINDER', '{"condition": "configuredAmount <= amount", "valuePath": {"amount": {"path": ["Data", "ReminderEvent", "Val", "TxnAmount"]}}}',
		'{"actionArr": [{"data": {}, "type": "ACTION_TYPE_REMINDER"}]}',
		'1', '2147483647',
		'RULE_STATE_ACTIVE','RULE_TYPE_AMOUNT_SPENDS_REMINDER'
	);
