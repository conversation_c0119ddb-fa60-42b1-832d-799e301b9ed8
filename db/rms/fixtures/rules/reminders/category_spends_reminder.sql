INSERT INTO rules (
	id, name, description, event_type, client,
	category, condition, actions, max_subscriptions_per_actor,
	max_subscriptions,
	state,rule_type_for_special_handling
)
VALUES
	(
		'd81b1323-0700-4eaa-adad-a72b5effeaf1','CATEGORY SPENDS REMINDER', '{"displayStr":"Remind me when I spend more than {configuredAmount} on {configuredCategory} in a {configuredDuration}","inputParams":[{"name":"configuredDuration","inputType":"DURATION"},{"name":"configuredAmount","inputType":"MONEY"},{"name":"configuredCategory","inputType":"STRING_INPUT"}]}',
		'CATEGORY_SPENDS_REMINDER', 'BUDGETING',
		'REMINDER', '{"condition": "configuredAmount <= amount", "valuePath": {"amount": {"path": ["Data", "ReminderEvent", "Val", "TxnAmount"]}}}',
		'{"actionArr": [{"data": {}, "type": "ACTION_TYPE_REMINDER"}]}',
		'2147483647', '2147483647',
		'RULE_STATE_ACTIVE','RULE_TYPE_CATEGORY_SPENDS_REMINDER'
	);
