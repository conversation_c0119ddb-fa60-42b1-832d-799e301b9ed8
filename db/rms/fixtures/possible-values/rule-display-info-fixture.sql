DELETE from rule_display_infos where category = 'AUTO_SAVE' and name in ('TIP YOURSELF', 'HEALTHY, WEALTHY, WISE', 'DON''T SHOP TILL YOU DROP', 'ABOVE AND BEYOND', '<PERSON><PERSON><PERSON> MAIDEN!', 'CAN’T STOP, WON’T STOP', 'DYNAMIC DUO', 'DRAWN OUT', 'SUPER SIXER', 'HOWZAAATTT', 'ONE TEAM, ONE DREAM', '<PERSON><PERSON><PERSON> MAIDEN', 'EYES ON THE TROPHY', 'GOOOOOOAL', 'OWNING THE GAME', 'PEDAL TO THE MEDAL', '<PERSON><PERSON> MODE');

INSERT INTO rule_display_infos (category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color) values
    ('AUTO_SAVE', 'TIP YOURSELF', E'TIP \nYOURSELF', 'Tip Yourself', 'When I make a payment, round up and save','https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Home.png','When I make a payment, round up to nearest <u>defaultMoneyValue</u> and <u>save</u> the difference','https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Landing.png','#CDC6E8','#9183C7'),
    -- merchant rules --
    ('AUTO_SAVE', 'HEALTHY, WEALTHY, WISE', E'HEALTHY, WEALTHY,\n WISE', 'Healthy, Wealthy, Wise', 'When I order takeout, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/HWW-Home.png', 'When I order food from <u>defaultUniqueValue</u> with Fi, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/HWW-Landing.png', '#F4E7BF', '#C8AB52'),
    ('AUTO_SAVE', 'DON''T SHOP TILL YOU DROP', E'DON''T SHOP TILL\nYOU DROP', 'Don''t Shop till you Drop','When I order on <u>subscribedUniqueValue</u>, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Home.png', 'When I order from <u>defaultUniqueValue</u> with Fi, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/DontShopTillYouDrop-Landing.png', '#D1DAF1', '#768CC3'),
    -- cricket rules --
    ('AUTO_SAVE', 'ABOVE AND BEYOND', E'ABOVE AND BEYOND\n','Above and Beyond', 'When <u>subscribedUniqueValue</u> hits a boundary, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/SuperSixer-Home.png', 'When <u>defaultUniqueValue</u> hits a boundary, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Sixer_Landing.png', '#EABEBE', '#BC6B6B'),
    ('AUTO_SAVE', 'MAIDEN MAIDEN!', E'MAIDEN MAIDEN!\n','Maiden Maiden!', 'When <u>subscribedUniqueValue</u> bowls a maiden, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Maiden-Home.png', 'When <u>defaultUniqueValue</u> bowls a maiden, save <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Maiden_Landing.png', '#CDE5C1', '#75AA5B'),
    ('AUTO_SAVE', 'CAN’T STOP, WON’T STOP', E'CAN’T STOP, WON’T STOP\n', 'CAN’T STOP, WON’T STOP','For every <u>subscribedIntVal</u> runs <u>subscribedUniqueValue</u> scores, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/IPLBatsman.png', 'For every <u>defaultIntVal</u> runs <u>defaultUniqueValue</u> scores, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/IPLBatsman.png', '#D1DAF1', '#768CC3'),
    ('AUTO_SAVE', 'DYNAMIC DUO', E'DYNAMIC DUO\n', 'Dynamic Duo','When partnership in team <u>subscribedUniqueValue</u> scores <u>subscribedIntVal</u> runs together, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/Partnership.png', 'When partnership in team <u>defaultUniqueValue</u> scores <u>defaultIntVal</u> runs together, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/Partnership.png', '#CDC6E8', '#9183C7'),
    ('AUTO_SAVE', 'DRAWN OUT', E'DRAWN OUT\n', 'Drawn Out','If a test match ends in a draw, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner-Home.png', 'If a test match ends in a draw, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner_Landing.png', '#F4E7BF', '#C8AB52'),
    ('AUTO_SAVE', 'SUPER SIXER', E'SUPER SIXER\n', 'Super Sixer','When <u>subscribedUniqueValue</u> hits a 6, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/SuperSixer-Home.png','When <u>defaultUniqueValue</u> hits a 6, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Sixer_Landing.png','#EABEBE','#BC6B6B'),
    ('AUTO_SAVE', 'HOWZAAATTT', E'HOWZAAATTT\n', 'Howzaaattt', 'When <u>subscribedUniqueValue</u> takes a wicket, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Wicket-Home.png','When <u>defaultUniqueValue</u> takes a wicket, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Wicket_Landing.png','#DEEEF2','#72BACC'),
    ('AUTO_SAVE', 'ONE TEAM, ONE DREAM', E'ONE TEAM,\n ONE DREAM', 'One Team, One Dream', 'When <u>subscribedUniqueValue</u> wins, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner-Home.png','When <u>defaultUniqueValue</u> wins, put aside <u>defaultMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/Winner_Landing.png','#F4E7BF','#C8AB52'),
    ('AUTO_SAVE', 'MAIDEN MAIDEN', E'MAIDEN MAIDEN\n', 'Maiden Maiden', 'When <u>subscribedUniqueValue</u> bowl a maiden, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Maiden-Home.png','When any bowler from <u>defaultUniqueValue</u> bowls a maiden, save <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Maiden_Landing.png','#CDE5C1','#75AA5B'),
    -- football rules --
    ('AUTO_SAVE', 'EYES ON THE TROPHY', E'EYES ON THE TROPHY\n', 'Eyes on the Trophy', 'When <u>subscribedUniqueValue</u> wins, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/EyesOnTheTrophy_Home.png','When <u>defaultUniqueValue</u> wins, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/EyesOnTheTrophy.png','#CDC6E8','#9287BD'),
    ('AUTO_SAVE', 'GOOOOOOAL', E'GOOOOOOAL\n', 'Gooooooal', 'When <u>subscribedUniqueValue</u> scores a goal, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Goal-Home.png','When <u>defaultUniqueValue</u> scores a goal, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Gooooooal.png','#EABEBE','#CF8888'),
    ('AUTO_SAVE', 'OWNING THE GAME', E'OWNING THE GAME\n', 'Owning the Game', 'When <u>subscribedUniqueValue</u> keeps a clean sheet, put aside <u>subscribedMoneyValue</u>', 'https://epifi-icons.pointz.in/fittt-images/png-illustrations/OwningTheGame_Home.png','When <u>defaultUniqueValue</u> keeps a clean sheet, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/OwningTheGame.png','#CDE5C1','#A8CF92'),
    -- olympic rule --
    ('AUTO_SAVE', 'PEDAL TO THE MEDAL', E'PEDAL TO THE MEDAL\n', 'Pedal to the Medal', 'When India wins an Olympic medal, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/PedalToTheMedal_Home.png','When India wins an Olympic medal, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/PedalToTheMedal.png','#D1DAF1','#7C91C9'),
    -- app rules --
    ('AUTO_SAVE', 'ZEN MODE', E'ZEN MODE\n', 'Zen Mode', 'When I spend less than <u>subscribedDuration</u> on <u>subscribedUniqueValue</u>, put aside <u>subscribedMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Zen_Home.png','When I spend less than <u>defaultDuration</u> on <u>defaultUniqueValue</u>, put aside <u>defaultMoneyValue</u>','https://epifi-icons.pointz.in/fittt-images/png-illustrations/Zen.png','#CDE5C1','#87BA6B');
