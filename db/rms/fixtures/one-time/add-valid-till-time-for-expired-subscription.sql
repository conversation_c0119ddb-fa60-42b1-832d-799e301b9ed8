-- version_valid_till timestamp for few subscriptions  are not present for below 3 subscription versions
-- adding timestamps manually to stop execution of such subscriptions
update rule_subscriptions set version_valid_till = '2022-02-07 10:43:50.835', updated_at = now() where version_id = '050de565-55d3-41f7-82cd-a163a4a95b22';
update rule_subscriptions set version_valid_till = '2022-03-01 01:30:09.340', updated_at = now() where version_id = '4054fb1f-624f-4812-9b9c-a0032c81335e';
update rule_subscriptions set version_valid_till = '2022-05-22 11:25:26.882', updated_at = now() where version_id = '12e6c54e-85c2-4b77-a2b9-67e354ce18a7';
update rule_subscriptions set version_valid_till = '2021-09-27 19:15:56.220', updated_at = now() where version_id = 'befb8608-2a3d-40f5-a0d1-ecacbb27e265';
update rule_subscriptions set version_valid_till = '2022-05-30 15:46:25.239', updated_at = now() where version_id = '1af5c4fb-0482-4383-a8b0-dbe958ecd9cb';
