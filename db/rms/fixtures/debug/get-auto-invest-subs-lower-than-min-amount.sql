-- getting all subscriptions whose minimum value criteria isnt met
select
	id
from
	subscription_runtime_infos
where
	(
				(
							rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
					)= 'MF220131bDIszoW/Q16HywNTpXRTRw=='
			and CAST(
							rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 100
		)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF220131w8sIXk4+Qsa9IC1NWgitaQ=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 100
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF2204144TsjljqXRu2Vo4hY1rBaTQ=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 100
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF220505AGpkU2bDRM6m6FWch1RzsA=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 100
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF220505TpwvDjWkQNWtL3+Ix1az+Q=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 100
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF220511R6P2PkpFRomqYUndDq2MmA=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 100
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF2205055e7xAqCjQJCyPwFZPmjTZg=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 500
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF220505Y9kSpJAPR6Kf3hUUeVPpTw=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 500
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF2205052o8Eo2myRfi6h9mncdRYSQ=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 5000
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF220505nlH1c/nPQyKZ3WD1jD90jA=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 5000
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF220131bDIszoW/Q16HywNTpXRTRw=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 100
	)
   OR (
			(
						rule_param_values -> 'ruleParamValues' -> 'mutualFundVal' -> 'mutualFundVal' ->> 'mfId'
				)= 'MF220505nlH1c/nPQyKZ3WD1jD90jA=='
		and CAST(
						rule_param_values -> 'ruleParamValues' -> 'purchaseAmount' -> 'moneyVal' ->> 'units' AS integer
    ) < 100
	);
