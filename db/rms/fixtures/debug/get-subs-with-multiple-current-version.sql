select
    id, version_id, state, rule_param_values, version_valid_from,
    version_valid_till, version_state, state_change_reason, state_change_provenance
from
    rule_subscriptions
where
        id in (
        select
            id
        from
            rule_subscriptions
        where
                version_state = 'CURRENT'
        group by
            id
        having
                count(*) > 1
    ) and version_state = 'CURRENT' order by id, version_valid_from;
