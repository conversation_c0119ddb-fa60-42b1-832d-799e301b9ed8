update
    rule_subscriptions
set
    state = 'INACTIVE',
    state_change_reason = 'ACTOR_INELIGIBLE_FOR_MF_PURCHASE_ORDER',
    state_change_provenance = 'INTERNAL',
    updated_at = now()
where
        actor_id in ('AC220322tkUYnz7CQDOhweIWt3BI1Q==',
					 'AC210617Jh5Le171RoyKlqr0JjhCAA==',
					 'AC221127AtX2I0hmReyOT4bhpe86KQ==',
					 'AC220424uvow1kCnSDisuPLfB1AqZg==',
					 'AC211231hWKgSBEeQt+b/i8JfmYklQ==',
					 'AC211004nuKXW288TBGYS65m7r7N3A==',
					 'AC220912EkkWFk1pTryizC86/520xA==',
					 'AC2112226nIxCX54TaWsqQog1FT2Vg==',
					 'AC220722kj+VFV3URnGNM8gE7iZpqg==',
					 'ACjB3+3mwuTKG9XHzH/0VsPQ230403==',
					 'AC220207FfY2prWgS2Wa6kWN9OoeUA==',
					 'AC210624KOIoKp2HQ8ycTHq/uaZuOw==',
					 'AC221119VYBoWW1WSiGVE63Vsb2SpQ==',
					 'AC220609Llg+Va2BRHiWDp3q+K1rpQ==')
  and rule_id in (
    select
        id
    from
        rules
    where
            category = 'AUTO_INVEST'
      and state = 'RULE_STATE_ACTIVE'
)
  and state = 'ACTIVE'
  and version_state = 'CURRENT';


update
    subscription_runtime_infos
set
    state = 'INACTIVE',
    updated_at = now()
where
        actor_id in ('AC220322tkUYnz7CQDOhweIWt3BI1Q==',
					 'AC210617Jh5Le171RoyKlqr0JjhCAA==',
					 'AC221127AtX2I0hmReyOT4bhpe86KQ==',
					 'AC220424uvow1kCnSDisuPLfB1AqZg==',
					 'AC211231hWKgSBEeQt+b/i8JfmYklQ==',
					 'AC211004nuKXW288TBGYS65m7r7N3A==',
					 'AC220912EkkWFk1pTryizC86/520xA==',
					 'AC2112226nIxCX54TaWsqQog1FT2Vg==',
					 'AC220722kj+VFV3URnGNM8gE7iZpqg==',
					 'ACjB3+3mwuTKG9XHzH/0VsPQ230403==',
					 'AC220207FfY2prWgS2Wa6kWN9OoeUA==',
					 'AC210624KOIoKp2HQ8ycTHq/uaZuOw==',
					 'AC221119VYBoWW1WSiGVE63Vsb2SpQ==',
					 'AC220609Llg+Va2BRHiWDp3q+K1rpQ==')
  and rule_id in (
    select
        id
    from
        rules
    where
            category = 'AUTO_INVEST'
      and state = 'RULE_STATE_ACTIVE'
)
  and state = 'ACTIVE';
