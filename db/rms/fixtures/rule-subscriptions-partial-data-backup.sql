DROP TABLE if exists rule_subscriptions_backup;

CREATE TABLE rule_subscriptions_backup AS
SELECT *
FROM rule_subscriptions
WHERE version_id in
      (
          -- returns version_id of duplicate versions in current state
          select version_id from
              (
                  select id, version_id, row_number() over (
                partition by (id, version_valid_from) order by updated_at) rn
                  from
                      rule_subscriptions
                  where
                          version_state = 'EXPIRED'
              ) tmp
          where rn > 1
      );
