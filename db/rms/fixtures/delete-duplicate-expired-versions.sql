-- duplicate versions are having exact same `version_valid_from` timestamp --
-- select query partitions the table by id and version_valid_from
delete from rule_subscriptions where
        version_id in (
        -- returns version_id of duplicate versions in expired state
        select version_id from
            (
                select id, version_id, row_number() over (
                partition by (id, version_valid_from) order by updated_at) rn
                from
                    rule_subscriptions
                where
                        version_state = 'EXPIRED'
            ) tmp
        where rn > 1
    );
