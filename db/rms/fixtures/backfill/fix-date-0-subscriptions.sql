UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDateOfMonth,intVal}', jsonb('1'), true),
    state = 'INACTIVE',
    updated_at = now()
WHERE
    id in ('533d8eb6-48fd-42e8-99a6-1494f1cdcb23', 'cc1a78c1-15a8-428e-99a5-363f3e57c06d',
           '5e2ebc7f-c031-4642-985b-ceb48b920cea', '62492a27-7fda-4260-ac91-bc2124900493',
           '1e7f1a14-a25b-4aee-8587-f02e90b7a935', '8c8aec62-fb18-4918-b2ca-9fb822612acc',
           '6a44cb83-d61c-4440-859a-52a4d84d9616', '7259a8d2-348a-4a4b-ae8a-a5d42d1b37e8',
           '02c201e4-a4b1-4707-8446-4e013f597215', '12c19119-7a35-4bd2-b3bf-784aee407281',
           '666aca15-adba-4ffe-80d2-1730067a45cb')
  and version_state = 'CURRENT';

UPDATE
    subscription_runtime_infos
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDateOfMonth,intVal}', jsonb('1'), true),
    state = 'INACTIVE',
    updated_at = now()
WHERE
    id in ('533d8eb6-48fd-42e8-99a6-1494f1cdcb23', 'cc1a78c1-15a8-428e-99a5-363f3e57c06d',
           '5e2ebc7f-c031-4642-985b-ceb48b920cea', '62492a27-7fda-4260-ac91-bc2124900493',
           '1e7f1a14-a25b-4aee-8587-f02e90b7a935', '8c8aec62-fb18-4918-b2ca-9fb822612acc',
           '6a44cb83-d61c-4440-859a-52a4d84d9616', '7259a8d2-348a-4a4b-ae8a-a5d42d1b37e8',
           '02c201e4-a4b1-4707-8446-4e013f597215', '12c19119-7a35-4bd2-b3bf-784aee407281',
           '666aca15-adba-4ffe-80d2-1730067a45cb');
