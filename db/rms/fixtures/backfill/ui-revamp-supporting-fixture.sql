-- few changes are made in maintaining data as part of ui revamp
---- 1. subscribed params in rule_subscriptions stores paramId for each param
---- 2. A new table (subscription_runtime_infos) is created for maintaining runtime state of subscriptions.
------- Every write operation for subscription should be consistent in both the tables

-- This fixture
-- 1. Sets paramIds for subscriptions which have param defined in possible_param_values
---- (players for some older tournaments were not configured through possible_param_values, back then constants were used,
---- subscriptions for such tournaments will remain as it is)
-- 2. Clears all existing data inside subscription_runtime_infos and copies all the data from rule_subscriptions and other relevant tables

-- APP --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredApp,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'APP' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredApp' -> 'appVal' ->> 'appName' = possible_param_values.value -> 'app_val' ->> 'app_name';

-- DURATION --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDuration,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'DURATION' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredDuration' -> 'duration' ->> 'value' = possible_param_values.value -> 'duration' ->> 'value';

-- MERCHANT --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredMerchant,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'MERCHANT' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredMerchant' -> 'merchantVal' ->> 'name' = possible_param_values.value -> 'merchant_val' ->> 'merchant_name';

-- CRICKET PLAYER --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredCricketPlayer,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'CRICKET_PLAYER' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredCricketPlayer' -> 'playerVal' ->> 'id' = possible_param_values.value -> 'cricketPlayerVal' ->> 'playerId';

-- CRICKET TEAM --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredCricketTeam,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'CRICKET_TEAM' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredCricketTeam' -> 'teamVal' ->> 'id' = possible_param_values.value -> 'cricketTeamVal' ->> 'teamId';

-- FOOTBALL PLAYER --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredFootballPlayer,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'FOOTBALL_PLAYER' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredFootballPlayer' -> 'footballPlayerVal' ->> 'id' = possible_param_values.value -> 'football_player_val' ->> 'id';

-- FOOTBALL TEAM --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredFootballTeam,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'FOOTBALL_TEAM' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredFootballTeam' -> 'footballTeamVal' ->> 'id' = possible_param_values.value -> 'football_team_val' ->> 'id';

-- DAY OF WEEK (STRING VALUE) --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDayOfWeek,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'STRING_INPUT' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredDayOfWeek' ->> 'strVal' = possible_param_values.value ->> 'str_val';

-- DATE OF MONTH (INT VALUE) --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDateOfMonth,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'INT_INPUT' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredDateOfMonth' ->> 'intVal' = possible_param_values.value ->> 'int_val';

-- MONEY --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredDepositAmount,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'MONEY' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredDepositAmount' -> 'moneyVal' ->> 'units' = possible_param_values.value -> 'money_val' ->> 'units';

-- MONEY --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,depositAmount,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'MONEY' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'depositAmount' -> 'moneyVal' ->> 'units' = possible_param_values.value -> 'money_val' ->> 'units';

-- MONEY --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredRoundAmount,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
FROM
    possible_param_values
WHERE
    param_value_type = 'MONEY' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredRoundAmount' -> 'moneyVal' ->> 'units' = possible_param_values.value -> 'money_val' ->> 'units';

-- clean existing entries
DELETE FROM subscription_runtime_infos;

-- adding current states from rule_subscriptions table --
INSERT INTO subscription_runtime_infos (
  SELECT
    id, rule_id, actor_id, state, rule_id, 'AGGREGATE_ON_RULE', rule_param_values, null, valid_from, updated_at, deleted_at
  FROM
    rule_subscriptions
  WHERE
    version_state = 'CURRENT'
);

-- update subscriptions with aggregation type tag --
UPDATE
  subscription_runtime_infos
SET
  aggregation_id = tag_id,
  aggregation_type = 'AGGREGATE_ON_TAG'
FROM
  (
    SELECT
      tag_id, rule_id
    FROM
      rule_tag_mappings
    WHERE
      tag_id IN (
        SELECT
          id
        FROM
          rule_tags
        WHERE
          ('Sports.Cricket' @> path
          OR 'Sports.Football' @> path)
          AND type = 'TERMINAL'
      )
  ) AS a
WHERE
  subscription_runtime_infos.rule_id = a.rule_id;

-- update last_executed_at for all subscriptions from rule_executions table --
UPDATE
  subscription_runtime_infos
SET
  last_executed_at = re.last_executed_at
FROM
  (
    SELECT
      rule_subscription_id, MAX(created_at) AS last_executed_at
    FROM
      rule_executions
    WHERE
      STATE = 'ACTION_PROCESSING_SUCCESS'
    GROUP BY
      rule_subscription_id
  ) AS re
WHERE
  subscription_runtime_infos.id = re.rule_subscription_id;
