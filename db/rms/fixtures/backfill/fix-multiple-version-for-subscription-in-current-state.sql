update
    rule_subscriptions
set
    version_state = 'EXPIRED',
    updated_at = now()
where
        version_id in(
        select
            version_id
        from
            (
                select
                    version_id,
                    rank() over (
                        partition by id
                        order by
                        version_valid_from desc
                    ) version_rank
                from
                    rule_subscriptions
                where
                    version_state = 'CURRENT'
            ) as a
        where
            a.version_rank >= 2
        );
