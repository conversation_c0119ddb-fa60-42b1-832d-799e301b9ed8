-- CRICKET PLAYER --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredCricketPlayer,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
    FROM
    possible_param_values
WHERE
    param_value_type = 'CRICKET_PLAYER' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredCricketPlayer' -> 'playerVal' ->> 'id' = possible_param_values.value -> 'cricketPlayerVal' ->> 'playerId' AND
    rule_subscriptions.rule_id in ('0417cca3-5a37-4e7f-93a3-d994e7eb5a60', 'fa68779f-d914-483f-a348-5f8e37356bbb', '82a7305e-3b94-44e2-bf36-7115b6acf41a') AND
    length(rule_param_values->'ruleParamValues'->'configuredCricketPlayer'->>'paramId') < 36;

UPDATE
    subscription_runtime_infos
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredCricketPlayer,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
    FROM
    possible_param_values
WHERE
    param_value_type = 'CRICKET_PLAYER' AND
    subscription_runtime_infos.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredCricketPlayer' -> 'playerVal' ->> 'id' = possible_param_values.value -> 'cricketPlayerVal' ->> 'playerId' AND
    subscription_runtime_infos.rule_id in ('0417cca3-5a37-4e7f-93a3-d994e7eb5a60', 'fa68779f-d914-483f-a348-5f8e37356bbb', '82a7305e-3b94-44e2-bf36-7115b6acf41a') AND
    length(rule_param_values->'ruleParamValues'->'configuredCricketPlayer'->>'paramId') < 36;

-- CRICKET TEAM --
UPDATE
    rule_subscriptions
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredCricketTeam,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
    FROM
    possible_param_values
WHERE
    param_value_type = 'CRICKET_TEAM' AND
    rule_subscriptions.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredCricketTeam' -> 'teamVal' ->> 'id' = possible_param_values.value -> 'cricketTeamVal' ->> 'teamId' AND
    rule_subscriptions.rule_id in ('0417cca3-5a37-4e7f-93a3-d994e7eb5a60', 'fa68779f-d914-483f-a348-5f8e37356bbb', '82a7305e-3b94-44e2-bf36-7115b6acf41a') AND
    length(rule_param_values->'ruleParamValues'->'configuredCricketTeam'->>'paramId') < 36;

UPDATE
    subscription_runtime_infos
SET
    rule_param_values = jsonb_set( rule_param_values, '{ruleParamValues,configuredCricketTeam,paramId}', jsonb('"' || possible_param_values.id || '"'), true)
    FROM
    possible_param_values
WHERE
    param_value_type = 'CRICKET_TEAM' AND
    subscription_runtime_infos.rule_id = possible_param_values.rule_id AND
    rule_param_values -> 'ruleParamValues' -> 'configuredCricketTeam' -> 'teamVal' ->> 'id' = possible_param_values.value -> 'cricketTeamVal' ->> 'teamId' AND
    subscription_runtime_infos.rule_id in ('0417cca3-5a37-4e7f-93a3-d994e7eb5a60', 'fa68779f-d914-483f-a348-5f8e37356bbb', '82a7305e-3b94-44e2-bf36-7115b6acf41a') AND
    length(rule_param_values->'ruleParamValues'->'configuredCricketTeam'->>'paramId') < 36;
