update
    rule_subscriptions
set
    state = 'CLOSED',
    updated_at = now()
where
    version_state = 'CURRENT' and
    state != 'CLOSED' and
    rule_id in (
    select
        rule_id
    from
        rule_tag_mappings
    where
            tag_id = 'iplt20_2023'
);

update
    subscription_runtime_infos
set
    state = 'CLOSED',
    updated_at = now()
where
    state != 'CLOSED' and
    rule_id in (
    select
        rule_id
    from
        rule_tag_mappings
    where
            tag_id = 'iplt20_2023'
);
