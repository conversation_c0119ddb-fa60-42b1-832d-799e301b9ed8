delete from rule_subscriptions where
    version_id in (
        -- returns version_id of duplicate versions in current state
        select version_id from
            (
                select id, version_id, row_number() over (
                partition by (id) order by updated_at) rn
                from
                    rule_subscriptions
                where
                    version_state = 'CURRENT'
            ) tmp
        where rn > 1
    );
