update
    rule_subscriptions
set
    state = 'INACTIVE',
    state_change_reason = 'INVALID_TRANSACTION_CONSTRAINTS',
    state_change_provenance = 'INTERNAL',
    updated_at = now()
where
id = 'e244984f-eff2-406c-b863-796ee753921b' and version_state = 'CURRENT';


update
    subscription_runtime_infos
set
    state = 'INACTIVE',
    updated_at = now()
where
 id = 'e244984f-eff2-406c-b863-796ee753921b';



update
    rule_subscriptions
set
    state = 'INACTIVE',
    state_change_reason = 'INVALID_TRANSACTION_CONSTRAINTS',
    state_change_provenance = 'INTERNAL',
    updated_at = now()
where
id = 'aa2cbc0d-6d09-4868-a9e9-8b7350b963fc' and version_state = 'CURRENT';


update
    subscription_runtime_infos
set
    state = 'INACTIVE',
    updated_at = now()
where
 id = 'aa2cbc0d-6d09-4868-a9e9-8b7350b963fc';
