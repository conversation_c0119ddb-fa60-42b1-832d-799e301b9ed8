CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.beneficiaries (
    id character varying NOT NULL,
    actor_from character varying NOT NULL,
    pi_to character varying NOT NULL,
    cooldown_start_time timestamp with time zone,
    cooldown_end_time timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.beneficiaries IS 'Stores the data related to beneficiaries of an actor to enable financial transactions';
COMMENT ON COLUMN public.beneficiaries.actor_from IS 'actor in whose context beneficiary is added';
COMMENT ON COLUMN public.beneficiaries.pi_to IS 'payment instrument of the beneficiary';
COMMENT ON COLUMN public.beneficiaries.cooldown_start_time IS 'depicts the start time of the cooldown imposed on the beneficiary';
COMMENT ON COLUMN public.beneficiaries.cooldown_end_time IS 'depicts the end time of the cooldown imposed on the beneficiary';
CREATE TABLE public.beneficiary_activation_logs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    beneficiary_id character varying NOT NULL,
    activation_mode character varying DEFAULT 'ACTIVATION_MODE_UNSPECIFIED'::character varying NOT NULL,
    activation_status character varying DEFAULT 'ACTIVATION_STATUS_UNSPECIFIED'::character varying NOT NULL,
    client_request_id character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.beneficiary_activation_logs IS 'Stores the data related to beneficiary activation such as activation mode, time, to help with audit';
COMMENT ON COLUMN public.beneficiary_activation_logs.beneficiary_id IS 'foriegn key refrencing to the beneficiaries table';
COMMENT ON COLUMN public.beneficiary_activation_logs.activation_mode IS 'mode used to activate the beneficiary. for e.g. liveness';
COMMENT ON COLUMN public.beneficiary_activation_logs.activation_status IS 'activation status of the beneficiary';
COMMENT ON COLUMN public.beneficiary_activation_logs.client_request_id IS 'id of the source used for activation. for e.g. liveness id';
CREATE TABLE public.enach_mandate_actions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    client_request_id character varying,
    enach_mandate_id character varying NOT NULL,
    action_type character varying DEFAULT 'ENACH_ACTION_TYPE_UNSPECIFIED'::character varying NOT NULL,
    action_status character varying DEFAULT 'ENACH_ACTION_STATUS_UNSPECIFIED'::character varying NOT NULL,
    action_sub_status character varying DEFAULT 'ENACH_ACTION_SUB_STATUS_UNSPECIFIED'::character varying NOT NULL,
    vendor_request_id character varying,
    action_metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.enach_mandate_actions IS 'stores the data related to action taken on the enach mandate';
COMMENT ON COLUMN public.enach_mandate_actions.client_request_id IS 'denotes the request id passed by the client for initiating the request';
COMMENT ON COLUMN public.enach_mandate_actions.enach_mandate_id IS 'denotes the id of enach mandate for which the request was raised';
COMMENT ON COLUMN public.enach_mandate_actions.action_type IS '{"proto_type":"enach.enums.EnachActionType", "comment":"denotes the type of action taken on the enach mandate"}';
COMMENT ON COLUMN public.enach_mandate_actions.action_status IS '{"proto_type":"enach.enums.EnachActionStatus", "comment":"denotes the current status of request e.g IN_PROGRESS, SUCCESS, FAILED etc"}';
COMMENT ON COLUMN public.enach_mandate_actions.action_sub_status IS '{"proto_type":"enach.enums.EnachActionSubStatus", "comment":"denotes granular status of action like AWAITING_AUTHORIZATION"}';
COMMENT ON COLUMN public.enach_mandate_actions.vendor_request_id IS 'denotes the request id passed to vendor in vendor api call  in case vendor api call is needed for fulfilling the request';
COMMENT ON COLUMN public.enach_mandate_actions.action_metadata IS '{"proto_type":"enach.EnachActionMetadata", "comment":"stores the metadata related to action taken on the metadata"}';
CREATE TABLE public.enach_mandates (
    id character varying NOT NULL,
    umrn character varying,
    recurring_payment_id character varying NOT NULL,
    registration_auth_mode character varying DEFAULT 'ENACH_REGISTRATION_AUTH_MODE_UNSPECIFIED'::character varying NOT NULL,
    registration_provenance character varying DEFAULT 'ENACH_REGISTRATION_PROVENANCE_UNSPECIFIED'::character varying NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    vendor character varying DEFAULT 'VENDOR_UNSPECIFIED'::character varying NOT NULL
);
COMMENT ON TABLE public.enach_mandates IS 'stores the data related to enach mandate';
COMMENT ON COLUMN public.enach_mandates.umrn IS 'unique mandate refernce number: unique identifier for enach mandate at banks end';
COMMENT ON COLUMN public.enach_mandates.recurring_payment_id IS 'denotes the id of recurring payment for which enach mandate is created';
COMMENT ON COLUMN public.enach_mandates.registration_auth_mode IS '{"proto_type":"enach.enums.EnachRegistrationAuthMode", "comment":"denotes the authentication mode selected by the payer for authorizing mandate registration"}';
COMMENT ON COLUMN public.enach_mandates.registration_provenance IS '{"proto_type":"enach.enums.EnachRegistrationProvenance", "comment":"denotes the source from where the enach registration was performed by the user"}';
COMMENT ON COLUMN public.enach_mandates.metadata IS '{"proto_type":"enach.EnachMandateMetadata", "comment":"stores the metadata related to enach mandate"}';
COMMENT ON COLUMN public.enach_mandates.vendor IS '{"comment": "Vendor who is fulfilling the mandate creation e.g FEDERAL_BANK, DIGIO etc"}';
CREATE TABLE public.enach_mandates_actions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    client_request_id character varying,
    enach_mandate_id character varying NOT NULL,
    action_type character varying DEFAULT 'ENACH_ACTION_TYPE_UNSPECIFIED'::character varying NOT NULL,
    action_status character varying DEFAULT 'ENACH_ACTION_STATUS_UNSPECIFIED'::character varying NOT NULL,
    action_sub_status character varying DEFAULT 'ENACH_ACTION_SUB_STATUS_UNSPECIFIED'::character varying NOT NULL,
    vendor_request_id character varying,
    action_metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    vendor_batch_request_id character varying,
    action_detailed_status jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.enach_mandates_actions IS 'stores the data related to action taken on the enach mandate';
COMMENT ON COLUMN public.enach_mandates_actions.client_request_id IS 'denotes the request id passed by the client for initiating the request';
COMMENT ON COLUMN public.enach_mandates_actions.enach_mandate_id IS 'denotes the id of enach mandate for which the request was raised';
COMMENT ON COLUMN public.enach_mandates_actions.action_type IS '{"proto_type":"enach.enums.EnachActionType", "comment":"denotes the type of action taken on the enach mandate"}';
COMMENT ON COLUMN public.enach_mandates_actions.action_status IS '{"proto_type":"enach.enums.EnachActionStatus", "comment":"denotes the current status of request e.g IN_PROGRESS, SUCCESS, FAILED etc"}';
COMMENT ON COLUMN public.enach_mandates_actions.action_sub_status IS '{"proto_type":"enach.enums.EnachActionSubStatus", "comment":"denotes granular status of action like AWAITING_AUTHORIZATION"}';
COMMENT ON COLUMN public.enach_mandates_actions.vendor_request_id IS 'denotes the request id passed to vendor in vendor api call  in case vendor api call is needed for fulfilling the request';
COMMENT ON COLUMN public.enach_mandates_actions.action_metadata IS '{"proto_type":"enach.EnachActionMetadata", "comment":"stores the metadata related to action taken on the metadata"}';
COMMENT ON COLUMN public.enach_mandates_actions.vendor_batch_request_id IS '{"comment": "denotes the batch request id passed to vendor in case vendor api call is made by collating multiple requests together"}';
COMMENT ON COLUMN public.enach_mandates_actions.action_detailed_status IS '{"proto_type":"enach.ActionDetailedStatus", "comment":"stores the vendor response granular status like transaction-flag & transaction-response-code"}';
CREATE TABLE public.partner_balance_raw_responses (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    account_id text NOT NULL,
    raw_response_from_partner jsonb NOT NULL,
    api_used text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.partner_balance_raw_responses IS 'table to store raw response of balances from vendor for account';
CREATE TABLE public.savings_account_balances (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    account_id text NOT NULL,
    opening_balance_from_partner jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    available_balance_from_partner jsonb,
    ledger_balance_from_partner jsonb,
    balance_from_partner_updated_at timestamp with time zone,
    last_balance_fetch_attempted_at timestamp with time zone
);
COMMENT ON TABLE public.savings_account_balances IS 'table to store details of savings account balances for account';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.beneficiaries
    ADD CONSTRAINT beneficiaries_actor_from_pi_to_key UNIQUE (actor_from, pi_to);
ALTER TABLE ONLY public.beneficiaries
    ADD CONSTRAINT beneficiaries_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.beneficiary_activation_logs
    ADD CONSTRAINT beneficiary_activation_logs_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.enach_mandate_actions
    ADD CONSTRAINT enach_mandate_actions_client_request_id_key UNIQUE (client_request_id);
ALTER TABLE ONLY public.enach_mandate_actions
    ADD CONSTRAINT enach_mandate_actions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.enach_mandate_actions
    ADD CONSTRAINT enach_mandate_actions_vendor_request_id_key UNIQUE (vendor_request_id);
ALTER TABLE ONLY public.enach_mandates_actions
    ADD CONSTRAINT enach_mandates_actions_client_request_id_key UNIQUE (client_request_id);
ALTER TABLE ONLY public.enach_mandates_actions
    ADD CONSTRAINT enach_mandates_actions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.enach_mandates_actions
    ADD CONSTRAINT enach_mandates_actions_vendor_request_id_key UNIQUE (vendor_request_id);
ALTER TABLE ONLY public.enach_mandates
    ADD CONSTRAINT enach_mandates_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.enach_mandates
    ADD CONSTRAINT enach_mandates_recurring_payment_id_key UNIQUE (recurring_payment_id);
ALTER TABLE ONLY public.enach_mandates
    ADD CONSTRAINT enach_mandates_umrn_key UNIQUE (umrn);
ALTER TABLE ONLY public.partner_balance_raw_responses
    ADD CONSTRAINT partner_balance_raw_responses_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.savings_account_balances
    ADD CONSTRAINT savings_account_balances_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
CREATE INDEX beneficiaries_updated_at ON public.beneficiaries USING btree (updated_at DESC);
CREATE INDEX beneficiary_activation_logs_beneficiary_id ON public.beneficiary_activation_logs USING btree (beneficiary_id);
CREATE UNIQUE INDEX beneficiary_activation_logs_client_request_id_idx ON public.beneficiary_activation_logs USING btree (client_request_id);
CREATE INDEX beneficiary_activation_logs_updated_at ON public.beneficiary_activation_logs USING btree (updated_at DESC);
CREATE INDEX enach_mandate_actions_enach_mandate_id_action_type_idx ON public.enach_mandates_actions USING btree (enach_mandate_id, action_type);
CREATE INDEX enach_mandate_actions_updated_at ON public.enach_mandate_actions USING btree (updated_at);
CREATE INDEX enach_mandate_actions_vendor_batch_request_id_idx ON public.enach_mandates_actions USING btree (vendor_batch_request_id);
CREATE INDEX enach_mandates_action_enach_mandate_id_action_type_composite_id ON public.enach_mandates_actions USING btree (enach_mandate_id, action_type);
CREATE INDEX enach_mandates_action_updated_at ON public.enach_mandates_actions USING btree (updated_at);
CREATE INDEX enach_mandates_actions_updated_at ON public.enach_mandates_actions USING btree (updated_at);
CREATE INDEX enach_mandates_updated_at ON public.enach_mandates USING btree (updated_at);
CREATE UNIQUE INDEX partner_balance_raw_responses_account_id_idx ON public.partner_balance_raw_responses USING btree (account_id);
CREATE INDEX partner_balance_raw_responses_updated_at_idx ON public.partner_balance_raw_responses USING btree (updated_at);
CREATE UNIQUE INDEX savings_account_balances_account_id_idx ON public.savings_account_balances USING btree (account_id);
CREATE INDEX savings_account_balances_last_balance_fetch_attempted_at_idx ON public.savings_account_balances USING btree (last_balance_fetch_attempted_at);
CREATE INDEX savings_account_balances_updated_at_idx ON public.savings_account_balances USING btree (updated_at);
ALTER TABLE ONLY public.beneficiary_activation_logs
    ADD CONSTRAINT beneficiary_activation_logs_beneficiary_id_fkey FOREIGN KEY (beneficiary_id) REFERENCES public.beneficiaries(id);
