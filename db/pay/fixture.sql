-- savings account balances
INSERT INTO savings_account_balances (account_id, opening_balance_from_partner, available_balance_from_partner, ledger_balance_from_partner, balance_from_partner_updated_at) VALUES
('ac-2', '{"openingBalance": {"currencyCode": "INR", "units": "116452"}, "updatedAt": "2022-06-30T18:30:00Z"}', '{"currency_code": "INR", "units": 100}', '{"currency_code": "INR", "units": 100}', now()) ON CONFLICT DO NOTHING;

-- beneficiaries
INSERT INTO beneficiaries (id,actor_from, pi_to,cooldown_start_time,cooldown_end_time,created_at,updated_at) VALUES
	('beneficiary-1','actor-from-1', 'pi-to-1',now(),now(), now() + INTERVAL '1 MINUTE', now() + INTERVAL '1 MINUTE'),
	('beneficiary-2','actor-from-1', 'pi-to-2',now(),now(), now() + INTERVAL '2 MINUTE', now() + INTERVAL '2 MINUTE'),
	('beneficiary-3','actor-from-1', 'pi-to-3',now(),now(), now() + INTERVAL '3 MINUTE', now() + INTERVAL '3 MINUTE') ON CONFLICT DO NOTHING;

-- beneficiary activation logs
INSERT into beneficiary_activation_logs (id,beneficiary_id,activation_mode,activation_status,client_request_id,created_at,updated_at) values
	    ('43284dbf-3deb-4bdd-a8b8-3eb13d527e52','beneficiary-2','ACTIVATION_MODE_LIVENESS','ACTIVATION_STATUS_CREATED','client-request-id-2',now() + INTERVAL '1 MINUTE',now() + INTERVAL '1 MINUTE'),
        ('43284dbf-3deb-4bdd-a8b8-3eb13d527e51','beneficiary-3','ACTIVATION_MODE_LIVENESS','ACTIVATION_STATUS_INITIATED','client-request-id-3',now() + INTERVAL '2 MINUTE',now() + INTERVAL '2 MINUTE') ON CONFLICT DO NOTHING;

