CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS savings_account_balances (
	id uuid default uuid_generate_v4 () not null,
	account_id text NOT NULL,
	opening_balance_from_partner jsonb null,
	available_balance_from_partner jsonb not null,
	ledger_balance_from_partner jsonb not null,
	balance_from_partner_updated_at TIMESTAMPTZ not null,
	created_at    timestamp with time zone default now()              not null,
	updated_at    timestamp with time zone default now()              not null,
	deleted_at    timestamp with time zone default null,
	PRIMARY KEY (id)
);

COMMENT ON TABLE public.savings_account_balances IS 'table to store details of savings account balances for account';
CREATE INDEX IF NOT EXISTS savings_account_balances_updated_at_idx ON savings_account_balances (updated_at ASC);
CREATE UNIQUE INDEX IF NOT EXISTS savings_account_balances_account_id_idx ON savings_account_balances (account_id ASC);
