CREATE TABLE IF NOT EXISTS beneficiary_activation_logs (
	id uuid default uuid_generate_v4 () not null,
	beneficiary_id varchar not null,
	FOREIGN KEY (beneficiary_id) REFERENCES beneficiaries (id),
	activation_mode varchar not null default 'ACTIVATION_MODE_UNSPECIFIED',
	activation_status varchar not null default 'ACTIVATION_STATUS_UNSPECIFIED',
	activation_id varchar ,
	created_at    timestamp with time zone default now()              not null,
	updated_at    timestamp with time zone default now()              not null,
	deleted_at    timestamp with time zone default null,
								PRIMARY KEY (id)
	);

comment on table beneficiary_activation_logs is 'Stores the data related to beneficiary activation such as activation mode, time, to help with audit';
comment on column beneficiary_activation_logs.beneficiary_id is 'foriegn key refrencing to the beneficiaries table';
comment on column beneficiary_activation_logs.activation_mode is 'mode used to activate the beneficiary. for e.g. liveness';
comment on column beneficiary_activation_logs.activation_status is 'activation status of the beneficiary';
comment on column beneficiary_activation_logs.activation_id is 'id of the source used for activation. for e.g. liveness id';

-- data team need this index for periodic snapshot query
CREATE INDEX IF NOT EXISTS beneficiary_activation_logs_updated_at ON beneficiary_activation_logs (updated_at desc);
CREATE INDEX IF NOT EXISTS beneficiary_activation_logs_beneficiary_id ON beneficiary_activation_logs (beneficiary_id)
