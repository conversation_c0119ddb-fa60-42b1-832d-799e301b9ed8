CREATE TABLE IF NOT EXISTS enach_mandates_actions (
	id uuid default uuid_generate_v4 () not null,
	client_request_id varchar unique,
	enach_mandate_id varchar not null,
	action_type varchar not null default '<PERSON>NACH_ACTION_TYPE_UNSPECIFIED',
	action_status varchar not null default 'ENACH_ACTION_STATUS_UNSPECIFIED',
    action_sub_status varchar not null default 'ENACH_ACTION_SUB_STATUS_UNSPECIFIED',
    vendor_request_id varchar unique,
	action_metadata JSONB DEFAULT '{}'::jsonb,
	created_at    timestamp with time zone default now()              not null,
	updated_at    timestamp with time zone default now()              not null,
	deleted_at    timestamp with time zone default null,
								PRIMARY KEY (id)
	);

comment on table enach_mandates_actions is 'stores the data related to action taken on the enach mandate';
comment on column enach_mandates_actions.client_request_id is 'denotes the request id passed by the client for initiating the request';
comment on column enach_mandates_actions.enach_mandate_id is 'denotes the id of enach mandate for which the request was raised';
comment on column enach_mandates_actions.action_type is '{"proto_type":"enach.enums.EnachActionType", "comment":"denotes the type of action taken on the enach mandate"}';
comment on column enach_mandates_actions.action_status is '{"proto_type":"enach.enums.EnachActionStatus", "comment":"denotes the current status of request e.g IN_PROGRESS, SUCCESS, FAILED etc"}';
comment on column enach_mandates_actions.action_sub_status is '{"proto_type":"enach.enums.EnachActionSubStatus", "comment":"denotes granular status of action like AWAITING_AUTHORIZATION"}';
comment on column enach_mandates_actions.vendor_request_id is 'denotes the request id passed to vendor in vendor api call  in case vendor api call is needed for fulfilling the request';
comment on column enach_mandates_actions.action_metadata is '{"proto_type":"enach.EnachActionMetadata", "comment":"stores the metadata related to action taken on the metadata"}';

CREATE INDEX IF NOT EXISTS enach_mandate_actions_enach_mandate_id_action_type_idx ON enach_mandates_actions (enach_mandate_id,action_type);
-- data team need this index for periodic snapshot query
CREATE INDEX IF NOT EXISTS enach_mandates_actions_updated_at ON enach_mandates_actions (updated_at);

