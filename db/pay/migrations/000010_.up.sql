CREATE TABLE IF NOT EXISTS beneficiaries (
	id varchar not null,
    actor_from varchar not null,
    pi_to varchar not null,
    cooldown_start_time timestamp with time zone default null,
    cooldown_end_time timestamp with time zone default null,
    created_at    timestamp with time zone default now()              not null,
	updated_at    timestamp with time zone default now()              not null,
	deleted_at    timestamp with time zone default null,
	CONSTRAINT "beneficiaries_actor_from_pi_to_key" UNIQUE (actor_from,pi_to),
    PRIMARY KEY (id)
	);

comment on table beneficiaries is 'Stores the data related to beneficiaries of an actor to enable financial transactions';
comment on column beneficiaries.actor_from is 'actor in whose context beneficiary is added';
comment on column beneficiaries.pi_to is 'payment instrument of the beneficiary';
comment on column beneficiaries.cooldown_start_time is 'depicts the start time of the cooldown imposed on the beneficiary';
comment on column beneficiaries.cooldown_end_time is 'depicts the end time of the cooldown imposed on the beneficiary';

-- data team need this index for periodic snapshot query
CREATE INDEX IF NOT EXISTS beneficiaries_updated_at ON beneficiaries (updated_at desc);
