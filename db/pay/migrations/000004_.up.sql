CREATE TABLE IF NOT EXISTS enach_mandates (
	id varchar not null,
	umrn varchar unique,
	recurring_payment_id varchar unique not null,
	registration_auth_mode varchar not null default 'ENACH_REGISTRATION_AUTH_MODE_UNSPECIFIED',
	registration_provenance varchar not null default 'ENACH_REGISTRATION_PROVENANCE_UNSPECIFIED',
	metadata JSONB DEFAULT '{}'::jsonb,
	created_at    timestamp with time zone default now()              not null,
	updated_at    timestamp with time zone default now()              not null,
	deleted_at    timestamp with time zone default null,
								PRIMARY KEY (id)
	);

comment on table enach_mandates is 'stores the data related to enach mandate';
comment on column enach_mandates.umrn is 'unique mandate refernce number: unique identifier for enach mandate at banks end';
comment on column enach_mandates.recurring_payment_id is 'denotes the id of recurring payment for which enach mandate is created';
comment on column enach_mandates.registration_auth_mode is '{"proto_type":"enach.enums.EnachRegistrationAuthMode", "comment":"denotes the authentication mode selected by the payer for authorizing mandate registration"}';
comment on column enach_mandates.registration_provenance is '{"proto_type":"enach.enums.EnachRegistrationProvenance", "comment":"denotes the source from where the enach registration was performed by the user"}';
comment on column enach_mandates.metadata is '{"proto_type":"enach.EnachMandateMetadata", "comment":"stores the metadata related to enach mandate"}';

-- data team need this index for periodic snapshot query
CREATE INDEX IF NOT EXISTS enach_mandates_updated_at ON enach_mandates (updated_at);
