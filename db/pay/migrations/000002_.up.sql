CREATE TABLE IF NOT EXISTS partner_balance_raw_responses (
		id uuid default uuid_generate_v4 () not null,
		account_id text NOT NULL,
		raw_response_from_partner jsonb not null,
		api_used text not null,
		created_at    timestamp with time zone default now()              not null,
		updated_at    timestamp with time zone default now()              not null,
		deleted_at    timestamp with time zone default null,
		PRIMARY KEY (id)
);

COMMENT ON TABLE public.partner_balance_raw_responses IS 'table to store raw response of balances from vendor for account';
CREATE INDEX IF NOT EXISTS partner_balance_raw_responses_updated_at_idx ON partner_balance_raw_responses (updated_at ASC);
CREATE UNIQUE INDEX IF NOT EXISTS partner_balance_raw_responses_account_id_idx ON partner_balance_raw_responses (account_id ASC);

