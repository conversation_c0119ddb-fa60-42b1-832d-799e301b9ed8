-- This script is used to get the weekly stats of the users based on the chats and prompts
-- Fetches the chats initiated by the user and the prompts sent by the user weekly
SELECT
	date_trunc('week', to_timestamp(c.updated_at))::date AS week_start,
	a.email,
	COUNT(distinct(c.id)) AS chats, -- This counts the chats independently of the filtered messages
	SUM(
		CASE
			WHEN message_details.role = 'user' THEN 1
			ELSE 0
			END
	) AS prompts
FROM
	chat c
		JOIN
	"user" a ON c.user_id = a.id
		LEFT JOIN
	LATERAL (
		SELECT
			message.value->>'role' AS role
FROM
	jsonb_each(c.chat::jsonb->'history'->'messages') AS message
	) AS message_details ON true
WHERE
	to_timestamp(c.updated_at) >= CURRENT_DATE - INTERVAL '1 year'
GROUP BY
	week_start, a.email
ORDER BY
	week_start, prompts DESC;
