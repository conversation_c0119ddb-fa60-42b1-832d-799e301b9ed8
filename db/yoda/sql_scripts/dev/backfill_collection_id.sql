WITH collection_uuid AS (
	SELECT uuid
	FROM public.langchain_pg_collection
	WHERE name = 'epifi_risk_ops'
),

WITH matched_resources AS (
SELECT r.id AS resource_id, e.collection_id AS embedding_collection_id
FROM public.resources r
JOIN public.langchain_pg_embedding e
ON r.id::text = (e.cmetadata->>'resource_id') AND e.collection_id = '3b727440-d100-4206-bc79-5e2990a8dc9a'
)
UPDATE resources r
SET collection_id = '3b727440-d100-4206-bc79-5e2990a8dc9a' WHERE r.id IN (SELECT resource_id FROM matched_resources);
