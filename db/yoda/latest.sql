CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA public;
COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';
CREATE EXTENSION IF NOT EXISTS vector WITH SCHEMA public;
COMMENT ON EXTENSION vector IS 'vector data type and ivfflat and hnsw access methods';
CREATE TABLE public.langchain_pg_collection (
    name character varying,
    cmetadata json,
    uuid uuid NOT NULL
);
CREATE TABLE public.langchain_pg_embedding (
    collection_id uuid,
    embedding public.vector(1024),
    document character varying,
    cmetadata json,
    uuid uuid NOT NULL
);
CREATE TABLE public.resources (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    resource_type character varying NOT NULL,
    origin_resource_id character varying NOT NULL,
    last_updated_at_src timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    metadata jsonb NOT NULL
);
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.langchain_pg_collection
    ADD CONSTRAINT langchain_pg_collection_name_key UNIQUE (name);
ALTER TABLE ONLY public.langchain_pg_collection
    ADD CONSTRAINT langchain_pg_collection_pkey PRIMARY KEY (uuid);
ALTER TABLE ONLY public.langchain_pg_embedding
    ADD CONSTRAINT langchain_pg_embedding_pkey PRIMARY KEY (uuid);
ALTER TABLE ONLY public.resources
    ADD CONSTRAINT resources_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
CREATE INDEX langchain_pg_embedding_collection_id ON public.langchain_pg_embedding USING btree (collection_id);
CREATE INDEX langchain_pg_embedding_hnsw ON public.langchain_pg_embedding USING hnsw (embedding public.vector_cosine_ops) WITH (m='24', ef_construction='128');
CREATE INDEX langchain_pg_embedding_vector_dims_collection_id_idx ON public.langchain_pg_embedding USING btree (public.vector_dims(embedding), collection_id);
CREATE UNIQUE INDEX resources_resource_type_resource_id_idx ON public.resources USING btree (resource_type, origin_resource_id);
ALTER TABLE ONLY public.langchain_pg_embedding
    ADD CONSTRAINT langchain_pg_embedding_collection_id_fkey FOREIGN KEY (collection_id) REFERENCES public.langchain_pg_collection(uuid) ON DELETE CASCADE;
ALTER TABLE ONLY public.resources
	ADD CONSTRAINT resources_collection_id_fkey FOREIGN KEY (collection_id) REFERENCES public.langchain_pg_collection(uuid) ON DELETE CASCADE;

