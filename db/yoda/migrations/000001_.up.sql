CREATE TABLE IF NOT EXISTS resources (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	resource_type character varying NOT NULL,
	origin_resource_id character varying NOT NULL,
	last_updated_at_src timestamp with time zone DEFAULT now() NOT NULL,
	created_at timestamp with time zone DEFAULT now() NOT NULL,
	updated_at timestamp with time zone DEFAULT now() NOT NULL,
	deleted_at timestamp with time zone,
	metadata jsonb NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS resources_resource_type_resource_id_idx ON resources (resource_type, origin_resource_id);
