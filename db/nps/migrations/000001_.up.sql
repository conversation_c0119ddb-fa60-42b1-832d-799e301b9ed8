CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE TABLE IF NOT EXISTS nps_pension_fund_managers
(
	id 				UUID                     NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
	external_id     VARCHAR                  UNIQUE NOT NULL,  ----external_id is the unique identifier for the pension fund manager
	pfm_name   VARCHAR                  NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE UNIQUE INDEX IF NOT EXISTS nps_pension_fund_managers_external_id ON nps_pension_fund_managers(external_id);

CREATE TABLE IF NOT EXISTS nps_schemes
(
	id 				UUID                     NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
	external_id     VARCHAR                  UNIQUE NOT NULL,
	nps_pfm_id UUID          NOT NULL,
	scheme_name   <PERSON><PERSON><PERSON><PERSON>,
	logo  VARCHAR                  NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP

);
CREATE UNIQUE INDEX IF NOT EXISTS nps_scheme_external_id ON nps_schemes(external_id);

CREATE TABLE IF NOT EXISTS nps_nav_history
(
	id 				UUID                     NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
	scheme_id     UUID                     NOT NULL,
	nav_date      DATE                     NOT NULL,
	nav_value     JSONB                   NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	deleted_at_unix INT8 NOT NULL DEFAULT 0

);
CREATE UNIQUE INDEX IF NOT EXISTS nps_nav_history_scheme_date ON nps_nav_history(scheme_id , nav_date, deleted_at_unix);


