CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.nps_daily_nav (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    scheme_id uuid NOT NULL,
    nav_date date NOT NULL,
    nav_value numeric NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at_unix timestamp with time zone
);
CREATE TABLE public.nps_nav_history (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    scheme_id uuid NOT NULL,
    nav_date date NOT NULL,
    nav_value jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_at_unix bigint DEFAULT 0 NOT NULL
);
CREATE TABLE public.nps_pension_fund_managers (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    external_id character varying NOT NULL,
    pfm_name character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE public.nps_schemes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    external_id character varying NOT NULL,
    nps_pfm_id uuid NOT NULL,
    scheme_name character varying,
    logo character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.nps_daily_nav
    ADD CONSTRAINT nps_daily_nav_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.nps_nav_history
    ADD CONSTRAINT nps_nav_history_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.nps_pension_fund_managers
    ADD CONSTRAINT nps_pension_fund_managers_external_id_key UNIQUE (external_id);
ALTER TABLE ONLY public.nps_pension_fund_managers
    ADD CONSTRAINT nps_pension_fund_managers_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.nps_schemes
    ADD CONSTRAINT nps_schemes_external_id_key UNIQUE (external_id);
ALTER TABLE ONLY public.nps_schemes
    ADD CONSTRAINT nps_schemes_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
CREATE INDEX nps_dail_nav_deleted_at_unix ON public.nps_daily_nav USING btree (deleted_at_unix);
CREATE INDEX nps_daily_nav_scheme_date ON public.nps_daily_nav USING btree (scheme_id, nav_date);
CREATE INDEX nps_nav_history_scheme_date ON public.nps_nav_history USING btree (scheme_id, nav_date, deleted_at_unix);
CREATE UNIQUE INDEX nps_pension_fund_managers_external_id ON public.nps_pension_fund_managers USING btree (external_id);
CREATE UNIQUE INDEX nps_scheme_external_id ON public.nps_schemes USING btree (external_id);
