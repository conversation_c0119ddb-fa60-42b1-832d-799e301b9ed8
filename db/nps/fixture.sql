INSERT INTO public.nps_pension_fund_managers (id, external_id, pfm_name, created_at, updated_at) VALUES
	('dbc54ccc-e5b7-46d6-be16-31dd05fa8bde', 'PFM001', 'SBI PENSION FUNDS PRIVATE LIMITED', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
	('9bf849ed-2ae5-41ed-9ba7-f824009d37e0', 'PFM002', 'UTI PENSION FUND LIMITED', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
    ('812f0c32-4e87-48d9-8bee-823d146a5d44', 'PFM003', 'LIC PENSION FUND LIMITED', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
	('5f7a0b22-e611-4706-bcf7-eca82917367c', 'PFM005', 'KOTAK MAHINDRA PENSION FUND LIMITED', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30');

INSERT INTO public.nps_schemes (id, external_id, nps_pfm_id, scheme_name, logo, created_at, updated_at) VALUES
    ('f47ac10b-58cc-4372-a567-0e02b2c3d479', 'SM001016', (SELECT id FROM nps_pension_fund_managers WHERE external_id = 'PFM001'), 'NPS TRUST - A/C SBI PENSION FUND SCHEME - NPS TIER - II COMPOSITE SCHEME', 'https://example.com/dummy-logo/sbi-nps.png', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
	('550e8400-e29b-41d4-a716-446655440000', 'SM001006', (SELECT id FROM nps_pension_fund_managers WHERE external_id = 'PFM001'), 'SBI PENSION FUND SCHEME E - TIER II', 'https://example.com/dummy-logo/sbi-scheme-e.png', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
    ('6ba7b810-9dad-11d1-80b4-00c04fd430c8', 'SM002001', (SELECT id FROM nps_pension_fund_managers WHERE external_id = 'PFM002'), 'NPS TRUST- A/C - UTI PENSION FUND SCHEME - CENTRAL GOVT', 'https://example.com/dummy-logo/uti-central.png', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
    ('6ba7b811-9dad-11d1-80b4-00c04fd430c8', 'SM002002', (SELECT id FROM nps_pension_fund_managers WHERE external_id = 'PFM002'), 'NPS TRUST- A/C - UTI PENSION FUND SCHEME - STATE GOVT', 'https://example.com/dummy-logo/uti-state.png', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
	('6ba7b812-9dad-11d1-80b4-00c04fd430c8', 'SM003016', (SELECT id FROM nps_pension_fund_managers WHERE external_id = 'PFM003'), 'NPS TRUST - A/C LIC PENSION FUND SCHEME - NPS TIER - II COMPOSITE SCHEME', 'https://example.com/dummy-logo/lic-nps.png', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
	('6ba7b813-9dad-11d1-80b4-00c04fd430c8', 'SM005005', (SELECT id FROM nps_pension_fund_managers WHERE external_id = 'PFM005'), 'KOTAK PENSION FUND SCHEME C - TIER II', 'https://example.com/dummy-logo/kotak-scheme-c.png', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
	('6ba7b814-9dad-11d1-80b4-00c04fd430c8', 'SM005007', (SELECT id FROM nps_pension_fund_managers WHERE external_id = 'PFM005'), 'NPS TRUST A/C-KOTAK MAHINDRA PENSION FUND LIMITED- NPS LITE SCHEME - GOVT. PATTERN', 'https://example.com/dummy-logo/kotak-lite.png', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30'),
	('6ba7b815-9dad-11d1-80b4-00c04fd430c8', 'SM005008', (SELECT id FROM nps_pension_fund_managers WHERE external_id = 'PFM005'), 'KOTAK PENSION FUND SCHEME A - TIER I', 'https://example.com/dummy-logo/kotak-scheme-a.png', '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30');

INSERT INTO public.nps_nav_history (id, scheme_id, nav_date, nav_value, created_at, updated_at, deleted_at_unix) VALUES
	('7ba7b816-9dad-11d1-80b4-00c04fd430c8', (SELECT id FROM nps_schemes WHERE external_id = 'SM001016'), '2024-03-13 18:30:00+00:00', '{"currency_code": "INR", "units": 45, "decimals": 6789}'::jsonb, '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30', 0),
	('7ba7b817-9dad-11d1-80b4-00c04fd430c8', (SELECT id FROM nps_schemes WHERE external_id = 'SM001006'), '2024-03-13 18:30:00+00:00', '{"currency_code": "INR", "units": 34, "decimals": 5678}'::jsonb, '2024-03-19 10:00:00+05:30', '2024-03-19 10:00:00+05:30', 0);


