CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.coupons (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    sku_id uuid NOT NULL,
    inventory_total integer NOT NULL,
    inventory_left integer NOT NULL,
    coupon_details jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.coupons IS 'stores the attributed of a coupon for a given sku';
COMMENT ON COLUMN public.coupons.sku_id IS 'denotes the sku for which the coupon is created';
COMMENT ON COLUMN public.coupons.inventory_total IS 'denotes the total inventory for the coupon';
COMMENT ON COLUMN public.coupons.inventory_left IS 'denotes the currently available inventory for the coupon';
COMMENT ON COLUMN public.coupons.coupon_details IS 'denotes the details of the coupon, eg.- coupon code, activation url, will be encrypted';
CREATE TABLE public.products (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    brand_name character varying NOT NULL,
    name character varying NOT NULL,
    description character varying NOT NULL,
    product_type character varying NOT NULL,
    value integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.products IS 'stores the attributes of a product, will have one-to-many relationship with sku';
COMMENT ON COLUMN public.products.brand_name IS 'denotes the brand to which the product belongs';
COMMENT ON COLUMN public.products.name IS 'denotes the name of the product eg.-UBER_250';
COMMENT ON COLUMN public.products.description IS 'denotes the description of the product, eg.- Flat Rs250 off on Uber ride';
COMMENT ON COLUMN public.products.product_type IS 'denotes the type of the product, eg.- egv, discount code';
COMMENT ON COLUMN public.products.value IS 'denotes the value of the product, will be used only for analytics purposes';
CREATE TABLE public.redemptions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    reference_id uuid NOT NULL,
    coupon_id uuid,
    status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.redemptions IS 'stores the attributed related to redemption of a cupon';
COMMENT ON COLUMN public.redemptions.reference_id IS 'denotes the reference id that will be send by the client, will bve used to uniquely identify a redemption';
COMMENT ON COLUMN public.redemptions.coupon_id IS 'denotes the coupon id for which the redemption has been made, can be nil if redemption is not successful';
COMMENT ON COLUMN public.redemptions.status IS 'denotes the current status of the redemption, eg.- PROCESSING, SUCCESSFUL ect';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.skus (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    product_id uuid NOT NULL,
    valid_from timestamp with time zone NOT NULL,
    valid_till timestamp with time zone NOT NULL,
    tncs jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.skus IS 'stores the attributes of a sku, wil have one-to-many relationship with coupon';
COMMENT ON COLUMN public.skus.product_id IS 'denotes the product to which the sku belongs';
COMMENT ON COLUMN public.skus.valid_from IS 'denotes the time from which the sku will be valid for redemption';
COMMENT ON COLUMN public.skus.valid_till IS 'denotes the time till which the sku will be valid for redemption';
COMMENT ON COLUMN public.skus.tncs IS 'denotes the terms and conditions for the sku';
ALTER TABLE ONLY public.coupons
    ADD CONSTRAINT coupons_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_name_key UNIQUE (name);
ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.redemptions
    ADD CONSTRAINT redemptions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.redemptions
    ADD CONSTRAINT redemptions_reference_id_key UNIQUE (reference_id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.skus
    ADD CONSTRAINT skus_pkey PRIMARY KEY (id);
CREATE INDEX coupons_inventory_left_index ON public.coupons USING btree (inventory_left);
CREATE INDEX coupons_inventory_total_index ON public.coupons USING btree (inventory_total);
CREATE INDEX coupons_sku_id_index ON public.coupons USING btree (sku_id);
CREATE INDEX coupons_updated_at_index ON public.coupons USING btree (updated_at);
CREATE INDEX products_updated_at_index ON public.products USING btree (updated_at);
CREATE INDEX redemptions_updated_at_index ON public.redemptions USING btree (updated_at);
CREATE INDEX skus_updated_at_index ON public.skus USING btree (updated_at);
CREATE INDEX skus_valid_from_index ON public.skus USING btree (valid_from);
CREATE INDEX skus_valid_till_index ON public.skus USING btree (valid_till);
ALTER TABLE ONLY public.redemptions
    ADD CONSTRAINT fk_coupon_id_refs_coupons FOREIGN KEY (coupon_id) REFERENCES public.coupons(id);
ALTER TABLE ONLY public.skus
    ADD CONSTRAINT fk_product_id_refs_products FOREIGN KEY (product_id) REFERENCES public.products(id);
ALTER TABLE ONLY public.coupons
    ADD CONSTRAINT fk_sku_id_refs_skus FOREIGN KEY (sku_id) REFERENCES public.skus(id);
