set timezone to 'UTC';

--products sql
INSERT INTO public.products (id, brand_name, name, description, product_type, value, created_at, updated_at, deleted_at) VALUES
    ('7514b238-0eb8-40dc-a6c0-a991feb78b93', 'brand-name-1', 'name-1', 'description-1', 'PRODUCT_TYPE_EGV', 1000, '2023-02-08 00:00:00', '2023-02-08 00:00:00', null);

--skus sql
INSERT INTO public.skus (id, product_id, valid_from, valid_till, tncs, created_at, updated_at, deleted_at) VALUES
    ('85b3d25c-da25-4fe2-8ee3-e069197e6e2c', '7514b238-0eb8-40dc-a6c0-a991feb78b93', '2023-02-08 00:00:00', '2050-08-20 00:00:00', '{"points": ["point-1"]}', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null),
    ('5881523c-4fc7-4f70-86e6-4a28148a9b0c', '7514b238-0eb8-40dc-a6c0-a991feb78b93', '2023-02-08 00:00:00', '2050-08-20 00:00:00', '{"points": ["point-1"]}', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null),
    ('457f0718-036f-4d0c-abc7-6180ba1285b2', '7514b238-0eb8-40dc-a6c0-a991feb78b93', '2023-12-08 00:00:00', '2050-08-20 00:00:00', '{"points": ["point-1"]}', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null),
    ('15ccb918-7f63-4d1f-b6f7-57feae5947b1', '7514b238-0eb8-40dc-a6c0-a991feb78b93', '2023-02-08 00:00:00', '2050-08-20 00:00:00', '{"points": ["point-1"]}', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null);

--coupons sql
INSERT INTO public.coupons (id, sku_id, inventory_total, inventory_left, coupon_details, created_at, updated_at, deleted_at) VALUES
    ('821d234d-8569-4fb7-be5f-56d81b504730', '85b3d25c-da25-4fe2-8ee3-e069197e6e2c', 100, 100, '{"key_value_pairs": [{"key": "key-1", "value": "value-1"}], "encrypted_data_key": "dummyEncryptionKey", "master_key_id": "dummykmskeyid"}', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null),
    ('bf1ba6f3-6a1d-49fe-b410-9036bad96ab5', '5881523c-4fc7-4f70-86e6-4a28148a9b0c', 100, 0, '{"key_value_pairs": [{"key": "key-1", "value": "value-1"}], "encrypted_data_key": "dummyEncryptionKey", "master_key_id": "dummykmskeyid"}', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null),
    ('b207ec91-7dfe-4cac-ae95-3a411e211996', '15ccb918-7f63-4d1f-b6f7-57feae5947b1', 100, 100, '{"key_value_pairs": [{"key": "key-1", "value": "value-1"}], "encrypted_data_key": "dummyEncryptionKey", "master_key_id": "dummykmskeyid"}', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null),
    ('813e0fd6-7b20-4829-b585-6d9f412efa06', '15ccb918-7f63-4d1f-b6f7-57feae5947b1', 100, 100, '{"key_value_pairs": [{"key": "key-2", "value": "value-2"}], "encrypted_data_key": "dummyEncryptionKey", "master_key_id": "dummykmskeyid"}', '2023-01-08 00:00:00', '2023-02-08 00:00:00', null);

--redemptions sql
INSERT INTO public.redemptions (id, reference_id, coupon_id, status, created_at, updated_at, deleted_at) VALUES
    ('49b33210-ce4e-4f07-aa8e-4e5f5218d082', '8a66de05-6599-438a-b18d-564d26398771', '821d234d-8569-4fb7-be5f-56d81b504730', 'REDEMPTION_STATUS_COMPLETE', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null),
    ('4aa68b28-ef38-4102-a992-f3f35b3e6413', 'a44bae35-4738-4cb3-b7dc-3ef7954093cd', '821d234d-8569-4fb7-be5f-56d81b504730', 'REDEMPTION_STATUS_PROCESSING', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null),
    ('cf15c2a6-aa96-49ae-8056-e1bb6eec6670', '7f58ff63-6184-4ecd-b813-46da68ecb1a2', '821d234d-8569-4fb7-be5f-56d81b504730', 'REDEMPTION_STATUS_FAILED', '2023-02-08 00:00:00', '2023-02-08 00:00:00', null);
