CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

create table if not exists products
(
    id uuid default uuid_generate_v4() not null primary key,
    -- brand whose product will be stored
    brand_name varchar not null,
    -- name of the product
    name varchar not null unique,
    -- description of the product.
    description varchar not null,
    -- type of the product eg. egv, discount coupon retc
    product_type varchar not null,
    -- indicated value of product, only for analytics use
    value integer not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone
);
-- data team need this index for periodic snapshot query.
create index if not exists products_updated_at_index ON products USING btree (updated_at);

comment on table products is 'stores the attributes of a product, will have one-to-many relationship with sku';
comment on column products.brand_name is 'denotes the brand to which the product belongs';
comment on column products.name is 'denotes the name of the product eg.-UBER_250';
comment on column products.description is 'denotes the description of the product, eg.- Flat Rs250 off on Uber ride';
comment on column products.product_type is 'denotes the type of the product, eg.- egv, discount code';
comment on column products.value is 'denotes the value of the product, will be used only for analytics purposes';

create table if not exists skus
(
    id uuid default uuid_generate_v4() not null primary key,
    -- product id for which the sku is created
    product_id uuid not null,
    -- time from when the sku will be valid
    valid_from timestamp with time zone not null,
    -- time till when the sku will be valid
    valid_till timestamp with time zone not null,
    -- terms & conditions related to th sku
    tncs jsonb,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone,
    CONSTRAINT fk_product_id_refs_products FOREIGN KEY (product_id) REFERENCES products(id)
);
-- data team need this index for periodic snapshot query.
create index if not exists skus_updated_at_index ON skus USING btree (updated_at);
create index if not exists skus_valid_from_index ON skus USING btree (valid_from);
create index if not exists skus_valid_till_index ON skus USING btree (valid_till);

comment on table skus is 'stores the attributes of a sku, wil have one-to-many relationship with coupon';
comment on column skus.product_id is 'denotes the product to which the sku belongs';
comment on column skus.valid_from is 'denotes the time from which the sku will be valid for redemption';
comment on column skus.valid_till is 'denotes the time till which the sku will be valid for redemption';
comment on column skus.tncs is 'denotes the terms and conditions for the sku';

create table if not exists coupons
(
    id uuid default uuid_generate_v4() not null primary key,
    -- sku id for which the coupon is created
    sku_id uuid not null,
    -- total inventory for the coupon
    inventory_total integer not null,
    -- inventory left currently for the coupon
    inventory_left integer not null,
    -- details related to to coupon, eg- coupon code, will be encrypted
    coupon_details jsonb not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone,
    CONSTRAINT fk_sku_id_refs_skus FOREIGN KEY (sku_id) REFERENCES skus(id)
);
-- data team need this index for periodic snapshot query.
create index if not exists coupons_updated_at_index ON coupons USING btree (updated_at);
create index if not exists coupons_inventory_total_index ON coupons USING btree (inventory_total);
create index if not exists coupons_inventory_left_index ON coupons USING btree (inventory_left);
create index if not exists coupons_sku_id_index ON coupons USING btree (sku_id);

comment on table coupons is 'stores the attributed of a coupon for a given sku';
comment on column coupons.sku_id is 'denotes the sku for which the coupon is created';
comment on column coupons.inventory_total is 'denotes the total inventory for the coupon';
comment on column coupons.inventory_left is 'denotes the currently available inventory for the coupon';
comment on column coupons.coupon_details is 'denotes the details of the coupon, eg.- coupon code, activation url, will be encrypted';

create table if not exists redemptions
(
    id uuid default uuid_generate_v4() not null primary key,
    -- reference id to uniquely define a redemption
    reference_id uuid not null unique,
    -- coupon id corresponding to which the redemption has been done
    coupon_id uuid not null,
    -- current status of the redemption eg.- PROCESSING, COMPLETED etc.
    status varchar not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    deleted_at timestamp with time zone,
    CONSTRAINT fk_coupon_id_refs_coupons FOREIGN KEY (coupon_id) REFERENCES coupons(id)
);
-- data team need this index for periodic snapshot query.
create index if not exists redemptions_updated_at_index ON redemptions USING btree (updated_at);

comment on table redemptions is 'stores the attributed related to redemption of a cupon';
comment on column redemptions.reference_id is 'denotes the reference id that will be send by the client, will bve used to uniquely identify a redemption';
comment on column redemptions.coupon_id is 'denotes the coupon id for which the redemption has been made, can be nil if redemption is not successful';
comment on column redemptions.status is 'denotes the current status of the redemption, eg.- PROCESSING, SUCCESSFUL ect';