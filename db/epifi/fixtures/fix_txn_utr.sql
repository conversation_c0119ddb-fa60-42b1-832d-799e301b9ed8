-- due to lack of proper parsing rules UTR for this legacy transaction is wrongly marked as `ATM3`
-- updating the txn to have right utr so that txn dedupe can work properly
UPDATE transactions SET utr = '5161:15012021:S80930197', updated_at = now() where id = 'TXN210115s+4frDALS2+cnugrix9jOA==' and utr = 'ATM3';

-- due to lack of proper parsing rules UTR for this legacy transaction is wrongly marked as `ATMCARDPIN`
-- updating the txn to have right utr so that txn dedupe can work properly
UPDATE transactions SET utr = '1:28012021:S31860990', updated_at = now() where id = 'TXN210128ce04k1cHQjujleBqRRnYjw==' and utr = 'ATMCARDPIN';
