-- due to a bug in the resolve other actor, we were creating merchant actiors for which the pi was of type generic
-- we have a static PI with the id used below, for this pi also, merchant pi resolution was done and hence merchant was created
-- deleting the merchant via fixture. The affected orders and transactions will be fixed by the backfill workflow
-- Soft deletion is done because if we have missed any edge case, we will again create the merchant pi resolution link, fix the issue and again break the link

UPDATE merchant_pis
SET deleted_at = now(),updated_at = now()
WHERE merchant_id = '5fc9eeb8-f7bf-4df5-84f1-657509df8df7';
