-- create computed column based upon:
-- 1. debitedAt 2. creditedAt 3. updatedAt
ALTER TABLE transactions
	ADD COLUMN IF NOT EXISTS computed_executed_at TIMESTAMPTZ NULL FAMILY seldom_updated AS (
	CASE
	WHEN (debited_at IS NOT NULL)
	THEN
	debited_at
	when (credited_at IS NOT NULL)
	THEN
	credited_at
	ELSE
	updated_at
	END
	) STORED;
COMMENT ON COLUMN transactions.computed_executed_at IS 'executed time stamp based upon COALESCE(debitedAt,creditedAt,updatedAt)';
