-- for actor AC211108gb2kp5sQRO6wiZaxzIwxjw== we deleted existing auth factor updates
-- and change the AFU attempt status to REREGISTRATION_REQUESTED to restart enquiry calls

update auth_factor_updates
set deleted_at= now():::timestamp
where id ='c907b34e-5674-4e97-b285-8c2ce61223c6';

update auth_factor_updates
set vendor_context = jsonb_set(vendor_context, '{state}', '"REREGISTRATION_REQUESTED"') - 'reRegistrationStatus' - 'failureType',
	updated_at = now():::timestamp,
	overall_status = 'OVERALL_STATUS_STUCK',
	deleted_at = null
where id = '4c851678-fca5-4cfc-a403-e899671d8891';
