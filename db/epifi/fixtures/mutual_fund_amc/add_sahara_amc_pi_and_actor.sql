-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-sahara-amc-business-account', 'EXTERNAL_USER', null, 'Sahara Mutual Fund', 'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-sahara-amc-business-account', 'BANK_ACCOUNT', 'Sahara Mutual Fund', '{"account": {"account_type": "CURRENT", "actual_account_number": "DUMMYACCAMCSAHARA", "ifsc_code": "DUM<PERSON><PERSON>IFSCAMCSAHARA", "name": "Sahara Mutual Fund", "secure_account_number": "xxxxxxx2438"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
