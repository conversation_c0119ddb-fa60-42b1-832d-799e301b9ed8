-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments
UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-absl-amc-business-account', 'EXTERNAL_USER', null, 'Aditya Birla Sun Life AMC Ltd', 'EPIFI_WEALTH');

-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-absl-amc-business-account', 'BANK_ACCOUNT', 'Aditya Birla Sun Life AMC Ltd', '{"account": {"account_type": "CURRENT", "actual_account_number": "ABCH86EPIFI", "ifsc_code": "HDFC0000060", "name": "Aditya BIRLA MUTUAL FUND SK A/C", "secure_account_number": "xxxxxxxEPIFI"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
