-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments
UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-uti-amc-business-account', 'EXTERNAL_USER', null, 'UTI', 'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-uti-amc-business-account', 'BANK_ACCOUNT', 'UTI Mutual Fund Collection Acc', '{"account": {"account_type": "CURRENT", "actual_account_number": "UTIMFCHN6379EPF", "ifsc_code": "HSBC0400002", "name": "UTI Mutual Fund Collection Acc", "secure_account_number": "xxxxxxxxxx9EPF"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
