-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-pgim-amc-business-account', 'EXTERNAL_USER', null, 'PGIM India Asset Management Pvt Lmt',
		'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT
INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count,
								 issuer_classification)
VALUES ('paymentinstrument-pgim-amc-business-account', 'BANK_ACCOUNT', 'PGIM India Asset Management Pvt Lmt',
		'{"account": {"account_type": "CURRENT", "actual_account_number": "PGIMVIREPIFIWEA", "ifsc_code": "SCBL0036001", "name": "PGIM India Asset Management Pvt Lmt", "secure_account_number": "xxxxxxxxxIFIWEA"}}',
		'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
