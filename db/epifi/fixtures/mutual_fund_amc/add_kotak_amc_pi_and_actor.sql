-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-kotak-amc-business-account', 'EXTERNAL_USER', null, 'Kotak Mahindra Asset Management Co Ltd', 'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-kotak-amc-business-account', 'BANK_ACCOUNT', 'Kotak Mahindra Asset Management Co Ltd', '{"account": {"account_type": "CURRENT", "actual_account_number": "KMMFEPIFIRIA", "ifsc_code": "KKBK0000958", "name": "Kotak Mahindra Asset Management Co Ltd", "secure_account_number": "xxxxxxxIRIA"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
