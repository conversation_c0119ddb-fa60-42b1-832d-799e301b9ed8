-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments
UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-axis-amc-business-account', 'EXTERNAL_USER', null, 'Axis Asset Management Company Limited', 'EPIFI_WEALTH');

-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-axis-amc-business-account', 'BANK_ACCOUNT', 'Axis Asset Management Company Limited', '{"account": {"account_type": "CURRENT", "actual_account_number": "***************", "ifsc_code": "UTIB0000004", "name": "AXIS MF GATEWAY COLLECTION A/C", "secure_account_number": "xxxxxxx7875"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
