-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-lnt-amc-business-account', 'EXTERNAL_USER', null, 'L And T Investment Management Ltd', 'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-lnt-amc-business-account', 'BANK_ACCOUNT', 'L And T Mutual Fund Collection AC', '{"account": {"account_type": "CURRENT", "actual_account_number": "LTMFCHEPIFIWLT", "ifsc_code": "HDFC0000060", "name": "L And T Mutual Fund Collection AC", "secure_account_number": "xxxxxxxIWLT"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
