-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-invesco-amc-business-account', 'EXTERNAL_USER', null, 'Invesco Mutual Fund', 'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-invesco-amc-business-account', 'BANK_ACCOUNT', 'Invesco MF Channel Collection AC', '{"account": {"account_type": "CURRENT", "actual_account_number": "INVECPEPIFI", "ifsc_code": "ICIC0000106", "name": "Invesco MF Channel Collection AC", "secure_account_number": "xxxxxxxPIFI"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
