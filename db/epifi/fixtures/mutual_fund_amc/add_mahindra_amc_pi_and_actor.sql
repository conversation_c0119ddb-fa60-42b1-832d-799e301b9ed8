-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-mahindra-amc-business-account', 'EXTERNAL_USER', null, 'Mahindra Manulife Investment Management Pvt Lmt',
		'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT
INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count,
								 issuer_classification)
VALUES ('paymentinstrument-mahindra-amc-business-account', 'BANK_ACCOUNT', 'Mahindra MF Online Collection Ac',
		'{"account": {"account_type": "CURRENT", "actual_account_number": "**************", "ifsc_code": "HDFC0000060", "name": "Ma<PERSON>dra MF Online Collection Ac", "secure_account_number": "xxxxxxxxx16667"}}',
		'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
