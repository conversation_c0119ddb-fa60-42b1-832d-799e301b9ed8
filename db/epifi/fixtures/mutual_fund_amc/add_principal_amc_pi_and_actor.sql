-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- Adding actors for all onboarded AMCs for mutual fund investments

UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-principal-amc-business-account', 'EXTERNAL_USER', null, 'Principal Mutual Fund', 'EPIFI_WEALTH');


-- Adding pi for onboarded amcs
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
	('paymentinstrument-principal-amc-business-account', 'BANK_ACCOUNT', 'Principal Mutual Fund', '{"account": {"account_type": "CURRENT", "actual_account_number": "DUMMYACCAMCPRINCIPAL", "ifsc_code": "DUMMY<PERSON><PERSON>AM<PERSON><PERSON>NCIPAL", "name": "Principal Mutual Fund", "secure_account_number": "xxxxxxx2438"}}', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
