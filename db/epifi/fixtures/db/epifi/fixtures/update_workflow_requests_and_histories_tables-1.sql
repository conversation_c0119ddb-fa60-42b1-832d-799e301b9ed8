-- https://monorail.pointz.in/p/fi-app/issues/detail?id=52395
-- P2P_FUND_TRANSFER orders are IN_PAYMENT when txn status is FAILED
-- Related to https://monorail.pointz.in/p/fi-app/issues/detail?id=54457
-- Moving orders to PAYMENT_FAILED, WF to FAILED in workflow_requests and workflow_histories tables
-- Details : https://docs.google.com/spreadsheets/d/1VFcgd8M09h6jnPdPmzYJS8w2PM7exUDfVymOLdnLbPs/edit#gid=0
-- Order fixture : db/epifi/fixtures/update_p2p_fund_transfer_orders_to_failed-9.sql

UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFR0+Z8PVi0QxSGArg44p+HcQ230502==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFR903Mvy0IQ/e2+MXXwhhcBA230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRDDwVtJ4ZR8GA/YYupDZ48A230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRO4QC83JsQ8+zBw5AXBajSg230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRPhK3O41jS36+BlXjfFyMtA230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRQPL5VpwRQYihWgOSEGtQ2Q230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRQwtvtrbbTYG7hMAsBJa/XQ230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRWRf+vbokSLmLLTmJqyadYw230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRWzwylQUlQ56JmkknzrkdSQ230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRXtOwy0DzQXO0xdS37wMOrQ230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRZ13sk37BTcmuEJeXR9oCrg230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRZz4lfMphRH6NIQATsHdmfA230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFReKDOvhrrSkGUJXrX86RE0g230502==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRhyyTjohCSwqtHQxE44FkLw230502==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRoVICCxjZQ+qjoKSjLzlg8w230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRpEiCQD5RTJCCamHbIqqVnw230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRpmd240tNQmKpbVD2e8vC5w230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRqxPtHiXVQVe3bbYdkWhYKQ230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRs7CI2/lPTNa856Exjy83Sg230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRxn6tCsIkQSW73CpSus7NWA230502==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRydoR+DcsQmme+OjDjYHNUw230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_requests SET STATUS = 'FAILED', updated_at = NOW() where ID = 'WFRz+C46lPpRgej3r+9ImAp1Q230504==' AND STATUS = 'INITIATED' ;

UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFR0+Z8PVi0QxSGArg44p+HcQ230502==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFR903Mvy0IQ/e2+MXXwhhcBA230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRDDwVtJ4ZR8GA/YYupDZ48A230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRO4QC83JsQ8+zBw5AXBajSg230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRPhK3O41jS36+BlXjfFyMtA230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRQPL5VpwRQYihWgOSEGtQ2Q230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRQwtvtrbbTYG7hMAsBJa/XQ230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRWRf+vbokSLmLLTmJqyadYw230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRWzwylQUlQ56JmkknzrkdSQ230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRXtOwy0DzQXO0xdS37wMOrQ230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRZ13sk37BTcmuEJeXR9oCrg230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRZz4lfMphRH6NIQATsHdmfA230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFReKDOvhrrSkGUJXrX86RE0g230502==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRhyyTjohCSwqtHQxE44FkLw230502==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRoVICCxjZQ+qjoKSjLzlg8w230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRpEiCQD5RTJCCamHbIqqVnw230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRpmd240tNQmKpbVD2e8vC5w230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRqxPtHiXVQVe3bbYdkWhYKQ230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRs7CI2/lPTNa856Exjy83Sg230503==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRxn6tCsIkQSW73CpSus7NWA230502==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRydoR+DcsQmme+OjDjYHNUw230504==' AND STATUS = 'INITIATED' ;
UPDATE workflow_histories SET STATUS = 'FAILED', updated_at = NOW() where WF_REQ_ID = 'WFRz+C46lPpRgej3r+9ImAp1Q230504==' AND STATUS = 'INITIATED' ;
