--  Fixing timeline for cx issues, Ticket no: 1537457
UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,2,createdAt}', '"2022-07-09T10:34:59Z"')
where id = 'P2PIT220709NoE83AswQrKiPlfROkOg/A==';
UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,3,createdAt}', '"2022-07-09T10:35:59Z"')
where id = 'P2PIT220709NoE83AswQrKiPlfROkOg/A==';
UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,4,createdAt}', '"2022-07-09T10:36:59Z"')
where id = 'P2PIT220709NoE83AswQrKiPlfROkOg/A==';
UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,5,createdAt}', '"2022-07-09T11:37:59Z"')
where id = 'P2PIT220709NoE83AswQrKiPlfROkOg/A==';

--  Fixing timeline for cx issues, Ticket no: 1578644
UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,2,createdAt}', '"2022-07-27T01:44:21.925177399Z"')
where id = 'P2PIT220727XlWjaUo3Q+Ow/aVGqkPtWA==';

UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,3,createdAt}', '"2022-07-27T01:44:29.925177399Z"')
where id = 'P2PIT220727XlWjaUo3Q+Ow/aVGqkPtWA==';

UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,4,createdAt}', '"2022-07-27T02:44:45.925177399Z"')
where id = 'P2PIT220727XlWjaUo3Q+Ow/aVGqkPtWA==';

UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,5,createdAt}', '"2022-07-27T02:49:21.925177399Z"')
where id = 'P2PIT220727XlWjaUo3Q+Ow/aVGqkPtWA==';
