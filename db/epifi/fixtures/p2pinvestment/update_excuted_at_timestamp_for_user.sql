-- for investor id: P2PIVR220724oG0a5IblRjufG53b0LPMiw== and investment transaction id : P2PIT220724Xpt+t4f7RZiWm4bcj8i3eQ==
-- to show correct maturity date we need to update executed_at timestamp
-- also need to update the payment received date

UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{executionTimestamp}', '"2022-07-25T00:00:20Z"')
where id = 'P2PIT220724Xpt+t4f7RZiWm4bcj8i3eQ==';

UPDATE p2p_investment_transactions
SET details = jsonb_set(details, '{timeline,2,createdAt}', '"2022-07-24T14:04:59Z"')
where id = 'P2PIT220724Xpt+t4f7RZiWm4bcj8i3eQ==';
