--/ transaction went under manual intervention after UTR collision (utr with given value already exist)
--/ because of this payment unable to notify order that payment is successful due to which p2p investment transaction remain in_progress
--/ payment is already done by investor, so need to mark transaction as successful and need to add utr to p2p investment transaction as payment_ref_id

UPDATE transactions
SET status = 'SUCCESS'
where id = 'TXN220724qfvHGWuVQLO0+RAjUNQgbA==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '220519823993'
where id = 'P2PIT220724Xpt+t4f7RZiWm4bcj8i3eQ==';


UPDATE transactions
SET status = 'SUCCESS'
where id = 'TXN220722C0Mxg30lS62tHxKZQfaSVg==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '220314526444'
where id = 'P2PIT220722nQ13x/DzROmtHt6e74dDwQ==';


UPDATE transactions
SET status = 'SUCCESS'
where id = 'TXN220709XemoWqx/TOinJVzm2QY8Dg==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '219015346006'
where id = 'P2PIT220709NoE83AswQrKiPlfROkOg/A==';


UPDATE transactions
SET status = 'SUCCESS'
where id = 'TXN2207278cnlorpbS96Ym93TtooNcQ==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '220807281495'
where id = 'P2PIT220727XlWjaUo3Q+Ow/aVGqkPtWA==';


UPDATE transactions
SET status = 'SUCCESS'
where id = 'TXN220801/tScgEILROuo9A5tltifbw==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '221315346720'
where id = 'P2PIT220801yAS4n5CVRtKjt8XrEequrQ==';

UPDATE transactions
SET status = 'SUCCESS'
where id = 'TXN220707YT9EmDKpS/KqPrw8sQkafQ==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '218822934338'
where id = 'P2PIT220707y7e9E+ZlQzOGNalO3dS8cA==';

UPDATE transactions
SET status = 'SUCCESS'
where id = 'TXN220729fHcVZUF6T9WKg3LnUQlAsQ==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '221101785706'
where id = 'P2PIT2207292IIXFyXVTAm8qx/Vco1j9A==';
