-- This fixture adds scheme fixture for p2p investment

--  NEW 7% Flexi scheme
UPSERT
INTO p2p_investment_schemes (id, vendor_scheme_id, vendor, details, deleted_at, name)
VALUES ('invest-scheme-flexi-7-id', '1017', 'VENDOR_LIQUILOANS', '{
	"lockInTenureInDays": 0,
	"tenureInDays": 365,
	"maxInvestableAmount": {
		"currencyCode": "INR",
		"nanos": 0,
		"units": "300000"
	},
	"eligibilityDetails": {
		"defaultAvgBalanceThreshold": 5000
	},
	"withdrawalConstraints": {
		"hardLockInTerm": {
			"days": 0,
			"months": 0,
			"years": 0
		},
		"softLockInTerm": {
			"days": 0,
			"months": 0,
			"years": 0
		}
	},
	"tenure": {
		"days": 0,
		"months": 0,
		"years": 1
	},
	"investmentRoi": 7
}', null, 'SCHEME_NAME_LL_FLEXI');

--  NEW 8% Short term scheme
UPSERT
INTO p2p_investment_schemes (id, vendor_scheme_id, vendor, details, deleted_at, name)
VALUES ('invest-scheme-short-term-8-id', '1018', 'VENDOR_LIQUILOANS', '{
	"lockInTenureInDays": 90,
	"tenureInDays": 90,
	"eligibilityDetails": {
		"defaultAvgBalanceThreshold": 5000
	},
	"withdrawalConstraints": {
		"hardLockInTerm": {
			"days": 0,
			"months": 3,
			"years": 0
		},
		"softLockInTerm": {
			"days": 0,
			"months": 0,
			"years": 0
		}
	},
	"tenure": {
		"days": 0,
		"months": 3,
		"years": 0
	},
	"investmentRoi": 8
}', null, 'SCHEME_NAME_LL_SHORT_TERM');

--  NEW 9% Long term scheme
UPSERT
INTO p2p_investment_schemes (id, vendor_scheme_id, vendor, details, deleted_at, name)
VALUES ('invest-scheme-long-term-9-id', '891', 'VENDOR_LIQUILOANS', '{
	"lockInTenureInDays": 30,
	"tenureInDays": 365,
	"eligibilityDetails": {
		"defaultAvgBalanceThreshold": 5000
	},
	"withdrawalConstraints": {
		"hardLockInTerm": {
			"days": 0,
			"months": 1,
			"years": 0
		},
		"softLockInTerm": {
			"days": 0,
			"months": 0,
			"years": 1
		}
	},
	"tenure": {
		"days": 0,
		"months": 0,
		"years": 1
	},
	"investmentRoi": 9
}', null, 'SCHEME_NAME_LL_LONG_TERM');

-- Older 9% scheme
UPSERT
INTO p2p_investment_schemes (id, vendor_scheme_id, vendor, details, deleted_at, name)
VALUES ('invest-scheme-id-1', '871', 'VENDOR_LIQUILOANS', '{
	"lockInTenureInDays": 90,
	"tenureInDays": 365,
	"withdrawalConstraints": {
		"hardLockInTerm": {
			"days": 0,
			"months": 0,
			"years": 0
		},
		"softLockInTerm": {
			"days": 0,
			"months": 3,
			"years": 0
		}
	},
	"tenure": {
		"days": 0,
		"months": 0,
		"years": 1
	},
	"investmentRoi": 9
}', null, 'SCHEME_NAME_LL_DEFAULT');

-- Older 9% liquid scheme used for withdrawal without penalty
UPSERT
INTO p2p_investment_schemes (id, vendor_scheme_id, vendor, details, deleted_at, name)
VALUES ('withdrawal-no-penalty-scheme-id', '599', 'VENDOR_LIQUILOANS', '{
	"investmentRoi": 9
}', null, 'SCHEME_NAME_LL_DEFAULT_NO_PENALTY');
