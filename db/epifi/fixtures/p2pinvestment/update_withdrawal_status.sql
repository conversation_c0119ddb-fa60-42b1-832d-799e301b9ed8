-- P2P withdrawal transactions reach SUCCESS state after both investment vendor and partner bank have confirmed
-- the transaction.
-- Partner bank sends us an inbound notification for every transaction on user's savings account. P2P service checks
-- for these notifications with a specific TranParticular ("NDX P2P"). On receiving such notifications, P2P service
-- check if a withdrawal transaction exists in the system in non-terminal state and with the same amount. If yes, it treats
-- it as a P2P withdrawal confirmation form partner bank.

-- Need to mark these transaction as success manually because for these txn we were not able to mark the inbound notification
-- as p2p inboound notification. This led to processing of those notifications as offline txn, and not p2p txns.

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:********:S11385102'
where id in ('P2PIT220801dNygr17EQua49t7D7EBg0g==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:********:S63797572'
where id in ('P2PIT2208049Z62IZGeRFav1hem7pf08Q==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:********:S63800003'
where id in ('P2PIT2208049Z62IZGeRFav1hem7pf08Q==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:28072022:S50788545'
where id in ('P2PIVR220725ynn7kpp0SdCXll1yw5Uspw==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:04082022:S23477585'
where id in ('P2PIVR220718xO+hNsM0S4WjC6jc1uIg3Q==');

UPDATE p2p_investment_transactions
SET details = jsonb_set(
	details::jsonb,
	array['timeline'],
	(details->'timeline')::jsonb || json_build_object('status','SUCCESS','subStatus','SUCCESS_WITHDRAWAL_COMPLETE','createdAt','2022-08-10T18:45:10.513719990Z')::jsonb)
where id in ('P2PIT220801dNygr17EQua49t7D7EBg0g==',
			 'P2PIT2208049Z62IZGeRFav1hem7pf08Q==',
			 'P2PIT2208049Z62IZGeRFav1hem7pf08Q==',
			 'P2PIVR220725ynn7kpp0SdCXll1yw5Uspw==',
			 'P2PIVR220718xO+hNsM0S4WjC6jc1uIg3Q=='
	);

UPDATE orders
SET status = 'PAID'
where id in ('OD2208013IYPL1hAQYWBf2ePREAEHw==',
			 'OD220804Vf082aBiQcOEdIE/p/6CKg==',
			 'OD220805DG6t1exYQhmwI3LAJFtwtg==',
			 'OD220727noBfPtMWSkWin56mIHw52A==',
			 'OD220802KfRKs7wjQfqe0M38+MLKqA=='
	);

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:11082022:S93891880'
where id in ('P2PIVR220731kyfNLgKXQEWyQBq/PyHyLA==');

UPDATE p2p_investment_transactions
SET details = jsonb_set(
	details::jsonb,
	array['timeline'],
	(details->'timeline')::jsonb || json_build_object('status','SUCCESS','subStatus','SUCCESS_WITHDRAWAL_COMPLETE','createdAt','2022-08-15T18:45:10.513719990Z')::jsonb)
where id in (
    'P2PIVR220731kyfNLgKXQEWyQBq/PyHyLA=='
	);

UPDATE orders
SET status = 'PAID'
where id in (
    'OD220809VCY4FNwNR4ObLesP6Ql47g=='
	);

-- utr collision cases

UPDATE transactions
SET status = 'SUCCESS', utr = '219015346006'
where id = 'TXN220709XemoWqx/TOinJVzm2QY8Dg==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '219015346006'
where id = 'P2PIT220709NoE83AswQrKiPlfROkOg/A==';

UPDATE transactions
SET status = 'SUCCESS', utr = '220807281495'
where id = 'TXN2207278cnlorpbS96Ym93TtooNcQ==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '220807281495'
where id = 'P2PIT220727XlWjaUo3Q+Ow/aVGqkPtWA==';

UPDATE transactions
SET status = 'SUCCESS', utr = '221315346720'
where id = 'TXN220801/tScgEILROuo9A5tltifbw==';

UPDATE p2p_investment_transactions
SET payment_ref_id = '221315346720'
where id = 'P2PIT220801yAS4n5CVRtKjt8XrEequrQ==';

UPDATE orders
SET status = 'MANUAL_INTERVENTION'
where id in (
    'OD220709kz/jrA2fSuSs5bwMLfoEaA==',
	'OD220727tB+M18HHTYWcVp1DE+qbzQ==',
    'OD220801c/1q/2X/S1++H2+zrY3WwA=='
            );
