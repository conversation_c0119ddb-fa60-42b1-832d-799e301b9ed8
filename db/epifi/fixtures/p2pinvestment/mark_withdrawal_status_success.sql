UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:10082022:S83136479'
where id in ('P2PIT220807OoxojG3dTHm66xPTcTSuKg==');

UPDATE p2p_investment_transactions
SET details = jsonb_set(
	details::jsonb,
	array['timeline'],
	(details->'timeline')::jsonb || json_build_object('status','SUCCESS','subStatus','SUCCESS_WITHDRAWAL_COMPLETE','createdAt','2022-08-15T18:45:10.513719990Z')::jsonb)
where id in ('P2PIT220807OoxojG3dTHm66xPTcTSuKg==');

UPDATE orders
SET status = 'PAID'
where id in ('OD220807+VxlVWsZRjqre9CkV/4y2Q==');
