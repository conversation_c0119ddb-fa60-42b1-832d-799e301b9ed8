-- cases where we are unable to classify p2p vendor notification as p2p transaction
-- Marking those cases as success

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:30082022:S81937871'
where id in ('P2PIT220826Fv0s/rfGQOynQWJId1+N6A==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:30082022:S81952850'
where id in ('P2PIT220826ggkojyY7SbeFRgXdNGUQBg==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:30082022:S81936697'
where id in ('P2PIT220826p/+h0voNREqUzaRBO1MWHw==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:30082022:S81937954'
where id in ('P2PIT2208263PF+ANjFToiJWni4rZURuw==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:30082022:S81938002'
where id in ('P2PIT220826r8exUlalR1KczPYwcwpRLg==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:29082022:S71220883'
where id in ('P2PIT2208267N5dk2vOT3evy7U0OB/guA==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:29082022:S71215105'
where id in ('P2PIT220826bgAUqYkuRf+nmCrtzXwzng==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:29082022:S71220993'
where id in ('P2PIT220826BV8LBa7ZSN+g9QiN3rcxhQ==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:29082022:S71214992'
where id in ('P2PIT220826ntp6QTutTiq4dOyDUhimPA==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:29082022:S71210495'
where id in ('P2PIT220826M5dastJvR0W9ahA/eq4UtQ==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:26082022:S42591925'
where id in ('P2PIT2208246KQIVa1dTA6sEu1yb6dZuw==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:26082022:S42592016'
where id in ('P2PIT220824atscku9WSjukwugBZcxvjQ==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:25082022:S32440050'
where id in ('P2PIT2208242IseBCfHQbeksuuHdeA9/w==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:25082022:S32444636'
where id in ('P2PIT220824DMfJAjRTQWCGcPaV1c9oXw==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:25082022:S32441914'
where id in ('P2PIT220824PG5nSHV5SoevP4rvRmGHCg==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:25082022:S32438868'
where id in ('P2PIT2208236CNIB5M/QtyBS3CbWdY67g==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:24082022:S24270121'
where id in ('P2PIT220826M5dastJvR0W9ahA/eq4UtQ==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:24082022:S24273458'
where id in ('P2PIT220822RkgmiIgHToaeuVzv71Hqcg==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:24082022:S24273365'
where id in ('P2PIT220822mLmSjU3LSguqW1HmWiyU2g==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:24082022:S24273206'
where id in ('P2PIT220822MCoHB7YfT8OfTf7ZeFQDBg==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:24082022:S24273283'
where id in ('P2PIT220822zYSslwVESPi/k7u4UNznUQ==');

UPDATE p2p_investment_transactions
SET status         = 'SUCCESS',
	sub_status     = 'SUCCESS_WITHDRAWAL_COMPLETE',
	payment_ref_id = '2:24082022:S24270256'
where id in ('P2PIT220822cqOvr5L9RRmqrBNDQF/fjQ==');

UPDATE p2p_investment_transactions
SET details = jsonb_set(
	details::jsonb,
	array['timeline'],
	(details->'timeline')::jsonb || json_build_object('status','SUCCESS','subStatus','SUCCESS_WITHDRAWAL_COMPLETE','createdAt','2022-08-31T18:45:10.513719990Z')::jsonb)
where id in (
    'P2PIT220826Fv0s/rfGQOynQWJId1+N6A==',
    'P2PIT220826ggkojyY7SbeFRgXdNGUQBg==',
    'P2PIT220826p/+h0voNREqUzaRBO1MWHw==',
    'P2PIT2208263PF+ANjFToiJWni4rZURuw==',
    'P2PIT220826r8exUlalR1KczPYwcwpRLg==',
    'P2PIT2208267N5dk2vOT3evy7U0OB/guA==',
    'P2PIT220826bgAUqYkuRf+nmCrtzXwzng==',
    'P2PIT220826BV8LBa7ZSN+g9QiN3rcxhQ==',
    'P2PIT220826ntp6QTutTiq4dOyDUhimPA==',
    'P2PIT220826M5dastJvR0W9ahA/eq4UtQ=='
);

UPDATE p2p_investment_transactions
SET details = jsonb_set(
	details::jsonb,
	array['timeline'],
	(details->'timeline')::jsonb || json_build_object('status','SUCCESS','subStatus','SUCCESS_WITHDRAWAL_COMPLETE','createdAt','2022-08-29T18:45:10.513719990Z')::jsonb)
where id in (
	'P2PIT2208246KQIVa1dTA6sEu1yb6dZuw==',
    'P2PIT220824atscku9WSjukwugBZcxvjQ==',
    'P2PIT2208242IseBCfHQbeksuuHdeA9/w==',
    'P2PIT220824DMfJAjRTQWCGcPaV1c9oXw==',
    'P2PIT220824PG5nSHV5SoevP4rvRmGHCg==',
    'P2PIT2208236CNIB5M/QtyBS3CbWdY67g==',
    'P2PIT220823FYatuf7TRZuCLR7CgDVkig==',
    'P2PIT220822RkgmiIgHToaeuVzv71Hqcg==',
    'P2PIT220822mLmSjU3LSguqW1HmWiyU2g==',
    'P2PIT220822MCoHB7YfT8OfTf7ZeFQDBg==',
);

UPDATE p2p_investment_transactions
SET details = jsonb_set(
	details::jsonb,
	array['timeline'],
	(details->'timeline')::jsonb || json_build_object('status','SUCCESS','subStatus','SUCCESS_WITHDRAWAL_COMPLETE','createdAt','2022-08-29T18:45:10.513719990Z')::jsonb)
where id in (
	'P2PIT220822cqOvr5L9RRmqrBNDQF/fjQ=='
	);

UPDATE orders
SET status = 'PAID'
where id in (
    'OD220826DyuYl4YVR2ernG3LocrhKA==',
    'OD220826J+VEJG3MQ8OObG7tgk0y3Q==',
    'OD220826WKDVYwmVSdq+R+vCxWe6TA==',
    'OD220826OusoHL2NTsyNZLjU4Y+X3g==',
    'OD220826LZb0swXKSsydUlWk0+2KDQ=='
);
UPDATE orders
SET status = 'PAID'
where id in (
	'OD220826rBL6HZptSmeQzPUnEHO92A==',
    'OD2208265WqtFTw/TRu65HPa6KHBlg==',
    'OD220826lK/N+sQOQ020qBcNa7eIbA==',
    'OD220826jd/3ZLdXTd+m+iiIR03QKg==',
    'OD220826PD4azIgfR0Kc1fWw125E/g=='
);
UPDATE orders
SET status = 'PAID'
where id in (
	'OD22082436eYBIzzTzCutXAS3UzhxA==',
    'OD220824n/e29oCMQyqK4o+fxqVhCA==',
    'OD220824+ln/9J54SLKZQWY6c6faWw==',
    'OD220824/zCKXFpTQR64mKeFdlXoIA==',
    'OD2208240qk/HicdQmORTLrGT1OcfA=='
);
UPDATE orders
SET status = 'PAID'
where id in (
	'OD220823Q2d0A+j1RniD9M2UakmbHQ==',
    'OD220823+bWvE2u8QQC7E8HiARU77g==',
    'OD220822SbsCuQJCSpWscpd2GB0AOQ==',
    'OD220822pfNeCakZSYCD3AD5Pw9diA==',
    'OD220822FWUSsGh4TlyrF2sohQs+IQ=='
);
UPDATE orders
SET status = 'PAID'
where id in (
	'OD220822tm7FGxM6QTeZ2KCsVeKDyg==',
    'OD220822eEet6lzpT4iMUYCyJT3tag=='
);
