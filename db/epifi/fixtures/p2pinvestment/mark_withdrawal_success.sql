-- Our vendor had issue for liquid scheme withdrawal due to which investor were not able to take out their money
-- Investor P2PIVR220709REuXl8JIRCGS0fot5JUD0Q== and P2PIVR220704QVnIDGfxS5mbsdW1WNGWIQ== were in need of money so we provided them with money async.
-- since that transfer was not attached to their withdrawal request we were not able to mark those transaction as success.

-- So need to mark withdrawal transaction as success for these two cases

UPDATE p2p_investment_transactions
set status = 'SUCCESS', sub_status = 'SUCCESS_WITHDRAWAL_COMPLETE', details = jsonb_set(details, '{timeline,1,status}', '"SUCCESS"')
where id in ('P2PIT221015gFcH0/+QSK6kQKAXxa82Cw==', 'P2PIT221017Q0VtcdC5QB+SM3inMWfLEg==');

UPDATE p2p_investment_transactions
set details = jsonb_set(details, '{timeline,1,subStatus}', '"SUCCESS_WITHDRAWAL_COMPLETE"')
where id in ('P2PIT221015gFcH0/+QSK6kQKAXxa82Cw==', 'P2PIT221017Q0VtcdC5QB+SM3inMWfLEg==');
