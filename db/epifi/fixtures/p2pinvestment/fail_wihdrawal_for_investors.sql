-- we are getting vendor side error during withdrawal of money from liquid scheme
-- marking these cases as FAILED so that user can initiate the new one.


UPDATE p2p_investment_transactions
SET status = 'FAILED', sub_status = 'FAILED_WITHDRAWAL' where id in (
'P2PIT221018ROhGFVKqRh67ya1dQe8z0A==',
'P2PIT221018BuQIWZsuSzK+T8wBMQ7DUQ==',
'P2PIT221016ernWoYymSuuGly+A/S6EQQ==',
'P2PIT221016LOfxzACGSTGl02G7ut6Ajw==',
'P2PIT221016DjBAX3ETTie6h0zbr+P3Sw==',
'P2PIT2210160rudhq8TTyGcKvsfAmdfjg==',
'P2PIT221015B2+zMON3Rvied6diGn8Fsw==',
'P2PIT221015Wdki+5DYSHaU0OETCz+lrw==',
'P2PIT221015YA1QRjQmSz6m2nYhxZP2yw==',
'P2PIT221015XFCd/Q88T6CUgYgOeT0cmw==',
'P2PIT221017+e/U+PHtRXuDgf40SWiFQA=='
);
