-- there was series of issue at vendor side due to which some withdrawal request was not went through even with the retry strategy
-- marking those withdrawal request as failed so that user can place a fresh one

UPDATE p2p_investment_transactions
SET status = 'FAILED',
    sub_status = 'FAILED_WITHDRAWAL',
	details = jsonb_set(
		details::jsonb,
		array['timeline'],
		(details->'timeline')::jsonb || json_build_object('status','FAILED','subStatus','FAILED_WITHDRAWAL','createdAt','2022-09-05T20:45:42.342945928Z')::jsonb)
where id in (
'P2PIT220905oFQQFyo0QCykCi3z7hrKGQ=='
);
