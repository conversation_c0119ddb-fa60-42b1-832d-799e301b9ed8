-- investor with 'P2PIVR220817+XEWBTtDRAi+EhUnKKomrg==' and 'P2PIVR221001d6D/0L4wSoCIxpTYKzWtlg==' got the refund
-- need to mark those transaction as failed and release the slot for investor
-- in the getdashboard backend RPC we check if any transaction is in stuck state(order in created state and order expiry date has crossed and investment transaction in unknown state), if it stuck we mark transaction failed and free the slot
-- so as soon as investor opens the dashboard he will get the updated values


UPDATE p2p_investment_transactions
SET status = 'UNKNOWN'
where id in ('P2PIT221001RT0KYuhaSiy/oCCaxHSpkw==', 'P2PIT221001yOCVOQJ4Sm2RzeP3fHY58g==');

UPDATE orders
SET status = 'CREATED'
where id in ('OD221001bvVKCbH0QR+AwWkL7RPrLw==', 'OD221001JqHyIoD2QO2qw5DB39roKA==');
