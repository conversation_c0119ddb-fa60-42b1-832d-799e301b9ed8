-- This fixture adds an actor and a payment instrument for IDFC vendor to be used by pre-approved loans

-- Adding actor for IDFC Personal Loans
UPSERT
INTO actors (id, type, entity_id, name)
VALUES ('actor-idfc-pl', 'EXTERNAL_USER', null, 'IDFC');

-- Adding PI for IDFC Personal Loans - Collection
-- used for loan pre-payments and fore-closures
UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, deleted_at, capabilities, issuer_classification, ownership)
VALUES ('paymentinstrument-idfc-pl', 'BANK_ACCOUNT', 'FI- MONEY COLLECTION',
		'{"account": {"account_type": "CURRENT", "actual_account_number": "***********", "ifsc_code": "IDFB0040101", "secure_account_number": "xxxxxxx2172", "name":"FI- MONEY COLLECTION"}, "card": null, "upi": null}',
		'VERIFIED',
		NULL, '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 'EXTERNAL', 'EPIFI_TECH');

-- Adding PI for IDFC Personal Loans - Disbursal
-- used for loan cancellations
UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, deleted_at, capabilities, issuer_classification, ownership)
VALUES ('paymentinstrument-idfc-pl-disbursal', 'BANK_ACCOUNT', 'FI- MONEY DISBURSAL ACCOUNT',
		'{"account": {"account_type": "CURRENT", "actual_account_number": "***********", "ifsc_code": "IDFB0040101", "secure_account_number": "xxxxxxx2178", "name":"FI- MONEY DISBURSAL ACCOUNT"}, "card": null, "upi": null}',
		'VERIFIED',
		NULL, '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 'EXTERNAL', 'EPIFI_TECH');
