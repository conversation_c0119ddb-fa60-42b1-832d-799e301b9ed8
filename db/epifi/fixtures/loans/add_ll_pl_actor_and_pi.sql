-- This fixture adds an actor and a payment instrument for liquiloans vendor to be used for personal loans

-- Adding actor for Liquiloans vendor
UPSERT
INTO actors (id, type, entity_id, name)
VALUES ('actor-pl-liquiloans', 'EXTERNAL_USER', null, 'Liquiloans');

-- Adding pi for liquiloans  personal loan

UPSERT
INTO payment_instruments (id, type, verified_name, identifier, state, deleted_at, capabilities, issuer_classification, ownership)
VALUES ('PILEcFKdcmQFWa8BfipSET/Q230915==', 'BANK_ACCOUNT', 'NDX P2P PRIVATE LIMITED',
		'{"account": {"account_type": "CURRENT", "actual_account_number": "NLIQUI70028", "ifsc_code": "ICIC0000106", "secure_account_number": "xxxxxxx0028", "name":"NDX P2P PRIVATE LIMITED BORROWER REPAYMENT ESCROW ACCOUNT"}, "card": null, "upi": null}',
		'VERIFIED',
		NULL, '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 'EXTERNAL', 'EPIFI_TECH');
