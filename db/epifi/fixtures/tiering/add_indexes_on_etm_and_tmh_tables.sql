--- eligible_tier_movements
CREATE INDEX IF NOT EXISTS actor_id_status_created_at_idx ON eligible_tier_movements(actor_id asc , status asc, created_at desc);
CREATE INDEX IF NOT EXISTS actor_id_movement_type_created_at_idx ON eligible_tier_movements(actor_id asc , movement_type asc, created_at desc);

--- tier_movement_histories
CREATE INDEX IF NOT EXISTS actor_id_from_tier_movement_type_created_at_idx ON tier_movement_histories(actor_id asc , from_tier asc, movement_type asc, created_at desc);
CREATE INDEX IF NOT EXISTS actor_id_movement_type_created_at_idx ON tier_movement_histories(actor_id asc , movement_type asc, created_at desc);
CREATE INDEX IF NOT EXISTS actor_id_to_tier_created_at_idx ON tier_movement_histories(actor_id asc , to_tier asc, created_at desc);
