-- PostgreSQL allows you store and compare UUID values but it does not include functions for generating the UUID values in its core.
-- Instead, it relies on the third-party modules that provide specific algorithms to generate UUIDs. For example the uuid-ossp module provides
-- some handy functions that implement standard algorithms for generating UUIDs.
-- To install the uuid-ossp module, you use the CREATE EXTENSION statement as follows:
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS change_feeds
(
	id             UUID  NOT NULL           DEFAULT uuid_generate_v4(),
	row_identifier TEXT  NOT NULL,
	updated_column TEXT  NOT NULL,
	table_name     TEXT  NOT NULL,
	change_log     JSONB NOT NULL,
	created_at     TIMESTAMP WITH TIME ZONE DEFAULT now(),
	updated_at     TIMESTAMP WITH TIME ZONE DEFAULT now(),
	PRIMARY KEY (table_name, row_identifier, updated_column, created_at, id)
);
CREATE UNIQUE INDEX IF NOT EXISTS change_feeds_id_key ON change_feeds (id);
CREATE INDEX IF NOT EXISTS row_id_by_table_name_idx ON change_feeds (row_identifier, table_name);
CREATE INDEX IF NOT EXISTS change_feeds_updated_at_idx ON change_feeds (updated_at);
COMMENT ON TABLE change_feeds IS 'A table to store the changefeed for tracked columns of any table';
COMMENT ON COLUMN change_feeds.row_identifier IS 'the uuid of the row being tracked';
COMMENT ON COLUMN change_feeds.table_name IS 'the parent table of the row being tracked';
COMMENT ON COLUMN change_feeds.change_log IS 'type:"type Change struct {
	Type string      `json:"type"`
	Path []string    `json:"path"`
	From interface{} `json:"from"`
	To   interface{} `json:"to"`
}", description: "the changelog object of the row being tracked"';
