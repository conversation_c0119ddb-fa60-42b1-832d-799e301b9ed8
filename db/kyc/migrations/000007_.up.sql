-- PostgreSQL allows you store and compare UUID values but it does not include functions for generating the UUID values in its core.
-- Instead, it relies on the third-party modules that provide specific algorithms to generate UUIDs. For example the uuid-ossp module provides
-- some handy functions that implement standard algorithms for generating UUIDs.
-- To install the uuid-ossp module, you use the CREATE EXTENSION statement as follows:
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS extracted_documents
(
	id                        UUID  NOT NULL DEFAULT uuid_generate_v4(),
	client_request_id 		  TEXT NOT NULL,
	actor_id                  TEXT NOT NULL,
	flow                      TEXT,
	doc_type 				  TEXT,
	vendor 					  TEXT,
	raw_response 			  TEXT,
	uploaded_doc_storage_info JSONB,
	extracted_data			  JSONB,
	failure_reason 			  TEXT,
	failure_reason_raw		  TEXT,
	status 					  TEXT,
	upload_status			  TEXT,
	created_at                TIMESTAMP WITH TIME ZONE DEFAULT now(),
	updated_at                TIMESTAMP WITH TIME ZONE DEFAULT now(),
	PRIMARY KEY (id)
);
CREATE INDEX IF NOT EXISTS extracted_docs_updated_at_idx ON extracted_documents (updated_at);
CREATE UNIQUE INDEX IF NOT EXISTS extracted_docs_client_req_id_uniq_idx ON extracted_documents (client_request_id);
COMMENT ON TABLE extracted_documents IS 'Table to store extracted documents';
