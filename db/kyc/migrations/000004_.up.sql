CREATE TABLE IF NOT EXISTS kyc_data
(
	id              TEXT NOT NULL,
	kyc_attempt_id  TEXT NOT NULL,
	vendor_data     JSONB,
	user_data       JSONB,
	delete_by       TIMESTAMPTZ,
	created_at      TIMESTAMP WITH TIME ZONE DEFAULT now(),
	updated_at      TIMESTAMP WITH TIME ZONE DEFAULT now(),
	deleted_at_unix BIGINT                   DEFAULT 0,
	CONSTRAINT kyc_data_primary_key PRIMARY KEY (id)
);
CREATE UNIQUE INDEX IF NOT EXISTS kyc_data_attempt_id on kyc_data (kyc_attempt_id);
CREATE INDEX IF NOT EXISTS kyc_data_updated_at_idx on kyc_data (updated_at);
CREATE INDEX IF NOT EXISTS kyc_data_deleted_at_unix_delete_by on kyc_data (delete_by, deleted_at_unix);
CREATE INDEX IF NOT EXISTS kyc_data_delete_by_idx on kyc_data (delete_by);
