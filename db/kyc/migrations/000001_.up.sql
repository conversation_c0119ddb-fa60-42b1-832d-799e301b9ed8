CREATE TABLE IF NOT EXISTS kyc_agents
(
	id              TEXT   NOT NULL,
	external_id     TEXT   NOT NULL,
	roles           TEXT[] NOT NULL,
	vendor          TEXT   NOT NULL,
	state_info      JSONB  NOT NULL,
	name            J<PERSON>NB  NOT NULL,
	phone_number    TEXT   NOT NULL,
	email_id        TEXT   NOT NULL,
	actor_id        TEXT   NULL,
	created_at      TIMESTAMP WITH TIME ZONE DEFAULT now(),
	updated_at      TIMESTAMP WITH TIME ZONE DEFAULT now(),
	deleted_at_unix BIGINT                   DEFAULT 0,
	PRIMARY KEY (id)
);

CREATE INDEX kyc_agents_updated_at_idx ON kyc_agents (updated_at);
CREATE UNIQUE INDEX kyc_agents_external_id ON kyc_agents (external_id);
CREATE UNIQUE INDEX kyc_agents_phone_number ON kyc_agents (phone_number);
CREATE UNIQUE INDEX kyc_agents_email_id ON kyc_agents (email_id);
CREATE UNIQUE INDEX kyc_agents_actor_id ON kyc_agents (actor_id);
