CREATE TABLE IF NOT EXISTS scanned_pan_attempts
(
	id                        TEXT NOT NULL,
	actor_id                  TEXT NOT NULL,
	pan                       TEXT,
	client_request_id         TEXT NOT NULL,
	pan_name                  TEXT,
	guardian_info             JSONB,
	pan_dob                   DATE,
	user_pan_image_suffix_url TEXT,
	date_of_issue             DATE,
	pan_sign_image_suffix_url TEXT,
	pan_image_suffix_url      TEXT,
	data_verification_details JSONB,
	created_at                TIMESTAMP WITH TIME ZONE DEFAULT now(),
	updated_at                TIMESTAMP WITH TIME ZONE DEFAULT now(),
	deleted_at_unix           BIGINT                   DEFAULT 0,
	PRIMARY KEY (actor_id, client_request_id)
);
CREATE INDEX updated_at_idx ON scanned_pan_attempts (updated_at);
CREATE UNIQUE INDEX client_req_id_uniq_idx ON scanned_pan_attempts (client_request_id);
CREATE UNIQUE INDEX id_uniq_idx ON scanned_pan_attempts (id);

