CREATE TABLE IF NOT EXISTS kyc_attempts
(
	id                   TEXT NOT NULL,
	actor_id             TEXT NOT NULL,
	client_request_id    TEXT NOT NULL,
	vendor               TEXT NOT NULL,
	flow                 TEXT NOT NULL,
	processor            TEXT NOT NULL,
	kyc_type             TEXT NOT NULL,
	status               TEXT,
	sub_status           TEXT,
	failure_reason       TEXT,
	pii_data             JSONB,
	non_pii_data         JSONB,
	redirection_deeplink JSONB,
	completed_at         TIMESTAMPTZ,
	created_at           TIMESTAMP WITH TIME ZONE DEFAULT now(),
	updated_at           TIMESTAMP WITH TIME ZONE DEFAULT now(),
	CONSTRAINT kyc_attempts_primary_key PRIMARY KEY (actor_id, id)
);
CREATE UNIQUE INDEX IF NOT EXISTS kyc_attempts_id on kyc_attempts (id);
CREATE UNIQUE INDEX IF NOT EXISTS kyc_attempts_client_request_id_idx on kyc_attempts (client_request_id);
CREATE INDEX IF NOT EXISTS kyc_attempts_actor_id_vendor_type_idx on kyc_attempts (actor_id, kyc_type, vendor);
CREATE INDEX IF NOT EXISTS kyc_attempts_kyc_type on kyc_attempts (kyc_type);
CREATE INDEX IF NOT EXISTS kyc_attempts_updated_at_idx on kyc_attempts (updated_at);
