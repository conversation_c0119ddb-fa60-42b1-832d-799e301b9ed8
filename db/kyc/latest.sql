CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.change_feeds (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    row_identifier text NOT NULL,
    updated_column text NOT NULL,
    table_name text NOT NULL,
    change_log jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now()
);
COMMENT ON TABLE public.change_feeds IS 'A table to store the changefeed for tracked columns of any table';
COMMENT ON COLUMN public.change_feeds.row_identifier IS 'the uuid of the row being tracked';
COMMENT ON COLUMN public.change_feeds.table_name IS 'the parent table of the row being tracked';
COMMENT ON COLUMN public.change_feeds.change_log IS 'type:"type Change struct {
	Type string      `json:"type"`
	Path []string    `json:"path"`
	From interface{} `json:"from"`
	To   interface{} `json:"to"`
}", description: "the changelog object of the row being tracked"';
CREATE TABLE public.extracted_documents (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    client_request_id text NOT NULL,
    actor_id text NOT NULL,
    flow text,
    doc_type text,
    vendor text,
    raw_response text,
    uploaded_doc_storage_info jsonb,
    extracted_data jsonb,
    failure_reason text,
    failure_reason_raw text,
    status text,
    upload_status text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
COMMENT ON TABLE public.extracted_documents IS 'Table to store extracted documents';
CREATE TABLE public.kyc_agents (
    id text NOT NULL,
    external_id text NOT NULL,
    roles text[] NOT NULL,
    vendor text NOT NULL,
    state_info jsonb NOT NULL,
    name jsonb NOT NULL,
    phone_number text NOT NULL,
    email_id text NOT NULL,
    actor_id text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    deleted_at_unix bigint DEFAULT 0
);
CREATE TABLE public.kyc_attempts (
    id text NOT NULL,
    actor_id text NOT NULL,
    client_request_id text NOT NULL,
    vendor text NOT NULL,
    flow text NOT NULL,
    processor text NOT NULL,
    kyc_type text NOT NULL,
    status text,
    sub_status text,
    failure_reason text,
    pii_data jsonb,
    non_pii_data jsonb,
    redirection_deeplink jsonb,
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    failure_info jsonb
);
CREATE TABLE public.kyc_data (
    id text NOT NULL,
    kyc_attempt_id text NOT NULL,
    vendor_data jsonb,
    user_data jsonb,
    delete_by timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    deleted_at_unix bigint DEFAULT 0
);
CREATE TABLE public.scanned_pan_attempts (
    id text NOT NULL,
    actor_id text NOT NULL,
    pan text,
    client_request_id text NOT NULL,
    pan_name text,
    guardian_info jsonb,
    pan_dob date,
    user_pan_image_suffix_url text,
    date_of_issue date,
    pan_sign_image_suffix_url text,
    pan_image_suffix_url text,
    data_verification_details jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    deleted_at_unix bigint DEFAULT 0
);
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.change_feeds
    ADD CONSTRAINT change_feeds_pkey PRIMARY KEY (table_name, row_identifier, updated_column, created_at, id);
ALTER TABLE ONLY public.extracted_documents
    ADD CONSTRAINT extracted_documents_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.kyc_agents
    ADD CONSTRAINT kyc_agents_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.kyc_attempts
    ADD CONSTRAINT kyc_attempts_primary_key PRIMARY KEY (actor_id, id);
ALTER TABLE ONLY public.kyc_data
    ADD CONSTRAINT kyc_data_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.scanned_pan_attempts
    ADD CONSTRAINT scanned_pan_attempts_pkey PRIMARY KEY (actor_id, client_request_id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
CREATE UNIQUE INDEX change_feeds_id_key ON public.change_feeds USING btree (id);
CREATE INDEX change_feeds_updated_at_idx ON public.change_feeds USING btree (updated_at);
CREATE UNIQUE INDEX client_req_id_uniq_idx ON public.scanned_pan_attempts USING btree (client_request_id);
CREATE UNIQUE INDEX extracted_docs_client_req_id_uniq_idx ON public.extracted_documents USING btree (client_request_id);
CREATE INDEX extracted_docs_updated_at_idx ON public.extracted_documents USING btree (updated_at);
CREATE UNIQUE INDEX id_uniq_idx ON public.scanned_pan_attempts USING btree (id);
CREATE UNIQUE INDEX kyc_agents_actor_id ON public.kyc_agents USING btree (actor_id);
CREATE UNIQUE INDEX kyc_agents_email_id_deleted_at_unix ON public.kyc_agents USING btree (email_id, deleted_at_unix);
CREATE UNIQUE INDEX kyc_agents_external_id ON public.kyc_agents USING btree (external_id);
CREATE UNIQUE INDEX kyc_agents_phone_number_deleted_at_unix ON public.kyc_agents USING btree (phone_number, deleted_at_unix);
CREATE INDEX kyc_agents_updated_at_idx ON public.kyc_agents USING btree (updated_at);
CREATE INDEX kyc_attempts_actor_id_vendor_type_idx ON public.kyc_attempts USING btree (actor_id, kyc_type, vendor);
CREATE UNIQUE INDEX kyc_attempts_client_request_id_idx ON public.kyc_attempts USING btree (client_request_id);
CREATE UNIQUE INDEX kyc_attempts_id ON public.kyc_attempts USING btree (id);
CREATE INDEX kyc_attempts_kyc_type ON public.kyc_attempts USING btree (kyc_type);
CREATE INDEX kyc_attempts_updated_at_idx ON public.kyc_attempts USING btree (updated_at);
CREATE UNIQUE INDEX kyc_data_attempt_id ON public.kyc_data USING btree (kyc_attempt_id);
CREATE INDEX kyc_data_delete_by_idx ON public.kyc_data USING btree (delete_by);
CREATE INDEX kyc_data_deleted_at_unix_delete_by ON public.kyc_data USING btree (delete_by, deleted_at_unix);
CREATE INDEX kyc_data_updated_at_idx ON public.kyc_data USING btree (updated_at);
CREATE INDEX row_id_by_table_name_idx ON public.change_feeds USING btree (row_identifier, table_name);
CREATE INDEX updated_at_idx ON public.scanned_pan_attempts USING btree (updated_at);
