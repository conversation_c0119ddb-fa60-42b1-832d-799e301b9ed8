CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS actor_pi_resolutions
(
	id         varchar                                   NOT NULL,
	actor_from varchar                                   NOT NULL,
	actor_to   varchar                                   NOT NULL,
	pi_from    varchar,
	pi_to      varchar,
	created_at timestamp with time zone default now() not null,
	updated_at timestamp with time zone default now() not null,
	deleted_at timestamp with time zone default null
);

COMMENT ON TABLE public.actor_pi_resolutions IS '{"comment": "actor_pi_resolutions represents a relation between 2 actors, usually in the form of a payment transfer."}';
COMMENT ON COLUMN public.actor_pi_resolutions.actor_from IS '{"comment": "represents an actor who initiates a payment"}';
COMMENT ON COLUMN public.actor_pi_resolutions.actor_to IS '{"comment": "represents an actor who receives a payment"}';
COMMENT ON COLUMN public.actor_pi_resolutions.pi_from IS '{"comment": "reference to PI table''s primary key, for outbound transfer (actor_from, actor_to, pi_to), it holds no significance, For inbound transfer (actor_from, actor_to, pi_from), it represents the PI from which money has been debited from."}';
COMMENT ON COLUMN public.actor_pi_resolutions.pi_to IS '{"comment": "reference to PI table''s primary key, For outbound transfer (actor_from, actor_to, pi_to), it represents the PI in which money has been credited to, For inbound transfer (actor_from, actor_to, pi_from), it holds no significance."}';

CREATE INDEX IF NOT EXISTS actor_pi_resolutions_updated_at_idx ON actor_pi_resolutions (updated_at desc);

CREATE TABLE IF NOT EXISTS actors
(
	id         varchar                                   NOT NULL,
	type       varchar                                   NOT NULL,
	entity_id  varchar,
	name       varchar,
	created_at timestamp with time zone default now() not null,
	updated_at timestamp with time zone default now() not null,
	deleted_at timestamp with time zone default null,
	ownership  varchar                     DEFAULT 'EPIFI_TECH',
	user_layer INT2 NULL
);

COMMENT ON COLUMN public.actors.type IS '{"proto_type": "types.actor","comment": "represents type of the actor like user/merchant etc."}';
COMMENT ON COLUMN public.actors.entity_id IS '{"comment": "User ID or Merchant ID based on the type of actor"}';
COMMENT ON COLUMN public.actors.ownership IS '{"proto_type":"types.ownership", "comment": "Enum to store actors ownership.", "ref": "api.types.ownership.proto"}';
COMMENT ON COLUMN public.actors.user_layer IS '{"comment": "user_layer column stores number in the range [0..99] using FNV-1 hash algorithm on actor_id. Any change in hashing algorithm or the range should recompute the values of user_layer column for all the actors. This is used for A/B experiment discussed in this doc https://docs.google.com/document/d/1vkLCtDvirU_jiYM0SmGBNu3r6ibEqy7-jSPl29RlJkY"}';

CREATE INDEX IF NOT EXISTS actors_updated_at_idx ON actors (updated_at desc);


CREATE TABLE IF NOT EXISTS blocked_actors_map
(
	id_v2             uuid                     default uuid_generate_v4() NOT NULL,
	actor_id         varchar                                                NOT NULL,
	blocked_actor_id varchar                                                NOT NULL,
	is_spam          bool                                                NOT NULL DEFAULT false,
	created_at       timestamp with time zone default now()              not null,
	updated_at       timestamp with time zone default now()              not null,
	deleted_at       timestamp with time zone default null
);

COMMENT ON COLUMN public.blocked_actors_map.actor_id IS '{"comment": "actor id corresponding to the actor who did the block"}';
COMMENT ON COLUMN public.blocked_actors_map.blocked_actor_id IS '{"comment": "actor id corresponding to the actor who is blocked"}';
COMMENT ON COLUMN public.blocked_actors_map.is_spam IS '{"comment": "signifies whether the blocked actor is also marked as spam or not"}';

CREATE INDEX IF NOT EXISTS blocked_actors_map_updated_at_idx ON blocked_actors_map (updated_at desc);
