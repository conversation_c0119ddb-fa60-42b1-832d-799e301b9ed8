-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- assuming we will have different account in case we start doing p2p international fund transfer

-- Adding actors pool accounts for outward remittance for US stocks
INSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-federal-pool-outward-account', 'EXTERNAL_USER', null, 'US Stocks', 'EPIFI_TECH')
ON CONFLICT (id) DO UPDATE SET type      = excluded.type,
							   entity_id = excluded.entity_id,
							   name      = excluded.name,
							   ownership = excluded.ownership;

-- Adding actors pool accounts for inward remittance for US stocks
INSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-federal-pool-inward-account', 'EXTERNAL_USER', null, 'US Stocks', 'EPIFI_TECH')
ON CONFLICT (id) DO UPDATE SET type      = excluded.type,
							   entity_id = excluded.entity_id,
							   name      = excluded.name,
							   ownership = excluded.ownership;
