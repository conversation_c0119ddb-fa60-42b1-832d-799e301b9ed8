CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.actor_pi_resolutions (
    id character varying NOT NULL,
    actor_from character varying NOT NULL,
    actor_to character varying NOT NULL,
    pi_from character varying,
    pi_to character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.actor_pi_resolutions IS '{"comment": "actor_pi_resolutions represents a relation between 2 actors, usually in the form of a payment transfer."}';
COMMENT ON COLUMN public.actor_pi_resolutions.actor_from IS '{"comment": "represents an actor who initiates a payment"}';
COMMENT ON COLUMN public.actor_pi_resolutions.actor_to IS '{"comment": "represents an actor who receives a payment"}';
COMMENT ON COLUMN public.actor_pi_resolutions.pi_from IS '{"comment": "reference to PI table''s primary key, for outbound transfer (actor_from, actor_to, pi_to), it holds no significance, For inbound transfer (actor_from, actor_to, pi_from), it represents the PI from which money has been debited from."}';
COMMENT ON COLUMN public.actor_pi_resolutions.pi_to IS '{"comment": "reference to PI table''s primary key, For outbound transfer (actor_from, actor_to, pi_to), it represents the PI in which money has been credited to, For inbound transfer (actor_from, actor_to, pi_from), it holds no significance."}';
CREATE TABLE public.actors (
    id character varying NOT NULL,
    type character varying NOT NULL,
    entity_id character varying,
    name character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    ownership character varying DEFAULT 'EPIFI_TECH'::character varying,
    user_layer smallint
);
COMMENT ON COLUMN public.actors.type IS '{"proto_type": "types.actor","comment": "represents type of the actor like user/merchant etc."}';
COMMENT ON COLUMN public.actors.entity_id IS '{"comment": "User ID or Merchant ID based on the type of actor"}';
COMMENT ON COLUMN public.actors.ownership IS '{"proto_type":"types.ownership", "comment": "Enum to store actors ownership.", "ref": "api.types.ownership.proto"}';
COMMENT ON COLUMN public.actors.user_layer IS '{"comment": "user_layer column stores number in the range [0..99] using FNV-1 hash algorithm on actor_id. Any change in hashing algorithm or the range should recompute the values of user_layer column for all the actors. This is used for A/B experiment discussed in this doc https://docs.google.com/document/d/1vkLCtDvirU_jiYM0SmGBNu3r6ibEqy7-jSPl29RlJkY"}';
CREATE TABLE public.blocked_actors_map (
    id_v2 uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    blocked_actor_id character varying NOT NULL,
    is_spam boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON COLUMN public.blocked_actors_map.actor_id IS '{"comment": "actor id corresponding to the actor who did the block"}';
COMMENT ON COLUMN public.blocked_actors_map.blocked_actor_id IS '{"comment": "actor id corresponding to the actor who is blocked"}';
COMMENT ON COLUMN public.blocked_actors_map.is_spam IS '{"comment": "signifies whether the blocked actor is also marked as spam or not"}';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.actor_pi_resolutions
    ADD CONSTRAINT actor_pi_resolutions_id_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.actors
    ADD CONSTRAINT actors_id_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.blocked_actors_map
    ADD CONSTRAINT blocked_actors_map_id_pkey PRIMARY KEY (id_v2);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
CREATE INDEX actor_pi_resolutions_actor_from_actor_to_idx ON public.actor_pi_resolutions USING btree (actor_from, actor_to);
CREATE UNIQUE INDEX actor_pi_resolutions_actor_from_actor_to_pi_from_key ON public.actor_pi_resolutions USING btree (actor_from, actor_to, pi_from);
CREATE UNIQUE INDEX actor_pi_resolutions_actor_from_actor_to_pi_to_key ON public.actor_pi_resolutions USING btree (actor_from, actor_to, pi_to);
CREATE INDEX actor_pi_resolutions_actor_from_pi_to_idx ON public.actor_pi_resolutions USING btree (actor_from, pi_to);
CREATE INDEX actor_pi_resolutions_actor_to_pi_from_idx ON public.actor_pi_resolutions USING btree (actor_to, pi_from);
CREATE INDEX actor_pi_resolutions_updated_at_idx ON public.actor_pi_resolutions USING btree (updated_at DESC);
CREATE UNIQUE INDEX actors_entity_id_key ON public.actors USING btree (entity_id);
CREATE INDEX actors_updated_at_idx ON public.actors USING btree (updated_at DESC);
CREATE INDEX blocked_actors_map_actor_id_blocked_actor_id_idx ON public.blocked_actors_map USING btree (actor_id, blocked_actor_id);
CREATE INDEX blocked_actors_map_updated_at_idx ON public.blocked_actors_map USING btree (updated_at DESC);
ALTER TABLE ONLY public.blocked_actors_map
    ADD CONSTRAINT blocked_actors_map_actor_id_fk FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE ONLY public.blocked_actors_map
    ADD CONSTRAINT blocked_actors_map_blocked_actor_id_fk FOREIGN KEY (blocked_actor_id) REFERENCES public.actors(id);
