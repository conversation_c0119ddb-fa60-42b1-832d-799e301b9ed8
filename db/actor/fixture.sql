-- actor_pi_resolutions
INSERT
INTO actor_pi_resolutions (id, actor_from, actor_to, pi_from, pi_to)
VALUES ('actor-ex-user-2-to-actor-user-1', 'actor-ex-user-2', 'actor-user-1', 'pi-ex-3', null),
       ('actor-user-1-to-actor-user-3', 'actor-user-1', 'actor-user-3', null, 'pi-2'),
       ('actor-user-1-to-actor-ex-user-3', 'actor-user-1', 'actor-ex-user-1', null, 'pi-2'),
       ('actor-user-1-to-actor-ex-user-3-13', 'actor-user-1', 'actor-ex-user-2', null, 'pi-3') ON CONFLICT DO NOTHING;


-- actors
INSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-user-1', 'USER', 'Test-User-1', NULL, 'EPIFI_TECH'),
       ('actor-ex-user-1', 'EXTERNAL_USER', NULL, 'Test-External-User', 'EPIFI_TECH'),
       ('actor-ex-user-2', 'EXTERNAL_USER', NULL, 'Pakalu Papito', 'EPIFI_TECH'),
       ('actor-1', 'USER', 'user-2', null, 'EPIFI_TECH'),
       ('actor-user-3', 'USER', 'user-3', NULL, 'EPIFI_TECH'),
       ('actor-2', 'USER', 'user-4', null, 'EPIFI_TECH'),
       ('actor-3', 'USER', 'user-5', null, 'EPIFI_TECH'),
       ('actor-merchant-1', 'MERCHANT', 'merchant-1', null, 'EPIFI_TECH'),
       ('actor-ex-merchant-1', 'EXTERNAL_MERCHANT', 'merchant-entity-id-1', null, 'EPIFI_TECH'),
       ('actor-fi', 'MERCHANT', 'EPIFI', NULL, 'EPIFI_TECH'),
       ('wl-actor-1', 'WAITLISTED_USER', 'bfc05f22-2722-4057-bd10-aa0351f591bf', NULL, 'EPIFI_TECH'),
       ('actor-4', 'USER', 'user-6', null, 'EPIFI_TECH'),
		('actor-5', 'USER', NULL, 'Gutsy Gibbon', 'EPIFI_TECH'),
       ('actor-aa-1', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('actor-aa-2', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('actor-aa-3', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-0', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-1', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-2', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-3', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-4', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-5', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('secondary-actor-user-6', 'EXTERNAL_USER', NULL, 'Drunk Boyd', 'EPIFI_WEALTH'),
       ('primary-actor-1', 'EXTERNAL_USER', NULL, 'Jake Peralta', 'EPIFI_WEALTH'),
	   ('fraud-actor-1', 'USER', 'fraud-user-1', null, 'EPIFI_TECH'),
	   ('fraud-actor-2', 'USER', 'fraud-user-2', null, 'EPIFI_TECH'),
	   ('ift-actor-1', 'USER', 'ift-user-1', 'ift actor 1', 'EPIFI_TECH'),
	   ('ift-actor-2', 'USER', 'ift-user-2', 'ift actor 2', 'EPIFI_TECH'),
	   ('tt-actor-1', 'USER', 'tt-user-1', 'tt actor 1', 'EPIFI_TECH'),
	   ('ift-international-actor-1', 'EXTERNAL_USER', 'international-actor-1', 'us stocks', 'EPIFI_TECH'),
       ('ift-actor-5', 'USER', 'ift-user-5', 'ift actor 5', 'EPIFI_TECH'),
       ('ift-actor-6', 'USER', 'ift-user-6', 'ift actor 6', 'EPIFI_TECH') ON CONFLICT DO NOTHING;

-- Adding actor for business account
INSERT
INTO public.actors (id, type, entity_id, name) VALUES
('actor-epifi-business-account', 'EXTERNAL_USER', null, 'Fi'),
('ACHKZ9wtGvSu++VNRdu/fRlA230401==', 'EXTERNAL_MERCHANT', null, 'EPIFI_TECH') ON CONFLICT DO NOTHING;

-- blocked_actors_map
INSERT
INTO blocked_actors_map(actor_id, blocked_actor_id, is_spam)
VALUES ('actor-1', 'actor-ex-user-2', true) ON CONFLICT DO NOTHING;

--merchant data for acceptance tests
INSERT
INTO public.actors (id, type, entity_id, name, created_at, updated_at, deleted_at) VALUES
	('AC2103210e4ZU15RTb65qM6cEC3Tiw==', 'EXTERNAL_MERCHANT', '6507c8cd-30ee-464c-b279-af1322a0d46b', NULL, '2021-03-21 08:23:55.140556+00:00', '2021-03-21 08:23:55.140556+00:00', NULL),
	('AC210321HFgdiuCaSmCUisO6dTcUKw==', 'EXTERNAL_MERCHANT', '9fec2cc3-b2d0-4cd2-b874-c03bee442367', NULL, '2021-03-21 08:23:55.318655+00:00', '2021-03-21 08:23:55.318655+00:00', NULL)  ON CONFLICT DO NOTHING;


INSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-icici-amc-business-account', 'EXTERNAL_USER', null, 'ICICI Prudential AMC Ltd', 'EPIFI_TECH') ON CONFLICT DO NOTHING;

-- Adding actors pool accounts for outward remittance for US stocks
INSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-federal-pool-outward-account', 'EXTERNAL_USER', null, 'US Stocks', 'EPIFI_TECH') ON CONFLICT DO NOTHING;

-- Adding actors pool accounts for outward remittance for US stocks
INSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-alpaca', 'EXTERNAL_USER', null, 'US Stocks', 'US_STOCKS_ALPACA') ON CONFLICT DO NOTHING;
