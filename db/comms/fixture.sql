INSERT INTO in_app_targeted_comms_elements(id, utility_type, structure_type, content, start_time, end_time, visibility_state, created_at, updated_at, meta_data, user_tags, segment_expression, screens, screens_meta, app_details) VALUES
('cd572fac-8339-4010-b1e2-38f46499ea6a', 'ELEMENT_UTILITY_TYPE_ALERT', 'ELEMENT_STRUCTURE_TYPE_BANNER', '{"banner": {"title": "t1", "body": "b1", "iconUrl": "i1"}}', '2022-01-27 23:23:33+05:30', '2122-01-29 03:10:13+05:30', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 19:12:33.08057+05:30', '2022-01-26 19:58:05.378308+05:30', '{"treatmentId": "TREATMENT_ID_R1_5_MIN_V1", "campaignName": "CAMPAIGN_NAME_OB_PRE_PAN", "notificationType": "NOTIFICATION_TYPE_PERSIST"}','{"USER_TAG_SAVINGS_ACCOUNT"}', 'IsMember(''segment-1'')', '{"HOME", "PAY_LANDING_SCREEN"}', '{"BODY_V2", "SUB_BODY"}', '{"platformToMinSupportedAppVersionMap": {"ANDROID": "20"}, "platformToMaxSupportedAppVersionMap": {"ANDROID": "100"}}'),
('f7918eab-1a97-4044-8560-52d6ef282d0f', 'ELEMENT_UTILITY_TYPE_ALERT', 'ELEMENT_STRUCTURE_TYPE_BANNER', '{"banner": {"title": "t2", "body": "b2", "iconUrl": "i2"}}', '2022-01-26 20:01:38+05:30', '2122-01-31 08:21:38+05:30', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 19:23:50.154323+05:30', '2022-01-28 12:58:46.310395+05:30', '{"treatmentId": "TREATMENT_ID_R2_72_HOURS_V1", "campaignName": "CAMPAIGN_NAME_OB_ATM_PIN_DROP_OFF", "notificationType": "NOTIFICATION_TYPE_PERSIST"}', '{"USER_TAG_SAVINGS_ACCOUNT"}', null, null, null, '{"platformToMinSupportedAppVersionMap": {"ANDROID": "20"}, "platformToMaxSupportedAppVersionMap": {"ANDROID": "100"}}'),
('f7918eab-1111-4444-8560-52d6ef282d0f', 'ELEMENT_UTILITY_TYPE_ALERT', 'ELEMENT_STRUCTURE_TYPE_BANNER', '{"banner": {"title": "t2", "body": "b2", "iconUrl": "i2"}}', '2022-01-26 20:01:38+05:30', '2122-01-31 08:21:38+05:30', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 19:23:50.154323+05:30', '2022-01-28 12:58:46.310395+05:30', '{"treatmentId": "TREATMENT_ID_R2_72_HOURS_V1", "campaignName": "CAMPAIGN_NAME_OB_ATM_PIN_DROP_OFF", "notificationType": "NOTIFICATION_TYPE_PERSIST"}', '{"USER_TAG_SAVINGS_ACCOUNT"}', null, null, null, null),
('f7918eab-2211-4444-8560-52d6ef282d0f', 'ELEMENT_UTILITY_TYPE_ALERT', 'ELEMENT_STRUCTURE_TYPE_BANNER', '{"banner": {"title": "t2", "body": "b2", "iconUrl": "i2"}}', '2022-01-26 20:01:38+05:30', '2122-01-31 08:21:38+05:30', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 19:23:50.154323+05:30', '2022-01-28 12:58:46.310395+05:30', '{"treatmentId": "TREATMENT_ID_R2_72_HOURS_V1", "campaignName": "CAMPAIGN_NAME_OB_ATM_PIN_DROP_OFF", "notificationType": "NOTIFICATION_TYPE_PERSIST"}', null, null, null, null, null),
('f7918eab-3311-4444-8560-52d6ef282d0f', 'ELEMENT_UTILITY_TYPE_ALERT', 'ELEMENT_STRUCTURE_TYPE_BANNER', '{"banner": {"title": "t2", "body": "b2", "iconUrl": "i2"}}', '2022-01-26 20:01:38+05:30', '2122-01-31 08:21:38+05:30', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 19:23:50.154323+05:30', '2022-01-28 12:58:46.310395+05:30', '{"treatmentId": "TREATMENT_ID_R2_72_HOURS_V1", "campaignName": "CAMPAIGN_NAME_OB_ATM_PIN_DROP_OFF", "notificationType": "NOTIFICATION_TYPE_PERSIST"}', null, null, '{"HOME", "PAY_LANDING_SCREEN"}', null, null),
('f7918eab-4411-4444-8560-52d6ef282d0f', 'ELEMENT_UTILITY_TYPE_ALERT', 'ELEMENT_STRUCTURE_TYPE_BANNER', '{"banner": {"title": "t2", "body": "b2", "iconUrl": "i2"}}', '2021-01-26 20:01:38+05:30', '2021-01-31 08:21:38+05:30', 'VISIBILITY_STATE_ACTIVE', '2021-01-26 19:23:50.154323+05:30', '2021-01-28 12:58:46.310395+05:30', '{"treatmentId": "TREATMENT_ID_R2_72_HOURS_V1", "campaignName": "CAMPAIGN_NAME_OB_ATM_PIN_DROP_OFF", "notificationType": "NOTIFICATION_TYPE_PERSIST"}', null, null, null, null, null),
('f7918eab-5511-4444-8560-52d6ef282d0f', 'ELEMENT_UTILITY_TYPE_MARKETING', 'ELEMENT_STRUCTURE_TYPE_GTM_POP_UP', '{"banner": {"title": "t2", "body": "b2", "iconUrl": "i2"}}', '2021-01-26 20:01:38+05:30', '2021-01-31 08:21:38+05:30', 'VISIBILITY_STATE_DISMISSED', '2021-01-26 19:23:50.154323+05:30', '2021-01-28 12:58:46.310395+05:30', '{"treatmentId": "TREATMENT_ID_R2_72_HOURS_V1", "campaignName": "CAMPAIGN_NAME_OB_ATM_PIN_DROP_OFF", "notificationType": "NOTIFICATION_TYPE_PERSIST"}', null, null, null, null, null);


INSERT INTO in_app_targeted_comms_mappings( id, element_id, mapping_type, mapped_value, mapped_value_meta, mapped_visibility_state, created_at, updated_at) VALUES
('86817af0-2aad-4f33-b3b7-870577e81aaf', 'cd572fac-8339-4010-b1e2-38f46499ea6a', 'MAPPING_TYPE_USER_TAG', 'USER_TAG_GENERIC', '', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:35:15.428764+05:30', '2022-01-26 23:35:15.428764+05:30'),
('191647cd-8f72-43c8-b567-fcc29a567a83', 'cd572fac-8339-4010-b1e2-38f46499ea6a', 'MAPPING_TYPE_SCREEN', 'HELP_MAIN', '', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:35:15.428764+05:30', '2022-01-26 23:35:15.428764+05:30'),
('970cdd96-e36a-4af1-8a1d-ff09e907f1e8', 'f7918eab-1a97-4044-8560-52d6ef282d0f', 'MAPPING_TYPE_USER_TAG', 'USER_TAG_GENERIC', '', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:44:44.878392+05:30', '2022-01-26 23:44:44.878392+05:30'),
('a8c3cc37-ab8e-4f0a-8c45-5b35a1fb57ca', 'f7918eab-1a97-4044-8560-52d6ef282d0f', 'MAPPING_TYPE_SCREEN', 'HELP_MAIN', '', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:44:44.878392+05:30', '2022-01-26 23:44:44.878392+05:30'),
('970cdd96-e36a-4af1-8a1d-ff09e907f1ea', 'f7918eab-1a97-4044-8560-52d6ef282d0f', 'MAPPING_TYPE_USER', 'actorID-1', '', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:44:44.878392+05:30', '2022-01-26 23:44:44.878392+05:30'),
('970cdd96-e36a-4af1-8a1d-ff09e907f1eb', 'f7918eab-1a97-4044-8560-52d6ef282d0f', 'MAPPING_TYPE_SCREEN', 'FAQ_CATEGORY', '12345678_Category Name', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:44:44.878392+05:30', '2022-01-26 23:44:44.878392+05:30'),
('970cdd96-e36a-4af1-8a1d-ff09e907f1ec', 'f7918eab-1a97-4044-8560-52d6ef282d0f', 'MAPPING_TYPE_SEGMENT_ID', 'seg-1', '', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:44:44.878392+05:30', '2022-01-26 23:44:44.878392+05:30'),
('970cdd96-e36a-4af1-8a1d-ff09e907f2ea', 'f7918eab-1111-4444-8560-52d6ef282d0f', 'MAPPING_TYPE_USER', 'actorID-2', '', 'VISIBILITY_STATE_DISMISSED', '2022-01-26 23:44:44.878392+05:30', '2022-01-26 23:44:44.878392+05:30'),
('970cdd96-e36a-4af1-8a1d-ff09e907f2eb', 'f7918eab-1111-4444-8560-52d6ef282d0f', 'MAPPING_TYPE_SCREEN', 'FAQ_CATEGORY', '87654321_Category Name', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:44:44.878392+05:30', '2022-01-26 23:44:44.878392+05:30'),
('970cdd96-e36a-4af1-8a1d-ff09e907f2ec', 'f7918eab-1a97-4044-8560-52d6ef282d0f', 'MAPPING_TYPE_SEGMENT_EXPRESSION', 'IsMember(''segment-1'') && !IsMember(''segment-2'')', '', 'VISIBILITY_STATE_ACTIVE', '2022-01-26 23:44:44.878392+05:30', '2022-01-26 23:44:44.878392+05:30');

INSERT INTO element_to_app_details_mappings(id, element_id, app_platform, min_app_version, max_app_version) VALUES
('8a945183-22a9-4ace-ab08-c6b8342bf605', 'f7918eab-1a97-4044-8560-52d6ef282d0f', 'ANDROID', 140, 150),
('8a945183-22a9-4ace-ab08-c6b8342bf606', 'cd572fac-8339-4010-b1e2-38f46499ea6a', 'IOS', 97, 2147483647),
('8a945183-22a9-4ace-ab08-c6b8342bf607', 'cd572fac-8339-4010-b1e2-38f46499ea6a', 'ANDROID', 0 , 2147483647),
('8a945183-22a9-4ace-ab08-c6b8342bf608', 'f7918eab-1111-4444-8560-52d6ef282d0f', 'ANDROID', 140, 150);

INSERT INTO comms_retry_logs(comms_msg_id, vendor, vendor_msg_id, status) VALUES
('cms1', 'ACL', 'vm1', 'FAILED'),
('cms1', 'KALEYRA', 'vm2', 'SENT_TO_VENDOR');

INSERT INTO user_comms_preferences(id, actor_id, medium, category, preference, deleted_at_unix) VALUES
('013f765e-1de8-4754-aa01-9ef9a26c99e2', 'actor-user-1', 'WHATSAPP', 'CATEGORY_PROMOTIONAL', 'ON', '0'),
('0fd3583f-018b-439a-8fa4-6586e538b55e', 'actor-user-1', 'WHATSAPP', 'CATEGORY_PROMOTIONAL', 'ON', '1617889365693050000');

INSERT INTO comms_messages(id, actor_id, medium, vendor_msg_id, vendor, status, qos, user_identifier_type, user_identifier_value) VALUES
('013f765e-1de8-4754-aa01-9ef9a26c99d4', 'test-actor-id-1', 'SMS', 'test-vendor-id-1', 'EXOTEL', 'DELIVERED', 'BEST_EFFORT', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99d2', 'test-actor-id-1', 'SMS', 'test-vendor-id-2', 'EXOTEL', 'DELIVERED', 'BEST_EFFORT', 'EMAIL_ID', '<EMAIL>'),
('013f765e-1de8-4754-aa01-9ef9a26c99d3', 'test-actor-id-1', 'EMAIL', 'test-vendor-id-3', 'SEND_GRID', 'DELIVERED', 'BEST_EFFORT', 'USER_ID', 'test-user-id-1'),
('013f765e-1de8-4754-aa01-9ef9a26c99d1', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99d5', 'test-actor-id-1', 'SMS', 'test-vendor-id-5', 'EXOTEL', 'DELIVERED', 'GUARANTEED', 'EMAIL_ID', '<EMAIL>'),
('013f765e-1de8-4754-aa01-9ef9a26c99d6', 'test-actor-id-1', 'EMAIL', 'test-vendor-id-6', 'AWS_SES', 'DELIVERED', 'GUARANTEED', 'USER_ID', 'test-user-id-1'),
('013f765e-1de8-4754-aa01-9ef9a26c99d7', 'test-actor-id-1', 'SMS', 'test-vendor-id-7', 'EXOTEL', 'DELIVERED', 'BEST_EFFORT', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99d8', 'test-actor-id-1', 'EMAIL', 'test-vendor-id-8', 'SEND_GRID', 'DELIVERED', 'BEST_EFFORT', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e1', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e2', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e3', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e4', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e5', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e6', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e7', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e8', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99e9', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189'),
('013f765e-1de8-4754-aa01-9ef9a26c99f1', 'test-actor-id-1', 'NOTIFICATION', 'test-vendor-id-4', 'FCM', 'DELIVERED', 'GUARANTEED', 'PHONE_NUMBER', '9971488189');

INSERT INTO whatsapp_message_histories (id, response_id, status, status_timestamp) VALUES
('013f765e-1de8-4754-aa01-9ef9a26c99e8', 'whatsapp-test-response-id-2', 'WHATSAPP_MESSAGE_STATUS_SENT', '2021-05-17 08:23:56.09453+00:00'),
('013f765e-1de8-4754-aa01-9ef9a26c99e9', 'whatsapp-test-response-id-2', 'WHATSAPP_MESSAGE_STATUS_READ', '2021-05-17 08:23:54.09453+00:00'),
('013f765e-1de8-4754-aa01-9ef9a26c99f1', 'whatsapp-test-response-id-2', 'WHATSAPP_MESSAGE_STATUS_DELIVERED', '2021-05-17 08:23:58.09453+00:00');

INSERT INTO sms_callbacks (response_id, status, vendor, medium) VALUES
('response-id-1', 'DELIVERED', 'ACL', 'SMS'),
('response-id-2', 'FAILED', 'ACL',null),
('response-id-3', 'UNKNOWN', 'ACL', null),
('a5004f18-5d52-4991-82a9-2a1e3010e990', 'actor-user-1', '123456', 'SMS');

INSERT INTO notifications_user_level_actions (id, actor_id, action, action_timestamp) VALUES
('d5004f18-5d52-4991-82a9-2a1e3010e991', 'actor-2', 'NOTIFICATION_ACTION_VIEW_ALL', '2022-04-06 00:00:00.000000 +05:30'),
('d5004f18-5d52-4991-82a9-2a1e3010e992', 'actor-2', 'NOTIFICATION_ACTION_DISMISS_ALL', '2022-04-08 08:45:05.173080 +05:30'),
('d5004f18-5d52-4991-82a9-2a1e3010e993', 'actor-3', 'NOTIFICATION_ACTION_DISMISS_ALL', '2022-04-10 08:45:05.173080 +05:30');

INSERT INTO email_callbacks(id, message_id, event_type, event_timestamp, vendor) VALUES
('014f765e-1de8-4754-aa01-9ef9a26c99a1', '1234567', 'OPEN', '2021-05-17 08:25:56.09453+00:00', 'AWS_SES'),
('014f765e-1de8-4754-aa01-9ef9a26c99a2', '1234567', 'CLICK', '2021-05-17 08:24:56.09453+00:00', 'AWS_SES'),
('014f765e-1de8-4754-aa01-9ef9a26c99a3', '1234567', 'OPEN', '2021-05-17 08:23:56.09453+00:00', 'AWS_SES'),
('014f765e-1de8-4754-aa01-9ef9a26c99a4', '1234567', 'DELIVERY', '2021-05-17 08:21:56.09453+00:00', 'AWS_SES'),
('014f765e-1de8-4754-aa01-9ef9a26c99a5', '1234567', 'SEND', '2021-05-17 08:19:56.09453+00:00', 'AWS_SES'),
('014f765e-1de8-4754-aa01-9ef9a26c99a6', '1234568', 'BOUNCE', '2021-05-17 08:23:56.09453+00:00', 'SEND_GRID'),
('014f765e-1de8-4754-aa01-9ef9a26c99a7', '1234569', 'FAILURE', '2021-05-17 08:23:56.09453+00:00', 'SEND_GRID'),
('014f765e-1de8-4754-aa01-9ef9a26c99a8', '1234560', 'COMPLAINT', '2021-05-17 08:23:56.09453+00:00', 'SEND_GRID');

INSERT INTO whatsapp_user_replies(id, response_id, response_info) VALUES
('013f765e-1de8-4754-aa01-9ef9a26c99e1', 'test-resp-id-1', '{"from_number":"**********","body":"test"}'),
('013f765e-1de8-4754-aa01-9ef9a26c99e2', 'test-resp-id-2', '{"from_number":"**********","body":"test new"}');

INSERT INTO whatsapp_callbacks(response_id, status, vendor, dlr_received_time) VALUES
('test-resp-id-1', 'SENT_TO_VENDOR', 'ACL', '2021-03-21 08:23:55.09453+00:00'),
('test-resp-id-2', 'SENT_TO_VENDOR', 'ACL', '2021-03-21 08:23:55.09453+00:00');

INSERT INTO email_templates(id, email_type, template_version, subject, template) VALUES
('014f765e-1de8-4754-aa01-9ef9a26c99a1', 'ACCOUNT_STATEMENT_EMAIL', 'VERSION_V1', 'test subject 1', 'test template 1'),
('014f765e-1de8-4754-aa01-9ef9a26c99a2', 'ACCOUNT_STATEMENT_EMAIL', 'VERSION_V2', 'test subject 2', 'test template 2'),
('014f765e-1de8-4754-aa01-9ef9a26c99a3', 'WAITLIST_USER_ACCEPTED', 'VERSION_V1', 'test subject 3', 'test template 3'),
('014f765e-1de8-4754-aa01-9ef9a26c99a4', 'WAITLIST_USER_ACCEPTED', 'VERSION_V2', 'test subject 4', 'test template 4'),
('014f765e-1de8-4754-aa01-9ef9a26c99a5', 'WAITLIST_USER_ACCEPTED', 'VERSION_V3', 'test subject 5', 'test template 5'),
('014f765e-1de8-4754-aa01-9ef9a26c99a6', 'FULL_KYC_USER_WELCOME_EMAIL', 'VERSION_V1', 'test subject', 'test template'),
('014f765e-1de8-4754-aa01-9ef9a26c99a7', 'WAITLIST_CBO_USER_FINITE_CODE', 'VERSION_V3', 'test subject', 'test template'),
('014f765e-1de8-4754-aa01-9ef9a26c99a8', 'SHERLOCK_VERIFICATION_EMAIL', 'VERSION_V1', 'test subject', 'test template'),
('04cb0137-3273-4cb2-99ea-41675e26dc1b', 'WORK_EMAIL_VERIFICATION', 'VERSION_V2', 'test subject', 'test template');

INSERT INTO comms_notifications(message_id, actor_id, type, data, status, expire_at, created_at, priority) VALUES
('013f765e-1de8-4754-aa01-9ef9a26c99e1', 'actor-user-1', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'ACTIVE', '2024-04-20 08:23:55.09453+00:00', '2022-04-20 08:23:55.09453+00:00', 'NOTIFICATION_PRIORITY_CRITICAL'),
('013f765e-1de8-4754-aa01-9ef9a26c99e2', 'actor-user-1', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'ACTIVE', '2024-04-20 08:23:55.09453+00:00', '2022-04-20 08:23:55.09453+00:00', 'NOTIFICATION_PRIORITY_HIGH'),
('013f765e-1de8-4754-aa01-9ef9a26c99e3', 'actor-user-1', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'ACTIVE', '2024-04-20 08:23:55.09453+00:00', '2022-04-20 08:25:55.09453+00:00', 'NOTIFICATION_PRIORITY_MEDIUM'),
('013f765e-1de8-4754-aa01-9ef9a26c99e4', 'actor-user-1', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'ACTIVE', '2024-04-20 08:23:55.09453+00:00', '2022-04-20 08:24:55.09453+00:00', 'NOTIFICATION_PRIORITY_MEDIUM'),
('013f765e-1de8-4754-aa01-9ef9a26c99e5', 'actor-user-1', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'ACTIVE', '2024-04-20 08:23:55.09453+00:00', '2022-04-20 08:23:55.09453+00:00', 'NOTIFICATION_PRIORITY_UNSPECIFIED'),
('013f765e-1de8-4754-aa01-9ef9a26c99e6', 'actor-user-1', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'ACTIVE', '2024-04-20 08:23:55.09453+00:00', '2022-04-20 08:23:55.09453+00:00', 'NOTIFICATION_PRIORITY_LOW'),
('013f765e-1de8-4754-aa01-9ef9a26c99e7', 'actor-2', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'ACTIVE', '2024-04-20 08:23:55.09453+00:00', '2022-04-20 08:23:55.09453+00:00', 'NOTIFICATION_PRIORITY_UNSPECIFIED'),
('013f765e-1de8-4754-aa01-9ef9a26c99e8', 'actor-2', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'ACTIVE', '2024-04-20 08:23:55.09453+00:00', '2022-04-20 08:23:55.09453+00:00', 'NOTIFICATION_PRIORITY_UNSPECIFIED'),
('013f765e-1de8-4754-aa01-9ef9a26c99e9', 'actor-user-1', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'INACTIVE', '2021-04-20 08:23:55.09453+00:00', '2022-04-20 08:23:55.09453+00:00', 'NOTIFICATION_PRIORITY_UNSPECIFIED'),
('013f765e-1de8-4754-aa01-9ef9a26c99f1', 'actor-user-1', 'IN_APP', '{"inAppTemplate": {}, "notificationType": "IN_APP"}', 'INACTIVE', '2021-04-20 08:23:55.09453+00:00', '2022-04-20 08:23:55.09453+00:00', 'NOTIFICATION_PRIORITY_UNSPECIFIED');

INSERT INTO in_app_targeted_comms_callbacks(id, element_id, actor_id, created_at, updated_at, deleted_at) VALUES
('113f765e-1de8-4754-aa01-9ef9a26c99e1', 'cd572fac-8339-4010-b1e2-38f46499ea6a', 'actor-id-1', '2022-01-26 19:12:33.08057+05:30', '2022-01-26 19:58:05.378308+05:30', null),
('113f765e-1de8-4754-aa01-9ef9a26c99e2', 'f7918eab-1111-4444-8560-52d6ef282d0f', 'actor-id-1', '2022-01-26 19:23:50.154323+05:30', '2022-01-28 12:58:46.310395+05:30', null),
('113f765e-1de8-4754-aa01-9ef9a26c99e3', 'cd572fac-8339-4010-b1e2-38f46499ea6a', 'actor-id-2', '2022-01-26 19:23:50.154323+05:30', '2022-01-28 12:58:46.310395+05:30', null);
