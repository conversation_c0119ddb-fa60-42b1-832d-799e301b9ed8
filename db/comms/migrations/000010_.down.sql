ALTER TABLE comms_messages DROP CONSTRAINT IF EXISTS comms_messages_primary_key;
ALTER TABLE fcm_device_tokens DROP CONSTRAINT IF EXISTS fcm_device_tokens_primary_key;
ALTER TABLE email_callbacks DROP CONSTRAINT IF <PERSON>XISTS email_callbacks_primary_key;
ALTER TABLE email_templates DROP CONSTRAINT IF EXISTS email_templates_primary_key;
ALTER TABLE comms_notifications DROP CONSTRAINT IF EXISTS comms_notifications_primary_key;
ALTER TABLE sms_callbacks DROP CONSTRAINT IF EXISTS sms_callbacks_primary_key;
ALTER TABLE sms_templates DROP CONSTRAINT IF EXISTS sms_templates_primary_key;
ALTER TABLE user_comms_preferences DROP CONSTRAINT IF EXISTS user_comms_preferences_primary_key;
ALTER TABLE whatsapp_callbacks DROP CONSTRAINT IF EXISTS whatsapp_callbacks_primary_key;
ALTER TABLE whatsapp_message_histories DROP CONSTRAINT IF EXISTS whatsapp_message_histories_primary_key;
ALTER TABLE whatsapp_user_replies DROP CONSTRAINT IF EXISTS whatsapp_user_replies_primary_key;
ALTER TABLE notifications_user_level_actions DROP CONSTRAINT IF EXISTS notifications_user_level_actions_primary_key;
