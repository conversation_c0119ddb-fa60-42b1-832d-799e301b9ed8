CREATE TABLE IF NOT EXISTS comms_messages (
	id UUID NOT NULL DEFAULT public.uuid_generate_v4(),
	client_id VARCHAR NULL,
	sqs_msg_id VARCHAR NULL,
	vendor VARCHAR NULL,
	vendor_msg_id VARCHAR NULL,
	retries INTEGER NULL,
	status VARCHAR NULL,
	medium VARCHAR NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
	actor_id VARCHAR NULL,
	user_identifier_type VARCHAR NULL,
	user_identifier_value VARCHAR NULL,
	qos VARCHAR NULL,
	metadata JSONB NULL,
	external_reference_id VARCHAR NULL
);



CREATE TABLE IF NOT EXISTS fcm_device_tokens (
	id UUID NOT NULL DEFAULT public.uuid_generate_v4(),
	actor_id VARCHAR NULL,
	device_token VARCHAR NULL,
	status VARCHAR NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS email_callbacks (
	id UUID NOT NULL DEFAULT public.uuid_generate_v4(),
	message_id VARCHAR NOT NULL,
	event_type VARCHAR NOT NULL,
	event_timestamp timestamp with time zone NOT NULL,
	vendor VARCHAR NOT NULL,
	event_meta JSONB NULL,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE email_callbacks IS 'table to store email events received in callbacks for various events on email';
COMMENT ON COLUMN email_callbacks.event_type IS '{"proto_type":"comms.EventType", "comment": "enum to store type of event for which callback is received"}';
COMMENT ON COLUMN email_callbacks.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment": "enum to store vendor information"}';
COMMENT ON COLUMN email_callbacks.event_meta IS '{"proto_type":"comms.EventMeta", "comment": "JSONB to store meta data for an event"}';

CREATE TABLE IF NOT EXISTS email_templates (
	id UUID NOT NULL DEFAULT public.uuid_generate_v4(),
	email_type VARCHAR NOT NULL,
	template_version VARCHAR NOT NULL,
	subject VARCHAR NOT NULL,
	template VARCHAR NOT NULL,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE email_templates IS 'table to store email templates to be used by comms service while sending emails';
COMMENT ON COLUMN email_templates.email_type IS '{"proto_type":"comms.EmailType", "comment": "enum to store list of all possible email types that can be sent via comms service"}';
COMMENT ON COLUMN email_templates.template_version IS '{"proto_type":"comms.TemplateVersion", "comment": "enum to store list of possible versions for a template"}';

CREATE TABLE IF NOT EXISTS comms_notifications (
	message_id UUID NOT NULL DEFAULT public.uuid_generate_v4(),
	actor_id VARCHAR NOT NULL,
	type VARCHAR NOT NULL,
	expire_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	data JSONB NOT NULL,
	status VARCHAR NOT NULL,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	priority VARCHAR NULL,
	in_app_section VARCHAR NULL,
	sub_status VARCHAR NULL
);
COMMENT ON TABLE comms_notifications IS 'table to store details of app notifications sent to users';
COMMENT ON COLUMN comms_notifications.type IS '{"proto_type":"comms.NotificationType", "comment": "type of notification"}';
COMMENT ON COLUMN comms_notifications.data IS '{"proto_type":"comms.NotificationData", "comment": "Notification data used for sending message"}';
COMMENT ON COLUMN comms_notifications.status IS '{"proto_type":"comms.NotificationStatus", "comment": "status of notification"}';
COMMENT ON COLUMN comms_notifications.priority IS '{"proto_type":"comms.InAppNotificationPriority"}';
COMMENT ON COLUMN comms_notifications.in_app_section IS '{"proto_type":"comms.InAppNotificationSection"}';
COMMENT ON COLUMN comms_notifications.sub_status IS '{"proto_type":"comms.NotificationSubStatus"}';

CREATE TABLE IF NOT EXISTS sms_callbacks (
	response_id VARCHAR NOT NULL,
	status VARCHAR NOT NULL,
	vendor VARCHAR NOT NULL,
	callback_info JSONB NULL,
	vendor_updated_time timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE TABLE IF NOT EXISTS sms_templates (
	id UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	template_type VARCHAR NOT NULL,
	template VARCHAR NOT NULL,
	dlt_template_id VARCHAR NOT NULL,
	version VARCHAR NOT NULL,
	is_deleted BOOLEAN NOT NULL,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_comms_preferences (
	id UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	actor_id VARCHAR NOT NULL,
	medium VARCHAR NOT NULL,
	category VARCHAR NOT NULL,
	preference VARCHAR NOT NULL,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	deleted_at_unix BIGINT NULL,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE user_comms_preferences IS 'table to store user preferences for receiving comms via various modes for various categories';
COMMENT ON COLUMN user_comms_preferences.medium IS '{"proto_type":"comms.Medium", "comment": "medium of communication"}';
COMMENT ON COLUMN user_comms_preferences.category IS '{"proto_type":"comms.Category", "comment": "category of communication"}';
COMMENT ON COLUMN user_comms_preferences.preference IS '{"proto_type":"comms.user_preference.Preference", "comment": "Preference for receiving communication"}';

CREATE TABLE IF NOT EXISTS whatsapp_callbacks (
	response_id VARCHAR NOT NULL,
	status VARCHAR NOT NULL,
	vendor VARCHAR NOT NULL,
	dlr_received_time timestamp with time zone,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE whatsapp_callbacks IS 'table to store status and other details sent by whatsapp vendors in callback';
COMMENT ON COLUMN whatsapp_callbacks.status IS '{"proto_type":"comms.MessageState", "comment": "state of message"}';
COMMENT ON COLUMN whatsapp_callbacks.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment": "vendor used for sending message"}';

CREATE TABLE IF NOT EXISTS whatsapp_message_histories (
	id UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	response_id VARCHAR NOT NULL,
	status VARCHAR NOT NULL,
	status_timestamp timestamp with time zone NOT NULL,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE whatsapp_message_histories IS 'table to store message history related to whatsapp message sent by us to user to capture times of sent, delivered and read';
COMMENT ON COLUMN whatsapp_message_histories.status IS '{"proto_type":"comms.WhatsappMessageStatus", "comment": "enum to store list of possible status of whatsapp message status after it is sent to vendor : sent, read, delivered"}';

CREATE TABLE IF NOT EXISTS whatsapp_user_replies (
	id UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	response_id VARCHAR NOT NULL,
	response_info JSONB NOT NULL,
	created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE whatsapp_user_replies IS 'table to store user reply details for whatsapp communication';
COMMENT ON COLUMN whatsapp_user_replies.response_info IS '{"proto_type":"comms.WhatsAppUserReplyInfo", "comment": "user reply detailed info"}';

CREATE TABLE IF NOT EXISTS notifications_user_level_actions (
	id UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	actor_id VARCHAR NOT NULL,
	action VARCHAR NOT NULL,
	action_timestamp timestamp with time zone NOT NULL,
	created_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE notifications_user_level_actions IS 'table to keep the latest timestamp of user level notifications events like view_all, dismiss_all etc';
COMMENT ON COLUMN notifications_user_level_actions.action IS '{"proto_type":"comms.NotificationAction"}';
