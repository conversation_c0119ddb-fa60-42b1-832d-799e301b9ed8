CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';

CREATE TABLE IF NOT EXISTS in_app_targeted_comms_elements (
	id                  UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	utility_type 	    VARCHAR NOT NULL,
	structure_type      VARCHAR NOT NULL,
	content             JSONB NOT NULL,
	start_time 		    TIMESTAMPTZ NULL,
	end_time 		    TIMESTAMPTZ NULL,
	visibility_state    VARCHAR NOT NULL,
	created_at 		    TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at 	        TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id)
);

CREATE INDEX IF NOT EXISTS in_app_targeted_comms_elements_updated_at_idx ON in_app_targeted_comms_elements (updated_at ASC);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_elements_structure_type_idx ON in_app_targeted_comms_elements (structure_type, visibility_state ASC);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_elements_utility_type_idx ON in_app_targeted_comms_elements (utility_type, visibility_state ASC);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_elements_visibility_state_idx ON in_app_targeted_comms_elements (visibility_state ASC);

comment on table in_app_targeted_comms_elements is 'table to store in_app_targeted_comms_elements information';
comment on column in_app_targeted_comms_elements.utility_type is '{"proto_type":"dynamic_elements.ElementUtilityType","comment": "Usage type of the element (eg: alert, marketing etc.)"}';
comment on column in_app_targeted_comms_elements.structure_type is '{"proto_type":"dynamic_elements.ElementStructureType","comment": "Structure of the element (eg: banner, bottom sheet, pop up etc.)"}';
comment on column in_app_targeted_comms_elements.content is '{"proto_type":"dynamic_elements.ElementContent","comment": "Details like title, body, icon etc. required to render the element. Params depend on structure_type"}';
comment on column in_app_targeted_comms_elements.visibility_state is '{"proto_type":"comms.inapptargetedcomms.VisibilityState","comment": "ACTIVE/DISABLED. Visible to the users when it is ACTIVE"}';
comment on column in_app_targeted_comms_elements.start_time is 'timestamp from which the targeted comms element must be activated';
comment on column in_app_targeted_comms_elements.end_time is 'timestamp after which the targeted comms element must be disabled';

CREATE TABLE IF NOT EXISTS in_app_targeted_comms_mappings (
	id                       UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	element_id               VARCHAR NOT NULL,
	mapping_type             VARCHAR NOT NULL,
	mapped_value             VARCHAR NOT NULL,
	mapped_visibility_state  VARCHAR NOT NULL,
	created_at 		         TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at               TIMESTAMPTZ NULL DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id),
	CONSTRAINT element_id_mapping_type_and_value_unique_key UNIQUE (element_id, mapping_type, mapped_value)
);

-- index useful for retrieving mappings for given element_ids
-- also useful for get_all query with filters on element_ids and mapped_values
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_mappings_element_id_idx ON in_app_targeted_comms_mappings (element_id, mapped_value ASC);
-- index for query based on mapping details(mapping_type + mapped_value)
-- also useful for segregating simply on mapping_type
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_mappings_mapping_type_idx ON in_app_targeted_comms_mappings (mapping_type, mapped_value, mapped_visibility_state  ASC);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_mappings_mapped_value_idx ON in_app_targeted_comms_mappings (mapped_value, mapped_visibility_state  ASC);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_mappings_mapped_visibility_state_idx ON in_app_targeted_comms_mappings (mapped_visibility_state  ASC);

comment on table in_app_targeted_comms_mappings is 'table to store in_app_targeted_comms_element mappings with user, user_tags, screen, app_platform';
comment on column in_app_targeted_comms_mappings.element_id is '{"comment": "id of the in_app_targeted_comms_element being mapped"}';
comment on column in_app_targeted_comms_mappings.mapping_type is '{"proto_type":"comms.inapptargetedcomms.MappingType","comment": "Can be MAPPING_TYPE_USER / MAPPING_TYPE_USER_TAG / MAPPING_TYPE_SCREEN / MAPPING_TYPE_APP_PLATFORM"}';
comment on column in_app_targeted_comms_mappings.mapped_value is '{"comment": "actor_id in case of user, Enum as string in case of user_tag, screen, app_platform"}';
comment on column in_app_targeted_comms_mappings.mapped_visibility_state is '{"proto_type":"comms.inapptargetedcomms.VisibilityState","comment": "Current visibility state of the in_app_targeted_comms_element w.r.t. this mapped value. Can be ACTIVE/DISABLED/DISMISSED"}';
