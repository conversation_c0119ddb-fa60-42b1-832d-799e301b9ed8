-- The existing index elements_visibility_state_start_time_end_time_idx wasn't being picked by query planner, hence adding this
CREATE INDEX IF NOT EXISTS elements_end_time_start_time_visibility_state_idx ON in_app_targeted_comms_elements(end_time, start_time, visibility_state);

-- Dropping unwanted indices to improve query planning time since we are using complex join query
DROP INDEX IF EXISTS in_app_targeted_comms_elements_structure_type_idx;
DROP INDEX IF EXISTS elements_visibility_state_start_time_end_time_idx;
-- elt_id_visibility_st_mapped_value_meta_mapping_type_value_idx index will do for elt_id and we will always query for mapped_value along with mapping_type
DROP INDEX IF EXISTS in_app_targeted_comms_mappings_element_id_idx;
-- we will always query for mapped_value along with mapping_type
DROP INDEX IF EXISTS in_app_targeted_comms_mappings_mapped_value_idx;
DROP INDEX IF EXISTS in_app_targeted_comms_mappings_mapped_visibility_state_idx;
