DROP TABLE IF EXISTS element_to_app_details_mappings;


ALTER TABLE in_app_targeted_comms_mappings ALTER COLUMN element_id TYPE VARCHAR;
DROP INDEX IF EXISTS mapping_type_mapped_value_visibility_st_mapped_value_meta_idx;
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_mappings_mapping_type_idx ON in_app_targeted_comms_mappings (mapping_type, mapped_value, mapped_visibility_state  ASC);
DROP INDEX IF EXISTS elt_id_visibility_st_mapped_value_meta_mapping_type_value_idx;

DROP INDEX IF EXISTS elements_visibility_state_start_time_end_time_idx;
ALTER TABLE in_app_targeted_comms_elements ALTER COLUMN start_time DROP NOT NULL;
ALTER TABLE in_app_targeted_comms_elements ALTER COLUMN end_time DROP NOT NULL;
