ALTER TABLE in_app_targeted_comms_mappings ADD COLUMN IF NOT EXISTS mapped_value_meta VARCHAR;

ALTER TABLE in_app_targeted_comms_mappings ADD CONSTRAINT element_id_mapping_type_value_meta_unique_key UNIQUE (element_id, mapping_type, mapped_value, mapped_value_meta);
ALTER TABLE in_app_targeted_comms_mappings DROP CONSTRAINT IF EXISTS element_id_mapping_type_and_value_unique_key;

comment on column in_app_targeted_comms_mappings.mapped_value_meta is '{"comment": "additional info to identify mapped value: eg: FAQ category screens"}';
