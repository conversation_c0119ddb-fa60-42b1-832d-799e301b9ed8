ALTER TABLE comms_messages ADD CONSTRAINT comms_messages_primary_key PRIMARY KEY (id);
ALTER TABLE fcm_device_tokens ADD CONSTRAINT fcm_device_tokens_primary_key PRIMARY KEY (id);
ALTER TABLE email_callbacks ADD CONSTRAINT email_callbacks_primary_key PRIMARY KEY (id);
ALTER TABLE email_templates ADD CONSTRAINT email_templates_primary_key PRIMARY KEY (id);
ALTER TABLE comms_notifications ADD CONSTRAINT comms_notifications_primary_key PRIMARY KEY (message_id);
ALTER TABLE sms_callbacks ADD CONSTRAINT sms_callbacks_primary_key PRIMARY KEY (response_id, vendor);
ALTER TABLE sms_templates ADD CONSTRAINT sms_templates_primary_key PRIMARY KEY (id);
ALTER TABLE user_comms_preferences ADD CONSTRAINT user_comms_preferences_primary_key PRIMARY KEY (id);
ALTER TABLE whatsapp_callbacks ADD CONSTRAINT whatsapp_callbacks_primary_key PRIMARY KEY (response_id, vendor);
ALTER TABLE whatsapp_message_histories ADD CONSTRAINT whatsapp_message_histories_primary_key PRIMARY KEY (id);
ALTER TABLE whatsapp_user_replies ADD CONSTRAINT whatsapp_user_replies_primary_key PRIMARY KEY (id);
ALTER TABLE notifications_user_level_actions ADD CONSTRAINT notifications_user_level_actions_primary_key PRIMARY KEY (actor_id, id);
