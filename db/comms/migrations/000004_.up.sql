CREATE TABLE IF NOT EXISTS element_to_app_details_mappings (
	id              UUID DEFAULT public.uuid_generate_v4() NOT NULL,
	element_id      UUID NOT NULL,
	app_platform    VARCHAR NOT NULL,
	min_app_version INTEGER NOT NULL default 0,
	-- Using max integer value as default
	max_app_version INTEGER NOT NULL default 2147483647,
	PRIMARY KEY (id),
	CONSTRAINT app_platform_element_id_unique_key UNIQUE (app_platform, element_id)
);

-- index to speed up join using element_id
CREATE INDEX IF NOT EXISTS elt_id_app_platform_min_app_version_max_app_version_idx ON element_to_app_details_mappings(element_id, app_platform, min_app_version, max_app_version);

comment on table element_to_app_details_mappings is 'table to store element_id to app details mappings. App details can include platform, range of versions, device model etc.';
comment on column element_to_app_details_mappings.element_id is '{"comment": "id of the in_app_targeted_comms_element which is to be mapped"}';
comment on column element_to_app_details_mappings.app_platform is '{"proto_type":"types.Platform","comment": "app platform (Android,Ios etc.)to which the element is to be mapped"}';
comment on column element_to_app_details_mappings.min_app_version is '{"comment": "minimum app version on which the mapped element must be shown"}';
comment on column element_to_app_details_mappings.max_app_version is '{"comment": "maximum app version on which the mapped element must be shown"}';

ALTER TABLE in_app_targeted_comms_mappings ALTER COLUMN element_id TYPE UUID USING element_id::uuid;
DROP INDEX IF EXISTS in_app_targeted_comms_mappings_mapping_type_idx;
CREATE INDEX IF NOT EXISTS mapping_type_mapped_value_visibility_st_mapped_value_meta_idx ON in_app_targeted_comms_mappings(mapping_type, mapped_value, mapped_value_meta, mapped_visibility_state);
-- elt_id_visibility_st_mapped_value_meta_idx is used for index-only scan in join for user attributes
CREATE INDEX IF NOT EXISTS elt_id_visibility_st_mapped_value_meta_mapping_type_value_idx ON in_app_targeted_comms_mappings(element_id, mapped_visibility_state, mapped_value_meta, mapping_type, mapped_value);

-- adding index to optimize query for active targeted comms elements
ALTER TABLE in_app_targeted_comms_elements ALTER COLUMN start_time SET NOT NULL;
ALTER TABLE in_app_targeted_comms_elements ALTER COLUMN end_time SET NOT NULL;
CREATE INDEX IF NOT EXISTS elements_visibility_state_start_time_end_time_idx ON in_app_targeted_comms_elements(visibility_state, start_time, end_time);
