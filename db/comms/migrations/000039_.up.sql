ALTER TABLE IF EXISTS in_app_targeted_comms_elements
    ADD COLUMN IF NOT EXISTS user_tags VARCHAR[];
ALTER TABLE IF EXISTS in_app_targeted_comms_elements
    ADD COLUMN IF NOT EXISTS segment_expression VARCHAR;
ALTER TABLE IF EXISTS in_app_targeted_comms_elements
    ADD COLUMN IF NOT EXISTS screens VARCHAR[];
ALTER TABLE IF EXISTS in_app_targeted_comms_elements
    ADD COLUMN IF NOT EXISTS screens_meta VARCHAR[];
ALTER TABLE IF EXISTS in_app_targeted_comms_elements
    ADD COLUMN IF NOT EXISTS app_details JSONB;

CREATE INDEX IF NOT EXISTS in_app_targeted_comms_elements_screens_idx ON in_app_targeted_comms_elements USING GIN (screens);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_elements_screens_meta_idx ON in_app_targeted_comms_elements USING GIN (screens_meta);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_elements_user_tags_idx ON in_app_targeted_comms_elements USING GIN (user_tags);

CREATE TABLE IF NOT EXISTS in_app_targeted_comms_callbacks
(
    id         UUID                     NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
    element_id VARCHAR                  NOT NULL,
    actor_id   VARCHAR                  NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX IF NOT EXISTS in_app_targeted_comms_callbacks_element_id_actor_id_unique_idx ON in_app_targeted_comms_callbacks (element_id, actor_id);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_callbacks_updated_at_idx ON in_app_targeted_comms_callbacks USING BTREE (updated_at);

COMMENT ON TABLE in_app_targeted_comms_callbacks IS 'table to store element actor callbacks';
COMMENT ON COLUMN in_app_targeted_comms_callbacks.element_id IS 'element id to reference in_app_targeted_comms_elements details';
COMMENT ON COLUMN in_app_targeted_comms_callbacks.actor_id IS 'actor id for which the element got call back';
