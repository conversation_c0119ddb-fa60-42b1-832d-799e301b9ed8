CREATE INDEX IF NOT EXISTS in_app_targeted_comms_elements_structure_type_idx ON in_app_targeted_comms_elements (structure_type, visibility_state ASC);
CREATE INDEX IF NOT EXISTS elements_visibility_state_start_time_end_time_idx ON in_app_targeted_comms_elements (visibility_state, start_time, end_time);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_mappings_element_id_idx ON in_app_targeted_comms_mappings (element_id, mapped_value ASC);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_mappings_mapped_value_idx ON in_app_targeted_comms_mappings (mapped_value, mapped_visibility_state  ASC);
CREATE INDEX IF NOT EXISTS in_app_targeted_comms_mappings_mapped_visibility_state_idx ON in_app_targeted_comms_mappings (mapped_visibility_state  ASC);

DROP INDEX IF EXISTS elements_end_time_start_time_visibility_state_idx;
