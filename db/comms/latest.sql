CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.comms_messages (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    client_id character varying,
    sqs_msg_id character varying,
    vendor character varying,
    vendor_msg_id character varying,
    retries integer,
    status character varying,
    medium character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    actor_id character varying,
    user_identifier_type character varying,
    user_identifier_value character varying,
    qos character varying,
    metadata jsonb,
    external_reference_id character varying,
    sub_status character varying
);
CREATE TABLE public.comms_notifications (
    message_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    type character varying NOT NULL,
    expire_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    data jsonb NOT NULL,
    status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    priority character varying,
    in_app_section character varying,
    sub_status character varying
);
COMMENT ON TABLE public.comms_notifications IS 'table to store details of app notifications sent to users';
COMMENT ON COLUMN public.comms_notifications.type IS '{"proto_type":"comms.NotificationType", "comment": "type of notification"}';
COMMENT ON COLUMN public.comms_notifications.data IS '{"proto_type":"comms.NotificationData", "comment": "Notification data used for sending message"}';
COMMENT ON COLUMN public.comms_notifications.status IS '{"proto_type":"comms.NotificationStatus", "comment": "status of notification"}';
COMMENT ON COLUMN public.comms_notifications.priority IS '{"proto_type":"comms.InAppNotificationPriority"}';
COMMENT ON COLUMN public.comms_notifications.in_app_section IS '{"proto_type":"comms.InAppNotificationSection"}';
COMMENT ON COLUMN public.comms_notifications.sub_status IS '{"proto_type":"comms.NotificationSubStatus"}';
CREATE TABLE public.comms_retry_logs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    comms_msg_id character varying NOT NULL,
    vendor character varying NOT NULL,
    vendor_account character varying,
    vendor_msg_id character varying NOT NULL,
    status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE public.comms_retry_logs IS 'table to track history for vendor msg id and status in cases where we are retrying msg on comms service end';
COMMENT ON COLUMN public.comms_retry_logs.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment":"specifies which vendor was used for comms: ACL, KALEYRA, etc"';
COMMENT ON COLUMN public.comms_retry_logs.vendor_account IS 'vendor account: EPIFI, FEDERAL, etc';
COMMENT ON COLUMN public.comms_retry_logs.status IS '{"proto_type":"comms.MessageState", "comment":"specifies status of the message: delivered, failed, etc"';
CREATE TABLE public.element_to_app_details_mappings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    element_id uuid NOT NULL,
    app_platform character varying NOT NULL,
    min_app_version integer DEFAULT 0 NOT NULL,
    max_app_version integer DEFAULT ********** NOT NULL
);
COMMENT ON TABLE public.element_to_app_details_mappings IS 'table to store element_id to app details mappings. App details can include platform, range of versions, device model etc.';
COMMENT ON COLUMN public.element_to_app_details_mappings.element_id IS '{"comment": "id of the in_app_targeted_comms_element which is to be mapped"}';
COMMENT ON COLUMN public.element_to_app_details_mappings.app_platform IS '{"proto_type":"types.Platform","comment": "app platform (Android,Ios etc.)to which the element is to be mapped"}';
COMMENT ON COLUMN public.element_to_app_details_mappings.min_app_version IS '{"comment": "minimum app version on which the mapped element must be shown"}';
COMMENT ON COLUMN public.element_to_app_details_mappings.max_app_version IS '{"comment": "maximum app version on which the mapped element must be shown"}';
CREATE TABLE public.email_callbacks (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    message_id character varying NOT NULL,
    event_type character varying NOT NULL,
    event_timestamp timestamp with time zone NOT NULL,
    vendor character varying NOT NULL,
    event_meta jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE public.email_callbacks IS 'table to store email events received in callbacks for various events on email';
COMMENT ON COLUMN public.email_callbacks.event_type IS '{"proto_type":"comms.EventType", "comment": "enum to store type of event for which callback is received"}';
COMMENT ON COLUMN public.email_callbacks.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment": "enum to store vendor information"}';
COMMENT ON COLUMN public.email_callbacks.event_meta IS '{"proto_type":"comms.EventMeta", "comment": "JSONB to store meta data for an event"}';
CREATE TABLE public.email_templates (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email_type character varying NOT NULL,
    template_version character varying NOT NULL,
    subject character varying NOT NULL,
    template character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE public.email_templates IS 'table to store email templates to be used by comms service while sending emails';
COMMENT ON COLUMN public.email_templates.email_type IS '{"proto_type":"comms.EmailType", "comment": "enum to store list of all possible email types that can be sent via comms service"}';
COMMENT ON COLUMN public.email_templates.template_version IS '{"proto_type":"comms.TemplateVersion", "comment": "enum to store list of possible versions for a template"}';
CREATE TABLE public.fcm_device_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying,
    device_token character varying,
    status character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE public.in_app_targeted_comms_callbacks (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    element_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.in_app_targeted_comms_callbacks IS 'table to store element actor callbacks';
COMMENT ON COLUMN public.in_app_targeted_comms_callbacks.element_id IS 'element id to reference in_app_targeted_comms_elements details';
COMMENT ON COLUMN public.in_app_targeted_comms_callbacks.actor_id IS 'actor id for which the element got call back';
CREATE TABLE public.in_app_targeted_comms_elements (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    utility_type character varying NOT NULL,
    structure_type character varying NOT NULL,
    content jsonb NOT NULL,
    start_time timestamp with time zone NOT NULL,
    end_time timestamp with time zone NOT NULL,
    visibility_state character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    meta_data jsonb,
    user_tags character varying[],
    segment_expression character varying,
    screens character varying[],
    screens_meta character varying[],
    app_details jsonb
);
COMMENT ON TABLE public.in_app_targeted_comms_elements IS 'table to store in_app_targeted_comms_elements information';
COMMENT ON COLUMN public.in_app_targeted_comms_elements.utility_type IS '{"proto_type":"dynamic_elements.ElementUtilityType","comment": "Usage type of the element (eg: alert, marketing etc.)"}';
COMMENT ON COLUMN public.in_app_targeted_comms_elements.structure_type IS '{"proto_type":"dynamic_elements.ElementStructureType","comment": "Structure of the element (eg: banner, bottom sheet, pop up etc.)"}';
COMMENT ON COLUMN public.in_app_targeted_comms_elements.content IS '{"proto_type":"dynamic_elements.ElementContent","comment": "Details like title, body, icon etc. required to render the element. Params depend on structure_type"}';
COMMENT ON COLUMN public.in_app_targeted_comms_elements.start_time IS 'timestamp from which the targeted comms element must be activated';
COMMENT ON COLUMN public.in_app_targeted_comms_elements.end_time IS 'timestamp after which the targeted comms element must be disabled';
COMMENT ON COLUMN public.in_app_targeted_comms_elements.visibility_state IS '{"proto_type":"comms.inapptargetedcomms.VisibilityState","comment": "ACTIVE/DISABLED. Visible to the users when it is ACTIVE"}';
COMMENT ON COLUMN public.in_app_targeted_comms_elements.meta_data IS '{"comment": "meta data related to element like campaign name, treatment id etc"}';
CREATE TABLE public.in_app_targeted_comms_mappings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    element_id uuid NOT NULL,
    mapping_type character varying NOT NULL,
    mapped_value character varying NOT NULL,
    mapped_visibility_state character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    mapped_value_meta character varying,
    last_callback_time timestamp with time zone
);
COMMENT ON TABLE public.in_app_targeted_comms_mappings IS 'table to store in_app_targeted_comms_element mappings with user, user_tags, screen, app_platform';
COMMENT ON COLUMN public.in_app_targeted_comms_mappings.element_id IS '{"comment": "id of the in_app_targeted_comms_element being mapped"}';
COMMENT ON COLUMN public.in_app_targeted_comms_mappings.mapping_type IS '{"proto_type":"comms.inapptargetedcomms.MappingType","comment": "Can be MAPPING_TYPE_USER / MAPPING_TYPE_USER_TAG / MAPPING_TYPE_SCREEN / MAPPING_TYPE_APP_PLATFORM"}';
COMMENT ON COLUMN public.in_app_targeted_comms_mappings.mapped_value IS '{"comment": "actor_id in case of user, Enum as string in case of user_tag, screen, app_platform"}';
COMMENT ON COLUMN public.in_app_targeted_comms_mappings.mapped_visibility_state IS '{"proto_type":"comms.inapptargetedcomms.VisibilityState","comment": "Current visibility state of the in_app_targeted_comms_element w.r.t. this mapped value. Can be ACTIVE/DISABLED/DISMISSED"}';
COMMENT ON COLUMN public.in_app_targeted_comms_mappings.mapped_value_meta IS '{"comment": "additional info to identify mapped value: eg: FAQ category screens"}';
COMMENT ON COLUMN public.in_app_targeted_comms_mappings.last_callback_time IS 'timestamp at which last callback has been received';
CREATE TABLE public.notifications_user_level_actions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    action character varying NOT NULL,
    action_timestamp timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE public.notifications_user_level_actions IS 'table to keep the latest timestamp of user level notifications events like view_all, dismiss_all etc';
COMMENT ON COLUMN public.notifications_user_level_actions.action IS '{"proto_type":"comms.NotificationAction"}';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.sms_callbacks (
    response_id character varying NOT NULL,
    status character varying NOT NULL,
    vendor character varying NOT NULL,
    callback_info jsonb,
    vendor_updated_time timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    medium character varying DEFAULT 'SMS'::character varying,
    phone_no character varying,
    actor_id character varying
);
CREATE TABLE public.sms_templates (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    template_type character varying NOT NULL,
    template character varying NOT NULL,
    dlt_template_id character varying NOT NULL,
    version character varying NOT NULL,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE public.user_comms_preferences (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    medium character varying NOT NULL,
    category character varying NOT NULL,
    preference character varying NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at_unix bigint,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE public.user_comms_preferences IS 'table to store user preferences for receiving comms via various modes for various categories';
COMMENT ON COLUMN public.user_comms_preferences.medium IS '{"proto_type":"comms.Medium", "comment": "medium of communication"}';
COMMENT ON COLUMN public.user_comms_preferences.category IS '{"proto_type":"comms.Category", "comment": "category of communication"}';
COMMENT ON COLUMN public.user_comms_preferences.preference IS '{"proto_type":"comms.user_preference.Preference", "comment": "Preference for receiving communication"}';
CREATE TABLE public.whatsapp_callbacks (
    response_id character varying NOT NULL,
    status character varying NOT NULL,
    vendor character varying NOT NULL,
    dlr_received_time timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    callback_info jsonb,
    phone_no character varying,
    actor_id character varying
);
COMMENT ON TABLE public.whatsapp_callbacks IS 'table to store status and other details sent by whatsapp vendors in callback';
COMMENT ON COLUMN public.whatsapp_callbacks.status IS '{"proto_type":"comms.MessageState", "comment": "state of message"}';
COMMENT ON COLUMN public.whatsapp_callbacks.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment": "vendor used for sending message"}';
CREATE TABLE public.whatsapp_message_histories (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    response_id character varying NOT NULL,
    status character varying NOT NULL,
    status_timestamp timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE public.whatsapp_message_histories IS 'table to store message history related to whatsapp message sent by us to user to capture times of sent, delivered and read';
COMMENT ON COLUMN public.whatsapp_message_histories.status IS '{"proto_type":"comms.WhatsappMessageStatus", "comment": "enum to store list of possible status of whatsapp message status after it is sent to vendor : sent, read, delivered"}';
CREATE TABLE public.whatsapp_user_replies (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    response_id character varying NOT NULL,
    response_info jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
COMMENT ON TABLE public.whatsapp_user_replies IS 'table to store user reply details for whatsapp communication';
COMMENT ON COLUMN public.whatsapp_user_replies.response_info IS '{"proto_type":"comms.WhatsAppUserReplyInfo", "comment": "user reply detailed info"}';
ALTER TABLE ONLY public.element_to_app_details_mappings
    ADD CONSTRAINT app_platform_element_id_unique_key UNIQUE (app_platform, element_id);
ALTER TABLE ONLY public.comms_messages
    ADD CONSTRAINT comms_messages_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.comms_notifications
    ADD CONSTRAINT comms_notifications_primary_key PRIMARY KEY (message_id);
ALTER TABLE ONLY public.comms_retry_logs
    ADD CONSTRAINT comms_retry_logs_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.comms_retry_logs
    ADD CONSTRAINT comms_retry_logs_vendor_msg_id_vendor UNIQUE (vendor_msg_id, vendor);
ALTER TABLE ONLY public.in_app_targeted_comms_mappings
    ADD CONSTRAINT element_id_mapping_type_value_meta_unique_key UNIQUE (element_id, mapping_type, mapped_value, mapped_value_meta);
ALTER TABLE ONLY public.element_to_app_details_mappings
    ADD CONSTRAINT element_to_app_details_mappings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.email_callbacks
    ADD CONSTRAINT email_callbacks_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.email_templates
    ADD CONSTRAINT email_templates_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.fcm_device_tokens
    ADD CONSTRAINT fcm_device_tokens_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.in_app_targeted_comms_callbacks
    ADD CONSTRAINT in_app_targeted_comms_callbacks_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.in_app_targeted_comms_elements
    ADD CONSTRAINT in_app_targeted_comms_elements_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.in_app_targeted_comms_mappings
    ADD CONSTRAINT in_app_targeted_comms_mappings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.notifications_user_level_actions
    ADD CONSTRAINT notifications_user_level_actions_primary_key PRIMARY KEY (actor_id, id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.sms_callbacks
    ADD CONSTRAINT sms_callbacks_primary_key PRIMARY KEY (response_id, vendor);
ALTER TABLE ONLY public.sms_templates
    ADD CONSTRAINT sms_templates_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.user_comms_preferences
    ADD CONSTRAINT user_comms_preferences_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.whatsapp_callbacks
    ADD CONSTRAINT whatsapp_callbacks_primary_key PRIMARY KEY (response_id, vendor);
ALTER TABLE ONLY public.whatsapp_message_histories
    ADD CONSTRAINT whatsapp_message_histories_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.whatsapp_user_replies
    ADD CONSTRAINT whatsapp_user_replies_primary_key PRIMARY KEY (id);
CREATE UNIQUE INDEX actor_id_event_key ON public.notifications_user_level_actions USING btree (actor_id, action);
CREATE INDEX actor_id_idx ON public.comms_messages USING btree (actor_id);
CREATE UNIQUE INDEX actor_id_medium_category_preference_deleted_at_unique_idx ON public.user_comms_preferences USING btree (actor_id, medium, category, deleted_at_unix);
CREATE INDEX comms_messages_external_reference_id_client_id_idx ON public.comms_messages USING btree (external_reference_id, client_id);
CREATE INDEX comms_messages_updated_at_idx ON public.comms_messages USING btree (updated_at);
CREATE INDEX comms_notifications_actor_id_expire_at_idx ON public.comms_notifications USING btree (actor_id, expire_at) INCLUDE (type, status, created_at);
CREATE INDEX comms_notifications_updated_at_idx ON public.comms_notifications USING btree (updated_at);
CREATE INDEX comms_retry_logs_comms_msg_id_key ON public.comms_retry_logs USING btree (comms_msg_id);
CREATE INDEX comms_retry_logs_updated_at_key ON public.comms_retry_logs USING btree (updated_at);
CREATE INDEX elements_end_time_start_time_visibility_state_idx ON public.in_app_targeted_comms_elements USING btree (end_time, start_time, visibility_state);
CREATE INDEX elt_id_app_platform_min_app_version_max_app_version_idx ON public.element_to_app_details_mappings USING btree (element_id, app_platform, min_app_version, max_app_version);
CREATE INDEX elt_id_visibility_st_mapped_value_meta_mapping_type_value_idx ON public.in_app_targeted_comms_mappings USING btree (element_id, mapped_visibility_state, mapped_value_meta, mapping_type, mapped_value);
CREATE INDEX email_callbacks_message_id_vendor_idx ON public.email_callbacks USING btree (message_id, vendor);
CREATE INDEX email_callbacks_updated_at_idx ON public.email_callbacks USING btree (updated_at);
CREATE INDEX email_templates_email_type_version_key ON public.email_templates USING btree (email_type, template_version);
CREATE INDEX email_templates_updated_at_idx ON public.email_templates USING btree (updated_at);
CREATE INDEX fcm_device_tokens_actor_id_idx ON public.fcm_device_tokens USING btree (actor_id);
CREATE INDEX fcm_device_tokens_device_token_idx ON public.fcm_device_tokens USING btree (device_token);
CREATE INDEX fcm_device_tokens_updated_at_idx ON public.fcm_device_tokens USING btree (updated_at);
CREATE UNIQUE INDEX in_app_targeted_comms_callbacks_element_id_actor_id_unique_idx ON public.in_app_targeted_comms_callbacks USING btree (element_id, actor_id);
CREATE INDEX in_app_targeted_comms_callbacks_updated_at_idx ON public.in_app_targeted_comms_callbacks USING btree (updated_at);
CREATE INDEX in_app_targeted_comms_elements_screens_idx ON public.in_app_targeted_comms_elements USING gin (screens);
CREATE INDEX in_app_targeted_comms_elements_screens_meta_idx ON public.in_app_targeted_comms_elements USING gin (screens_meta);
CREATE INDEX in_app_targeted_comms_elements_updated_at_idx ON public.in_app_targeted_comms_elements USING btree (updated_at);
CREATE INDEX in_app_targeted_comms_elements_user_tags_idx ON public.in_app_targeted_comms_elements USING gin (user_tags);
CREATE INDEX in_app_targeted_comms_elements_utility_type_idx ON public.in_app_targeted_comms_elements USING btree (utility_type, visibility_state);
CREATE INDEX in_app_targeted_comms_elements_visibility_state_idx ON public.in_app_targeted_comms_elements USING btree (visibility_state);
CREATE INDEX in_app_targeted_comms_mappings_mapped_value_idx ON public.in_app_targeted_comms_mappings USING btree (mapped_value);
CREATE INDEX in_app_targeted_comms_mappings_updated_at_idx ON public.in_app_targeted_comms_mappings USING btree (updated_at);
CREATE INDEX mapping_type_mapped_value_visibility_st_mapped_value_meta_idx ON public.in_app_targeted_comms_mappings USING btree (mapping_type, mapped_value, mapped_value_meta, mapped_visibility_state);
CREATE INDEX notifications_user_level_actions_updated_at_idx ON public.notifications_user_level_actions USING btree (updated_at);
CREATE UNIQUE INDEX response_id_status ON public.whatsapp_message_histories USING btree (response_id, status);
CREATE INDEX sms_callbacks_updated_at_idx ON public.sms_callbacks USING btree (updated_at);
CREATE UNIQUE INDEX sms_template_type_version_key ON public.sms_templates USING btree (template_type, version);
CREATE INDEX sms_templates_updated_at_idx ON public.sms_templates USING btree (updated_at);
CREATE INDEX user_comms_preferences_updated_at_idx ON public.user_comms_preferences USING btree (updated_at);
CREATE INDEX user_identifier_value_index ON public.comms_messages USING btree (user_identifier_value);
CREATE INDEX vendor_msg_id_idx ON public.comms_messages USING btree (vendor_msg_id);
CREATE INDEX whatsapp_callbacks_updated_at_idx ON public.whatsapp_callbacks USING btree (updated_at);
CREATE INDEX whatsapp_message_histories_updated_at_idx ON public.whatsapp_message_histories USING btree (updated_at);
CREATE INDEX whatsapp_user_replies_response_id_index ON public.whatsapp_user_replies USING btree (response_id);
CREATE INDEX whatsapp_user_replies_updated_at_idx ON public.whatsapp_user_replies USING btree (updated_at);
