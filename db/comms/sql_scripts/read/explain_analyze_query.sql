-- query from trace: 5a3ddf587fffbef3335f3963b150c3df
EXPLAIN ANALYZE
SELECT DISTINCT in_app_targeted_comms_elements.*,
				t2.mapping_type            as mapping_type,
				t2.mapped_value            as mapped_value,
				t2.mapped_visibility_state as mapped_visibility_state
FROM "in_app_targeted_comms_elements"
		 INNER JOIN element_to_app_details_mappings
					ON element_to_app_details_mappings.element_id = in_app_targeted_comms_elements.id
		 INNER JOIN in_app_targeted_comms_mappings as t1 ON (t1.element_id = in_app_targeted_comms_elements.id)
		 INNER JOIN in_app_targeted_comms_mappings as t2 ON (t2.element_id = in_app_targeted_comms_elements.id)
WHERE app_platform IN ('ANDROID')
  AND min_app_version <= 287
  AND max_app_version >= 287
  AND (t1.mapping_type, t1.mapped_value, t1.mapped_value_meta) IN
	  (('MAPPING_TYPE_SCREEN', 'HOME', 'VERSION_V2_SECTION_GTM_POPUP'))
  AND ((t2.mapping_type, t2.mapped_value, t2.mapped_value_meta) IN
	   (('MAPPING_TYPE_USER', 'AC2202096iHrkXcPRF2jl5RpzxsuLA==', ''),
		('MAPPING_TYPE_USER_TAG', 'USER_TAG_SAVINGS_ACCOUNT', '')) OR t2.mapping_type = 'MAPPING_TYPE_SEGMENT_ID' OR
	   t2.mapping_type = 'MAPPING_TYPE_SEGMENT_EXPRESSION')
  AND visibility_state = 'VISIBILITY_STATE_ACTIVE'
  AND start_time <= '2023-12-01 07:46:57.802'
  AND end_time > '2023-12-01 07:46:57.802';
