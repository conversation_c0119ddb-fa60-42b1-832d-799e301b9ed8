--> Migration Version: 29 

CREATE TABLE public.schema_lock (
	lock_id INT8 NOT NULL,
	CONSTRAINT schema_lock_pkey PRIMARY KEY (lock_id ASC)
);
CREATE TABLE public.schema_migrations (
	version INT8 NOT NULL,
	dirty BOOL NOT NULL,
	CONSTRAINT schema_migrations_pkey PRIMARY KEY (version ASC)
);
CREATE TABLE public.review_actions (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	case_id STRING NOT NULL,
	review_type STRING NOT NULL,
	action_type STRING NULL,
	parameters JSONB NULL,
	source STRING NULL,
	analyst_email STRING NULL,
	initiated_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	actor_id STRING NULL,
	status STRING NULL,
	processing_type STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (case_id ASC, id ASC),
	UNIQUE INDEX review_actions_id_key (id ASC),
	INDEX review_actions_updated_at_index (updated_at ASC),
	INDEX review_actions_actor_id_idx (actor_id ASC),
	FAMILY frequently_updated (updated_at, status, processing_type),
	FAMILY "primary" (id, case_id, review_type, action_type, source, parameters, analyst_email, initiated_at, created_at, actor_id)
);
COMMENT ON TABLE public.review_actions IS 'risk-case-management: This table will contain logs of all the actions against a case';
COMMENT ON COLUMN public.review_actions.review_type IS '"proto_type":"risk.case_store.enums", "comment":"type of review for which action was taken", "ref":"api.risk.case_store.enums.proto"';
COMMENT ON COLUMN public.review_actions.action_type IS '"proto_type":"risk.case_management.review.enums", "comment":"action type enum  which particular action was taken", "ref":"api.risk.case_management.review.enums.proto"';
COMMENT ON COLUMN public.review_actions.parameters IS '"proto_type":"risk.case_management.review.action", "comment":"for each action type we will have some action specific parameters, "ref":"api.risk.case_management.review.action.proto"';
COMMENT ON COLUMN public.review_actions.source IS '"proto_type":"risk.case_management.review.enums", "comment":"identify from where the action was taken", "ref":"api.risk.case_management.review.enums.proto"';
COMMENT ON COLUMN public.review_actions.analyst_email IS 'email of analyst taking the action';
COMMENT ON COLUMN public.review_actions.initiated_at IS 'time at which action was initiated from the source system';
COMMENT ON COLUMN public.review_actions.status IS '"comment":"indicates current status of the action", "ref":"api.risk.case_management.review.enums.ActionStatus"';
COMMENT ON COLUMN public.review_actions.processing_type IS '"comment":"indicates processing type of action", "ref":"api.risk.case_management.review.enums.ActionProcessingType"';
CREATE TABLE public.rules (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	name STRING NOT NULL,
	version INT8 NOT NULL,
	description STRING NOT NULL,
	evaluation_method STRING NOT NULL,
	external_id STRING NULL,
	provenance STRING NOT NULL,
	confidence_score INT8 NULL,
	state STRING NOT NULL,
	assessed_entity_type STRING NOT NULL,
	suspect_entity STRING NOT NULL,
	rule_group STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	added_by_email STRING NOT NULL DEFAULT 'UNSPECIFIED':::STRING,
	seed_precision FLOAT8 NOT NULL DEFAULT 0.0:::FLOAT8,
	force_use_seed_precision BOOL NOT NULL DEFAULT false,
	tags STRING[] NULL,
	CONSTRAINT rules_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX rules_name_version_key (name ASC, version ASC),
	INDEX rules_updated_at_idx (updated_at ASC),
	INDEX rules_external_id_idx (external_id ASC),
	INDEX rules_rule_group_idx (rule_group ASC)
);
COMMENT ON TABLE public.rules IS 'table to store risk rules related information, based on the rules alerts gets created';
COMMENT ON COLUMN public.rules.evaluation_method IS 'stores the evaluation method of a rule i.e. conditional or heuristic';
COMMENT ON COLUMN public.rules.external_id IS 'stores the id of a rule in external rule engine';
COMMENT ON COLUMN public.rules.provenance IS 'stores the source of the rules i.e. rules engine, internal rules, DS rules';
COMMENT ON COLUMN public.rules.confidence_score IS e'stores the confidence score of the rules, alerts generated with high\n    score will have high confidence of a entity being fraud';
COMMENT ON COLUMN public.rules.state IS 'stores the state of rule i.e. inactive, in-review, active etc';
COMMENT ON COLUMN public.rules.assessed_entity_type IS 'stores the entity type which is getting assessed i.e. transactions, users, liveness';
COMMENT ON COLUMN public.rules.suspect_entity IS 'stores the suspected entity against which the alert should be created';
COMMENT ON COLUMN public.rules.rule_group IS 'stores the rule_group of the rule it can be atm, crypto, aml etc';
COMMENT ON COLUMN public.rules.added_by_email IS 'analyst email for audit purposes';
COMMENT ON COLUMN public.rules.seed_precision IS 'seed precision value for the rule, used for case prioritization backdoor';
COMMENT ON COLUMN public.rules.tags IS 'stores tags attached with each rule';
CREATE TABLE public.alerts (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NULL,
	account_type STRING NULL,
	account_id STRING NULL,
	entity_type STRING NOT NULL,
	entity_id STRING NOT NULL,
	rule_id STRING NOT NULL,
	batch_name STRING NULL,
	verdict STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	case_id STRING NULL,
	handling_type STRING NULL,
	handling_reasons STRING[] NULL,
	initiated_at TIMESTAMPTZ NULL,
	rule_precision FLOAT8 NULL,
	CONSTRAINT alerts_pkey PRIMARY KEY (id ASC),
	INDEX alerts_updated_at_idx (updated_at ASC),
	INDEX alerts_actor_id_created_at_idx (actor_id ASC, created_at ASC),
	INDEX alerts_entity_type_entity_id_created_at_idx (entity_type ASC, entity_id ASC, created_at DESC),
	INDEX alerts_account_id_created_at_idx (account_id ASC, created_at DESC),
	INDEX alerts_case_id_created_at_idx (case_id ASC, created_at DESC),
	INDEX alerts_rule_id_created_at_idx (rule_id ASC, created_at ASC)
);
COMMENT ON TABLE public.alerts IS 'table to store risky alerts on a entity based on certain rule triggered';
COMMENT ON COLUMN public.alerts.actor_id IS 'stores the id of the actor against which the alert got generated';
COMMENT ON COLUMN public.alerts.account_id IS 'stores the id of the account against which the alert got generated';
COMMENT ON COLUMN public.alerts.entity_type IS 'stores the entity type such as transaction, liveness etc';
COMMENT ON COLUMN public.alerts.entity_id IS 'stores the entity id sucn as txn id, liveness id';
COMMENT ON COLUMN public.alerts.rule_id IS 'stores the rule id by which alert got triggered';
COMMENT ON COLUMN public.alerts.batch_name IS 'stores the batch identifier for the manual bulk upload';
COMMENT ON COLUMN public.alerts.verdict IS 'stores the analyst pass/fail after the review being done';
COMMENT ON COLUMN public.alerts.case_id IS 'stores the id of the case created for the alert';
COMMENT ON COLUMN public.alerts.handling_type IS '"proto_type":"risk.case_management.enums.AlertHandlingType", "comment":"specifies how alert was handled"';
COMMENT ON COLUMN public.alerts.handling_reasons IS '"proto_type":"risk.case_management.enums.AlertHandlingReason", "comment":"reasons for a specific handling of the alert"';
COMMENT ON COLUMN public.alerts.initiated_at IS 'Initiation timestamp of the alert';
COMMENT ON COLUMN public.alerts.rule_precision IS 'Effective precision of the rule at the time of alert creation.';
COMMENT ON INDEX public.alerts@alerts_updated_at_idx IS 'helps in piping the data to snowflake';
COMMENT ON INDEX public.alerts@alerts_actor_id_created_at_idx IS 'helps in dedupe the alerts based on the case_id and the recency';
COMMENT ON INDEX public.alerts@alerts_entity_type_entity_id_created_at_idx IS 'helps in getting the recent alerts based on entity id';
COMMENT ON INDEX public.alerts@alerts_account_id_created_at_idx IS 'helps in getting the recent alerts based on account id';
COMMENT ON INDEX public.alerts@alerts_case_id_created_at_idx IS 'helps in getting the recent alerts based on case id';
CREATE TABLE public.allowed_annotations (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	entity_type STRING NOT NULL,
	annotation_type STRING NULL,
	value STRING NOT NULL,
	added_by_email STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	annotation_type_id STRING NULL,
	CONSTRAINT allowed_annotations_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX allowed_annotations_entity_type_annotation_type_value_key (entity_type ASC, annotation_type ASC, value ASC),
	INDEX annotation_libraries_updated_at_idx (updated_at ASC),
	UNIQUE INDEX allowed_annotations_annotation_type_id_value_unique_idx (annotation_type_id ASC, value ASC) WHERE annotation_type_id IS NOT NULL
);
COMMENT ON TABLE public.allowed_annotations IS 'will store all the annotations possible for the risk reviews';
COMMENT ON COLUMN public.allowed_annotations.entity_type IS 'stores the type of entity on which this annotation is part of';
COMMENT ON COLUMN public.allowed_annotations.annotation_type IS 'stores the type of an annotation i.e. live-ness id or a entity level annotation or any other type e.g., user.profile.address';
COMMENT ON COLUMN public.allowed_annotations.value IS 'annotation value such as crypto etc';
COMMENT ON COLUMN public.allowed_annotations.added_by_email IS 'analyst email for audit purposes';
COMMENT ON COLUMN public.allowed_annotations.annotation_type_id IS 'Id of the mapped allowed annotation type';
COMMENT ON INDEX public.allowed_annotations@annotation_libraries_updated_at_idx IS 'helps in downstream piping to data lake';
CREATE TABLE public.annotations (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	entity_type STRING NOT NULL,
	entity_id STRING NOT NULL,
	allowed_annotation_id STRING NOT NULL,
	added_by_email STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	case_id STRING NULL,
	CONSTRAINT annotations_pkey PRIMARY KEY (id ASC),
	INDEX annotations_updated_at_idx (updated_at ASC),
	INDEX annotations_entity_type_entity_id_idx (entity_type ASC, entity_id ASC),
	INDEX annotations_case_id_idx (case_id ASC)
);
COMMENT ON TABLE public.annotations IS 'will store all the annotations against the entity';
COMMENT ON COLUMN public.annotations.entity_type IS 'stores the type of entity for which the annotation is part of';
COMMENT ON COLUMN public.annotations.entity_id IS 'stores the entity_id against which the annotation is';
COMMENT ON COLUMN public.annotations.allowed_annotation_id IS 'annotation id';
COMMENT ON COLUMN public.annotations.added_by_email IS 'analyst email for audit purposes';
COMMENT ON COLUMN public.annotations.case_id IS 'stores the id of the case against which the annotations has been added';
COMMENT ON INDEX public.annotations@annotations_updated_at_idx IS 'helps in downstream piping to data lake';
COMMENT ON INDEX public.annotations@annotations_entity_type_entity_id_idx IS 'helps in getting all the annotation for the combination';
COMMENT ON INDEX public.annotations@annotations_case_id_idx IS 'helps in retrieving the annotations against the case id';
CREATE TABLE public.comments (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	entity_type STRING NOT NULL,
	entity_id STRING NOT NULL,
	comment_type STRING NOT NULL,
	comment STRING NOT NULL,
	added_by_email STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	case_id STRING NULL,
	CONSTRAINT comments_pkey PRIMARY KEY (id ASC),
	INDEX comments_updated_at_idx (updated_at ASC),
	INDEX comments_entity_type_entity_id_idx (entity_type ASC, entity_id ASC),
	INDEX comments_case_id_idx (case_id ASC)
);
COMMENT ON TABLE public.comments IS 'will store all the comments against the entity';
COMMENT ON COLUMN public.comments.entity_type IS 'stores the type of entity against which the comment is added';
COMMENT ON COLUMN public.comments.entity_id IS 'stores the entity_id against which the comment is added';
COMMENT ON COLUMN public.comments.comment_type IS 'type against which the comments is added it can be kyc.field_name, kyc etc';
COMMENT ON COLUMN public.comments.comment IS 'review comments';
COMMENT ON COLUMN public.comments.added_by_email IS 'analyst email for audit purposes';
COMMENT ON COLUMN public.comments.case_id IS 'stores the id of the case against which the comments has been added';
COMMENT ON INDEX public.comments@comments_updated_at_idx IS 'helps in downstream piping to data lake';
COMMENT ON INDEX public.comments@comments_entity_type_entity_id_idx IS 'helps in getting all the annotation for the combination';
COMMENT ON INDEX public.comments@comments_case_id_idx IS 'helps in retrieving the comments against the case id';
CREATE TABLE public.suggested_actions (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	rule_id STRING NOT NULL,
	suggested_action_type STRING NOT NULL,
	request_reason STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT suggested_actions_pkey PRIMARY KEY (id ASC),
	INDEX suggested_actions_updated_at_idx (updated_at ASC),
	UNIQUE INDEX suggested_actions_rule_id_suggested_action_type_deleted_at_unix_key (rule_id ASC, suggested_action_type ASC, deleted_at_unix ASC)
);
COMMENT ON TABLE public.suggested_actions IS 'stores the suggested action against the rule';
COMMENT ON COLUMN public.suggested_actions.rule_id IS 'stores the rule id for which the actions needs to be performed';
COMMENT ON COLUMN public.suggested_actions.suggested_action_type IS 'stores the type of actions the rule is suggesting. It is part of enum SuggestedActionType in risk.case_management';
COMMENT ON COLUMN public.suggested_actions.request_reason IS 'stores the reason for the actions. It is part of RequestReason enum in risk.enums';
COMMENT ON INDEX public.suggested_actions@suggested_actions_updated_at_idx IS 'helps in piping the data into snowflake';
CREATE TABLE public.lea_complaints (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	account_type STRING NOT NULL,
	account_id STRING NOT NULL,
	external_reference_number STRING NOT NULL,
	audit_date TIMESTAMPTZ NOT NULL,
	init_sol_id STRING NOT NULL,
	audit_bod_date TIMESTAMPTZ NOT NULL,
	account_closure_date TIMESTAMPTZ NULL,
	freeze_code STRING NULL,
	freeze_reasons STRING[] NULL,
	remarks STRING[] NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	additional_details JSONB NULL,
	CONSTRAINT lea_complaints_pkey PRIMARY KEY (id ASC),
	INDEX lea_complaints_updated_at_idx (updated_at ASC),
	INDEX lea_complaints_actor_id_idx (actor_id ASC),
	INDEX lea_complaints_account_type_account_id_idx (account_type ASC, account_id ASC)
);
COMMENT ON TABLE public.lea_complaints IS 'stores the law enforcements agencies(LEA) complaints information';
COMMENT ON COLUMN public.lea_complaints.actor_id IS 'stores the actor id associated with the complaints';
COMMENT ON COLUMN public.lea_complaints.account_type IS 'stores the account type associated with the complaints';
COMMENT ON COLUMN public.lea_complaints.account_id IS 'stores the account id associated with the complaints';
COMMENT ON COLUMN public.lea_complaints.external_reference_number IS 'stores the external reference number generated by the partner bank';
COMMENT ON COLUMN public.lea_complaints.audit_date IS 'stores the action taken on the account date';
COMMENT ON COLUMN public.lea_complaints.init_sol_id IS 'stores the banks partner, of which the user has initially created the account';
COMMENT ON COLUMN public.lea_complaints.audit_bod_date IS 'audit branch opening date, this is a date on which the status gets updated in branch as per MIS';
COMMENT ON COLUMN public.lea_complaints.account_closure_date IS 'account closure date as per bank records';
COMMENT ON COLUMN public.lea_complaints.freeze_code IS 'indicates type of freeze applied credit freeze/debit freeze etc. It is a part of Enum AccountFreezeStatus in api/risk/enums';
COMMENT ON COLUMN public.lea_complaints.freeze_reasons IS 'narration with the freeze code';
COMMENT ON COLUMN public.lea_complaints.remarks IS 'Remarks with complaints date, source and other details. follows freeze reason 1, 2, 3, 4, 5 respectively';
COMMENT ON COLUMN public.lea_complaints.additional_details IS 'additional details linked to the complaint';
CREATE TABLE public.rule_review_type_mappings (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	rule_id STRING NOT NULL,
	review_type STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT rule_review_type_mappings_pkey PRIMARY KEY (id ASC),
	INDEX rule_review_type_mappings_updated_at_idx (updated_at ASC),
	UNIQUE INDEX rule_review_type_mappings_rule_id_review_type_deleted_at_unix_key (rule_id ASC, review_type ASC, deleted_at_unix ASC)
);
COMMENT ON TABLE public.rule_review_type_mappings IS 'stores the review type against rule';
COMMENT ON COLUMN public.rule_review_type_mappings.rule_id IS 'stores the rule id';
COMMENT ON COLUMN public.rule_review_type_mappings.review_type IS '"proto_type":"risk.case_management.review.enums", "comment":"review required against rule", "ref":"api.risk.case_management.review.enums"';
CREATE TABLE public.risky_user_required_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	question STRING NOT NULL,
	question_type STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT risky_user_required_infos_pkey PRIMARY KEY (id ASC),
	INDEX risky_user_required_infos_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.risky_user_required_infos IS 'stores the information needed from the risky users';
COMMENT ON COLUMN public.risky_user_required_infos.question IS 'stores the question needed to ask to the risky users';
COMMENT ON COLUMN public.risky_user_required_infos.question_type IS '"proto_type":"risk.case_management.risky_user_required_info.question_type", "comment":"stores the type of question needed to asked to the risky users, "ref":"api.risk.case_management.risky_user_required_info"';
CREATE TABLE public.risky_user_required_info_mappings (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	entity_type STRING NOT NULL,
	entity_id STRING NOT NULL,
	risky_user_required_info_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT risky_user_required_info_mappings_pkey PRIMARY KEY (id ASC),
	INDEX risky_user_required_info_mappings_updated_at_idx (updated_at ASC),
	INDEX risky_user_required_info_mappings_entity_type_entity_id_idx (entity_type ASC, entity_id ASC)
);
COMMENT ON TABLE public.risky_user_required_info_mappings IS 'stores the information needed from the risky users';
COMMENT ON COLUMN public.risky_user_required_info_mappings.entity_type IS '"proto_type":"risk.case_management.risky_user_required_info_mapping.entity_type", "comment":"stores the entity type mapping" "ref":"api.risk.case_management.risky_user_required_info_mapping"';
COMMENT ON COLUMN public.risky_user_required_info_mappings.entity_id IS 'stores the entity id';
COMMENT ON COLUMN public.risky_user_required_info_mappings.risky_user_required_info_id IS 'stores the required info id needed for risky user';
