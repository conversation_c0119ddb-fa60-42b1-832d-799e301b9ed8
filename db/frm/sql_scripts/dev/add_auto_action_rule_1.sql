-- This will create and enable the given snowflake rule for common creditor and debtor with lea
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)
VALUES ('AutoFreeze_CommonCreditorDebtorWithLEA', 1, 'Common creditor and debtor with lea', 'EVALUATION_METHOD_CONDITIONAL', 'AutoFreeze_CommonCreditorDebtorWithLEA_1', 'PROVENANCE_SNOWFLAKE_RULES', 70, 'RULE_STATE_ACTIVE', 'ENTITY_TYPE_TRANSACTION', '');
