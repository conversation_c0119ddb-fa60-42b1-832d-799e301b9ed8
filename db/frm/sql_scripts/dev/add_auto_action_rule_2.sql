-- This will create and enable the given snowflake rule for common creditor and debtor with lea
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)
VALUES ('AutoFreeze_NalsanEnterprises_RiskyMerchant', 1, 'AutoFreeze_NalsanEnterprises_RiskyMerchant', 'EVALUATION_METHOD_CONDITIONAL', 'AutoFreeze_NalsanEnterprises_RiskyMerchant_1', 'PROVENANCE_SNOWFLAKE_RULES', 70, 'RULE_STATE_ACTIVE', 'ENTITY_TYPE_TRANSACTION', '');
