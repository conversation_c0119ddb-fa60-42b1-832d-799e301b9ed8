-- After moving afu to risk screener default review type will be user review hence need to map afu review types against risk screener rule.
-- DS_AFU_RISK_MODEL_3
INSERT INTO rule_review_type_mappings (rule_id, review_type)
VALUES ('695cd810-e3ea-45ae-965e-9dc674e0fce0', 'REVIEW_TYPE_AFU_REVIEW');

-- DS_AFU_RISK_MODEL_2
INSERT INTO rule_review_type_mappings (rule_id, review_type)
VALUES ('effad010-276f-4fb6-90e6-a75837d9661d', 'REVIEW_TYPE_AFU_REVIEW');

-- DS_AFU_RISK_MODEL_1
INSERT INTO rule_review_type_mappings (rule_id, review_type)
VALUES ('ab0c76db-31ab-4804-9e4d-a7340d7548c9', 'REVIEW_TYPE_AFU_REVIEW');
