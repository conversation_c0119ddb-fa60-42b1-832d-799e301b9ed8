-- This will create and enable the given snowflake rule
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)
VALUES ('MEDIUM_DEBIT_TXN_AMT_NON_HIGH_CREDIT_UPI_TXN_L7D_CNT_REPORTED_CONTACT_ASSOCIATION', 1, 'Last 7 day Non UPI transaction Medium Confidence', 'EVALUATION_METHOD_CONDITIONAL', 'MEDIUM_DEBIT_TXN_AMT_NON_HIGH_CREDIT_UPI_TXN_L7D_CNT_REPORTED_CONTACT_ASSOCIATION_1', 'PROVENANCE_SNOWFLAKE_RULES', 70, 'RULE_STATE_ACTIVE', 'ENTITY_TYPE_TRANSACTION', '');
