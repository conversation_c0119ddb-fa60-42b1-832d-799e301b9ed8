insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_ATM_WITHDRAWAL_ALL_LOCATIONS_SUSPICIOUS_ATMS',1,'Actors  who withdraw a cumulative amount of atleast  5k from risky atms (flagged atms with >65% fraud txns) within 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_ATM_WITHDRAWAL_ALL_LOCATIONS_SUSPICIOUS_ATMS_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_ATM_WITHDRAWAL_RISKY_LOCATIONS_SUSPICIOUS_ATMS ',1,'Actors who withdraw a cumulative amount of >=5k from risky atms (flagged atms with >45% fraud txns) in 24 hrs and have been onboarded from risky states ("ASSAM" "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" "RAJASTH<PERSON>" "WEST BENGAL") ','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_ATM_WITHDRAWAL_RISKY_LOCATIONS_SUSPICIOUS_ATMS _1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_DEBIT_TXN_AMT_HIGH_CREDIT_UPI_TXN_CNT',1,'Actors who debits a cumulative amount of >= ₹100000  and receive credit from >= 25 number of UPI transactions within 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_DEBIT_TXN_AMT_HIGH_CREDIT_UPI_TXN_CNT_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_DEBITS_TO_MULTIPLE_DIFFERENT_SOURCES',1,'Actors who debited >= ₹10000 each time to >= 10 different persons in 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_DEBITS_TO_MULTIPLE_DIFFERENT_SOURCES_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_CREDIT_FROM_KNOWN_FRAUDSTERS',1,'Actors who receive >=₹20000 funds from known fraudsters (not obtained NOC and have at least 2 complaints against them)  or receives funds>=3 times from them in 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_CREDIT_FROM_KNOWN_FRAUDSTERS_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_PENNY_DROP_ABUSE',1,'Actors with >= 20 Re 1 or Re 2 credit transactions in their Fi account within 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_PENNY_DROP_ABUSE_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MULTIPLE_CREDITS_IMMEDIATE_DEBIT_CRYPTO_L30D_ACC_CREATED',1,'Actors who perform credit transactions for >1 times with a cumulative credit amount of  >=₹20000 in an hour and performs debit transactions on crypto merchants within next 60 minutes of making credit transactions with cumulative debit amount in the range of 80%-120% of cumulative credit amount and the user has created the account within last 30 days of transaction date','EVALUATION_METHOD_CONDITIONAL','DA_MULTIPLE_CREDITS_IMMEDIATE_DEBIT_CRYPTO_L30D_ACC_CREATED_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MULTIPLE_CREDITS_IMMEDIATE_DEBIT_CRYPTO_GT30D_ACC_CREATED',1,'Actors who perform credit transactions for >1 times with a cumulative credit amount of  >=₹20000 in an hour and performs debit transactions on crypto merchants within next 60 minutes of making credit transactions with cumulative debit amount in the range of 80%-120% of cumulative credit amount and the user has created the account before the last 30 days of transaction date','EVALUATION_METHOD_CONDITIONAL','DA_MULTIPLE_CREDITS_IMMEDIATE_DEBIT_CRYPTO_GT30D_ACC_CREATED_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_DEBIT_CRYPTO',1,'Actors who perform debit transaction on crypto merchants or add crypto/binance in transaction remarks with a cumulative amount>=10000 within 24 hrs  ','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_DEBIT_CRYPTO_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MULTIPLE_CREDITS_HIGH_DEBIT_PMB',1,'Actors who perform >=2 credit transactions with a cumulative credit amount >=6000 in a 4 hr window and performs debit transactions through PMB in the same 4 hr window with a cumulative debit amount > 80 % of cumulative credit amount ','EVALUATION_METHOD_CONDITIONAL','DA_MULTIPLE_CREDITS_HIGH_DEBIT_PMB_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_CREDIT_MULTIPLE_DEBITS_PMB',1,'Actors who perform high cumulative credit transaction in a 4 hr window and performs >=2 debit transactions through PMB in the same 4 hr window with a cumulative debit amount > 80 % of cumulative credit amount and the cumuative debit amount>=6000 ','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_CREDIT_MULTIPLE_DEBITS_PMB_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_CREDIT_FROM_DIFFERENT_SOURCES_IMMEDIATE_DEBIT_GT1K',1,'Actors who  credited >= ₹1000 each time from >= 7 different persons in 1 hr, and debits >= 80% of credit amount within following 1 hour','EVALUATION_METHOD_CONDITIONAL','DA_CREDIT_FROM_DIFFERENT_SOURCES_IMMEDIATE_DEBIT_GT1K_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_AFU_MULTIPLE_HIGH_CREDITS_IMMEDIATE_DEBIT',1,'For Users who did a Phone/ Device AFU,
Atleast 2 out of 6 days since AFU have large, freq credits followed by almost full debit of the amt
     ( Credit amt in one day >= 40,000  &
       Credit Txns in the same day > 3 &
       Total credit amount is between 95% to 105% of the Total debit amount in the same day)','EVALUATION_METHOD_CONDITIONAL','DA_AFU_MULTIPLE_HIGH_CREDITS_IMMEDIATE_DEBIT_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_FIFO_V1',1,'
1. Total Credit amount in last 5 days >= 500000 and Total Credit transactions in last 5 days > 30
And
2. Total Credit amount in last 5 days is between 95% to 105% of the
    Total Debit amount (This checks if credit and debit amount are always same)
','EVALUATION_METHOD_CONDITIONAL','DA_FIFO_V1_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_CREDIT_FROM_GOLDEN_SET_OF_FRAUDSTERS',1,'Actors with (> 30K credit or >= 3 times credit in 24 hrs of 5000 each amount) from any fraudster (golden data set)','EVALUATION_METHOD_CONDITIONAL','DA_CREDIT_FROM_GOLDEN_SET_OF_FRAUDSTERS_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_DEBIT_TXN_AMT',1,'Actors who debited a cumulative amount of >= ₹100000 in 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_DEBIT_TXN_AMT_1','PROVENANCE_SNOWFLAKE_RULES',44,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MULTIPLE_HIGH_DEBITS_TO_SAME_SOURCES',1,'Actors who debit >=₹5000 in every transaction to the same person/account. Alert actors who have done this type of debit transactions>=5 times in 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_MULTIPLE_HIGH_DEBITS_TO_SAME_SOURCES_1','PROVENANCE_SNOWFLAKE_RULES',44,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_FAST_IN_FAST_OUT',1,'Actors who credited >= ₹5000 in a single transaction and within 30 minutes of credit, performs debit transactions resulting a total debit in the range of 80%-120% of credit amount.  Alert actors which have done this type of credit/debit transaction >= 3 times within 24 hrs time window','EVALUATION_METHOD_CONDITIONAL','DA_FAST_IN_FAST_OUT_1','PROVENANCE_SNOWFLAKE_RULES',44,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MEDIUM_DEBIT_TXN_AMT_NON_HIGH_CREDIT_UPI_TXN_L7D_CNT_REPORTED_CONTACT_ASSOCIATION',1,'Actors who debited cumulative amounts in the range of [₹50000,₹100000) within 24 hrs and have received credits through < 70 UPI transactions in the last 7 days. Out of all the identified actors alert the ones which are associated with already reported actors. (Either through referral or contact list association).','EVALUATION_METHOD_CONDITIONAL','DA_MEDIUM_DEBIT_TXN_AMT_NON_HIGH_CREDIT_UPI_TXN_L7D_CNT_REPORTED_CONTACT_ASSOCIATION_1','PROVENANCE_SNOWFLAKE_RULES',44,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_ATM_TXN_CNT',1,'Actors who have done >= 5 ATM withdrawals within 24 hrs time window','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_ATM_TXN_CNT_1','PROVENANCE_SNOWFLAKE_RULES',44,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MEDIUM_DEBIT_TXN_AMT',1,'Actors who debited a cumulative amount in the range of [50000,100000) within 24 hrs time window','EVALUATION_METHOD_CONDITIONAL','DA_MEDIUM_DEBIT_TXN_AMT_1','PROVENANCE_SNOWFLAKE_RULES',44,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_SALARY_DISCREPANCY_GT100X',1,'Actors who receive a cumulative credit of amount >100 times the monthly salary declared in a month','EVALUATION_METHOD_CONDITIONAL','DA_SALARY_DISCREPANCY_GT100X_1','PROVENANCE_SNOWFLAKE_RULES',44,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_PMB_WITHDRAWAL_HIGH_CREDIT_UPI_CNT',1,'Actors that debited >= ₹50000 or have done >= 5 debit transactions in 24 hrs through partner bank’s mobile banking services and have received credits through >= 8 UPI transactions in same time window','EVALUATION_METHOD_CONDITIONAL','DA_PMB_WITHDRAWAL_HIGH_CREDIT_UPI_CNT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_CREDIT_IMMEDIATE_ATM_PMB',1,'Actors who credited >= ₹9999 in a single transaction and within 60 minutes of credit, performs a debit transaction of amount> 50 % of credit amount through ATM or Partner Mobile Banking i.e. > ₹(0.5*9999) . Alert actors which have done this type of credit/debit transaction >= 3 times within 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_CREDIT_IMMEDIATE_ATM_PMB_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_DEBIT_TXN_AMT_NON_HIGH_CREDIT_UPI_TXN_CNT_LOW_PMB_CREDIT_L7D_AMT',1,'Actors who debited >= ₹100000 and received credit from <= 25 number of UPI transactions in 24 hrs and have received sums of ₹1 to ₹45000 from partner mobile banking in the last 7 days.','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_DEBIT_TXN_AMT_NON_HIGH_CREDIT_UPI_TXN_CNT_LOW_PMB_CREDIT_L7D_AMT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MULTIPLE_HIGH_DEBITS_TO_SAME_SOURCES_PMB_L7D_CREDIT',1,'Actors who debited >= ₹5000 to the same person/account >= 5 times within 24 hrs and have credited  >= 1 PMB transaction in the last 7 days','EVALUATION_METHOD_CONDITIONAL','DA_MULTIPLE_HIGH_DEBITS_TO_SAME_SOURCES_PMB_L7D_CREDIT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MULTIPLE_HIGH_CREDITS_FROM_SAME_SOURCES',1,'Actors who receives >=₹10000 in every transaction from a single source. Alert actors who have done this type of credit transactions>=5 times in 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_MULTIPLE_HIGH_CREDITS_FROM_SAME_SOURCES_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_CREDIT_FROM_KNOWN_FRAUDSTERS_ONE_COMPLAINT',1,'Actors who receive >=₹20000 funds from known fraudsters (not obtained NOC and have exactly 1 complaint against them) or receives funds>=3 times from them in 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_CREDIT_FROM_KNOWN_FRAUDSTERS_ONE_COMPLAINT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_PMB_WITHDRAWAL',1,'Actors who debit >= ₹50000 or perform >=5 debit transactions in 24 hrs through partner mobile banking services','EVALUATION_METHOD_CONDITIONAL','DA_PMB_WITHDRAWAL_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_CREDITS_FROM_MULTIPLE_DIFFERENT_SOURCES',1,'Actors who credited >= ₹5000 each time from >= 10 different persons in 24 hrs time window','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_CREDITS_FROM_MULTIPLE_DIFFERENT_SOURCES_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_SMALL_DEBIT_IMMEDIATE_HIGH_CREDIT',1,'Actors who credited >= ₹4999 in a single transaction immediately within 60 minutes of a debit transaction <= ₹10, Alert actors which have done the above type of credit/debit transaction >= 3 times within 24 hrs time window','EVALUATION_METHOD_CONDITIONAL','DA_SMALL_DEBIT_IMMEDIATE_HIGH_CREDIT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MULTIPLE_HIGH_DEBITS_TO_SAME_SOURCES_NO_PMB_L7D_CREDIT_HIGH_CREDIT_UPI_L7D_AMT',1,'Actors who debited big amounts >= ₹5000 to the same person/account >=5 times within 24 hrs and have done no PMB transactions in the last 7 days but have received >= ₹300000  from UPI in the last 7 days.','EVALUATION_METHOD_CONDITIONAL','DA_MULTIPLE_HIGH_DEBITS_TO_SAME_SOURCES_NO_PMB_L7D_CREDIT_HIGH_CREDIT_UPI_L7D_AMT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_CREDIT_TXN_CNT',1,'Actors who have done >=50 credit transactions within 24 hrs ','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_CREDIT_TXN_CNT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MEDIUM_DEBIT_TXN_AMT_HIGH_CREDIT_UPI_TXN_L7D_CNT',1,'Actors who debited cumulative amounts in the range of [₹50000,₹100000) within 24 hrs and received credits through >= 70 UPI transactions in the last 7 days','EVALUATION_METHOD_CONDITIONAL','DA_MEDIUM_DEBIT_TXN_AMT_HIGH_CREDIT_UPI_TXN_L7D_CNT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_MULTIPLE_CREDITS_IMMEDIATE_DEBIT',1,'Actors who credited cumulative amount of >= ₹2000 in >1 successful attempts in an hour followed by debit transactions of cumulative amount in the range of 80%-120% of cumulative credit amount performed within next 60 minutes of credit transactions.  Alert actors which have done this type of credit/debit transaction >= 3 times in 24 hrs.','EVALUATION_METHOD_CONDITIONAL','DA_MULTIPLE_CREDITS_IMMEDIATE_DEBIT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('DA_HIGH_DEBIT_TXN_CNT',1,'Actors who debit >= 50 times in 24 hrs','EVALUATION_METHOD_CONDITIONAL','DA_HIGH_DEBIT_TXN_CNT_1','PROVENANCE_SNOWFLAKE_RULES',50,'RULE_STATE_ACTIVE','ENTITY_TYPE_TRANSACTION','');
