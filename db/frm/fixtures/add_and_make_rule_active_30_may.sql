update
	rules
set
	state = 'RULE_STATE_ACTIVE'
where
	external_id in ('HIGH_DEBITS_TO_MULTIPLE_DIFFERENT_SOURCES:0:1801',
	                'HIGH_DEBITS_TO_MULTIPLE_DIFFERENT_SOURCES:0:2001',
	                'HIGH_CREDIT_MULTIPLE_DEBITS_PMB:0:1807',
	                'HIGH_CREDIT_MULTIPLE_DEBITS_PMB:0:1907',
	                'HIGH_CREDIT_MULTIPLE_DEBITS_PMB:0:2007',
	                'PMB_WITHDRAWAL_HIGH_CREDIT_UPI_CNT:0:1808',
					'PMB_WITHDRAWAL_HIGH_CREDIT_UPI_CNT:0:2008',
					'HIGH_DEBIT_TXN_AMT_HIGH_CREDIT_UPI_TXN_CNT:0:2000',
					'HIGH_DEBIT_TXN_AMT_HIGH_CREDIT_UPI_TXN_CNT:0:1900',
					'HIGH_DEBIT_TXN_AMT_HIGH_CREDIT_UPI_TXN_CNT:0:1800',
					'PENNY_DROP_ABUSE:1:1802',
					'PENNY_DROP_ABUSE:1:2002',
					'PENNY_DROP_ABUSE:1:1902',
					'MULTIPLE_CREDITS_HIGH_DEBIT_PMB:0:1806',
					'MULTIPLE_CREDITS_HIGH_DEBIT_PMB:0:1906',
					'MULTIPLE_CREDITS_HIGH_DEBIT_PMB:0:2006');

insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)Values('HUNTER_API_VALIDATION',1,'Actors who have low credit score','EVALUATION_METHOD_CONDITIONAL','HUNTER_API_VALIDATION_1','PROVENANCE_SNOWFLAKE_RULES',60,'RULE_STATE_ACTIVE','ENTITY_TYPE_USER','');
