-- This will create and enable the given snowflake rule
insert into rules (name, version, description, evaluation_method, external_id, provenance, confidence_score, state, assessed_entity_type, suspect_entity)
VALUES ('HIGH_DEBIT_TXN_AMT', 1, 'High amount of Debit Txn', 'EVALUATION_METHOD_CONDITIONAL', 'HIGH_DEBIT_TXN_AMT_1', 'PROVENANCE_SNOWFLAKE_RULES', 70, 'RULE_STATE_ACTIVE', 'ENTITY_TYPE_TRANSACTION', '');
