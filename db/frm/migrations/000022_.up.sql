ALTER TABLE frm.public.review_actions ADD COLUMN status STRING NULL FAMILY "frequently_updated";
ALTER TABLE frm.public.review_actions ADD COLUMN processing_type STRING NULL FAMILY "frequently_updated";
COMMENT ON COLUMN frm.public.review_actions.status IS '"comment":"indicates current status of the action", "ref":"api.risk.case_management.review.enums.ActionStatus"';
COMMENT ON COLUMN frm.public.review_actions.processing_type IS '"comment":"indicates processing type of action", "ref":"api.risk.case_management.review.enums.ActionProcessingType"';
