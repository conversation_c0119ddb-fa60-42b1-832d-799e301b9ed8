CREATE TABLE IF NOT EXISTS alerts
(
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	case_id int,
	actor_id string,
	account_type string,
	account_id string,
	entity_type string NOT NULL,
	entity_id string NOT NULL,
	rule_id string NOT NULL,
	batch_name string,
	verdict string,
	created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	deleted_at TIMESTAMPTZ,
	PRIMARY KEY(id ASC),
	INDEX alerts_updated_at_idx (updated_at ASC),
	INDEX alerts_actor_id_account_id_idx (actor_id ASC, account_id ASC) where actor_id != null,
	INDEX alerts_entity_type_entity_id_idx (entity_type ASC, entity_id ASC),
	INDEX alerts_actor_id_created_at_idx (actor_id, created_at ASC),
	INDEX alerts_case_id (case_id ASC)
	);
COMMENT ON TABLE alerts IS 'table to store risky alerts on a entity based on certain rule triggered';
COMMENT ON COLUMN alerts.case_id IS 'stores the id of the case created for the alert';
COMMENT ON COLUMN alerts.actor_id IS 'stores the id of the actor against which the alert got generated';
COMMENT ON COLUMN alerts.account_id IS 'stores the id of the account against which the alert got generated';
COMMENT ON COLUMN alerts.entity_type IS 'stores the entity type such as transaction, liveness etc';
COMMENT ON COLUMN alerts.entity_id IS 'stores the entity id sucn as txn id, liveness id';
COMMENT ON COLUMN alerts.rule_id IS 'stores the rule id by which alert got triggered';
COMMENT ON COLUMN alerts.batch_name IS 'stores the batch identifier for the manual bulk upload';
COMMENT ON COLUMN alerts.verdict IS 'stores the analyst pass/fail after the review being done';
COMMENT ON INDEX alerts_updated_at_idx IS 'helps in piping the data to snowflake';
COMMENT ON INDEX alerts_actor_id_account_id_idx IS 'helps in collate the alerts based on actor id or account id';
COMMENT ON INDEX alerts_entity_type_entity_id_idx IS 'helps in getting the information specific to entity';
COMMENT ON INDEX alerts_actor_id_created_at_idx IS 'helps in dedupe the alerts based on the case_id and the recency';
COMMENT ON INDEX alerts_case_id IS 'helps in getting the alerts with the same case id';
