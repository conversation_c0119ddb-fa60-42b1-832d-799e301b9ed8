CREATE TABLE IF NOT EXISTS allowed_annotations
(
	id             UUID        	   NOT NULL DEFAULT gen_random_uuid(),
	entity_type string not null,
	annotation_type string not null,
	value string not null,
	added_by_email string not null,
	created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at     TIMESTAMPTZ,
	PRIMARY KEY(id ASC),
	UNIQUE 		   (entity_type, annotation_type, value),
	INDEX annotation_libraries_updated_at_idx (updated_at ASC)
	);

COMMENT ON TABLE allowed_annotations IS 'will store all the annotations possible for the risk reviews';

COMMENT ON COLUMN allowed_annotations.entity_type IS 'stores the type of entity on which this annotation is part of';
COMMENT ON COLUMN allowed_annotations.annotation_type IS 'stores the type of an annotation i.e. live-ness id or a entity level annotation or any other type e.g., user.profile.address';
COMMENT ON COLUMN allowed_annotations.value IS 'annotation value such as crypto etc';
COMMENT ON COLUMN allowed_annotations.added_by_email IS 'analyst email for audit purposes';

COMMENT ON INDEX annotation_libraries_updated_at_idx IS 'helps in downstream piping to data lake';
