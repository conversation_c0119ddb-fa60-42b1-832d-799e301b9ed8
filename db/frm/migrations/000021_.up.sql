ALTER TABLE alerts ADD COLUMN IF NOT EXISTS handling_type string NULL;
ALTER TABLE alerts ADD COLUMN IF NOT EXISTS handling_reasons string[] NULL;
COMMENT ON COLUMN alerts.handling_type IS '"proto_type":"risk.case_management.enums.AlertHandlingType", "comment":"specifies how alert was handled"';
COMMENT ON COLUMN alerts.handling_reasons IS '"proto_type":"risk.case_management.enums.AlertHandlingReason", "comment":"reasons for a specific handling of the alert"';
