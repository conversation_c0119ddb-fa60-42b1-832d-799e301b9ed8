CREATE TABLE IF NOT EXISTS annotations
(
	id             UUID        	   NOT NULL DEFAULT gen_random_uuid(),
	entity_type string not null,
	entity_id string not null,
	allowed_annotation_id string not null,
	added_by_email string not null,
	created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at     TIMESTAMPTZ,
	PRIMARY KEY(id ASC),
	INDEX annotations_updated_at_idx (updated_at ASC),
	INDEX annotations_entity_type_entity_id_idx (entity_type asc, entity_id asc)
	);

COMMENT ON TABLE annotations IS 'will store all the annotations against the entity';

COMMENT ON COLUMN annotations.entity_type IS 'stores the type of entity for which the annotation is part of';
COMMENT ON COLUMN annotations.entity_id IS 'stores the entity_id against which the annotation is';
COMMENT ON COLUMN annotations.allowed_annotation_id IS 'annotation id';
COMMENT ON COLUMN annotations.added_by_email IS 'analyst email for audit purposes';

COMMENT ON INDEX annotations_updated_at_idx IS 'helps in downstream piping to data lake';
COMMENT ON INDEX annotations_entity_type_entity_id_idx IS 'helps in getting all the annotation for the combination';
