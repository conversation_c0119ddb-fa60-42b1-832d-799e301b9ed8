CREATE TABLE public.risky_user_required_infos (
												  id UUID NOT NULL DEFAULT gen_random_uuid(),
												  question STRING NOT NULL,
												  question_type STRING NOT NULL,
												  created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIM<PERSON><PERSON>MPTZ,
												  updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
												  deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
												  PRIMARY KEY(id ASC),
												  INDEX risky_user_required_infos_updated_at_idx (updated_at ASC)
);

COMMENT ON TABLE risky_user_required_infos IS 'stores the information needed from the risky users';

COMMENT ON COLUMN risky_user_required_infos.question IS 'stores the question needed to ask to the risky users';
COMMENT ON COLUMN risky_user_required_infos.question_type IS '"proto_type":"risk.case_management.risky_user_required_info.question_type", "comment":"stores the type of question needed to asked to the risky users, "ref":"api.risk.case_management.risky_user_required_info"';
