CREATE TABLE IF NOT EXISTS comments
(
	id             UUID        	   NOT NULL DEFAULT gen_random_uuid(),
	entity_type string not null,
	entity_id string not null,
	comment_type string not null,
	comment string not null,
	added_by_email string not null,
	created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at     TIMESTAMPTZ,
	PRIMARY KEY(id ASC),
	INDEX comments_updated_at_idx (updated_at ASC),
	INDEX comments_entity_type_entity_id_idx (entity_type asc, entity_id asc)
	);

COMMENT ON TABLE comments IS 'will store all the comments against the entity';

COMMENT ON COLUMN comments.entity_type IS 'stores the type of entity against which the comment is added';
COMMENT ON COLUMN comments.entity_id IS 'stores the entity_id against which the comment is added';
COMMENT ON COLUMN comments.comment_type IS 'type against which the comments is added it can be kyc.field_name, kyc etc';
COMMENT ON COLUMN comments.comment IS 'review comments';
COMMENT ON COLUMN comments.added_by_email IS 'analyst email for audit purposes';

COMMENT ON INDEX comments_updated_at_idx IS 'helps in downstream piping to data lake';
COMMENT ON INDEX comments_entity_type_entity_id_idx IS 'helps in getting all the annotation for the combination';
