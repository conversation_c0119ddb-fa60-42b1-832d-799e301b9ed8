CREATE TABLE IF NOT EXISTS public.rule_review_type_mappings
(
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	rule_id STRING NOT NULL,
	review_type STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	PRIMARY KEY (id ASC),
	INDEX (updated_at ASC),
	UNIQUE (rule_id, review_type, deleted_at_unix)
);

COMMENT ON TABLE rule_review_type_mappings IS 'stores the review type against rule';

COMMENT ON COLUMN rule_review_type_mappings.rule_id IS 'stores the rule id';
COMMENT ON COLUMN rule_review_type_mappings.review_type IS '"proto_type":"risk.case_management.review.enums", "comment":"review required against rule", "ref":"api.risk.case_management.review.enums"';
