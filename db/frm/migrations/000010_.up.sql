CREATE TABLE IF NOT EXISTS lea_complaints
(
	id             UUID        	   NOT NULL DEFAULT gen_random_uuid(),
	actor_id	string	not null,
	account_type	string not null,
	account_id string not null,
	external_reference_number string not null,
	audit_date TIMESTAMPTZ not null,
	init_sol_id	string not null,
	audit_bod_date TIMESTAMPTZ not null,
	account_closure_date TIMESTAMPTZ,
	freeze_code string,
	freeze_reasons string[],
	remarks string[],
	created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	INDEX lea_complaints_updated_at_idx (updated_at ASC),
	INDEX lea_complaints_actor_id_idx (actor_id ASC),
	INDEX lea_complaints_account_type_account_id_idx (account_type ASC, account_id ASC)
);

COMMENT ON TABLE lea_complaints IS 'stores the law enforcements agencies(LEA) complaints information';

COMMENT ON COLUMN lea_complaints.actor_id IS 'stores the actor id associated with the complaints';
COMMENT ON COLUMN lea_complaints.account_type IS 'stores the account type associated with the complaints';
COMMENT ON COLUMN lea_complaints.account_id IS 'stores the account id associated with the complaints';
COMMENT ON COLUMN lea_complaints.external_reference_number IS 'stores the external reference number generated by the partner bank';
COMMENT ON COLUMN lea_complaints.audit_date IS 'stores the action taken on the account date';
COMMENT ON COLUMN lea_complaints.init_sol_id IS 'stores the banks partner, of which the user has initially created the account';
COMMENT ON COLUMN lea_complaints.audit_bod_date IS 'audit branch opening date, this is a date on which the status gets updated in branch as per MIS';
COMMENT ON COLUMN lea_complaints.account_closure_date IS 'account closure date as per bank records';
COMMENT ON COLUMN lea_complaints.freeze_code IS 'indicates type of freeze applied credit freeze/debit freeze etc. It is a part of Enum AccountFreezeStatus in api/risk/enums';
COMMENT ON COLUMN lea_complaints.freeze_reasons IS 'narration with the freeze code';
COMMENT ON COLUMN lea_complaints.remarks IS 'Remarks with complaints date, source and other details. follows freeze reason 1, 2, 3, 4, 5 respectively';
