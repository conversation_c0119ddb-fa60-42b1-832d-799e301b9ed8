AlTER TABLE alerts DROP COLUMN IF EXISTS case_id;
ALTER TABLE alerts ADD COLUMN case_id string;
DROP INDEX IF EXISTS alerts_actor_id_account_id_idx;
DROP INDEX IF EXISTS alerts_entity_type_entity_id_idx;
DROP INDEX IF EXISTS alerts_case_id;

CREATE INDEX IF NOT EXISTS alerts_entity_type_entity_id_created_at_idx on alerts (entity_type ASC, entity_id ASC,
                                                                                  created_at DESC);
CREATE INDEX IF NOT EXISTS alerts_account_id_created_at_idx on alerts (account_id ASC, created_at DESC);
CREATE INDEX IF NOT EXISTS alerts_case_id_created_at_idx on alerts (case_id ASC, created_at DESC);

COMMENT ON INDEX alerts_entity_type_entity_id_created_at_idx IS 'helps in getting the recent alerts based on entity id';
COMMENT ON INDEX alerts_account_id_created_at_idx IS 'helps in getting the recent alerts based on account id';
COMMENT ON INDEX alerts_case_id_created_at_idx IS 'helps in getting the recent alerts based on case id';
COMMENT ON COLUMN alerts.case_id IS 'stores the id of the case created for the alert'
