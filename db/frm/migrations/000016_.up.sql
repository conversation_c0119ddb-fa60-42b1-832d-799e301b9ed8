CREATE TABLE public.risky_user_required_info_mappings (
														  id UUID NOT NULL DEFAULT gen_random_uuid(),
														  entity_type STRING NOT NULL,
														  entity_id STRING NOT NULL,
														  risky_user_required_info_id STRING NOT NULL,
														  created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
														  updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
														  deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
														  PRIMARY KEY(id ASC),
														  INDEX risky_user_required_info_mappings_updated_at_idx (updated_at ASC),
														  INDEX risky_user_required_info_mappings_entity_type_entity_id_idx (entity_type ASC, entity_id ASC)

);

COMMENT ON TABLE risky_user_required_info_mappings IS 'stores the information needed from the risky users';

COMMENT ON COLUMN risky_user_required_info_mappings.entity_type IS '"proto_type":"risk.case_management.risky_user_required_info_mapping.entity_type", "comment":"stores the entity type mapping" "ref":"api.risk.case_management.risky_user_required_info_mapping"';
COMMENT ON COLUMN risky_user_required_info_mappings.entity_id IS 'stores the entity id';
COMMENT ON COLUMN risky_user_required_info_mappings.risky_user_required_info_id IS 'stores the required info id needed for risky user';



