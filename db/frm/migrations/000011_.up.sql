CREATE TABLE IF NOT EXISTS suggested_actions
(
	id             UUID        	   NOT NULL DEFAULT gen_random_uuid(),
	rule_id	string	not null,
	suggested_action_type	string not null,
	request_reason string not null,
	created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	PRIMARY KEY(id ASC),
	INDEX suggested_actions_updated_at_idx (updated_at ASC),
	UNIQUE 		   (rule_id, suggested_action_type, deleted_at_unix)
);

COMMENT ON TABLE suggested_actions IS 'stores the suggested action against the rule';

COMMENT ON COLUMN suggested_actions.rule_id IS 'stores the rule id for which the actions needs to be performed';
COMMENT ON COLUMN suggested_actions.suggested_action_type IS 'stores the type of actions the rule is suggesting. It is part of enum SuggestedActionType in risk.case_management';
COMMENT ON COLUMN suggested_actions.request_reason IS 'stores the reason for the actions. It is part of RequestReason enum in risk.enums';

COMMENT ON INDEX suggested_actions_updated_at_idx IS 'helps in piping the data into snowflake';
