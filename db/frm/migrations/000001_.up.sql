CREATE TABLE IF NOT EXISTS review_actions
(
	id             UUID        	   	NOT NULL DEFAULT gen_random_uuid(),
	case_id        STRING		   	NOT NULL,
	review_type    STRING 		   	NOT NULL,
	action_type    STRING,
	parameters     JSONB,
	source 		   STRING,
	analyst_email  STRING,
	initiated_at   TIMESTAMPTZ,
	created_at     TIMESTAMPTZ 		NOT NULL DEFAULT now(),
	updated_at     TIMESTAMPTZ 		NOT NULL DEFAULT now(),

    CONSTRAINT 	   "primary" 		PRIMARY KEY (case_id, id),
	UNIQUE 		   (id),
	INDEX		   review_actions_updated_at_index(updated_at),

    FAMILY "frequently_updated" (updated_at),
	-- Contains column which are never updated after insertion
	FAMILY "primary" (id, case_id, review_type, action_type, source, parameters, analyst_email, initiated_at, created_at)
);

COMMENT ON TABLE review_actions IS 'risk-case-management: This table will contain logs of all the actions against a case';
COMMENT ON COLUMN review_actions.review_type IS '"proto_type":"risk.case_store.enums", "comment":"type of review for which action was taken", "ref":"api.risk.case_store.enums.proto"';
COMMENT ON COLUMN review_actions.action_type IS '"proto_type":"risk.case_management.review.enums", "comment":"action type enum  which particular action was taken", "ref":"api.risk.case_management.review.enums.proto"';
COMMENT ON COLUMN review_actions.parameters IS '"proto_type":"risk.case_management.review.action", "comment":"for each action type we will have some action specific parameters, "ref":"api.risk.case_management.review.action.proto"';
COMMENT ON COLUMN review_actions.source IS '"proto_type":"risk.case_management.review.enums", "comment":"identify from where the action was taken", "ref":"api.risk.case_management.review.enums.proto"';
COMMENT ON COLUMN review_actions.analyst_email IS 'email of analyst taking the action';
COMMENT ON COLUMN review_actions.initiated_at IS 'time at which action was initiated from the source system';

