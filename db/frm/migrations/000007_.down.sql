AlTER TABLE alerts DROP COLUMN IF EXISTS case_id;
ALTER TABLE alerts ADD COLUMN case_id int8;

DROP INDEX IF EXISTS alerts_entity_type_entity_id_created_at_idx;
DROP INDEX IF EXISTS alerts_account_id_created_at_idx;
DROP INDEX IF EXISTS alerts_case_id_created_at_idx;

CREATE INDEX IF NOT EXISTS alerts_actor_id_account_id_idx on alerts (actor_id ASC, account_id ASC);
CREATE INDEX IF NOT EXISTS alerts_entity_type_entity_id_idx on alerts (entity_type ASC, entity_id ASC);
CREATE INDEX IF NOT EXISTS alerts_case_id on alerts (case_id ASC);
