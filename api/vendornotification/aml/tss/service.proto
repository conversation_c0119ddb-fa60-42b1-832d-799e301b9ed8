// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.aml.tss;

import "api/vendors/tss/tss.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/aml/tss";
option java_package = "com.github.epifi.gamma.api.vendornotification.aml.tss";

service TSS {
  rpc ProcessWebhookCallBack (vendors.tss.ProcessWebhookCallBackRequest) returns (vendors.tss.ProcessWebhookCallBackResponse) {
    option (google.api.http) = {
      post: "/aml/tss/response"
      body: "*"
    };
  }

}
