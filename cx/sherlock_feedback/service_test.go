package sherlock_feedback

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"flag"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_feedback"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/test"
	dao_mock "github.com/epifi/gamma/cx/test/mocks/sherlock_feedback/dao"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, _, teardown := test.InitTestServer(false)

	sbTS = SherlockFeedbackTestSuite{
		conf: conf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type SherlockFeedbackTestSuite struct {
	conf *config.Config
}

var (
	sbTS                  SherlockFeedbackTestSuite
	createFeedbackSuccess = &sbPb.SherlockFeedbackDetails{
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMessage:         "hello",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1664416630, Nanos: 919000000},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1664416630, Nanos: 919000000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "123412",
		FeedbackMetaData:        &sbPb.FeedbackMetaData{},
	}
	getSherlockFeedbackFilter = &sbPb.SherlockFeedbackFilters{
		FeedbackCategory: sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMetaData: &sbPb.FeedbackMetaData{
			Frequency:           sbPb.FeedbackFrequency_FEEDBACK_FREQUENCY_FREQUENT,
			IsUrgent:            commontypes.BooleanEnum_TRUE,
			IsHighlightedBefore: commontypes.BooleanEnum_TRUE,
		},
		FromDate:   timestampPb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)),
		ToDate:     timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)),
		AgentEmail: "<EMAIL>",
	}
	getSherlockFeedbackResponse1 = &sbPb.SherlockFeedbackDetails{
		Id:                      "de34d771-2c76-44e3-81f9-48c730feef7d",
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMessage:         "hello",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1665468555},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1659949515, Nanos: 428764000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "123",
		FeedbackMetaData: &sbPb.FeedbackMetaData{
			Frequency:           sbPb.FeedbackFrequency_FEEDBACK_FREQUENCY_FREQUENT,
			IsUrgent:            commontypes.BooleanEnum_TRUE,
			IsHighlightedBefore: commontypes.BooleanEnum_TRUE,
		},
	}
)

func TestSherlockFeedbackService_SubmitFeedback(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	sherlockFeedbackDetailsDao := dao_mock.NewMockISherlockFeedbackDetailsDao(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *sbPb.SubmitFeedbackRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.SubmitFeedbackResponse
		wantErr bool
	}{
		{
			name: "missing parameters",
			args: args{
				ctx: context.Background(),
				req: &sbPb.SubmitFeedbackRequest{
					SherlockFeedbackDetails: &sbPb.SherlockFeedbackDetails{},
				},
				mocks: []interface{}{
					sherlockFeedbackDetailsDao.EXPECT().Create(gomock.Any(), &sbPb.SherlockFeedbackDetails{}).Return(nil, errors.New("FAIL")),
				},
			},
			want: &sbPb.SubmitFeedbackResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &sbPb.SubmitFeedbackRequest{
					SherlockFeedbackDetails: createFeedbackSuccess,
				},
				mocks: []interface{}{
					sherlockFeedbackDetailsDao.EXPECT().Create(gomock.Any(), createFeedbackSuccess).Return(createFeedbackSuccess, nil),
				},
			},
			want: &sbPb.SubmitFeedbackResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewSherlockFeedbackService(sbTS.conf, sherlockFeedbackDetailsDao)
			got, err := s.SubmitFeedback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SubmitFeedback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SubmitFeedback() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSherlockFeedbackService_GetFeedback(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	sherlockFeedbackDetailsDao := dao_mock.NewMockISherlockFeedbackDetailsDao(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *sbPb.GetFeedbackRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.GetFeedbackResponse
		wantErr bool
	}{
		{
			name: "missing mandatory params",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetFeedbackRequest{
					Filters: &sbPb.SherlockFeedbackFilters{},
				},
				mocks: []interface{}{
					sherlockFeedbackDetailsDao.EXPECT().GetAllFeedbacks(context.Background(), nil, 20, &sbPb.SherlockFeedbackFilters{}).Return(nil, nil, errors.New("Internal Server Error")),
				},
			},
			want: &sbPb.GetFeedbackResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "wrong page token is passed",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetFeedbackRequest{
					Filters: &sbPb.SherlockFeedbackFilters{},
					PageContextRequest: &rpcPb.PageContextRequest{
						Token: &rpcPb.PageContextRequest_AfterToken{
							AfterToken: "aa",
						},
					},
				},
			},
			want: &sbPb.GetFeedbackResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid page token"),
			},
		},
		{
			name: "no record found",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetFeedbackRequest{
					Filters: getSherlockFeedbackFilter,
				},
				mocks: []interface{}{
					sherlockFeedbackDetailsDao.EXPECT().GetAllFeedbacks(context.Background(), nil, 20, getSherlockFeedbackFilter).Return(nil, nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &sbPb.GetFeedbackResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetFeedbackRequest{
					Filters: getSherlockFeedbackFilter,
				},
				mocks: []interface{}{
					sherlockFeedbackDetailsDao.EXPECT().GetAllFeedbacks(context.Background(), nil, 20, getSherlockFeedbackFilter).Return([]*sbPb.SherlockFeedbackDetails{getSherlockFeedbackResponse1}, nil, nil),
				},
			},
			want: &sbPb.GetFeedbackResponse{
				Status:                  rpcPb.StatusOk(),
				SherlockFeedbackDetails: []*sbPb.SherlockFeedbackDetails{getSherlockFeedbackResponse1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewSherlockFeedbackService(sbTS.conf, sherlockFeedbackDetailsDao)
			got, err := s.GetFeedback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFeedback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFeedback() got = %v, want %v", got, tt.want)
			}
		})
	}
}
