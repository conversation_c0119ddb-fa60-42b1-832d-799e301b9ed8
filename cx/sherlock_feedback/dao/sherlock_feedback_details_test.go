package dao_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_feedback"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/sherlock_feedback/dao"
	"github.com/epifi/gamma/cx/test"
	"github.com/epifi/be-common/pkg/pagination"

	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"
)

type SherlockFeedbackDAOTestSuite struct {
	db                  *gormV2.DB
	conf                *config.Config
	sherlockFeedbackDao dao.ISherlockFeedbackDetailsDao
}

var (
	sfdTS                 SherlockFeedbackDAOTestSuite
	createFeedbackSuccess = &sbPb.SherlockFeedbackDetails{
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMessage:         "hello",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1664416630, Nanos: 919000000},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1664416630, Nanos: 919000000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "123412",
		FeedbackMetaData:        &sbPb.FeedbackMetaData{},
	}
	createFeedbackSuccess1 = &sbPb.SherlockFeedbackDetails{
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMessage:         "hello",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1664416630, Nanos: 919000000},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1664416630, Nanos: 919000000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "123412",
		FeedbackMetaData: &sbPb.FeedbackMetaData{
			Frequency:           sbPb.FeedbackFrequency_FEEDBACK_FREQUENCY_FREQUENT,
			IsUrgent:            commontypes.BooleanEnum_TRUE,
			IsHighlightedBefore: commontypes.BooleanEnum_FALSE,
		},
	}
	createFeedbackSuccess2 = &sbPb.SherlockFeedbackDetails{
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_USER_RESEARCH,
		FeedbackMessage:         "hello",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1664416630, Nanos: 919000000},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1664416630, Nanos: 919000000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "123412",
	}
	getAllWithFiltersResponse1 = &sbPb.SherlockFeedbackDetails{
		Id:                      "de34d771-2c76-44e3-81f9-48c730feef7d",
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMessage:         "hello",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1665468555},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1659949515, Nanos: 428764000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "123",
		FeedbackMetaData: &sbPb.FeedbackMetaData{
			Frequency:           sbPb.FeedbackFrequency_FEEDBACK_FREQUENCY_FREQUENT,
			IsUrgent:            commontypes.BooleanEnum_TRUE,
			IsHighlightedBefore: commontypes.BooleanEnum_TRUE,
		},
	}
	getAllWithFiltersResponse2 = &sbPb.SherlockFeedbackDetails{
		Id:                      "de34d771-2c76-44e3-81f9-48c730feef81",
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMessage:         "hello ia m soory",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1665554955},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1659949515, Nanos: 428764000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "1234",
		FeedbackMetaData:        &sbPb.FeedbackMetaData{},
	}
	getAllWithFiltersResponse3 = &sbPb.SherlockFeedbackDetails{
		Id:                      "de34d771-2c76-44e3-81f9-48c730feef9d",
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMessage:         "hello",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1665641355},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1659949515, Nanos: 428764000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "123",
		FeedbackMetaData:        &sbPb.FeedbackMetaData{},
	}
	getAllWithFiltersResponse4 = &sbPb.SherlockFeedbackDetails{
		Id:                      "de34d771-2c76-44e3-81f9-48c730feef10",
		FeedbackCategory:        sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
		FeedbackMessage:         "hello",
		AgentEmail:              "<EMAIL>",
		CreatedAt:               &timestampPb.Timestamp{Seconds: 1665727755},
		UpdatedAt:               &timestampPb.Timestamp{Seconds: 1659949515, Nanos: 428764000},
		FeedbackIdentifierType:  sbPb.FeedbackIdentifierType_FEEDBACK_IDENTIFIER_TYPE_TICKET_ID,
		FeedbackIdentifierValue: "1233",
		FeedbackMetaData:        &sbPb.FeedbackMetaData{},
	}
)

func TestSherlockFeedbackDao_Create(t *testing.T) {
	type args struct {
		ctx      context.Context
		feedback *sbPb.SherlockFeedbackDetails
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.SherlockFeedbackDetails
		wantErr bool
	}{
		{
			name: "mandatory parameters missing",
			args: args{
				ctx:      context.Background(),
				feedback: &sbPb.SherlockFeedbackDetails{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success - feedback meta data needs to be stored but empty",
			args: args{
				ctx:      context.Background(),
				feedback: createFeedbackSuccess,
			},
			want:    createFeedbackSuccess,
			wantErr: false,
		},
		{
			name: "success - feedback meta data needs to be stored and present",
			args: args{
				ctx:      context.Background(),
				feedback: createFeedbackSuccess1,
			},
			want:    createFeedbackSuccess1,
			wantErr: false,
		},
		{
			name: "success - feedback meta data does not need be stored",
			args: args{
				ctx:      context.Background(),
				feedback: createFeedbackSuccess2,
			},
			want:    createFeedbackSuccess2,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// in order to create the test case atomic in nature
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, sfdTS.db, sfdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := sfdTS.sherlockFeedbackDao.Create(tt.args.ctx, tt.args.feedback)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}
func compareGetAllFeedbacks(got []*sbPb.SherlockFeedbackDetails, want []*sbPb.SherlockFeedbackDetails) error {
	if len(got) != len(want) {
		return fmt.Errorf("compareGetAllFeedbacks() got len = %v, want len %v", len(got), len(want))
	}

	for index := range got {
		want[index].Id = got[index].GetId()
		want[index].CreatedAt = got[index].GetCreatedAt()
		want[index].UpdatedAt = got[index].GetUpdatedAt()
		if !proto.Equal(got[index], want[index]) {
			return fmt.Errorf("got = %v, want %v", got, want)
		}
	}
	return nil
}

func TestSherlockFeedbackDao_GetAllFeedbacks(t *testing.T) {
	t.Skip()
	type args struct {
		ctx       context.Context
		pageToken *pagination.PageToken
		pageSize  int
		filters   *sbPb.SherlockFeedbackFilters
	}
	tests := []struct {
		name                    string
		args                    args
		wantFeedback            []*sbPb.SherlockFeedbackDetails
		wantPageContextResponse *rpc.PageContextResponse
		wantErr                 bool
	}{
		{
			name: "mandatory parameters are missing",
			args: args{
				ctx: context.Background(),
				filters: &sbPb.SherlockFeedbackFilters{
					AgentEmail: "<EMAIL>",
				},
			},
			wantFeedback:            nil,
			wantPageContextResponse: nil,
			wantErr:                 true,
		},
		{
			name: "no record found",
			args: args{
				ctx: context.Background(),
				filters: &sbPb.SherlockFeedbackFilters{
					FeedbackCategory: sbPb.FeedbackCategory_FEEDBACK_CATEGORY_USER_OPINION,
					FromDate:         timestampPb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)),
					ToDate:           timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)),
					AgentEmail:       "<EMAIL>",
				},
				pageSize:  1,
				pageToken: nil,
			},
			wantFeedback:            nil,
			wantPageContextResponse: nil,
			wantErr:                 true,
		},
		{
			name: "no record found with feedbackMetaData",
			args: args{
				ctx: context.Background(),
				filters: &sbPb.SherlockFeedbackFilters{
					FeedbackCategory: sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
					FeedbackMetaData: &sbPb.FeedbackMetaData{
						Frequency:           sbPb.FeedbackFrequency_FEEDBACK_FREQUENCY_FREQUENT,
						IsUrgent:            commontypes.BooleanEnum_FALSE,
						IsHighlightedBefore: commontypes.BooleanEnum_FALSE,
					},
					FromDate:   timestampPb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)),
					ToDate:     timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)),
					AgentEmail: "<EMAIL>",
				},
				pageSize:  1,
				pageToken: nil,
			},
			wantFeedback:            nil,
			wantPageContextResponse: nil,
			wantErr:                 true,
		},
		{
			name: "success by passing necessary params",
			args: args{
				ctx: context.Background(),
				filters: &sbPb.SherlockFeedbackFilters{
					FromDate: timestampPb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)),
					ToDate:   timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)),
				},
				pageSize:  2,
				pageToken: nil,
			},
			wantFeedback: []*sbPb.SherlockFeedbackDetails{
				getAllWithFiltersResponse4,
				getAllWithFiltersResponse3,
			},
			wantPageContextResponse: &rpc.PageContextResponse{
				HasAfter:   true,
				AfterToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY2NTY0MTM1NX0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjpmYWxzZX0=",
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				filters: &sbPb.SherlockFeedbackFilters{
					FeedbackCategory: sbPb.FeedbackCategory_FEEDBACK_CATEGORY_FEATURE_REQUEST,
					FeedbackMetaData: &sbPb.FeedbackMetaData{
						Frequency:           sbPb.FeedbackFrequency_FEEDBACK_FREQUENCY_FREQUENT,
						IsUrgent:            commontypes.BooleanEnum_TRUE,
						IsHighlightedBefore: commontypes.BooleanEnum_TRUE,
					},
					FromDate:   timestampPb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)),
					ToDate:     timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)),
					AgentEmail: "<EMAIL>",
				},
				pageSize:  1,
				pageToken: nil,
			},
			wantFeedback: []*sbPb.SherlockFeedbackDetails{
				getAllWithFiltersResponse1,
			},
			wantPageContextResponse: &rpc.PageContextResponse{},
			wantErr:                 false,
		},
		{
			name: "success with token response",
			args: args{
				ctx:      context.Background(),
				pageSize: 1,
				filters: &sbPb.SherlockFeedbackFilters{
					FromDate: timestampPb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)),
					ToDate:   timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)),
				},
				pageToken: nil,
			},
			wantFeedback: []*sbPb.SherlockFeedbackDetails{
				getAllWithFiltersResponse4,
			},
			wantPageContextResponse: &rpc.PageContextResponse{
				HasAfter:   true,
				AfterToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY2NTcyNzc1NX0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjpmYWxzZX0=",
			},
			wantErr: false,
		},
		{
			name: "success with passing page token",
			args: args{
				ctx:      context.Background(),
				pageSize: 1,
				filters: &sbPb.SherlockFeedbackFilters{
					FromDate: timestampPb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)),
					ToDate:   timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)),
				},
				pageToken: &pagination.PageToken{
					Timestamp: timestampPb.New(time.Date(2022, 10, 13, 11, 39, 15, 0, time.Local)),
					IsReverse: false,
					Offset:    1,
				},
			},
			wantFeedback: []*sbPb.SherlockFeedbackDetails{
				getAllWithFiltersResponse2,
			},
			wantPageContextResponse: &rpc.PageContextResponse{
				HasAfter:    true,
				HasBefore:   true,
				BeforeToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY2NTU1NDk1NX0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjp0cnVlfQ==",
				AfterToken:  "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY2NTU1NDk1NX0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjpmYWxzZX0=",
			},
			wantErr: false,
		},
		{
			name: "success with passing page token and accessing previous page",
			args: args{
				ctx:      context.Background(),
				pageSize: 1,
				filters: &sbPb.SherlockFeedbackFilters{
					FromDate: timestampPb.New(time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)),
					ToDate:   timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)),
				},
				pageToken: &pagination.PageToken{
					Timestamp: timestampPb.New(time.Date(2022, 10, 12, 11, 39, 15, 0, time.Local)),
					IsReverse: true,
					Offset:    1,
				},
			},
			wantFeedback: []*sbPb.SherlockFeedbackDetails{
				getAllWithFiltersResponse3,
			},
			wantPageContextResponse: &rpc.PageContextResponse{
				HasBefore:   true,
				HasAfter:    true,
				BeforeToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY2NTY0MTM1NX0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjp0cnVlfQ==",
				AfterToken:  "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY2NTY0MTM1NX0sIk9mZnNldCI6MSwiSXNSZXZlcnNlIjpmYWxzZX0=",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, sfdTS.db, sfdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			gotFeedback, _, err := sfdTS.sherlockFeedbackDao.GetAllFeedbacks(tt.args.ctx, tt.args.pageToken, tt.args.pageSize, tt.args.filters)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllFeedbacks() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			feedbackErr := compareGetAllFeedbacks(gotFeedback, tt.wantFeedback)
			if feedbackErr != nil {
				t.Errorf("compareGetAllFeedbacks() got and want is not equal, err: %v", feedbackErr)
			}
		})
	}
}
