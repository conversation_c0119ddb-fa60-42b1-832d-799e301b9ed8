//go:generate dao_metrics_gen .
package dao

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_feedback"
	"github.com/epifi/be-common/pkg/pagination"
)

// ISherlockFeedbackDetailsDao - Interface which exposes DAO methods on top of SherlockFeedback and SherlockFeedbackMetaDataMapping table
type ISherlockFeedbackDetailsDao interface {
	// Create - method accepts SherlockFeedbackDetail with required details
	// returns SherlockFeedbackDetail or any errors which are encountered
	Create(ctx context.Context, feedbackDetails *sbPb.SherlockFeedbackDetails) (*sbPb.SherlockFeedbackDetails, error)
	// GetAllFeedbacks - method to retrieve list of SherlockFeedbackDetail from db
	// mandatory fields in filters are FromDate and ToDate
	// Returns Paginated list of SherlockFeedbackDetail sorted on created_at, page context response or error if any encountered
	GetAllFeedbacks(ctx context.Context, pageToken *pagination.PageToken, pageLimit int, filters *sbPb.SherlockFeedbackFilters) ([]*sbPb.SherlockFeedbackDetails, *rpc.PageContextResponse, error)
}
