package model

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"time"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_feedback"
	"github.com/epifi/be-common/pkg/pagination"
)

type SherlockFeedback struct {
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	// describe the type of feedback
	FeedbackCategory sbPb.FeedbackCategory
	//  actual feedback
	FeedbackMessage string
	// agent email
	AgentEmail string
	//  what type of feedback identifier it is: ticket_id
	FeedbackIdentifierType sbPb.FeedbackIdentifierType
	// contain the value of a particular identifier type
	FeedbackIdentifierValue string
	// timestamp at which this row was created in DB
	CreatedAt time.Time
	// timestamp at which this row was last updated
	UpdatedAt time.Time
}

// this is combined data model for SherlockF<PERSON>back and SherlockFeedbackMetaDataMapping entities
type SherlockFeedbackDetails struct {
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	// describe the type of feedback
	FeedbackCategory sbPb.FeedbackCategory
	//  actual feedback
	FeedbackMessage string
	// agent email
	AgentEmail string
	//  what type of feedback identifier it is: ticket_id
	FeedbackIdentifierType sbPb.FeedbackIdentifierType
	// contain the value of a particular identifier type
	FeedbackIdentifierValue string
	// describes the frequency of issue reported by agent
	Frequency sbPb.FeedbackFrequency
	// describes if agent highlighted current feedback/issue before
	IsHighlightedBefore commontypes.BooleanEnum
	// tell if the current feedback/issue is urgent or not
	IsUrgent commontypes.BooleanEnum
	// timestamp at which this row was created in DB
	CreatedAt time.Time
	// timestamp at which this row was last updated
	UpdatedAt time.Time
}

type SherlockFeedbackMetaDataMapping struct {
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	// feedback_id references id value from sherlock_feedbacks table,
	// this is to reference extended feedback information received on the same feedback.
	FeedbackId string
	// describes the frequency of issue reported by agent
	Frequency sbPb.FeedbackFrequency
	// describes if agent highlighted current feedback/issue before
	IsHighlightedBefore commontypes.BooleanEnum
	// tell if the current feedback/issue is urgent or not
	IsUrgent commontypes.BooleanEnum
	// timestamp at which this row was created in DB
	CreatedAt time.Time
	// timestamp at which this row was last updated
	UpdatedAt time.Time
}

func NewSherlockFeedbackFromProtoMsg(msg *sbPb.SherlockFeedbackDetails) *SherlockFeedback {
	modelMsg := &SherlockFeedback{
		FeedbackCategory:        msg.GetFeedbackCategory(),
		FeedbackMessage:         msg.GetFeedbackMessage(),
		AgentEmail:              msg.GetAgentEmail(),
		FeedbackIdentifierType:  msg.GetFeedbackIdentifierType(),
		FeedbackIdentifierValue: msg.GetFeedbackIdentifierValue(),
	}
	return modelMsg
}

func NewSherlockFeedbackMetaDataMappingFromProtoMsg(msg *sbPb.FeedbackMetaData, feedbackId string) *SherlockFeedbackMetaDataMapping {
	modelMsg := &SherlockFeedbackMetaDataMapping{
		FeedbackId:          feedbackId,
		Frequency:           msg.GetFrequency(),
		IsHighlightedBefore: msg.GetIsHighlightedBefore(),
		IsUrgent:            msg.GetIsUrgent(),
	}
	return modelMsg
}

func ConvertToSherlockFeedbackDetailsProtoMsg(feedback *sbPb.SherlockFeedbackDetails, metaData *sbPb.SherlockFeedbackMetaData) *sbPb.SherlockFeedbackDetails {
	protoMsg := &sbPb.SherlockFeedbackDetails{
		Id:                      feedback.GetId(),
		FeedbackCategory:        feedback.GetFeedbackCategory(),
		FeedbackMessage:         feedback.GetFeedbackMessage(),
		AgentEmail:              feedback.GetAgentEmail(),
		FeedbackIdentifierType:  feedback.GetFeedbackIdentifierType(),
		FeedbackIdentifierValue: feedback.GetFeedbackIdentifierValue(),
		CreatedAt:               feedback.GetCreatedAt(),
		UpdatedAt:               feedback.GetUpdatedAt(),
	}
	if metaData != nil {
		protoMsg.FeedbackMetaData = &sbPb.FeedbackMetaData{
			Frequency:           metaData.GetFrequency(),
			IsHighlightedBefore: metaData.GetIsHighlightedBefore(),
			IsUrgent:            metaData.GetIsUrgent(),
		}
	}
	return protoMsg
}

func (s *SherlockFeedback) ToProtoMessage() *sbPb.SherlockFeedbackDetails {
	protoMsg := &sbPb.SherlockFeedbackDetails{
		Id:                      s.Id,
		FeedbackCategory:        s.FeedbackCategory,
		FeedbackMessage:         s.FeedbackMessage,
		AgentEmail:              s.AgentEmail,
		FeedbackIdentifierType:  s.FeedbackIdentifierType,
		FeedbackIdentifierValue: s.FeedbackIdentifierValue,
		CreatedAt:               timestampPb.New(s.CreatedAt),
		UpdatedAt:               timestampPb.New(s.UpdatedAt),
	}
	return protoMsg
}

func (s *SherlockFeedbackDetails) ToProtoMessage() *sbPb.SherlockFeedbackDetails {
	protoMsg := &sbPb.SherlockFeedbackDetails{
		Id:                      s.Id,
		FeedbackCategory:        s.FeedbackCategory,
		FeedbackMessage:         s.FeedbackMessage,
		AgentEmail:              s.AgentEmail,
		FeedbackIdentifierType:  s.FeedbackIdentifierType,
		FeedbackIdentifierValue: s.FeedbackIdentifierValue,
		CreatedAt:               timestampPb.New(s.CreatedAt),
		UpdatedAt:               timestampPb.New(s.UpdatedAt),
		FeedbackMetaData: &sbPb.FeedbackMetaData{
			Frequency:           s.Frequency,
			IsHighlightedBefore: s.IsHighlightedBefore,
			IsUrgent:            s.IsUrgent,
		},
	}
	return protoMsg
}

func (s *SherlockFeedbackMetaDataMapping) ToProtoMessage() *sbPb.SherlockFeedbackMetaData {
	protoMsg := &sbPb.SherlockFeedbackMetaData{
		Id:                  s.Id,
		FeedbackId:          s.FeedbackId,
		Frequency:           s.Frequency,
		IsHighlightedBefore: s.IsHighlightedBefore,
		IsUrgent:            s.IsUrgent,
		UpdatedAt:           timestampPb.New(s.UpdatedAt),
		CreatedAt:           timestampPb.New(s.CreatedAt),
	}
	return protoMsg
}

func ConvertToProtoList(feedbackList []*SherlockFeedbackDetails) []*sbPb.SherlockFeedbackDetails {
	var protoList []*sbPb.SherlockFeedbackDetails
	for _, userFeedback := range feedbackList {
		protoList = append(protoList, userFeedback.ToProtoMessage())
	}
	return protoList
}

// SherlockFeedbackDetails implements pagination.Rows to use pagination on sherlock_feedbacks table
type SherlockFeedbackDetailsList []*SherlockFeedbackDetails

func (s SherlockFeedbackDetailsList) Slice(start, end int) pagination.Rows { return s[start:end] }
func (s SherlockFeedbackDetailsList) GetTimestamp(index int) time.Time     { return s[index].CreatedAt }
func (s SherlockFeedbackDetailsList) Size() int                            { return len(s) }
