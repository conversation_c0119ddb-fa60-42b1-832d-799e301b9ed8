package manual_ticket_stage_wise_comms

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
)

type StageWiseCommsTestSuite struct {
	genConf *genconf.Config
}

var (
	swcts StageWiseCommsTestSuite
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	//nolint:dogsled
	_, genConf, _, teardown := test.InitTestServer(false)
	swcts = StageWiseCommsTestSuite{genConf: genConf}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
