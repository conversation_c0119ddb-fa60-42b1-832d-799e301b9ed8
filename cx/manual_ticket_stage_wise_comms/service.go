//nolint:funlen
package manual_ticket_stage_wise_comms

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"

	"go.uber.org/zap"

	"github.com/epifi/gamma/cx/config/genconf"

	types "github.com/epifi/gamma/api/typesv2"

	issueConfigPb "github.com/epifi/gamma/api/cx/issue_config"
	issueConfigDao "github.com/epifi/gamma/cx/issue_config/dao"
	"github.com/epifi/be-common/pkg/epifierrors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	stageWiseCommsPb "github.com/epifi/gamma/api/cx/manual_ticket_stage_wise_comms"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	genericComms "github.com/epifi/gamma/cx/internal/genric_ticket_status_comms"
	"github.com/epifi/gamma/cx/ticket/dao"
	"github.com/epifi/be-common/pkg/logger"
)

type ManualTicketStageWiseCommsService struct {
	issueConfigDao   issueConfigDao.IssueConfigDao
	supportTicketDao dao.ISupportTicketDao
	conf             *genconf.Config
}

func NewManualTicketStageWiseCommsService(supportTicketDao dao.ISupportTicketDao, issueConfigDao issueConfigDao.IssueConfigDao, conf *genconf.Config) *ManualTicketStageWiseCommsService {
	return &ManualTicketStageWiseCommsService{
		issueConfigDao:   issueConfigDao,
		supportTicketDao: supportTicketDao,
		conf:             conf,
	}
}

var _ stageWiseCommsPb.ManualTicketStageWiseCommsServer = &ManualTicketStageWiseCommsService{}

func (m *ManualTicketStageWiseCommsService) IsIncidentValid(ctx context.Context, request *watsonPb.IsIncidentValidRequest) (*watsonPb.IsIncidentValidResponse, error) {
	if request.GetIncident().GetClient() != types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE {
		logger.Error(ctx, "invalid client received in request", zap.String(logger.CLIENT, request.GetIncident().GetClient().String()))
		return &watsonPb.IsIncidentValidResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	if request.GetIncident().GetClientRequestId() == "" {
		logger.Info(ctx, "client request id is empty in request")
		return &watsonPb.IsIncidentValidResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	// for the incidents handled through this client
	// we are using ticket id as client request id, hence the same is expected in requests from watson
	ticketId, ticketIdErr := strconv.Atoi(request.GetIncident().GetClientRequestId())
	if ticketIdErr != nil {
		logger.Error(ctx, "error while parsing ticket id from client request id", zap.Error(ticketIdErr), zap.String(logger.CLIENT_REQUEST_ID, request.GetIncident().GetClientRequestId()))
		return &watsonPb.IsIncidentValidResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	ticketDetails, ticketDetailsErr := m.supportTicketDao.GetById(ctx, int64(ticketId))
	if ticketDetailsErr != nil {
		logger.Error(ctx, "error while fetching ticket details", zap.Error(ticketDetailsErr), zap.Int(logger.TICKET_ID, ticketId))
		if errors.Is(ticketDetailsErr, epifierrors.ErrRecordNotFound) {
			return &watsonPb.IsIncidentValidResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &watsonPb.IsIncidentValidResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// if the ticket is closed or resolved, incident will not be valid further
	if ticketDetails.GetStatus() == ticketPb.Status_STATUS_RESOLVED ||
		ticketDetails.GetStatus() == ticketPb.Status_STATUS_CLOSED {
		return &watsonPb.IsIncidentValidResponse{
			Status:          rpcPb.StatusOk(),
			IsIncidentValid: false,
		}, nil
	}
	// for other ticket statuses incident will remain to be valid
	return &watsonPb.IsIncidentValidResponse{
		Status:          rpcPb.StatusOk(),
		IsIncidentValid: true,
	}, nil
}

func (m *ManualTicketStageWiseCommsService) GetTicketDetails(ctx context.Context, request *watsonPb.GetTicketDetailsRequest) (*watsonPb.GetTicketDetailsResponse, error) {
	// this RPC is not expected to be called by the workflow which will be used for sending
	// comms for manually created ticket
	return &watsonPb.GetTicketDetailsResponse{
		Status:        rpcPb.StatusUnimplemented(),
		TicketDetails: nil,
	}, nil
}

func (m *ManualTicketStageWiseCommsService) GetCommsDetails(ctx context.Context, request *watsonPb.GetCommsDetailsRequest) (*watsonPb.GetCommsDetailsResponse, error) {
	if validateErr := validateGetCommsDetailsRequest(request); validateErr != nil {
		logger.Error(ctx, "invalid request recieved", zap.Error(validateErr), zap.String("CommsType", request.GetCommsType().String()), zap.String(logger.CLIENT_REQUEST_ID, request.GetIncident().GetClientRequestId()))
		return &watsonPb.GetCommsDetailsResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	genericConfig := genericComms.GetGenericStageWiseCommsDetails()
	if m.conf.StageWiseCommsConfig().IsIssueConfigSpecificCommsEnabled() {
		issueCategorySpecificComms, issueCategorySpecificCommsErr := m.GetIssueConfigSpecificComms(ctx, request)
		// if there was an error while fetching issue specific comms
		// we will return internal status here
		if issueCategorySpecificCommsErr != nil {
			if !errors.Is(issueCategorySpecificCommsErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error while fetching issue config for stage wise comms", zap.Error(issueCategorySpecificCommsErr), zap.String(logger.ISSUE_CATEGORY_ID, request.GetIncident().GetIssueCategoryId()))
				return &watsonPb.GetCommsDetailsResponse{
					Status: rpcPb.StatusInternal(),
				}, nil
			}
		} else {
			return &watsonPb.GetCommsDetailsResponse{
				Status:       rpcPb.StatusOk(),
				CommsDetails: m.populateFieldsInCommsDetails(ctx, issueCategorySpecificComms, request.GetCommsMetadata()),
			}, nil
		}
	}
	// if issue specific comms are not available, hence
	// fallback to generic comms if generic comms are enabled
	var commsDetails []*watsonPb.CommsDetail
	if m.conf.StageWiseCommsConfig().IsGenericCommsEnabled() {
		commsDetails = getCommsDetailsFromDefaultConfig(request.GetCommsType(), request.GetCommsMetadata().GetManualTicketStatusChangeCommsMeta().GetCurStatus(), genericConfig)
	}
	return &watsonPb.GetCommsDetailsResponse{
		Status:       rpcPb.StatusOk(),
		CommsDetails: m.populateFieldsInCommsDetails(ctx, commsDetails, request.GetCommsMetadata()),
	}, nil
}

func validateGetCommsDetailsRequest(req *watsonPb.GetCommsDetailsRequest) error {
	if req.GetIncident().GetClientRequestId() == "" {
		return errors.New("client request id cannot be empty")
	}
	if req.GetIncident().GetClient() != types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE {
		return errors.New(fmt.Sprintf("invalid client present in request : %v", req.GetIncident().GetClient()))
	}
	if req.GetCommsMetadata().GetTicketId() == 0 {
		return errors.New("ticket id cannot be empty")
	}
	switch req.GetCommsType() {
	case watsonPb.CommsType_COMMS_TYPE_UNSPECIFIED:
		return errors.New("comms type cannot be unspecified")
	case watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE:
		if req.GetCommsMetadata().GetManualTicketStatusChangeCommsMeta().GetCurStatus() == ticketPb.Status_STATUS_UNSPECIFIED {
			return errors.New("current status cannot be unspecified")
		}
		return nil
	case watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_SLA_BREACHED:
		// in case of SLA breached comms, current status is not needed to fetch comms
		// hence not validating the current status field in metadata here
		if req.GetCommsMetadata().GetManualTicketStatusChangeCommsMeta().GetTicketResolutionSla() == "" {
			return errors.New("sla cannot be empty")
		}
		return nil
	default:
		return errors.New(fmt.Sprintf("unhandled comms type : %v", req.GetCommsType()))
	}
}

func getCommsDetailsFromDefaultConfig(commsType watsonPb.CommsType, status ticketPb.Status, defaultStageWiseCommsConfig *stageWiseCommsPb.ManualTicketStageBasedCommsDetails) []*watsonPb.CommsDetail {
	if commsType == watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_SLA_BREACHED {
		// check if generic sla comms are enabled
		if !defaultStageWiseCommsConfig.GetSlaBreachCommsConfig().GetIsCommsEnabled() {
			return []*watsonPb.CommsDetail{}
		}
		return defaultStageWiseCommsConfig.GetSlaBreachCommsConfig().GetCommsDetails()
	} else if commsType == watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE {
		// check if generic comms are available for current ticket status
		genericCommsConfig, isGenericCommsConfigAvailable := defaultStageWiseCommsConfig.GetTicketStatusCommsConfigMap()[status.String()]
		if !isGenericCommsConfigAvailable {
			return []*watsonPb.CommsDetail{}
		}
		if !genericCommsConfig.GetIsCommsEnabled() {
			return []*watsonPb.CommsDetail{}
		}
		return genericCommsConfig.GetCommsDetails()
	}
	return []*watsonPb.CommsDetail{}
}

// GetIssueConfigSpecificComms returns the issue specific comms, only if issue specific comms are not found then we will fall back to generic comms
// once issue specific comms are defined, the entire ownership of chosen comms lies on issue config
func (m *ManualTicketStageWiseCommsService) GetIssueConfigSpecificComms(ctx context.Context, request *watsonPb.GetCommsDetailsRequest) ([]*watsonPb.CommsDetail, error) {
	// if issue specific config is defined i.e found, comms details from there would be utilized, even if they are empty
	// otherwise we will fall back to generic comms
	issueConfig, issueConfigErr := m.issueConfigDao.Get(ctx, request.GetIncident().GetIssueCategoryId(), issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS)
	if issueConfigErr != nil {
		if errors.Is(issueConfigErr, epifierrors.ErrRecordNotFound) {
			return []*watsonPb.CommsDetail{}, errors.Wrap(issueConfigErr, "issue config not found")
		} else {
			return []*watsonPb.CommsDetail{}, errors.Wrap(issueConfigErr, "error while fetching issue config")
		}
	}
	commsConfig := issueConfig.GetConfigPayload().GetManualTicketStageBasedCommsDetails()
	// get the issue config based on the comms type
	// get the current status for which comms are requested through manual ticket status change comms meta
	ticketStatus := request.GetCommsMetadata().GetManualTicketStatusChangeCommsMeta().GetCurStatus().String()
	switch request.GetCommsType() {
	case watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE:
		statusbasedConfig, isStatusBasedConfigDefined := commsConfig.GetTicketStatusCommsConfigMap()[ticketStatus]
		// if status based config is not defined or if status based comms are disabled, no comms will be sent
		if !isStatusBasedConfigDefined || !statusbasedConfig.GetIsCommsEnabled() {
			return []*watsonPb.CommsDetail{}, nil
		}
		return statusbasedConfig.GetCommsDetails(), nil
	case watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_SLA_BREACHED:
		if !commsConfig.GetSlaBreachCommsConfig().GetIsCommsEnabled() {
			return []*watsonPb.CommsDetail{}, nil
		}
		return commsConfig.GetSlaBreachCommsConfig().GetCommsDetails(), nil
	default:
		logger.Info(ctx, "unhandled comms type received in request", zap.String("commsType", request.GetCommsType().String()), zap.String(logger.CLIENT_REQUEST_ID, request.GetIncident().GetClientRequestId()))
		return []*watsonPb.CommsDetail{}, errors.New("unhandled comms type in request")
	}
}

func (m *ManualTicketStageWiseCommsService) populateFieldsInCommsDetails(ctx context.Context, commsDetails []*watsonPb.CommsDetail, commsMetadata *watsonPb.CommsMetadataForClient) []*watsonPb.CommsDetail {
	// we are not validating the fields again here as they are already validated in GetCommsDetails request
	commsDetails = genericComms.PopulateFieldsInGenericCommsDetails(ctx, genericComms.TICKET_ID, strconv.FormatInt(commsMetadata.GetTicketId(), 10), commsDetails)
	commsDetails = genericComms.PopulateFieldsInGenericCommsDetails(ctx, genericComms.SLA, commsMetadata.GetManualTicketStatusChangeCommsMeta().GetTicketResolutionSla(), commsDetails)
	return commsDetails
}
