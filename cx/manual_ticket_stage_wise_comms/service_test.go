package manual_ticket_stage_wise_comms

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	genericComms "github.com/epifi/gamma/cx/internal/genric_ticket_status_comms"

	"github.com/epifi/gamma/api/frontend/fcm"

	types "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/cx/manual_ticket_stage_wise_comms"

	issueConfigPb "github.com/epifi/gamma/api/cx/issue_config"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"

	"github.com/epifi/be-common/pkg/epifierrors"

	rpcPb "github.com/epifi/be-common/api/rpc"

	watsonPb "github.com/epifi/gamma/api/cx/watson"

	mockDao2 "github.com/epifi/gamma/cx/test/mocks/issue_config/dao"
	mockDao "github.com/epifi/gamma/cx/test/mocks/ticket/dao"
)

func TestManualTicketStageWiseCommsService_IsIncidentValid(t *testing.T) {
	t.Parallel(
	// Setup logger for test env
	)

	logger.Init(cfg.TestEnv)
	ctr := gomock.NewController(t)
	mockSupportTicketDao := mockDao.NewMockISupportTicketDao(ctr)
	mockIssueConfigDao := mockDao2.NewMockIssueConfigDao(ctr)
	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		request	*watsonPb.IsIncidentValidRequest
	}
	tests := []struct {
		name	string
		args	args
		want	*watsonPb.IsIncidentValidResponse
		wantErr	bool
	}{
		{
			name:	"invalid client name in request",
			args: args{
				mocks:		nil,
				ctx:		context.Background(),
				request:	&watsonPb.IsIncidentValidRequest{Incident: &watsonPb.IncidentDetailsForClient{Client: types.ServiceName_CX_SERVICE}},
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr:	false,
		},
		{
			name:	"no client request id in request",
			args: args{
				mocks:		nil,
				ctx:		context.Background(),
				request:	&watsonPb.IsIncidentValidRequest{Incident: &watsonPb.IncidentDetailsForClient{Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE}},
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr:	false,
		},
		{
			name:	"client request id not parseable to ticket id",
			args: args{
				mocks:		nil,
				ctx:		context.Background(),
				request:	&watsonPb.IsIncidentValidRequest{Incident: &watsonPb.IncidentDetailsForClient{ClientRequestId: "abcd", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE}},
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr:	false,
		},
		{
			name:	"error while fetching ticket details",
			args: args{
				mocks: []interface{}{
					mockSupportTicketDao.EXPECT().GetById(context.Background(), int64(1234)).Return(nil, errors.New("error while fetching ticket")),
				},
				ctx:		context.Background(),
				request:	&watsonPb.IsIncidentValidRequest{Incident: &watsonPb.IncidentDetailsForClient{ClientRequestId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE}},
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr:	false,
		},
		{
			name:	"ticket details not found",
			args: args{
				mocks: []interface{}{
					mockSupportTicketDao.EXPECT().GetById(context.Background(), int64(1234)).Return(nil, errors.Wrap(epifierrors.ErrRecordNotFound, "ticket not found")),
				},
				ctx:		context.Background(),
				request:	&watsonPb.IsIncidentValidRequest{Incident: &watsonPb.IncidentDetailsForClient{ClientRequestId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE}},
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr:	false,
		},
		{
			name:	"success - incident is valid",
			args: args{
				mocks: []interface{}{
					mockSupportTicketDao.EXPECT().GetById(context.Background(), int64(1234)).Return(&ticketPb.TicketDetails{Status: ticketPb.Status_STATUS_OPEN}, nil),
				},
				ctx:		context.Background(),
				request:	&watsonPb.IsIncidentValidRequest{Incident: &watsonPb.IncidentDetailsForClient{ClientRequestId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE}},
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status:			rpcPb.StatusOk(),
				IsIncidentValid:	true,
			},
			wantErr:	false,
		},
		{
			name:	"success - incident is invalid",
			args: args{
				mocks: []interface{}{
					mockSupportTicketDao.EXPECT().GetById(context.Background(), int64(1234)).Return(&ticketPb.TicketDetails{Status: ticketPb.Status_STATUS_RESOLVED}, nil),
				},
				ctx:		context.Background(),
				request:	&watsonPb.IsIncidentValidRequest{Incident: &watsonPb.IncidentDetailsForClient{ClientRequestId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE}},
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status:			rpcPb.StatusOk(),
				IsIncidentValid:	false,
			},
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewManualTicketStageWiseCommsService(mockSupportTicketDao, mockIssueConfigDao, swcts.genConf)
			got, err := s.IsIncidentValid(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsIncidentValid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IsIncidentValid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManualTicketStageWiseCommsService_GetCommsDetails(t *testing.T) {
	t.Parallel(
	// Setup logger for test env
	)

	logger.Init(cfg.TestEnv)
	ctr := gomock.NewController(t)
	mockSupportTicketDao := mockDao.NewMockISupportTicketDao(ctr)
	mockIssueConfigDao := mockDao2.NewMockIssueConfigDao(ctr)
	placeholderCommsDetailsWithSla := genericComms.PopulateFieldsInGenericCommsDetails(context.Background(), genericComms.SLA, "2h", genericComms.GetGenericStageWiseCommsDetails().GetSlaBreachCommsConfig().GetCommsDetails())
	sampleTicketStatusMap := map[string]*manual_ticket_stage_wise_comms.ManualTicketStatusCommsConfig{
		"STATUS_OPEN": {
			IsCommsEnabled:	true,
			CommsInterval:	"2h",
			MaxNumOfComms:	2,
			CommsDetails: []*watsonPb.CommsDetail{
				{
					Detail: &watsonPb.CommsDetail_Notification{
						Notification: &comms.NotificationMessage{
							ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
							Notification: &fcm.Notification{
								NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
									SystemTrayTemplate: &fcm.SystemTrayTemplate{
										CommonTemplateFields: &fcm.CommonTemplateFields{
											Title:	"Random title",
											Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
										},
									},
								},
							},
						},
					},
				},
				{
					Detail: &watsonPb.CommsDetail_Notification{
						Notification: &comms.NotificationMessage{
							// setting skip device registration to true to send the
							// notification before device registration to get CSAT for those cases too
							// #TODO : Test for unregistered case flow
							ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
							Notification: &fcm.Notification{
								NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
								NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
									SystemTrayTemplate: &fcm.SystemTrayTemplate{
										CommonTemplateFields: &fcm.CommonTemplateFields{
											Title:	"Random title 2",
											Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
										},
									},
								},
							},
						},
					},
				},
			},
			CommsDelay:	"2h",
		},
		"STATUS_PENDING": {
			IsCommsEnabled: false,
		},
	}
	sampleSLABreachConfig := &manual_ticket_stage_wise_comms.ManualTicketStatusCommsConfig{
		IsCommsEnabled:	true,
		CommsInterval:	"2h",
		MaxNumOfComms:	2,
		CommsDetails: []*watsonPb.CommsDetail{
			{
				Detail: &watsonPb.CommsDetail_Notification{
					Notification: &comms.NotificationMessage{
						ShouldSkipDeviceRegistrationCheck:	commontypes.BooleanEnum_TRUE,
						Notification: &fcm.Notification{
							NotificationType:	fcm.NotificationType_SYSTEM_TRAY,
							NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
								SystemTrayTemplate: &fcm.SystemTrayTemplate{
									CommonTemplateFields: &fcm.CommonTemplateFields{
										Title:	"sla breached",
										Body:	"{#ticket_id#} is your Ticket ID.\nTap to view the ticket.",
									},
								},
							},
						},
					},
				},
			},
		},
		CommsDelay:	"2h",
	}
	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		request	*watsonPb.GetCommsDetailsRequest
	}
	tests := []struct {
		name	string
		args	args
		want	*watsonPb.GetCommsDetailsResponse
		wantErr	bool
	}{
		{
			name:	"invalid client name in request",
			args: args{
				mocks:		nil,
				ctx:		context.Background(),
				request:	&watsonPb.GetCommsDetailsRequest{Incident: &watsonPb.IncidentDetailsForClient{Client: types.ServiceName_CX_SERVICE}},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr:	false,
		},
		{
			name:	"comms type is missing in request",
			args: args{
				mocks:	nil,
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	0,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_CLOSED,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	0,
				},
			},
			want:		&watsonPb.GetCommsDetailsResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr:	false,
		},
		{
			name:	"issue category id is missing in request",
			args: args{
				mocks:	nil,
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	0,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_CLOSED,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_SLA_BREACHED,
				},
			},
			want:		&watsonPb.GetCommsDetailsResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr:	false,
		},
		{
			name:	"curr ticket status is missing in request",
			args: args{
				mocks:	nil,
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	0,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_UNSPECIFIED,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_SLA_BREACHED,
				},
			},
			want:		&watsonPb.GetCommsDetailsResponse{Status: rpcPb.StatusInvalidArgument()},
			wantErr:	false,
		},
		{
			name:	"error while fetching issue config",
			args: args{
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(context.Background(), "1234", issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS).Return(nil, errors.New("config error")),
				},
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	1234,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_OPEN,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE,
				},
			},
			want:		&watsonPb.GetCommsDetailsResponse{Status: rpcPb.StatusInternal()},
			wantErr:	false,
		},
		{
			name:	"unhandled comms type received in request",
			args: args{
				mocks:	[]interface{}{},
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	1234,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_OPEN,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr:	false,
		},
		{
			name:	"success - issue config not found, utilizing generic config for status change",
			args: args{
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(context.Background(), "1234", issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS).Return(nil, errors.Wrap(epifierrors.ErrRecordNotFound, "config not found")),
				},
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	1234,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_OPEN,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE,
				},
			},
			want:		&watsonPb.GetCommsDetailsResponse{Status: rpcPb.StatusOk(), CommsDetails: genericComms.PopulateFieldsInGenericCommsDetails(context.Background(), genericComms.TICKET_ID, "1234", genericComms.GetGenericStageWiseCommsDetails().GetTicketStatusCommsConfigMap()[ticketPb.Status_STATUS_OPEN.String()].GetCommsDetails())},
			wantErr:	false,
		},
		{
			name:	"success - issue config not found, utilizing generic config for sla breach",
			args: args{
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(context.Background(), "1234", issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS).Return(nil, errors.Wrap(epifierrors.ErrRecordNotFound, "config not found")),
				},
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	1234,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:		ticketPb.Status_STATUS_OPEN,
								CommsNumber:		0,
								TicketResolutionSla:	"2h",
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_SLA_BREACHED,
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status:		rpcPb.StatusOk(),
				CommsDetails:	genericComms.PopulateFieldsInGenericCommsDetails(context.Background(), genericComms.TICKET_ID, "1234", placeholderCommsDetailsWithSla),
			},
			wantErr:	false,
		},
		{
			name:	"success - issue specific comms defined but not available for current ticket status",
			args: args{
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(context.Background(), "1234", issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS).Return(
						&issueConfigPb.IssueConfig{
							ConfigType:	issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS,
							ConfigPayload: &issueConfigPb.ConfigPayload{
								Config: &issueConfigPb.ConfigPayload_ManualTicketStageBasedCommsDetails{
									ManualTicketStageBasedCommsDetails: &manual_ticket_stage_wise_comms.ManualTicketStageBasedCommsDetails{
										TicketStatusCommsConfigMap:	sampleTicketStatusMap,
										SlaBreachCommsConfig:		sampleSLABreachConfig,
									},
								},
							},
						}, nil),
				},
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	1234,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_NEEDS_CLARIFICATION_FROM_CX,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE,
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status:		rpcPb.StatusOk(),
				CommsDetails:	[]*watsonPb.CommsDetail{},
			},
			wantErr:	false,
		},
		{
			name:	"success - issue specific comms defined but disabled for current ticket status",
			args: args{
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(context.Background(), "1234", issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS).Return(
						&issueConfigPb.IssueConfig{
							ConfigType:	issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS,
							ConfigPayload: &issueConfigPb.ConfigPayload{
								Config: &issueConfigPb.ConfigPayload_ManualTicketStageBasedCommsDetails{
									ManualTicketStageBasedCommsDetails: &manual_ticket_stage_wise_comms.ManualTicketStageBasedCommsDetails{
										TicketStatusCommsConfigMap:	sampleTicketStatusMap,
										SlaBreachCommsConfig:		sampleSLABreachConfig,
									},
								},
							},
						}, nil),
				},
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	1234,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_PENDING,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE,
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status:		rpcPb.StatusOk(),
				CommsDetails:	[]*watsonPb.CommsDetail{},
			},
			wantErr:	false,
		},
		{
			name:	"success - issue specific comms fetched for ticket status change",
			args: args{
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(context.Background(), "1234", issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS).Return(
						&issueConfigPb.IssueConfig{
							ConfigType:	issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS,
							ConfigPayload: &issueConfigPb.ConfigPayload{
								Config: &issueConfigPb.ConfigPayload_ManualTicketStageBasedCommsDetails{
									ManualTicketStageBasedCommsDetails: &manual_ticket_stage_wise_comms.ManualTicketStageBasedCommsDetails{
										TicketStatusCommsConfigMap:	sampleTicketStatusMap,
										SlaBreachCommsConfig:		sampleSLABreachConfig,
									},
								},
							},
						}, nil),
				},
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	1234,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:	ticketPb.Status_STATUS_OPEN,
								CommsNumber:	0,
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE,
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status:		rpcPb.StatusOk(),
				CommsDetails:	sampleTicketStatusMap[ticketPb.Status_STATUS_OPEN.String()].GetCommsDetails(),
			},
			wantErr:	false,
		},
		{
			name:	"success - issue specific comms fetched for sla breach",
			args: args{
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(context.Background(), "1234", issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS).Return(
						&issueConfigPb.IssueConfig{
							ConfigType:	issueConfigPb.ConfigType_CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS,
							ConfigPayload: &issueConfigPb.ConfigPayload{
								Config: &issueConfigPb.ConfigPayload_ManualTicketStageBasedCommsDetails{
									ManualTicketStageBasedCommsDetails: &manual_ticket_stage_wise_comms.ManualTicketStageBasedCommsDetails{
										TicketStatusCommsConfigMap:	sampleTicketStatusMap,
										SlaBreachCommsConfig:		sampleSLABreachConfig,
									},
								},
							},
						}, nil),
				},
				ctx:	context.Background(),
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	&watsonPb.IncidentDetailsForClient{IssueCategoryId: "1234", Client: types.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, ClientRequestId: "1234"},
					CommsMetadata: &watsonPb.CommsMetadataForClient{
						TicketId:	1234,
						CommsTypeMeta: &watsonPb.CommsMetadataForClient_ManualTicketStatusChangeCommsMeta{
							ManualTicketStatusChangeCommsMeta: &watsonPb.ManualTicketStatusChangeCommsMeta{
								CurStatus:		ticketPb.Status_STATUS_OPEN,
								CommsNumber:		0,
								TicketResolutionSla:	"2h",
							},
						},
					},
					CommsType:	watsonPb.CommsType_COMMS_TYPE_MANUAL_TICKET_SLA_BREACHED,
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status:		rpcPb.StatusOk(),
				CommsDetails:	sampleSLABreachConfig.GetCommsDetails(),
			},
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewManualTicketStageWiseCommsService(mockSupportTicketDao, mockIssueConfigDao, swcts.genConf)
			got, err := s.GetCommsDetails(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCommsDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetCommsDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManualTicketStageWiseCommsService_GetTicketDetails(t *testing.T) {
	t.Parallel(
	// Setup logger for test env
	)

	logger.Init(cfg.TestEnv)
	ctr := gomock.NewController(t)
	mockSupportTicketDao := mockDao.NewMockISupportTicketDao(ctr)
	mockIssueConfigDao := mockDao2.NewMockIssueConfigDao(ctr)
	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		request	*watsonPb.GetTicketDetailsRequest
	}
	tests := []struct {
		name	string
		args	args
		want	*watsonPb.GetTicketDetailsResponse
		wantErr	bool
	}{
		{
			name:	"success - unimplemented status",
			args: args{
				mocks:		nil,
				ctx:		context.Background(),
				request:	&watsonPb.GetTicketDetailsRequest{Incident: &watsonPb.IncidentDetailsForClient{}},
			},
			want: &watsonPb.GetTicketDetailsResponse{
				Status: rpcPb.StatusUnimplemented(),
			},
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewManualTicketStageWiseCommsService(mockSupportTicketDao, mockIssueConfigDao, swcts.genConf)
			got, err := s.GetTicketDetails(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTicketDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTicketDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}
