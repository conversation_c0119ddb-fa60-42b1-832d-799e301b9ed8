//go:generate mockgen -source=get_banners_helper.go -destination=../../test/mocks/sherlock_banners/helper/mock_get_banners_helper.go -package=mock_get_banners_helper
package helper

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	casbinPb "github.com/epifi/gamma/api/casbin"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/sherlock_banners/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
)

// GetBannersHelper is wrapper to get sherlock banners for CX service
type GetBannersHelper interface {
	GetBanners(ctx context.Context, req *sbPb.GetBannersRequest) (*sbPb.GetBannersResponse, error)
}

func NewGetBannersFromDb(sherlockBannerDao dao.ISherlockBannerDao,
	bannerMappingDao dao.IBannerMappingDao) *GetBannersFromDb {
	return &GetBannersFromDb{
		sherlockBannerDao: sherlockBannerDao,
		bannerMappingDao:  bannerMappingDao,
	}
}

type GetBannersFromDb struct {
	sherlockBannerDao dao.ISherlockBannerDao
	bannerMappingDao  dao.IBannerMappingDao
}

//nolint:funlen
func (g *GetBannersFromDb) GetBanners(ctx context.Context, req *sbPb.GetBannersRequest) (*sbPb.GetBannersResponse, error) {
	// check for valid page token for pagination
	pageToken, tokenErr := pagination.GetPageToken(req.GetPageContextRequest())
	if tokenErr != nil {
		cxLogger.Error(ctx, "invalid page token", zap.Error(tokenErr))
		return &sbPb.GetBannersResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(tokenErr.Error()),
		}, nil
	}

	bannerIdList := req.GetFilters().GetBannerIds()
	if mappingItems := req.GetFilters().GetMappingItems(); mappingItems != nil {
		mappings, err := g.bannerMappingDao.GetAllWithFilters(ctx, []sbPb.BannerMappingFieldMask{sbPb.BannerMappingFieldMask_BannerMappingFieldMask_BANNER_ID},
			dao.WithMappingTypeValuePairList(getMappingTypeValuePairs(mappingItems)))
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &sbPb.GetBannersResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "failed to get banners Ids for given mapping items", zap.Error(err))
			return &sbPb.GetBannersResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while fetching banners Ids mapped to given mapping items"),
			}, nil
		}
		bannerIdList = append(bannerIdList, getBannerIds(mappings)...)
	}

	var filters []storageV2.FilterOption
	filters = append(filters, dao.WithRowIdList(bannerIdList), dao.WithBannerStructureTypeList(req.GetFilters().GetStructureTypeList()))
	if req.GetActiveNowFlag() {
		filters = append(filters, dao.WithActiveAtTime(time.Now()))
	}
	banners, pageCtxResp, err := g.sherlockBannerDao.GetAllWithFilters(ctx, pageToken, int(req.GetPageContextRequest().GetPageSize()), filters...)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &sbPb.GetBannersResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		// logging here to avoid logging for not found cases
		logger.Error(ctx, "failed to get banners with given filters", zap.Error(err))
		return &sbPb.GetBannersResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching sherlock banners details for Ids"),
		}, nil
	}
	// Populate mapping item details for these banners
	bannersMap := make(map[string]*sbPb.SherlockBanner)
	bannerIdList = nil
	for idx := range banners {
		banners[idx].MappingItems = &sbPb.MappingItems{}
		bannerId := banners[idx].GetId()
		bannersMap[bannerId] = banners[idx]
		bannerIdList = append(bannerIdList, bannerId)
	}
	// TODO: avoid additional db call to populate mapping items. option 1: use subquery in first db call, option 2: store mapping list in sherlock_banners db as meta
	mappings, err := g.bannerMappingDao.GetAllWithFilters(ctx, nil, dao.WithBannerIdList(bannerIdList))
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &sbPb.GetBannersResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("record not found while fetching mapping details for banners"),
			}, nil
		}
		// logging here to avoid logging for not found cases
		logger.Error(ctx, "failed to get mapping details for banners", zap.Error(err))
		return &sbPb.GetBannersResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching mapping details for banners"),
		}, nil
	}
	for _, mapping := range mappings {
		switch mapping.GetMappingType() {
		case sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE:
			role, ok := casbinPb.AccessLevel_value[mapping.GetMappingValue()]
			if ok {
				bannersMap[mapping.GetBannerId()].MappingItems.SherlockUserRoles = append(bannersMap[mapping.GetBannerId()].MappingItems.SherlockUserRoles, casbinPb.AccessLevel(role))
			}
		case sbPb.MappingType_MAPPING_TYPE_ACTOR_ID:
			bannersMap[mapping.GetBannerId()].MappingItems.ActorIdList = append(bannersMap[mapping.GetBannerId()].MappingItems.ActorIdList, mapping.GetMappingValue())
		default:
		}
	}
	return &sbPb.GetBannersResponse{
		Status:              rpcPb.StatusOk(),
		Banners:             banners,
		PageContextResponse: pageCtxResp,
	}, nil
}

func getMappingTypeValuePairs(items *sbPb.MappingItems) []*sbPb.MappingTypeValuePair {
	var pairList []*sbPb.MappingTypeValuePair
	for _, role := range items.GetSherlockUserRoles() {
		pairList = append(pairList, &sbPb.MappingTypeValuePair{
			MappingType:  sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE,
			MappingValue: role.String(),
		})
	}
	for _, actorId := range items.GetActorIdList() {
		pairList = append(pairList, &sbPb.MappingTypeValuePair{
			MappingType:  sbPb.MappingType_MAPPING_TYPE_ACTOR_ID,
			MappingValue: actorId,
		})
	}
	return pairList
}

func getBannerIds(mappings []*sbPb.BannerMapping) []string {
	var bannerIds []string
	for _, mapping := range mappings {
		bannerIds = append(bannerIds, mapping.GetBannerId())
	}
	return bannerIds
}
