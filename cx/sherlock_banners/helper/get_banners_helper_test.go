package helper

import (
	"context"
	"flag"
	"os"
	"sort"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	casbinPb "github.com/epifi/gamma/api/casbin"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
	dao_mock "github.com/epifi/gamma/cx/test/mocks/sherlock_banners/dao"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, genConf, _, teardown := test.InitTestServer(true)

	ghTs = GetHelperTestSuite{
		genConf: genConf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type GetHelperTestSuite struct {
	genConf *cxGenConf.Config
}

var (
	ghTs                   GetHelperTestSuite
	banner1WithoutMappings = &sbPb.SherlockBanner{
		Id:            "banner-id-1",
		BannerContent: &sbPb.SherlockBannerContent{Title: "t1", Body: "b1"},
		StartTime:     "2022-06-13T15:30:00",
		EndTime:       "2122-03-13T15:30:00",
	}

	banner1 = &sbPb.SherlockBanner{
		Id:            "banner-id-1",
		BannerContent: &sbPb.SherlockBannerContent{Title: "t1", Body: "b1"},
		StartTime:     "2022-06-13T15:30:00",
		EndTime:       "2122-03-13T15:30:00",
		MappingItems: &sbPb.MappingItems{
			SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT, casbinPb.AccessLevel_FEDERAL_AGENT},
		},
	}
)

func TestGetBannersFromDb_GetBanners(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	sherlockBannerDao := dao_mock.NewMockISherlockBannerDao(ctr)
	bannerMappingDao := dao_mock.NewMockIBannerMappingDao(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *sbPb.GetBannersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.GetBannersResponse
		wantErr bool
	}{
		{
			name: "invalid page token",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{PageContextRequest: &rpcPb.PageContextRequest{Token: &rpcPb.PageContextRequest_AfterToken{AfterToken: "random"}}},
			},
			want: &sbPb.GetBannersResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("failed to unmarshal page token: illegal base64 data at input byte 4"),
			},
			wantErr: false,
		},
		{
			name: "No active banners found",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{},
				mocks: []interface{}{
					sherlockBannerDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "bannerIds not found for given mapping filters",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{Filters: &sbPb.BannerFilters{MappingItems: &sbPb.MappingItems{
					SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT},
				}}},
				mocks: []interface{}{
					bannerMappingDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error while getting bannerIds from mapping dao",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{Filters: &sbPb.BannerFilters{MappingItems: &sbPb.MappingItems{
					SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT},
				}}},
				mocks: []interface{}{
					bannerMappingDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("fail")),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while fetching banners Ids mapped to given mapping items"),
			},
			wantErr: false,
		},
		{
			name: "records not found from banner dao",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{},
				mocks: []interface{}{
					sherlockBannerDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error while getting banners details from banner dao",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{},
				mocks: []interface{}{
					sherlockBannerDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, errors.New("fail")),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while fetching sherlock banners details for Ids"),
			},
			wantErr: false,
		},
		{
			name: "error getting mapping details for banners",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{},
				mocks: []interface{}{
					sherlockBannerDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sbPb.SherlockBanner{banner1WithoutMappings}, nil, nil),
					bannerMappingDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("fail")),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while fetching mapping details for banners"),
			},
			wantErr: false,
		},
		{
			name: "success: with mapping items",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{Filters: &sbPb.BannerFilters{MappingItems: &sbPb.MappingItems{
					SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT},
				}}},
				mocks: []interface{}{
					bannerMappingDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sbPb.BannerMapping{}, nil).Times(2),
					sherlockBannerDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sbPb.SherlockBanner{banner1WithoutMappings}, nil, nil),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{banner1WithoutMappings},
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{},
				mocks: []interface{}{
					sherlockBannerDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sbPb.SherlockBanner{banner1WithoutMappings}, nil, nil),
					bannerMappingDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sbPb.BannerMapping{
						{BannerId: banner1WithoutMappings.GetId(), MappingType: sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE, MappingValue: "AGENT"},
						{BannerId: banner1WithoutMappings.GetId(), MappingType: sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE, MappingValue: "FEDERAL_AGENT"},
					}, nil),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{banner1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewGetBannersFromDb(sherlockBannerDao, bannerMappingDao)
			got, err := s.GetBanners(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBanners() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualGetBannersResp(got, tt.want) {
				t.Errorf("GetBanners() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isDeepEqualGetBannersResp(got, want *sbPb.GetBannersResponse) bool {
	return proto.Equal(got.GetStatus(), want.GetStatus()) &&
		proto.Equal(got.GetPageContextResponse(), want.GetPageContextResponse()) &&
		isDeepEqualSherlockBannersList(got.GetBanners(), want.GetBanners())
}

func isDeepEqualSherlockBannersList(got, want []*sbPb.SherlockBanner) bool {
	if len(got) != len(want) {
		return false
	}
	sort.Slice(got, func(i, j int) bool {
		return got[i].GetId() < got[j].GetId()
	})
	sort.Slice(want, func(i, j int) bool {
		return want[i].GetId() < want[j].GetId()
	})
	for i := 0; i < len(got); i++ {
		if !(isSherlockBannerEqual(got[i], want[i])) {
			return false
		}
	}
	return true
}

func isSherlockBannerEqual(got, want *sbPb.SherlockBanner) bool {
	return proto.Equal(got.GetBannerContent(), want.GetBannerContent()) &&
		got.GetStartTime() == want.GetStartTime() && got.GetEndTime() == want.GetEndTime() &&
		isDeepEqualMappingItems(got.GetMappingItems(), want.GetMappingItems())
}

func isDeepEqualMappingItems(got, want *sbPb.MappingItems) bool {
	gotRoles := got.GetSherlockUserRoles()
	wantRoles := want.GetSherlockUserRoles()
	if len(gotRoles) != len(wantRoles) {
		return false
	}
	sort.Slice(gotRoles, func(i, j int) bool {
		return int(gotRoles[i]) < int(gotRoles[j])
	})
	sort.Slice(wantRoles, func(i, j int) bool {
		return int(wantRoles[i]) < int(wantRoles[j])
	})
	for i := 0; i < len(gotRoles); i++ {
		if gotRoles[i] != wantRoles[i] {
			return false
		}
	}
	return true
}
