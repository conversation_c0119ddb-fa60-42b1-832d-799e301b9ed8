package dao

import (
	"time"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	gormV2 "gorm.io/gorm"
)

// WithRowIdList sets the filter for list of rowIds of the table if the list is not empty
// used for sherlock_banners and banner_mappings table
func WithRowIdList(rowIdList []string) storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		if len(rowIdList) != 0 {
			db = db.Where("id IN (?)", rowIdList)
		}
		return db
	})
}

// WithActiveAtTime sets the filter on sherlock_banners for checking if the element
// is active w.r.t. time i.e. whether the given time lies between the start_time and end_time
func WithActiveAtTime(time time.Time) storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		db = db.Where("start_time <= ?", time)
		db = db.Where("end_time > ?", time)
		return db
	})
}

// WithBannerStructureTypeList sets the filter for list of SherlockBannerStructureType
// for the sherlock_banners table if the list is not empty
func WithBannerStructureTypeList(structureTypeList []sbPb.SherlockBannerStructureType) storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		if len(structureTypeList) != 0 {
			db = db.Where("structure_type IN (?)", structureTypeList)
		}
		return db
	})
}

// WithBannerIdList sets the filter for list of bannerIds for the
// banner_mappings table if the list is not empty
func WithBannerIdList(elementIdList []string) storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		if len(elementIdList) != 0 {
			db = db.Where("banner_id IN (?)", elementIdList)
		}
		return db
	})
}

// WithMappingTypeList sets the filter for list of MappingTypes
// for the banner_mappings table if the list is not empty
func WithMappingTypeList(mappingTypeList []sbPb.MappingType) storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		if len(mappingTypeList) != 0 {
			db = db.Where("mapping_type IN (?)", mappingTypeList)
		}
		return db
	})
}

// WithMappingTypeValuePairList sets the filter for list of MappingTypeValuePair{mapping_type,mapping_value}
// for the banner_mappings table if the list is not empty
func WithMappingTypeValuePairList(mappingDetailsList []*sbPb.MappingTypeValuePair) storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		if len(mappingDetailsList) != 0 {
			var compareList [][]interface{}
			for _, mapping := range mappingDetailsList {
				compareList = append(compareList, []interface{}{mapping.MappingType.String(), mapping.MappingValue})
			}
			db = db.Where("(mapping_type, mapping_value) IN (?)", compareList)
		}
		return db
	})
}
