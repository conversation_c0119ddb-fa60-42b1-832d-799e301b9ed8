package dao_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"sort"
	"testing"
	"time"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/sherlock_banners/dao"
	"github.com/epifi/gamma/cx/test"
	"github.com/epifi/be-common/pkg/pagination"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"
)

type SherlockBannerDAOTestSuite struct {
	db                *gormV2.DB
	conf              *config.Config
	sherlockBannerDao dao.ISherlockBannerDao
}

var (
	sbdTS               SherlockBannerDAOTestSuite
	createBannerSuccess = &sbPb.Sherlock<PERSON>anner{
		BannerContent: &sbPb.Sherlock<PERSON>annerContent{
			Title: "title-1",
			Body:  "body-1",
		},
		StartTime: "2022-06-13T15:30:00",
		EndTime:   "2122-06-13T15:30:00",
		IsActive:  commontypes.BooleanEnum_TRUE,
	}
	createBannerV2TitleBodySuccess = &sbPb.SherlockBanner{
		StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_STANDARD_TITLE_BODY,
		BannerContentV2: &sbPb.SherlockBannerContentV2{
			Content: &sbPb.SherlockBannerContentV2_TitleBodyContent{
				TitleBodyContent: &sbPb.SherlockBannerTitleBodyContent{
					Title: "title-1",
					Body:  "body-1"},
			},
		},
		StartTime: "2023-01-13T15:30:00",
		EndTime:   "2123-01-15T15:30:00",
		IsActive:  commontypes.BooleanEnum_TRUE,
	}
	createBannerV2LabelMessageSuccess = &sbPb.SherlockBanner{
		StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST,
		BannerContentV2: &sbPb.SherlockBannerContentV2{
			Content: &sbPb.SherlockBannerContentV2_LabelMessageListContent{
				LabelMessageListContent: &sbPb.SherlockBannerLabelMessageListContent{
					LabelMessageList: []*sbPb.SherlockBannerLabelMessageListContent_LabelMessage{{Label: "label1", Message: "Message 1"}},
				},
			},
		},
		StartTime: "2023-01-14T15:30:00",
		EndTime:   "2123-01-15T15:30:00",
		IsActive:  commontypes.BooleanEnum_TRUE,
	}
)

func TestSherlockBannerDao_Create(t *testing.T) {
	type args struct {
		ctx    context.Context
		banner *sbPb.SherlockBanner
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.SherlockBanner
		wantErr bool
	}{
		{
			name: "no parameters",
			args: args{
				ctx:    context.Background(),
				banner: &sbPb.SherlockBanner{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "start time parse error",
			args: args{
				ctx:    context.Background(),
				banner: &sbPb.SherlockBanner{BannerContent: &sbPb.SherlockBannerContent{Title: "title-1", Body: "body-1"}},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "end time parse error",
			args: args{
				ctx:    context.Background(),
				banner: &sbPb.SherlockBanner{BannerContent: &sbPb.SherlockBannerContent{Title: "title-1", Body: "body-1"}, StartTime: "2022-06-13T15:30:00", EndTime: ""},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx:    context.Background(),
				banner: createBannerSuccess,
			},
			want:    createBannerSuccess,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateTestDatabase(t, sbdTS.db, sbdTS.conf.EpifiDb.GetName())
			got, err := sbdTS.sherlockBannerDao.Create(tt.args.ctx, tt.args.banner)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSherlockBannerDao_CreateBatch(t *testing.T) {
	type args struct {
		ctx         context.Context
		bannersList []*sbPb.SherlockBanner
	}
	tests := []struct {
		name    string
		args    args
		want    []*sbPb.SherlockBanner
		wantErr bool
	}{
		{
			name: "mandatory parameters missing",
			args: args{
				ctx:         context.Background(),
				bannersList: []*sbPb.SherlockBanner{{BannerContentV2: &sbPb.SherlockBannerContentV2{}}},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success: one banner create",
			args: args{
				ctx:         context.Background(),
				bannersList: []*sbPb.SherlockBanner{createBannerSuccess},
			},
			want:    []*sbPb.SherlockBanner{createBannerSuccess},
			wantErr: false,
		},
		{
			name: "success: multiple banner create v1 and v2",
			args: args{
				ctx:         context.Background(),
				bannersList: []*sbPb.SherlockBanner{createBannerSuccess, createBannerV2LabelMessageSuccess, createBannerV2TitleBodySuccess},
			},
			want:    []*sbPb.SherlockBanner{createBannerSuccess, createBannerV2LabelMessageSuccess, createBannerV2TitleBodySuccess},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateTestDatabase(t, sbdTS.db, sbdTS.conf.EpifiDb.GetName())
			got, err := sbdTS.sherlockBannerDao.CreateBatch(tt.args.ctx, tt.args.bannersList)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualSherlockBannerList(got, tt.want) {
				t.Errorf("CreateBatch() got = %v, want %v", got, tt.want)
			}

		})
	}
}

func isDeepEqualSherlockBannerList(want, got []*sbPb.SherlockBanner) bool {
	if len(want) != len(got) {
		return false
	}
	sort.Slice(want, func(i, j int) bool {
		return want[i].GetStartTime() < want[j].GetStartTime()
	})
	sort.Slice(got, func(i, j int) bool {
		return got[i].GetStartTime() < got[j].GetStartTime()
	})
	for i := 0; i < len(want); i++ {
		if !(isSherlockBannerEqual(want[i], got[i])) {
			return false
		}
	}

	return true
}

func isSherlockBannerEqual(want, got *sbPb.SherlockBanner) bool {
	want.Id = got.GetId()
	want.CreatedAt = got.GetCreatedAt()
	want.UpdatedAt = got.GetUpdatedAt()
	return reflect.DeepEqual(got, want)
}

func TestSherlockBannerDao_Update(t *testing.T) {
	type args struct {
		ctx        context.Context
		banner     *sbPb.SherlockBanner
		updateMask []sbPb.BannerUpdateFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "update mask empty",
			args: args{
				ctx:    context.Background(),
				banner: &sbPb.SherlockBanner{Id: "id"},
			},
			wantErr: true,
		},
		{
			name: "banner ID empty",
			args: args{
				ctx:        context.Background(),
				banner:     &sbPb.SherlockBanner{},
				updateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_START_TIME},
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				banner: &sbPb.SherlockBanner{
					Id:        "f7918eab-1a97-4044-8560-52d6ef282d0f",
					StartTime: "2022-06-13T15:30:00",
					EndTime:   "2122-06-13T15:30:00",
				},
				updateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_START_TIME},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, sbdTS.db, sbdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			err := sbdTS.sherlockBannerDao.Update(tt.args.ctx, tt.args.banner, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestSherlockBannerDao_DeleteBatch(t *testing.T) {
	type args struct {
		ctx       context.Context
		bannerIds []string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "banner Id list empty",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "db error : invalid uuid",
			args: args{
				ctx:       context.Background(),
				bannerIds: []string{"rand-banner-id"},
			},
			wantErr: true,
		},
		{
			name: "no records found",
			args: args{
				ctx:       context.Background(),
				bannerIds: []string{"f7918eab-1a97-4044-8560-52d6ef281111"},
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx:       context.Background(),
				bannerIds: []string{"f7918eab-1a97-4044-8560-52d6ef282d0f"},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, sbdTS.db, sbdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			err := sbdTS.sherlockBannerDao.DeleteBatch(tt.args.ctx, tt.args.bannerIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("Delete() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestSherlockBannerDao_GetAllWithFilters(t *testing.T) {
	type args struct {
		ctx       context.Context
		pageToken *pagination.PageToken
		pageSize  int
		filters   []storageV2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		wantlen int
		wantErr bool
	}{
		{
			name: "db error invalid uuid",
			args: args{
				ctx: context.Background(),
				filters: []storageV2.FilterOption{
					dao.WithRowIdList([]string{"rand-row-id"}),
				},
			},
			wantlen: 0,
			wantErr: true,
		},
		{
			name: "no records found",
			args: args{
				ctx: context.Background(),
				filters: []storageV2.FilterOption{
					dao.WithRowIdList([]string{"f7918eab-1a97-4044-8560-52d6ef281111"}),
				},
			},
			wantlen: 0,
			wantErr: true,
		},
		{
			name: "currently active elements",
			args: args{
				ctx:      context.Background(),
				pageSize: 10,
				filters: []storageV2.FilterOption{
					dao.WithActiveAtTime(time.Now()),
				},
			},
			wantlen: 2,
			wantErr: false,
		},
		{
			name: "no filters - all records",
			args: args{
				ctx:      context.Background(),
				pageSize: 10,
			},
			wantlen: 3,
			wantErr: false,
		},
		{
			name: "limit on page size",
			args: args{
				ctx:      context.Background(),
				pageSize: 1,
			},
			wantlen: 1,
			wantErr: false,
		},
		{
			name: "limit on page size with page token",
			args: args{
				ctx: context.Background(),
				pageToken: &pagination.PageToken{
					Timestamp: &timestampPb.Timestamp{
						Seconds: 1643354926,
						Nanos:   310395000,
					},
					Offset:    1,
					IsReverse: false,
				},
				pageSize: 1,
			},
			wantlen: 1,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, sbdTS.db, sbdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, _, err := sbdTS.sherlockBannerDao.GetAllWithFilters(tt.args.ctx, tt.args.pageToken, tt.args.pageSize, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllWithFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(len(got), tt.wantlen) {
				t.Errorf("GetAllWithFilters() got = %v, want %v", got, tt.wantlen)
			}
		})
	}
}
