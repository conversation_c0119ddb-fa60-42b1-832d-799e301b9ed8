package dao

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	casbinPb "github.com/epifi/gamma/api/casbin"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	"github.com/epifi/gamma/cx/sherlock_banners/dao/model"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	gormV2 "gorm.io/gorm"
)

// BannerMappingDao holds the dependencies required to access DB models
type BannerMappingDao struct {
	db *gormV2.DB
}

var _ IBannerMappingDao = &BannerMappingDao{}

// NewBannerMappingDao creates a new BannerMappingDao instance
func NewBannerMappingDao(db types.SherlockPGDB) *BannerMappingDao {
	return &BannerMappingDao{
		db: db,
	}
}

var bannerMappingColumnMap = map[sbPb.BannerMappingFieldMask]string{
	sbPb.BannerMappingFieldMask_BannerMappingFieldMask_BANNER_ID:     "banner_id",
	sbPb.BannerMappingFieldMask_BannerMappingFieldMask_MAPPING_TYPE:  "mapping_type",
	sbPb.BannerMappingFieldMask_BannerMappingFieldMask_MAPPING_VALUE: "mapping_value",
}

func (b *BannerMappingDao) CreateBatch(ctx context.Context, mappings []*sbPb.BannerMapping) ([]*sbPb.BannerMapping, error) {
	defer metric_util.TrackDuration("cx/sherlock_banners/dao", "BannerMappingDao", "CreateBatch", time.Now())
	if len(mappings) == 0 {
		return nil, errors.New("mapping list cannot be empty")
	}

	for _, mapping := range mappings {
		// bannerId cannot be empty
		_, err := uuid.Parse(mapping.GetBannerId())
		if err != nil {
			return nil, errors.New("banner ID given is not a valid uuid")
		}
		if err = validateMappingTypeValue(mapping.GetMappingType(), mapping.GetMappingValue()); err != nil {
			return nil, err
		}
	}

	db := gormctxv2.FromContextOrDefault(ctx, b.db)

	mappingModelsList := model.NewBannerMappingModelList(mappings)
	if err := db.Create(mappingModelsList).Error; err != nil {
		if storageV2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, errors.Wrap(err, "failed to insert in app targeted comms mappings into db")
	}
	return model.BannerMappingListToProtos(mappingModelsList), nil
}

func (b *BannerMappingDao) DeleteBatch(ctx context.Context, filters ...storageV2.FilterOption) (int64, error) {
	defer metric_util.TrackDuration("cx/sherlock_banners/dao", "BannerMappingDao", "DeleteBatch", time.Now())
	// do no allow delete-all i.e. at least one filter must be applied
	if len(filters) == 0 {
		return 0, errors.New("Disallow delete all: at least one filter must be provided")
	}
	query := gormctxv2.FromContextOrDefault(ctx, b.db)
	// Apply all provided filters
	for _, opt := range filters {
		query = opt.ApplyInGorm(query)
	}
	query = query.Delete(model.BannerMapping{})
	if query.Error != nil {
		return 0, errors.Wrap(query.Error, "error while deleting banner mappings from db")
	}
	if query.RowsAffected == 0 {
		return 0, epifierrors.ErrRecordNotFound
	}
	return query.RowsAffected, nil
}

func (b *BannerMappingDao) GetAllWithFilters(ctx context.Context, distinctColumnMask []sbPb.BannerMappingFieldMask, filters ...storageV2.FilterOption) ([]*sbPb.BannerMapping, error) {
	defer metric_util.TrackDuration("cx/sherlock_banners/dao", "BannerMappingDao", "GetAllWithFilters", time.Now())
	var mappingModels []*model.BannerMapping
	query := gormctxv2.FromContextOrDefault(ctx, b.db)
	// Apply all provided filters
	for _, opt := range filters {
		query = opt.ApplyInGorm(query)
	}
	if len(distinctColumnMask) != 0 {
		query = query.Distinct(getBannerMappingColumnsFromMask(distinctColumnMask))
	}
	if err := query.Find(&mappingModels).Error; err != nil {
		return nil, errors.Wrap(err, "failed to fetch mapping data")
	}
	if len(mappingModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return model.BannerMappingListToProtos(mappingModels), nil
}

func validateMappingTypeValue(mappingType sbPb.MappingType, mappingValue string) error {
	switch mappingType {
	case sbPb.MappingType_MAPPING_TYPE_UNSPECIFIED:
		return errors.New("mapping type cannot be unspecified")
	case sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE:
		enumVal, ok := casbinPb.AccessLevel_value[mappingValue]
		if !ok || enumVal == 0 {
			return errors.New("invalid sherlock user role provided")
		}
	case sbPb.MappingType_MAPPING_TYPE_ACTOR_ID:
		if mappingValue == "" {
			return errors.New("actor Id cannot be empty")
		}
	default:
		return errors.New("invalid mapping type provided")
	}
	return nil
}

func getBannerMappingColumnsFromMask(columnMask []sbPb.BannerMappingFieldMask) []string {
	var selectColumns []string
	for _, field := range columnMask {
		selectColumns = append(selectColumns, bannerMappingColumnMap[field])
	}
	return selectColumns
}
