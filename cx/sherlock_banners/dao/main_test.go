package dao_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/sherlock_banners/dao"
	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, db, teardown := test.InitTestServer(true)

	sbdTS = SherlockBannerDAOTestSuite{
		db:                db,
		conf:              conf,
		sherlockBannerDao: dao.NewSherlockBannerDao(db),
	}

	bmdTS = BannerMappingDAOTestSuite{
		db:    db,
		conf:  conf,
		bmDao: dao.NewBannerMappingDao(db),
	}

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
