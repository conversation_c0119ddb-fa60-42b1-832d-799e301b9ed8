package dao

import (
	"context"
	"time"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	"github.com/epifi/gamma/cx/sherlock_banners/dao/model"

	"github.com/pkg/errors"
	gormV2 "gorm.io/gorm"
)

// SherlockBannerDao holds the dependencies required to access DB models
type SherlockBannerDao struct {
	db *gormV2.DB
}

var _ ISherlockBannerDao = &SherlockBannerDao{}

// NewSherlockBannerDao creates a new SherlockBannerDao instance
func NewSherlockBannerDao(db types.SherlockPGDB) *SherlockBannerDao {
	return &SherlockBannerDao{
		db: db,
	}
}

var sherlockBannerColumnMap = map[sbPb.BannerUpdateFieldMask]string{
	sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_TITLE: "banner_content",
	sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_BODY:  "banner_content",
	sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_START_TIME:    "start_time",
	sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_END_TIME:      "end_time",
}

func (s *SherlockBannerDao) Create(ctx context.Context, banner *sbPb.SherlockBanner) (*sbPb.SherlockBanner, error) {
	defer metric_util.TrackDuration("cx/sherlock_banners/dao", "SherlockBannerDao", "Create", time.Now())
	if err := validateBannerForCreate(banner); err != nil {
		return nil, errors.Wrap(err, "banner validation failed for create")
	}
	sherlockBannerModel := model.NewSherlockBannerFromProtoMsg(banner)
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	if err := db.Create(sherlockBannerModel).Error; err != nil {
		return nil, errors.Wrap(err, "error while creating sherlock banner record in db")
	}

	return sherlockBannerModel.ToProtoMessage(), nil
}

func (s *SherlockBannerDao) CreateBatch(ctx context.Context, bannerList []*sbPb.SherlockBanner) ([]*sbPb.SherlockBanner, error) {
	defer metric_util.TrackDuration("cx/sherlock_banners/dao", "SherlockBannerDao", "CreateBatch", time.Now())
	var sherlockBannerModelList []*model.SherlockBanner
	for _, banner := range bannerList {
		if err := validateBannerForCreate(banner); err != nil {
			return nil, errors.Wrap(err, "banner validation failed for create")
		}
		sherlockBannerModelList = append(sherlockBannerModelList, model.NewSherlockBannerFromProtoMsg(banner))
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	if err := db.Create(sherlockBannerModelList).Error; err != nil {
		return nil, errors.Wrap(err, "error while creating sherlock banner record in db")
	}

	return model.SherlockBannerListToProtos(sherlockBannerModelList), nil
}

func (s *SherlockBannerDao) Update(ctx context.Context, banner *sbPb.SherlockBanner, updateMask []sbPb.BannerUpdateFieldMask) error {
	defer metric_util.TrackDuration("cx/sherlock_banners/dao", "SherlockBannerDao", "Update", time.Now())
	if len(updateMask) == 0 {
		return errors.New("update mask cannot be empty")
	}
	if banner.GetId() == "" {
		return errors.New("banner id cannot be empty")
	}
	bannerModel := model.NewSherlockBannerFromProtoMsg(banner)
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	query := db.Model(&model.SherlockBanner{}).Where("id = ?", banner.GetId()).
		Select(getSherlockBannerColumnsForUpdate(updateMask)).Updates(bannerModel)
	if query.Error != nil {
		return errors.Wrap(query.Error, "error while updating sherlock banner in db")
	}
	return nil
}

func getSherlockBannerColumnsForUpdate(updateMask []sbPb.BannerUpdateFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMask {
		selectColumns = append(selectColumns, sherlockBannerColumnMap[field])
	}
	return selectColumns
}

func (s *SherlockBannerDao) DeleteBatch(ctx context.Context, ids []string) error {
	defer metric_util.TrackDuration("cx/sherlock_banners/dao", "SherlockBannerDao", "DeleteBatch", time.Now())
	if len(ids) == 0 {
		return errors.New("id list cannot be empty")
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)

	query := db.Where("id IN (?)", ids).Delete(model.SherlockBanner{})
	if query.Error != nil {
		return errors.Wrap(query.Error, "error while deleting sherlock banners from db")
	}
	if query.RowsAffected == 0 {
		return epifierrors.ErrRecordNotFound
	}
	return nil
}

func (s *SherlockBannerDao) GetAllWithFilters(ctx context.Context, pageToken *pagination.PageToken, pageSize int, filters ...storageV2.FilterOption) ([]*sbPb.SherlockBanner, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("cx/sherlock_banners/dao", "SherlockBannerDao", "GetAllWithFilters", time.Now())
	var sherlockBannerModelList []*model.SherlockBanner
	query := gormctxv2.FromContextOrDefault(ctx, s.db)
	// Apply all provided filters
	for _, opt := range filters {
		query = opt.ApplyInGorm(query)
	}
	if pageToken != nil {
		if pageToken.Timestamp != nil {
			if pageToken.IsReverse {
				query = query.Where("updated_at >= ?", pageToken.Timestamp.AsTime())
			} else {
				query = query.Where("updated_at <= ?", pageToken.Timestamp.AsTime())
			}
		}
		query = query.Offset(int(pageToken.Offset))
	}
	if err := query.Order("updated_at desc").Limit(pageSize + 1).Find(&sherlockBannerModelList).Error; err != nil {
		return nil, nil, errors.Wrap(err, "cannot get sherlockBanner records")
	}
	if len(sherlockBannerModelList) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, pageSize, model.SherlockBanners(sherlockBannerModelList))
	if err != nil {
		return nil, nil, errors.Wrap(err, "error in getting page context response")
	}

	sherlockBannerModelList = rows.(model.SherlockBanners)

	var sherlockBannerProtoList []*sbPb.SherlockBanner
	for _, bannerModel := range sherlockBannerModelList {
		sherlockBannerProtoList = append(sherlockBannerProtoList, bannerModel.ToProtoMessage())
	}
	return sherlockBannerProtoList, pageCtxResp, nil
}

// Here we validate only in terms of db constraints.
// Other validations on banner are done at service layer
func validateBannerForCreate(banner *sbPb.SherlockBanner) error {
	if banner.GetBannerContent() == nil && banner.GetBannerContentV2() == nil {
		return errors.New("banner content cannot be nil")
	}
	if banner.GetStartTime() == "" {
		return errors.New("start time cannot be empty")
	}
	if banner.GetEndTime() == "" {
		return errors.New("end time cannot be empty")
	}
	return nil
}
