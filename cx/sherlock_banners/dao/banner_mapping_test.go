package dao_test

import (
	"context"
	"reflect"
	"testing"

	casbinPb "github.com/epifi/gamma/api/casbin"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/sherlock_banners/dao"
	"github.com/epifi/gamma/cx/test"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"

	gormV2 "gorm.io/gorm"
)

type BannerMappingDAOTestSuite struct {
	db    *gormV2.DB
	conf  *config.Config
	bmDao dao.IBannerMappingDao
}

const (
	actorId1 = "actor-id-1"
)

var (
	bmdTS BannerMappingDAOTestSuite

	mappingRoleInvalid = &sbPb.MappingTypeValuePair{
		MappingType:  sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE,
		MappingValue: "invalid",
	}
	mappingRoleAgent = &sbPb.MappingTypeValuePair{
		MappingType:  sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE,
		MappingValue: "AGENT",
	}
	rowIdList1                = []string{"191647cd-8f72-43c8-b567-fcc29a567a83"}
	bannerIdList1             = []string{"cd572fac-8339-4010-b1e2-38f46499ea6a", "f7918eab-1a97-4044-8560-52d6ef282d0f"}
	elementIdList2            = []string{"invalid-id1", "invalid-id2"}
	mappingTypeValuePairList1 = []*sbPb.MappingTypeValuePair{mappingRoleAgent, mappingRoleInvalid}
	mappingTypeList1          = []sbPb.MappingType{sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE}
)

func TestBannerMappingDao_CreateBatch(t *testing.T) {
	type args struct {
		ctx     context.Context
		mapping []*sbPb.BannerMapping
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "empty mapping list",
			args: args{
				ctx:     context.Background(),
				mapping: []*sbPb.BannerMapping{},
			},
			wantErr: true,
		},
		{
			name: "invalid banner ID",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.BannerMapping{{
					BannerId: "banner-id-1",
				}},
			},
			wantErr: true,
		},
		{
			name: "invalid mapping type",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.BannerMapping{{
					BannerId:    "f7918eab-1a97-4044-8560-52d6ef282d11",
					MappingType: sbPb.MappingType_MAPPING_TYPE_UNSPECIFIED,
				}},
			},
			wantErr: true,
		},
		{
			name: "invalid Sherlock user role",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.BannerMapping{{
					BannerId:     "f7918eab-1a97-4044-8560-52d6ef282d11",
					MappingType:  sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE,
					MappingValue: casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED.String(),
				}},
			},
			wantErr: true,
		},
		{
			name: "duplicate entry",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.BannerMapping{
					{BannerId: "cd572fac-8339-4010-b1e2-38f46499ea6a", MappingType: sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE, MappingValue: casbinPb.AccessLevel_AGENT.String()},
					{BannerId: "cd572fac-8339-4010-b1e2-38f46499ea6a", MappingType: sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE, MappingValue: casbinPb.AccessLevel_AGENT.String()},
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.BannerMapping{
					{BannerId: "cd572fac-8339-4010-b1e2-38f46499ea6a", MappingType: sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE, MappingValue: casbinPb.AccessLevel_AGENT.String()},
					{BannerId: "cd572fac-8339-4010-b1e2-38f46499ea6a", MappingType: sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE, MappingValue: casbinPb.AccessLevel_BIZ_ADMIN.String()},
					{BannerId: "cd572fac-8339-4010-b1e2-38f46499ea6b", MappingType: sbPb.MappingType_MAPPING_TYPE_ACTOR_ID, MappingValue: actorId1},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateTestDatabaseTables(t, bmdTS.db, bmdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			_, err := bmdTS.bmDao.CreateBatch(tt.args.ctx, tt.args.mapping)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestBannerMappingDao_DeleteBatch(t *testing.T) {
	type args struct {
		ctx     context.Context
		filters []storageV2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    int64
		wantErr bool
	}{
		{
			name: "all lists empty",
			args: args{
				ctx: context.Background(),
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				ctx:     context.Background(),
				filters: []storageV2.FilterOption{dao.WithBannerIdList([]string{"191647cd-8f72-43c8-b567-fcc29a567a83"})},
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "rowId list",
			args: args{
				ctx:     context.Background(),
				filters: []storageV2.FilterOption{dao.WithRowIdList(rowIdList1)},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "bannerId List",
			args: args{
				ctx:     context.Background(),
				filters: []storageV2.FilterOption{dao.WithBannerIdList(bannerIdList1)},
			},
			want:    4,
			wantErr: false,
		},
		{
			name: "mappingDetails List",
			args: args{
				ctx:     context.Background(),
				filters: []storageV2.FilterOption{dao.WithMappingTypeValuePairList(mappingTypeValuePairList1)},
			},
			want:    2,
			wantErr: false,
		},
		{
			name: "two lists",
			args: args{
				ctx: context.Background(),
				filters: []storageV2.FilterOption{
					dao.WithMappingTypeValuePairList(mappingTypeValuePairList1),
					dao.WithBannerIdList(bannerIdList1),
				},
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, bmdTS.db, bmdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := bmdTS.bmDao.DeleteBatch(tt.args.ctx, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("DeleteBatch() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBannerMappingDao_GetAllWithFilters(t *testing.T) {
	type args struct {
		ctx                context.Context
		distinctColumnMask []sbPb.BannerMappingFieldMask
		filters            []storageV2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		wantlen int
		wantErr bool
	}{
		{
			name: "records not found",
			args: args{
				ctx:     context.Background(),
				filters: []storageV2.FilterOption{dao.WithBannerIdList(elementIdList2)},
			},
			wantlen: 0,
			wantErr: true,
		},
		{
			name: "banner id list and mapping type value pair list",
			args: args{
				ctx: context.Background(),
				filters: []storageV2.FilterOption{
					dao.WithMappingTypeValuePairList(mappingTypeValuePairList1),
					dao.WithBannerIdList(bannerIdList1),
				},
			},
			wantlen: 1,
			wantErr: false,
		},
		{
			name: "banner id list and mapping type",
			args: args{
				ctx:                context.Background(),
				distinctColumnMask: []sbPb.BannerMappingFieldMask{sbPb.BannerMappingFieldMask_BannerMappingFieldMask_BANNER_ID},
				filters: []storageV2.FilterOption{
					dao.WithBannerIdList(bannerIdList1),
					dao.WithMappingTypeList(mappingTypeList1),
				},
			},
			wantlen: 2,
			wantErr: false,
		},
		{
			name: "all filters",
			args: args{
				ctx: context.Background(),
				filters: []storageV2.FilterOption{
					dao.WithBannerIdList(bannerIdList1),
					dao.WithMappingTypeValuePairList(mappingTypeValuePairList1),
					dao.WithMappingTypeList(mappingTypeList1),
				},
			},
			wantlen: 1,
			wantErr: false,
		},
		{
			name: "no filters i.e. all mappings",
			args: args{
				ctx: context.Background(),
			},
			wantlen: 5,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, bmdTS.db, bmdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := bmdTS.bmDao.GetAllWithFilters(tt.args.ctx, tt.args.distinctColumnMask, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllWithFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(len(got), tt.wantlen) {
				t.Errorf("GetAllWithFilters() got = %v, want %v", got, tt.wantlen)
			}
		})
	}
}
