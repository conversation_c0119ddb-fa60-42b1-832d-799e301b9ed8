package model

import (
	"time"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"

	"google.golang.org/protobuf/types/known/timestamppb"
)

type BannerMapping struct {
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	// id from SherlockBanners table
	BannerId string
	// the type of mapping for this row
	MappingType sbPb.MappingType
	// value corresponding to the mapping type
	MappingValue string
	// timestamp at which this row was created in DB
	CreatedAt time.Time
	// timestamp at which this row was last updated
	UpdatedAt time.Time
}

func NewBannerMappingFromProtoMsg(msg *sbPb.BannerMapping) *BannerMapping {
	modelMsg := &BannerMapping{
		BannerId:     msg.GetBannerId(),
		MappingType:  msg.GetMappingType(),
		MappingValue: msg.GetMappingValue(),
	}
	return modelMsg
}

func NewBannerMappingModelList(protoList []*sbPb.BannerMapping) []*BannerMapping {
	var mappingModels []*BannerMapping
	for _, mappingProto := range protoList {
		mappingModels = append(mappingModels, NewBannerMappingFromProtoMsg(mappingProto))
	}
	return mappingModels
}

func (b *BannerMapping) ToProtoMessage() *sbPb.BannerMapping {
	protoMsg := &sbPb.BannerMapping{
		Id:           b.Id,
		BannerId:     b.BannerId,
		MappingType:  b.MappingType,
		MappingValue: b.MappingValue,
		CreatedAt:    timestamppb.New(b.CreatedAt),
		UpdatedAt:    timestamppb.New(b.UpdatedAt),
	}
	return protoMsg
}

func BannerMappingListToProtos(mappingModels []*BannerMapping) []*sbPb.BannerMapping {
	var mappings []*sbPb.BannerMapping
	for _, mappingModel := range mappingModels {
		mappings = append(mappings, mappingModel.ToProtoMessage())
	}
	return mappings
}
