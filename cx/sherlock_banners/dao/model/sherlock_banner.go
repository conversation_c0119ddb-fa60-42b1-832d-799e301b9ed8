package model

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"strings"
	"time"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/pagination"

	"google.golang.org/protobuf/types/known/timestamppb"
)

const istSuffix = "+05:30"

type SherlockBanner struct {
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	// the content of the banner includes title, body etc.
	BannerContent *sbPb.SherlockBannerContent
	// start time of the duration in which the banner is to be shown
	StartTime time.Time
	// end time of the duration in which the banner is to be shown
	EndTime time.Time
	// timestamp at which this row was created in DB
	CreatedAt time.Time
	// timestamp at which this row was last updated
	UpdatedAt time.Time
	// to identify what is the structure of the banner eg: it can be title-body type or list of key values type
	StructureType sbPb.SherlockBannerStructureType
	// the content of the banner -- version-2 is oneof struct corresponding to structure_type
	BannerContentV2 *sbPb.SherlockBannerContentV2
}

func NewSherlockBannerFromProtoMsg(msg *sbPb.SherlockBanner) *SherlockBanner {
	modelMsg := &SherlockBanner{
		Id:              msg.GetId(),
		BannerContent:   msg.GetBannerContent(),
		StructureType:   msg.GetStructureType(),
		BannerContentV2: msg.GetBannerContentV2(),
	}
	// We are reading time as string interpreted as IST. So add IST suffix and parse as time.Time
	startTime, err := time.Parse(time.RFC3339, strings.TrimSpace(msg.GetStartTime())+istSuffix)
	// not throwing error as StartTime is not mandatory to convert to Model. Eg: for Update banner start time need not be specified
	if err == nil {
		modelMsg.StartTime = startTime
	}
	endTime, err := time.Parse(time.RFC3339, strings.TrimSpace(msg.GetEndTime())+istSuffix)
	// not throwing error as EndTime is not mandatory to convert to Model. Eg: for Update banner end time need not be specified
	if err == nil {
		modelMsg.EndTime = endTime
	}
	return modelMsg
}

func (s *SherlockBanner) ToProtoMessage() *sbPb.SherlockBanner {
	protoMsg := &sbPb.SherlockBanner{
		Id:            s.Id,
		BannerContent: s.BannerContent,
		// Format time as IST string and remove the +05:30 suffix in the end
		// Use IST Location to convert the start_time/end_time stored in DB to IST
		StartTime:       strings.TrimSuffix(s.StartTime.In(datetime.IST).Format(time.RFC3339), istSuffix),
		EndTime:         strings.TrimSuffix(s.EndTime.In(datetime.IST).Format(time.RFC3339), istSuffix),
		CreatedAt:       timestamppb.New(s.CreatedAt),
		UpdatedAt:       timestamppb.New(s.UpdatedAt),
		StructureType:   s.StructureType,
		BannerContentV2: s.BannerContentV2,
	}
	// Set the IsActive flag if the current time is between the start_time and end_time
	// This is just for easy visibility to admins in the table view
	currTime := time.Now()
	if currTime.Before(s.EndTime) && currTime.After(s.StartTime) {
		protoMsg.IsActive = commontypes.BooleanEnum_TRUE
	} else {
		protoMsg.IsActive = commontypes.BooleanEnum_FALSE
	}
	return protoMsg
}

func SherlockBannerListToProtos(bannerModels []*SherlockBanner) []*sbPb.SherlockBanner {
	var banners []*sbPb.SherlockBanner
	for _, bannerModel := range bannerModels {
		banners = append(banners, bannerModel.ToProtoMessage())
	}
	return banners
}

// SherlockBanners implements pagination.Rows to use pagination on sherlock_banners table
type SherlockBanners []*SherlockBanner

func (s SherlockBanners) Slice(start, end int) pagination.Rows { return s[start:end] }
func (s SherlockBanners) GetTimestamp(index int) time.Time     { return s[index].UpdatedAt }
func (s SherlockBanners) Size() int                            { return len(s) }
