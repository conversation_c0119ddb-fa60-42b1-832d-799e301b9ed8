//go:generate dao_metrics_gen .
package dao

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	"github.com/epifi/be-common/pkg/pagination"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
)

// ISherlockBannerDao - Interface which exposes DAO methods on top of SherlockBanners table
type ISherlockBannerDao interface {
	// Create - method accepts SherlockBanner with required details
	// returns SherlockBanner with generated ID (AND) error for invalid input params or for db errors
	Create(ctx context.Context, banner *sbPb.SherlockBanner) (*sbPb.SherlockBanner, error)
	// CreateBatch - method accepts SherlockBanners with required details
	// (we should pass UUID for each banner_id from service layer to track the corresponding created banners from response)
	// returns SherlockBanners with generated ID (AND) error for invalid input params or for db errors
	CreateBatch(ctx context.Context, banner []*sbPb.SherlockBanner) ([]*sbPb.SherlockBanner, error)
	// Update - method accepts SherlockBanner and update mask
	// id and update mask are mandatory parameters
	// returns error for invalid input param or for db errors
	Update(ctx context.Context, banner *sbPb.SherlockBanner, updateMask []sbPb.BannerUpdateFieldMask) error
	// DeleteBatch method to delete SherlockBanners from db
	// ids is mandatory parameter
	// returns error for invalid input or for db errors
	DeleteBatch(ctx context.Context, ids []string) error
	// GetAllWithFilters - method to retrieve list of SherlockBanners from db
	// Valid filters are idList, ActiveAtTime
	// If no filters are applied all elements would be returned.
	// Returns Paginated list of SherlockBanners sorted on updated_at and an error
	GetAllWithFilters(ctx context.Context, pageToken *pagination.PageToken, pageLimit int, filters ...storageV2.FilterOption) ([]*sbPb.SherlockBanner, *rpc.PageContextResponse, error)
}

// IBannerMappingDao -  Interface which exposes DAO methods on top of BannerMappings table
type IBannerMappingDao interface {
	// CreateBatch method to bulk insert mapping records in db
	// method accepts list of mapping objects
	// returns list of inserted mappings and error for invalid mapping details or for db errors
	CreateBatch(ctx context.Context, mapping []*sbPb.BannerMapping) ([]*sbPb.BannerMapping, error)
	// DeleteBatch method to bulk delete mappings from db
	// Allowed filters are rowIdList, bannerIdList, sherlockUserRolesList
	// If no filters are provided nothing is deleted
	// returns number of rows deleted and error for db errors
	DeleteBatch(ctx context.Context, filters ...storageV2.FilterOption) (int64, error)
	// GetAllWithFilters - method to retrieve all mappings with optional filter params.
	// Valid filters are rowIdList, bannerIdList, sherlockUserRolesList
	// If no filters are provided all will be returned.
	// Returns list of mapping objects and error
	GetAllWithFilters(ctx context.Context, distinctColumnMask []sbPb.BannerMappingFieldMask,
		filters ...storageV2.FilterOption) ([]*sbPb.BannerMapping, error)
}
