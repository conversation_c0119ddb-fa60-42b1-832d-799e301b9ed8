package collector

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc"

	rpcPb "github.com/epifi/be-common/api/rpc"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/sherlock_banners/helper"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type CxSherlockBannersCollector struct {
	getBannersHelper helper.GetBannersHelper
}

func NewCxSherlockBannersCollector(getBannersHelper helper.GetBannersHelper) *CxSherlockBannersCollector {
	return &CxSherlockBannersCollector{
		getBannersHelper: getBannersHelper,
	}
}

const (
	// since this is for user specific active banners, it should not be more than 10
	maxNumOfBanners = 10
)

func (c *CxSherlockBannersCollector) FetchSherlockBanners(ctx context.Context, req *sbPb.FetchSherlockBannersRequest, opts ...grpc.CallOption) (*sbPb.FetchSherlockBannersResponse, error) {
	bannersResp, err := c.getBannersHelper.GetBanners(ctx, &sbPb.GetBannersRequest{
		ActiveNowFlag: true,
		Filters: &sbPb.BannerFilters{
			MappingItems: &sbPb.MappingItems{
				ActorIdList: []string{req.GetActorId()},
			},
			StructureTypeList: req.GetStructureTypeList(),
		},
		PageContextRequest: &rpcPb.PageContextRequest{
			PageSize: maxNumOfBanners,
		},
	})
	if te := epifigrpc.RPCError(bannersResp, err); te != nil {
		cxLogger.Error(ctx, "failed to get sherlock banners configured in CX service for user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.Error(te))
		return &sbPb.FetchSherlockBannersResponse{
			Status: bannersResp.GetStatus(),
		}, nil
	}
	return &sbPb.FetchSherlockBannersResponse{
		Status:  rpcPb.StatusOk(),
		Banners: bannersResp.GetBanners(),
	}, nil
}
