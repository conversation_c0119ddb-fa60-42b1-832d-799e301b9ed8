//go:generate mockgen -source=collector_factory.go -destination=../../test/mocks/sherlock_banners/collector/mock_collector_factory.go -package=mock_collector
package collector

import (
	"fmt"

	types "github.com/epifi/gamma/api/typesv2"
)

type ISherlockBannersCollectorFactory interface {
	GetSherlockBannersCollector(serviceName types.ServiceName) (SherlockBannersCollector, error)
	RegisterSherlockBannersCollector(serviceName types.ServiceName, collector SherlockBannersCollector)
}

type SherlockBannersCollectorFactory struct {
	SherlockBannersCollectorMap map[types.ServiceName]SherlockBannersCollector
}

func NewSherlockBannersCollectorFactory() *SherlockBannersCollectorFactory {
	collectorMap := make(map[types.ServiceName]SherlockBannersCollector)
	return &SherlockBannersCollectorFactory{
		SherlockBannersCollectorMap: collectorMap,
	}
}

// RegisterSherlockBannersCollector will register the BE service's collector implementation for Sherlock Banners
// stores the service-->collector mapping
func (d *SherlockBannersCollectorFactory) RegisterSherlockBannersCollector(serviceName types.ServiceName, collector SherlockBannersCollector) {
	d.SherlockBannersCollectorMap[serviceName] = collector
}

// GetSherlockBannersCollector gives the collector implementation for a given serviceName
func (d *SherlockBannersCollectorFactory) GetSherlockBannersCollector(serviceName types.ServiceName) (SherlockBannersCollector, error) {
	clc, ok := d.SherlockBannersCollectorMap[serviceName]
	if !ok {
		return nil, fmt.Errorf("no sherlock banners collector found for given service")
	}
	return clc, nil
}

var _ ISherlockBannersCollectorFactory = &SherlockBannersCollectorFactory{}
