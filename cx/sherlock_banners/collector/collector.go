//go:generate mockgen -source=collector.go -destination=../../test/mocks/sherlock_banners/collector/mock_collector.go -package=mock_collector
package collector

import (
	"context"

	"google.golang.org/grpc"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
)

// SherlockBannersCollector - The contract for the BE services willing to use Sherlock banners
// i.e. the BE service must implement the following RPCs to surface Banners on Sherlock
type SherlockBannersCollector interface {
	// FetchSherlockBanners is invoked by CX Sherlock Banner service to fetch banners from another BE service
	// The request contains ActorId of the user and other Context required like the banner structure type
	// The response must contain status code and a List of relevant Sherlock Banners
	FetchSherlockBanners(ctx context.Context, request *sbPb.FetchSherlockBannersRequest, opts ...grpc.CallOption) (*sbPb.FetchSherlockBannersResponse, error)
}
