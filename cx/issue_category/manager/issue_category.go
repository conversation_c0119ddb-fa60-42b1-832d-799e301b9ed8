package manager

import (
	"context"

	"github.com/epifi/gamma/cx/issue_category/common"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/pkg/errors"

	icPb "github.com/epifi/gamma/api/cx/issue_category"
	"github.com/epifi/gamma/cx/issue_category/dao"
)

type IssueCategoryManagerImpl struct {
	issueCategoryDao dao.IIssueCategoryDao
}

func NewIssueCategoryManagerImpl(issueCategoryDao dao.IIssueCategoryDao) *IssueCategoryManagerImpl {
	return &IssueCategoryManagerImpl{issueCategoryDao: issueCategoryDao}
}

var _ IssueCategoryManager = &IssueCategoryManagerImpl{}

func (i *IssueCategoryManagerImpl) GetId(ctx context.Context, productCategory, productCategoryDetails, subCategory string) (string, error) {
	issueCategory := &icPb.IssueCategory{
		ProductCategory:        productCategory,
		ProductCategoryDetails: productCategoryDetails,
		SubCategory:            subCategory,
	}
	if validateErr := common.ValidateIssueCategory(issueCategory); validateErr != nil {
		return "", errors.Wrap(epifierrors.ErrInvalidArgument, validateErr.Error())
	}
	issueCategoryIdList, getErr := i.issueCategoryDao.GetAllByFilter(ctx, &icPb.IssueCategoryFilter{
		IssueCategory: issueCategory,
		FilterDepth:   icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
	})
	if getErr != nil {
		return "", errors.Wrap(getErr, "error while fetching issue category id from db")
	}
	return issueCategoryIdList[0].GetId(), nil
}

func (i *IssueCategoryManagerImpl) GetIds(ctx context.Context, productCategory, productCategoryDetails, subCategory string) ([]string, error) {
	issueCategory := &icPb.IssueCategory{
		ProductCategory:        productCategory,
		ProductCategoryDetails: productCategoryDetails,
		SubCategory:            subCategory,
	}
	if validateErr := common.ValidateIssueCategory(issueCategory); validateErr != nil {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, validateErr.Error())
	}

	// determine filterDepth based on parameters provided, by default depth will be till L1
	filterDepth := icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY
	switch {
	case issueCategory.GetSubCategory() != "":
		filterDepth = icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY
	case issueCategory.GetProductCategoryDetails() != "":
		filterDepth = icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS
	}
	issueCategoryFilter := &icPb.IssueCategoryFilter{
		IssueCategory: &icPb.IssueCategory{
			ProductCategory:        productCategory,
			ProductCategoryDetails: productCategoryDetails,
			SubCategory:            subCategory,
		},
		FilterDepth: filterDepth,
	}

	issueCategoryList, getErr := i.issueCategoryDao.GetAllByFilter(ctx, issueCategoryFilter)
	if getErr != nil {
		return nil, errors.Wrap(getErr, "error while fetching issue category ids from db")
	}
	var issueCategoryIdList []string
	for _, issueCategory := range issueCategoryList {
		issueCategoryIdList = append(issueCategoryIdList, issueCategory.GetId())
	}
	return issueCategoryIdList, nil
}

func (i *IssueCategoryManagerImpl) GetValueById(ctx context.Context, issueCategoryId string) (*icPb.IssueCategory, error) {
	issueCategory, getErr := i.issueCategoryDao.GetById(ctx, issueCategoryId)
	if getErr != nil {
		return nil, errors.Wrap(getErr, "error while fetching issue category from db")
	}
	return issueCategory, nil
}
