package manager

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/protobuf/proto"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifierrors"

	mock_dao "github.com/epifi/gamma/cx/test/mocks/issue_category/dao"
	"github.com/golang/mock/gomock"

	icPb "github.com/epifi/gamma/api/cx/issue_category"
)

var (
	filter1	= &icPb.IssueCategoryFilter{
		IssueCategory: &icPb.IssueCategory{
			ProductCategory: "Accounts",
		},
		FilterDepth:	icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY,
	}
	issueCategory1	= &icPb.IssueCategory{
		Id:			"ABC",
		ProductCategory:	"Accounts",
		ProductCategoryDetails:	"Min KYC Expiry",
		SubCategory:		"Balance Refund",
	}
	mockErr	= errors.New("error")
)

func TestIssueCategoryManager_GetIds(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockIssueCategoryDao := mock_dao.NewMockIIssueCategoryDao(ctr)
	defer func() {
		ctr.Finish()
	}()
	type args struct {
		ctx							context.Context
		productCategory, productCategoryDetails, subCategory	string
		filterDepth						icPb.IssueCategoryMask
		mock							[]interface{}
	}
	tests := []struct {
		name	string
		args	args
		want	[]string
		wantErr	error
	}{
		{
			name:		"error: filter not passed",
			wantErr:	errors.Wrap(epifierrors.ErrInvalidArgument, "invalid issue category, product category is mandatory"),
			args: args{
				ctx: context.Background(),
			},
		},
		{
			name:	"error: while fetching issue category",
			args: args{
				ctx:	context.Background(),
				mock: []interface{}{
					mockIssueCategoryDao.EXPECT().GetAllByFilter(context.Background(), &icPb.IssueCategoryFilter{
						IssueCategory: &icPb.IssueCategory{
							ProductCategory: issueCategory1.GetProductCategory(),
						},
						FilterDepth:	icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY,
					}).
						Return(nil, mockErr),
				},
				productCategory:	issueCategory1.GetProductCategory(),
			},
			wantErr:	errors.Wrap(mockErr, "error while fetching issue category ids from db"),
		},
		{
			name:	"success: filter till L1",
			args: args{
				ctx:	context.Background(),
				mock: []interface{}{
					mockIssueCategoryDao.EXPECT().GetAllByFilter(context.Background(), &icPb.IssueCategoryFilter{
						IssueCategory: &icPb.IssueCategory{
							ProductCategory: issueCategory1.GetProductCategory(),
						},
						FilterDepth:	icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY,
					}).
						Return([]*icPb.IssueCategory{issueCategory1}, nil),
				},
				productCategory:	issueCategory1.GetProductCategory(),
			},
			want:	[]string{issueCategory1.GetId()},
		},
		{
			name:	"success: filter till L2",
			args: args{
				ctx:	context.Background(),
				mock: []interface{}{
					mockIssueCategoryDao.EXPECT().GetAllByFilter(context.Background(), &icPb.IssueCategoryFilter{
						IssueCategory: &icPb.IssueCategory{
							ProductCategory:	issueCategory1.GetProductCategory(),
							ProductCategoryDetails:	issueCategory1.GetProductCategoryDetails(),
						},
						FilterDepth:	icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS,
					}).
						Return([]*icPb.IssueCategory{issueCategory1}, nil),
				},
				productCategory:	issueCategory1.GetProductCategory(),
				productCategoryDetails:	issueCategory1.GetProductCategoryDetails(),
			},
			want:	[]string{issueCategory1.GetId()},
		},
		{
			name:	"success: filter till L3",
			args: args{
				ctx:	context.Background(),
				mock: []interface{}{
					mockIssueCategoryDao.EXPECT().GetAllByFilter(context.Background(), &icPb.IssueCategoryFilter{
						IssueCategory: &icPb.IssueCategory{
							ProductCategory:	issueCategory1.GetProductCategory(),
							ProductCategoryDetails:	issueCategory1.GetProductCategoryDetails(),
							SubCategory:		issueCategory1.GetSubCategory(),
						},
						FilterDepth:	icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
					}).
						Return([]*icPb.IssueCategory{issueCategory1}, nil),
				},
				productCategory:	issueCategory1.GetProductCategory(),
				productCategoryDetails:	issueCategory1.GetProductCategoryDetails(),
				subCategory:		issueCategory1.GetSubCategory(),
			},
			want:	[]string{issueCategory1.GetId()},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			categoryManager := NewIssueCategoryManagerImpl(mockIssueCategoryDao)
			got, err := categoryManager.GetIds(tt.args.ctx, tt.args.productCategory, tt.args.productCategoryDetails, tt.args.subCategory)
			if (err != nil || tt.wantErr != nil) && err.Error() != tt.wantErr.Error() {
				t.Errorf("GetIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetIds() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIssueCategoryManager_GetId(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockIssueCategoryDao := mock_dao.NewMockIIssueCategoryDao(ctr)
	defer func() {
		ctr.Finish()
	}()
	type args struct {
		ctx							context.Context
		productCategory, productCategoryDetails, subCategory	string
		mock							[]interface{}
	}
	tests := []struct {
		name	string
		args	args
		want	string
		wantErr	error
	}{
		{
			name:		"error: L1 not passed",
			wantErr:	errors.Wrap(epifierrors.ErrInvalidArgument, "invalid issue category, product category is mandatory"),
			args: args{
				ctx: context.Background(),
			},
		},
		{
			name:		"error: invalid combination of L1, L2, L3 passed",
			wantErr:	errors.Wrap(epifierrors.ErrInvalidArgument, "invalid issue category, cannot populate L3 without L2"),
			args: args{
				ctx:			context.Background(),
				productCategory:	"A",
				subCategory:		"C",
			},
		},
		{
			name:	"error: while fetching issue category",
			args: args{
				ctx:	context.Background(),
				mock: []interface{}{
					mockIssueCategoryDao.EXPECT().GetAllByFilter(gomock.Any(), &icPb.IssueCategoryFilter{
						IssueCategory: &icPb.IssueCategory{
							ProductCategory: issueCategory1.GetProductCategory(),
						},
						FilterDepth:	icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
					}).Return(nil, mockErr),
				},
				productCategory:	issueCategory1.GetProductCategory(),
			},
			wantErr:	errors.Wrap(mockErr, "error while fetching issue category id from db"),
		},
		{
			name:	"success",
			args: args{
				ctx:	context.Background(),
				mock: []interface{}{
					mockIssueCategoryDao.EXPECT().GetAllByFilter(gomock.Any(), &icPb.IssueCategoryFilter{
						IssueCategory: &icPb.IssueCategory{
							ProductCategory: issueCategory1.GetProductCategory(),
						},
						FilterDepth:	icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
					}).Return([]*icPb.IssueCategory{issueCategory1}, nil),
				},
				productCategory:	issueCategory1.GetProductCategory(),
			},
			want:	issueCategory1.GetId(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			categoryManager := NewIssueCategoryManagerImpl(mockIssueCategoryDao)
			got, err := categoryManager.GetId(tt.args.ctx, tt.args.productCategory, tt.args.productCategoryDetails, tt.args.subCategory)
			if (err != nil || tt.wantErr != nil) && err.Error() != tt.wantErr.Error() {
				t.Errorf("GetIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIssueCategoryManager_GetValueById(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockIssueCategoryDao := mock_dao.NewMockIIssueCategoryDao(ctr)
	defer func() {
		ctr.Finish()
	}()
	type args struct {
		ctx		context.Context
		issueCategoryId	string
		mocks		[]interface{}
	}
	tests := []struct {
		name	string
		args	args
		want	*icPb.IssueCategory
		wantErr	error
	}{
		{
			name:		"invalid args",
			wantErr:	errors.Wrap(mockErr, "error while fetching issue category from db"),
			args: args{
				ctx:			context.Background(),
				issueCategoryId:	"",
				mocks: []interface{}{
					mockIssueCategoryDao.EXPECT().GetById(context.Background(), "").
						Return(nil, mockErr),
				},
			},
		},
		{
			name:	"error: while fetching issue category",
			args: args{
				ctx:			context.Background(),
				issueCategoryId:	issueCategory1.GetId(),
				mocks: []interface{}{
					mockIssueCategoryDao.EXPECT().GetById(context.Background(), issueCategory1.GetId()).
						Return(nil, mockErr),
				},
			},
			wantErr:	errors.Wrap(mockErr, "error while fetching issue category from db"),
		},
		{
			name:	"success",
			args: args{
				ctx:			context.Background(),
				issueCategoryId:	issueCategory1.GetId(),
				mocks: []interface{}{
					mockIssueCategoryDao.EXPECT().GetById(context.Background(), issueCategory1.GetId()).
						Return(issueCategory1, nil),
				},
			},
			want:	issueCategory1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			categoryManager := NewIssueCategoryManagerImpl(mockIssueCategoryDao)
			got, err := categoryManager.GetValueById(tt.args.ctx, tt.args.issueCategoryId)
			if (err != nil || tt.wantErr != nil) && err.Error() != tt.wantErr.Error() {
				t.Errorf("GetValueById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetValueById() got = %v, want %v", got, tt.want)
			}
		})
	}
}
