//go:generate mockgen -source=manager.go -destination=../../test/mocks/issue_category/manager/mock_manager.go

package manager

import (
	"context"

	icPb "github.com/epifi/gamma/api/cx/issue_category"
)

// IssueCategoryManager exposes method that are used to abstract IssueCategoryDao
type IssueCategoryManager interface {
	// GetId method returns issue category id which is unique identifier for given (L1, L2, L3) combination in db
	// ProductCategory (L1) is mandatory parameter, if any parameter is not passed we will still use it to search
	// Ex. Parameters are L1: Accounts, L2 and L3 are empty. It will search for record with L1: Accounts and L2, L3 are empty
	// this method can be used when we require issue category id for a specific combination, and not for any child combinations
	// NotFound will be thrown when record is not found
	// for any other errors reason will be provided with error
	GetId(ctx context.Context, productCategory, productCategoryDetails, subCategory string) (string, error)

	// GetIds method returns a list of all issue category ids whose value matches the given values of (L1, L2, L3)
	// ProductCategory (L1) is mandatory parameter, filtering will only happen on parameters populated
	// Ex. Parameters are L1: Accounts, L2 and L3 are empty. It will search for all records where L1: Accounts and L2, L3 can be anything
	// this method can be used when we need all the child combinations of given issue category
	// NotFound will be thrown when record is not found
	// for any other errors reason will be provided with error
	GetIds(ctx context.Context, productCategory, productCategoryDetails, subCategory string) ([]string, error)

	// GetValueById method returns the IssueCategory for the provided issueCategoryId, which is mandatory
	// NotFound will be thrown when record is not found
	// for any other errors reason will be provided with error
	GetValueById(ctx context.Context, issueCategoryId string) (*icPb.IssueCategory, error)
}
