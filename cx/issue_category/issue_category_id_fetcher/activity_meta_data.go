package issue_category_id_fetcher

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	aaPb "github.com/epifi/gamma/api/actor_activity"
	issuePb "github.com/epifi/gamma/api/cx/issue_category"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type ActivityMetaData struct {
	actorActivityClient aaPb.ActorActivityClient
}

var _ IssueCategoryIdFetcher = &ActivityMetaData{}

func NewActivityMetaData(actorActivityClient aaPb.ActorActivityClient) *ActivityMetaData {
	return &ActivityMetaData{
		actorActivityClient: actorActivityClient,
	}
}

func (a *ActivityMetaData) GetIssueCategoryId(ctx context.Context, request *issuePb.GetIssueCategoryDetailsRequest) (string, error) {
	activityMetaData, err := a.actorActivityClient.GetActivityMetadata(ctx, &aaPb.GetActivityMetadataRequest{
		ActivityType: request.GetActivityMetaData().GetActivityType(),
		Area:         request.GetActivityMetaData().GetArea(),
	})
	if activityErr := epifigrpc.RPCError(activityMetaData, err); activityErr != nil {
		if errors.Is(activityErr, rpc.StatusAsError(rpc.StatusRecordNotFound())) {
			return "", epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "failed to get activity metadata", zap.Error(activityErr))
		return "", activityErr
	}
	return activityMetaData.GetActivityMetadata().GetIssueCategoryId(), nil
}
