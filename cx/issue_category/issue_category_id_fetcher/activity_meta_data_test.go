package issue_category_id_fetcher

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor_activity"
	"github.com/epifi/gamma/api/actor_activity/mocks"
	issuePb "github.com/epifi/gamma/api/cx/issue_category"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var issueCategoryId = "de34d771-2c76-44e3-81f9-48c730feef1d"

func TestActivityMetaData_GetIssueCategoryId(t *testing.T) {
	t.<PERSON>l()
	type args struct {
		request *issuePb.GetIssueCategoryDetailsRequest
	}

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockActorActivityClient := mocks.NewMockActorActivityClient(ctr)
	tests := []struct {
		name	string
		args	args
		mocks	[]interface{}
		want	string
		wantErr	error
	}{
		{
			name:	"issue category id not found",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockActorActivityClient.EXPECT().GetActivityMetadata(gomock.Any(), gomock.Any()).Return(&actor_activity.GetActivityMetadataResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil),
			},
			wantErr:	epifierrors.ErrRecordNotFound,
		},
		{
			name:	"failed to fetch issue category id",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockActorActivityClient.EXPECT().GetActivityMetadata(gomock.Any(), gomock.Any()).Return(&actor_activity.GetActivityMetadataResponse{
					Status: rpcPb.StatusInternal(),
				}, nil),
			},
			wantErr:	rpcPb.StatusAsError(rpcPb.StatusInternal()),
		},
		{
			name:	"success",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockActorActivityClient.EXPECT().GetActivityMetadata(gomock.Any(), gomock.Any()).Return(&actor_activity.GetActivityMetadataResponse{
					Status:	rpcPb.StatusOk(),
					ActivityMetadata: &actor_activity.ActivityMetadata{
						IssueCategoryId: issueCategoryId,
					},
				}, nil),
			},
			want:	issueCategoryId,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := NewActivityMetaData(mockActorActivityClient)
			got, err := a.GetIssueCategoryId(context.Background(), tt.args.request)
			if (err != nil) != (tt.wantErr != nil) || !errors.Is(err, tt.wantErr) {
				t.Errorf("GetIssueCategoryId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && got != tt.want {
				t.Errorf("GetIssueCategoryId() got = %v, want %v", got, tt.want)
			}
		})
	}
}
