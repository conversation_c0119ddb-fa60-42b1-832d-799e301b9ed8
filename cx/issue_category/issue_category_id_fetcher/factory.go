package issue_category_id_fetcher

import (
	"github.com/pkg/errors"

	issuePb "github.com/epifi/gamma/api/cx/issue_category"
)

type IssueCategoryIdFactory interface {
	GetIssueCategoryIdFactory(request *issuePb.GetIssueCategoryDetailsRequest) (IssueCategoryIdFetcher, error)
}

type IssueCategoryIdFactoryImpl struct {
	issueCategoryId  *IssueCategoryId
	activityMetaData *ActivityMetaData
}

func NewIssueCategoryIdFactoryImpl(issueCategoryId *IssueCategoryId, activityMetaData *ActivityMetaData) *IssueCategoryIdFactoryImpl {
	return &IssueCategoryIdFactoryImpl{
		issueCategoryId:  issueCategoryId,
		activityMetaData: activityMetaData,
	}
}

func (i *IssueCategoryIdFactoryImpl) GetIssueCategoryIdFactory(request *issuePb.GetIssueCategoryDetailsRequest) (IssueCategoryIdFetcher, error) {
	switch request.GetIdentifierType() {
	case issuePb.IssueCategoryIdentifierType_ISSUE_CATEGORY_IDENTIFIER_TYPE_ISSUE_CATEGORY_ID:
		return i.issueCategoryId, nil
	case issuePb.IssueCategoryIdentifierType_ISSUE_CATEGORY_IDENTIFIER_TYPE_ACTIVITY_META_DATA:
		return i.activityMetaData, nil
	default:
		return nil, errors.New("invalid identifier type")
	}
}
