package issue_category_id_fetcher

import (
	"context"

	issuePb "github.com/epifi/gamma/api/cx/issue_category"
)

type IssueCategoryId struct{}

func NewIssueCategoryId() *IssueCategoryId {
	return &IssueCategoryId{}
}

var _ IssueCategoryIdFetcher = &IssueCategoryId{}

func (i *IssueCategoryId) GetIssueCategoryId(_ context.Context, request *issuePb.GetIssueCategoryDetailsRequest) (string, error) {
	return request.GetIssueCategoryId(), nil
}
