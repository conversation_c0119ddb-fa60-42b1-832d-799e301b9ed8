package issue_category

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	issuePb "github.com/epifi/gamma/api/cx/issue_category"
	"github.com/epifi/gamma/cx/issue_category/dao"
	issueCtgIdFetcher "github.com/epifi/gamma/cx/issue_category/issue_category_id_fetcher"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

type IssueCategory struct {
	issueCategoryIdFactory issueCtgIdFetcher.IssueCategoryIdFactory
	issueCategoryDao       dao.IIssueCategoryDao
}

func NewIssueCategory(issueCategoryIdFactory issueCtgIdFetcher.IssueCategoryIdFactory, issueCategoryDao dao.IIssueCategoryDao) *IssueCategory {
	return &IssueCategory{
		issueCategoryIdFactory: issueCategoryIdFactory,
		issueCategoryDao:       issueCategoryDao,
	}
}

var _ issuePb.IssueCategoryServiceServer = &IssueCategory{}

func (i *IssueCategory) GetIssueCategoryDetails(ctx context.Context, request *issuePb.GetIssueCategoryDetailsRequest) (*issuePb.GetIssueCategoryDetailsResponse, error) {
	issueCategoryIdFetcher, err := i.issueCategoryIdFactory.GetIssueCategoryIdFactory(request)
	if err != nil {
		logger.Error(ctx, "invalid identifier type", zap.Error(err))
		return &issuePb.GetIssueCategoryDetailsResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	issueCategoryId, err := issueCategoryIdFetcher.GetIssueCategoryId(ctx, request)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &issuePb.GetIssueCategoryDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "failed to fetch issue category id", zap.Error(err))
		return &issuePb.GetIssueCategoryDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	issueCategoryDetails, err := i.issueCategoryDao.GetById(ctx, issueCategoryId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &issuePb.GetIssueCategoryDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "failed to get issue category details", zap.Error(err))
		return &issuePb.GetIssueCategoryDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &issuePb.GetIssueCategoryDetailsResponse{
		Status:               rpc.StatusOk(),
		IssueCategoryDetails: issueCategoryDetails,
	}, nil
}
