package issue_category

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer

//nolint:all
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, teardown = test.InitTestServer(false)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
