package issue_category

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	issuePb "github.com/epifi/gamma/api/cx/issue_category"
	mockdao "github.com/epifi/gamma/cx/test/mocks/issue_category/dao"
	mockfac "github.com/epifi/gamma/cx/test/mocks/issue_category/issue_category_id_fetcher"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var (
	mockErr			= errors.New("mock err")
	issueCategoryId		= "de34d771-2c76-44e3-81f9-48c730feef1d"
	issueCategoryDetails	= &issuePb.IssueCategory{
		Id:			issueCategoryId,
		ProductCategory:	"product_category",
		SubCategory:		"sub_category",
		ProductCategoryDetails:	"product_category_details",
	}
)

func TestIssueCategory_GetIssueCategoryDetails(t *testing.T) {
	t.<PERSON>l()
	type args struct {
		request *issuePb.GetIssueCategoryDetailsRequest
	}

	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockIssueCategoryDao := mockdao.NewMockIIssueCategoryDao(ctr)
	mockIssueCategoryIdFactory := mockfac.NewMockIssueCategoryIdFactory(ctr)
	mockIssueCategoryIdFetcher := mockfac.NewMockIssueCategoryIdFetcher(ctr)

	tests := []struct {
		name	string
		args	args
		mocks	[]interface{}
		want	*issuePb.GetIssueCategoryDetailsResponse
		wantErr	bool
	}{
		{
			name:	"invalid identifier type",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockIssueCategoryIdFactory.EXPECT().GetIssueCategoryIdFactory(gomock.Any()).Return(nil, mockErr),
			},
			want:	&issuePb.GetIssueCategoryDetailsResponse{Status: rpcPb.StatusInvalidArgument()},
		},
		{
			name:	"issue category id not found",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockIssueCategoryIdFactory.EXPECT().GetIssueCategoryIdFactory(gomock.Any()).Return(mockIssueCategoryIdFetcher, nil),
				mockIssueCategoryIdFetcher.EXPECT().GetIssueCategoryId(gomock.Any(), gomock.Any()).Return("", epifierrors.ErrRecordNotFound),
			},
			want:	&issuePb.GetIssueCategoryDetailsResponse{Status: rpcPb.StatusRecordNotFound()},
		},
		{
			name:	"failed to fetch issue category id",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockIssueCategoryIdFactory.EXPECT().GetIssueCategoryIdFactory(gomock.Any()).Return(mockIssueCategoryIdFetcher, nil),
				mockIssueCategoryIdFetcher.EXPECT().GetIssueCategoryId(gomock.Any(), gomock.Any()).Return("", mockErr),
			},
			want:	&issuePb.GetIssueCategoryDetailsResponse{Status: rpcPb.StatusInternal()},
		},
		{
			name:	"issue category details not found",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockIssueCategoryIdFactory.EXPECT().GetIssueCategoryIdFactory(gomock.Any()).Return(mockIssueCategoryIdFetcher, nil),
				mockIssueCategoryIdFetcher.EXPECT().GetIssueCategoryId(gomock.Any(), gomock.Any()).Return(issueCategoryId, nil),
				mockIssueCategoryDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
			},
			want:	&issuePb.GetIssueCategoryDetailsResponse{Status: rpcPb.StatusRecordNotFound()},
		},
		{
			name:	"failed to fetch issue category details",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockIssueCategoryIdFactory.EXPECT().GetIssueCategoryIdFactory(gomock.Any()).Return(mockIssueCategoryIdFetcher, nil),
				mockIssueCategoryIdFetcher.EXPECT().GetIssueCategoryId(gomock.Any(), gomock.Any()).Return(issueCategoryId, nil),
				mockIssueCategoryDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, mockErr),
			},
			want:	&issuePb.GetIssueCategoryDetailsResponse{Status: rpcPb.StatusInternal()},
		},
		{
			name:	"success",
			args:	args{request: &issuePb.GetIssueCategoryDetailsRequest{}},
			mocks: []interface{}{
				mockIssueCategoryIdFactory.EXPECT().GetIssueCategoryIdFactory(gomock.Any()).Return(mockIssueCategoryIdFetcher, nil),
				mockIssueCategoryIdFetcher.EXPECT().GetIssueCategoryId(gomock.Any(), gomock.Any()).Return(issueCategoryId, nil),
				mockIssueCategoryDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(issueCategoryDetails, nil),
			},
			want:	&issuePb.GetIssueCategoryDetailsResponse{Status: rpcPb.StatusOk(), IssueCategoryDetails: issueCategoryDetails},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := NewIssueCategory(mockIssueCategoryIdFactory, mockIssueCategoryDao)
			got, err := i.GetIssueCategoryDetails(context.Background(), tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetIssueCategoryDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetIssueCategoryDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}
