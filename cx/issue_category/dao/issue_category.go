package dao

import (
	"context"
	"time"

	"github.com/epifi/gamma/cx/issue_category/common"

	"github.com/epifi/gamma/cx/issue_category/dao/model"

	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"gorm.io/gorm/clause"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	icPb "github.com/epifi/gamma/api/cx/issue_category"

	"github.com/pkg/errors"
	gormV2 "gorm.io/gorm"
)

const maxNumberOfBulkFetchAllowed = 50

type IssueCategoryDao struct {
	db *gormV2.DB
}

var _ IIssueCategoryDao = &IssueCategoryDao{}

func NewIssueCategoryDao(db pkgTypes.SherlockPGDB) *IssueCategoryDao {
	return &IssueCategoryDao{
		db: db,
	}
}

func (c *IssueCategoryDao) CreateBatch(ctx context.Context, req []*icPb.IssueCategory) ([]*icPb.IssueCategory, error) {
	defer metric_util.TrackDuration("cx/issue_category/dao", "IssueCategoryDao", "CreateBatch", time.Now())
	if err := validateCategoryListForCreation(req); err != nil {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, err.Error())
	}
	modelList := model.NewCategoryListFromProtoList(req)
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	// ignore duplicate record error while insertion
	opt := clauseOnConflictDoNothing()
	db = opt.ApplyInGorm(db)
	if err := db.Create(modelList).Error; err != nil {
		return nil, errors.Wrap(err, "failed to create categories into db")
	}
	return model.NewCategoryProtoListFromModelList(modelList), nil
}

// clauseOnConflictDoNothing returns a filter option which ignores record already exists error while inserting records
func clauseOnConflictDoNothing() storageV2.FilterOption {
	return storageV2.NewFuncFilterOption(func(db *gormV2.DB) *gormV2.DB {
		return db.Clauses(clause.OnConflict{DoNothing: true})
	})
}

func validateCategoryListForCreation(categories []*icPb.IssueCategory) error {
	if len(categories) == 0 {
		return errors.New("category list cannot be empty while creation")
	}
	for _, category := range categories {
		err := common.ValidateIssueCategory(category)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *IssueCategoryDao) GetById(ctx context.Context, categoryId string) (*icPb.IssueCategory, error) {
	defer metric_util.TrackDuration("cx/issue_category/dao", "IssueCategoryDao", "GetById", time.Now())
	if categoryId == "" {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "category id is mandatory parameter")
	}
	categoryModel := &model.IssueCategory{}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	query := db.Where("id = ?", categoryId).First(categoryModel)
	if err := query.Error; err != nil {
		if errors.Is(err, gormV2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "failed to fetch category by id from db")
	}
	return categoryModel.ToProtoMessage(), nil
}

func (c *IssueCategoryDao) GetAllByFilter(ctx context.Context, filters *icPb.IssueCategoryFilter) ([]*icPb.IssueCategory, error) {
	defer metric_util.TrackDuration("cx/issue_category/dao", "IssueCategoryDao", "GetAllByFilter", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	validateErr := common.ValidateIssueCategory(filters.GetIssueCategory())
	if validateErr != nil {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, validateErr.Error())
	}
	query := c.getQueryForFilters(db, filters)
	var categoryModeList []*model.IssueCategory
	if err := query.Find(&categoryModeList).Error; err != nil {
		return nil, errors.Wrap(err, "failed to fetch category by filters from db")
	}
	if len(categoryModeList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return model.NewCategoryProtoListFromModelList(categoryModeList), nil
}

func (c *IssueCategoryDao) getQueryForFilters(db *gormV2.DB, filter *icPb.IssueCategoryFilter) *gormV2.DB {
	query := db
	filterCategory := model.NewCategoryFromProtoMsg(filter.GetIssueCategory())
	query = query.Where("product_category = ?", filterCategory.ProductCategory)
	if filter.GetFilterDepth() >= icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS {
		query = query.Where("product_category_details = ?", filterCategory.ProductCategoryDetails)
	}
	if filter.GetFilterDepth() >= icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY {
		query = query.Where("sub_category = ?", filterCategory.SubCategory)
	}
	return query
}

func (c *IssueCategoryDao) GetIssueCategoryTree(ctx context.Context) (*icPb.IssueCategoryTree, error) {
	defer metric_util.TrackDuration("cx/issue_category/dao", "IssueCategoryDao", "GetIssueCategoryTree", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	var categoryModelList []*model.IssueCategory
	if err := db.Find(&categoryModelList).Error; err != nil {
		return nil, errors.Wrap(err, "failed to fetch category by filters from db")
	}
	if len(categoryModelList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	protoList := model.NewCategoryProtoListFromModelList(categoryModelList)
	return buildIssueCategoryTree(protoList), nil
}

func buildIssueCategoryTree(list []*icPb.IssueCategory) *icPb.IssueCategoryTree {
	// creating a root node which will contain all L1s as children
	root := &icPb.IssueCategoryNode{}
	for _, combination := range list {
		root = insertIssueCategoryCombinationToTree(root, combination, icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY)
	}
	return &icPb.IssueCategoryTree{ProductCategoryNodes: root.GetChildren()}
}

// insertIssueCategoryCombinationToTree method takes current node, category and level
// it inserts the category combination into issue category tree
func insertIssueCategoryCombinationToTree(currNode *icPb.IssueCategoryNode, category *icPb.IssueCategory, level icPb.IssueCategoryMask) *icPb.IssueCategoryNode {
	// if we have inserted all L1, L2, and L3 no further insertion required so return
	if level == 4 {
		return currNode
	}

	nextValue := getValueForLevel(category, level)
	// if there is no value present at current level which means we have reached the end so no insertion required
	if nextValue == "" {
		return currNode
	}
	// check if a child already exists with given value, if so update that child with current combination
	for idx, child := range currNode.GetChildren() {
		if child.GetValue() == nextValue {
			currNode.Children[idx] = insertIssueCategoryCombinationToTree(child, category, level+1)
			// node found and update hence returning
			return currNode
		}
	}

	// if node not present with given value, create new node insert its child and finally update the currNode children with new node
	newNode := &icPb.IssueCategoryNode{Value: nextValue, Level: level}
	newNode = insertIssueCategoryCombinationToTree(newNode, category, level+1)
	currNode.Children = append(currNode.GetChildren(), newNode)

	return currNode
}

func getValueForLevel(category *icPb.IssueCategory, level icPb.IssueCategoryMask) string {
	switch level {
	case icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY:
		return category.GetSubCategory()
	case icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS:
		return category.GetProductCategoryDetails()
	case icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY:
		return category.GetProductCategory()
	default:
		return ""
	}
}

func getQueryForLevel(db *gormV2.DB, filter *icPb.IssueCategoryFilter) *gormV2.DB {
	query := db
	// neither of L1, L2, L3 are populated
	if filter.GetIssueCategory().GetProductCategory() == "" {
		// we will fetch issue category ids where L2 and L3 are empty, i.e all L1s
		query = query.Where("product_category != ? AND product_category_details = ? AND sub_category = ?", model.PlaceHolder, model.PlaceHolder, model.PlaceHolder)
		return query
	}
	// L1 is populated : fetch L1 as per filter, L2 is not empty and L3 is empty
	if filter.GetIssueCategory().GetProductCategoryDetails() == "" {
		// we will fetch issue category ids where L1 is as per filter, L2 is not empty and L3 is empty
		query = query.Where("product_category = ? AND product_category_details != ? AND sub_category = ?", filter.GetIssueCategory().GetProductCategory(), model.PlaceHolder, model.PlaceHolder)
		return query
	}
	// L1, L2 are populated : L1, L2 as per filters and L3 is not empty
	query = query.Where("product_category = ? AND product_category_details = ? AND sub_category != ?", filter.GetIssueCategory().GetProductCategory(), filter.GetIssueCategory().GetProductCategoryDetails(), model.PlaceHolder)
	return query
}

func validateIssueCategoryLevelFilter(filter *icPb.IssueCategoryFilter) error {
	if filter.GetIssueCategory().GetProductCategory() == "" && filter.GetIssueCategory().GetSubCategory() != "" {
		return errors.New("product category is mandatory for populating subcategory")
	}
	if filter.GetIssueCategory().GetProductCategory() == "" && filter.GetIssueCategory().GetProductCategoryDetails() != "" {
		return errors.New("product category is mandatory for populating product category details")
	}
	if filter.GetIssueCategory().GetProductCategoryDetails() == "" && filter.GetIssueCategory().GetSubCategory() != "" {
		return errors.New("product category details is mandatory for populating subcategory")
	}
	return nil
}

func (c *IssueCategoryDao) GetIssueCategoryLevel(ctx context.Context, filter *icPb.IssueCategoryFilter) ([]*icPb.IssueCategory, error) {
	defer metric_util.TrackDuration("cx/issue_category/dao", "IssueCategoryDao", "GetIssueCategoryLevel", time.Now())
	validationErr := validateIssueCategoryLevelFilter(filter)
	if validationErr != nil {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "invalid filter")
	}
	var categoryModelList []*model.IssueCategory
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	query := getQueryForLevel(db, filter)
	if err := query.Find(&categoryModelList).Error; err != nil {
		return nil, errors.Wrap(err, "fetch category level")
	}
	if len(categoryModelList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return model.NewCategoryProtoListFromModelList(categoryModelList), nil
}

func (c *IssueCategoryDao) GetByIds(ctx context.Context, categoryIds []string) (map[string]*icPb.IssueCategory, error) {
	defer metric_util.TrackDuration("cx/issue_category/dao", "IssueCategoryDao", "GetByIds", time.Now())
	if len(categoryIds) == 0 || len(categoryIds) > maxNumberOfBulkFetchAllowed {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "category id list cannot be empty or exceed max length")
	}

	var categoryModelList []*model.IssueCategory
	db := gormctxv2.FromContextOrDefault(ctx, c.db)

	query := db.Where("id IN (?)", categoryIds).Find(&categoryModelList)
	if err := query.Error; err != nil {
		return nil, errors.Wrap(err, "failed to fetch category by id from db")
	}

	if len(categoryModelList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	categoryMap := make(map[string]*icPb.IssueCategory)
	for _, categoryModel := range categoryModelList {
		categoryMap[categoryModel.Id] = categoryModel.ToProtoMessage()
	}

	return categoryMap, nil
}
