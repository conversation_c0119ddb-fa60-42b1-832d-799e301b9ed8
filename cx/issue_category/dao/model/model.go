package model

import (
	"time"

	"github.com/google/uuid"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	icPb "github.com/epifi/gamma/api/cx/issue_category"

	"github.com/epifi/be-common/pkg/nulltypes"
)

const PlaceHolder = "-"

type IssueCategory struct {
	// Will be used by the client services as a unique identifier of the Issue category .
	Id string
	// product to which the given issue belongs, also known as L1
	ProductCategory nulltypes.NullString
	// additional details related to issue based on ProductCategory, also known as L2
	ProductCategoryDetails nulltypes.NullString
	// additional details related to issue based on ProductCategoryDetails, also known as L3
	SubCategory nulltypes.NullString
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

func NewCategoryFromProtoMsg(category *icPb.IssueCategory) *IssueCategory {
	if category == nil {
		return nil
	}
	categoryModel := &IssueCategory{
		// generating this to have a uniform ID across all the environments
		Id:              generateIssueCategoryId(category),
		ProductCategory: nulltypes.NewNullString(category.GetProductCategory()),
		// Populating placeholder value for L2 and L3 to avoid them being NULL
		// Refer: https://docs.google.com/document/d/1DDxJAI1gsOGmknor0AG5mLRfVZJLiQkaSbzifjo7xTI/edit#heading=h.sa49kf974mtj
		ProductCategoryDetails: handleEmptyStringWithPlaceholder(category.GetProductCategoryDetails()),
		SubCategory:            handleEmptyStringWithPlaceholder(category.GetSubCategory()),
	}
	return categoryModel
}

func handleEmptyStringWithPlaceholder(s string) nulltypes.NullString {
	if s != "" {
		return nulltypes.NewNullString(s)
	}
	return nulltypes.NewNullString(PlaceHolder)
}

func generateIssueCategoryId(category *icPb.IssueCategory) string {
	hashKey := category.GetProductCategory() + category.GetProductCategoryDetails() + category.GetSubCategory()
	// id will be generated by combining uuid.Nil (all zeros) which acts as namespace and hashKey which acts as key
	// ref: https://stackoverflow.com/questions/10867405/generating-v5-uuid-what-is-name-and-namespace
	// this ensures that for given hashKey on any machine, environment same id will be obtained
	issueCategoryId := uuid.NewSHA1(uuid.Nil, []byte(hashKey))

	return issueCategoryId.String()
}

func (category *IssueCategory) ToProtoMessage() *icPb.IssueCategory {
	categoryProto := &icPb.IssueCategory{
		Id:                     category.Id,
		ProductCategory:        category.ProductCategory.GetValue(),
		ProductCategoryDetails: category.ProductCategoryDetails.GetValue(),
		SubCategory:            category.SubCategory.GetValue(),
		CreatedAt:              timestamp.New(category.CreatedAt),
		UpdatedAt:              timestamp.New(category.UpdatedAt),
	}
	// Convert placeholder value to empty string
	if categoryProto.GetProductCategoryDetails() == PlaceHolder {
		categoryProto.ProductCategoryDetails = ""
	}
	if categoryProto.GetSubCategory() == PlaceHolder {
		categoryProto.SubCategory = ""
	}
	return categoryProto
}

func NewCategoryListFromProtoList(categories []*icPb.IssueCategory) []*IssueCategory {
	var categoryModelList []*IssueCategory
	for _, category := range categories {
		categoryModelList = append(categoryModelList, NewCategoryFromProtoMsg(category))
	}
	return categoryModelList
}

func NewCategoryProtoListFromModelList(categories []*IssueCategory) []*icPb.IssueCategory {
	var categoryProtoList []*icPb.IssueCategory
	for _, category := range categories {
		categoryProtoList = append(categoryProtoList, category.ToProtoMessage())
	}
	return categoryProtoList
}
