package dao

import (
	"context"
	"reflect"
	"testing"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/epifierrors"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
	icPb "github.com/epifi/gamma/api/cx/issue_category"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/test"

	"github.com/go-test/deep"
	"github.com/pkg/errors"

	gormV2 "gorm.io/gorm"
)

type IssueCategoryDaoTestSuite struct {
	db               *gormV2.DB
	conf             *config.Config
	issueCategoryDao *IssueCategoryDao
}

var (
	issueCategoryDTS IssueCategoryDaoTestSuite

	issueCategory1 = &icPb.IssueCategory{
		ProductCategory:        "L1-ABC",
		ProductCategoryDetails: "L2-ABC",
		SubCategory:            "L3-ABC",
	}
	issueCategory2 = &icPb.IssueCategory{
		ProductCategory:        "L1-ABC",
		ProductCategoryDetails: "L2-ABC",
	}
	issueCategory3 = &icPb.IssueCategory{
		ProductCategory: "L1-ABC",
	}
	issueCategory4 = &icPb.IssueCategory{
		ProductCategory:        "Account",
		ProductCategoryDetails: "Blocked",
		SubCategory:            "Issue-1",
	}
	issueCategory5 = &icPb.IssueCategory{
		ProductCategory:        "Onboarding",
		ProductCategoryDetails: "KYC",
		SubCategory:            "Issue-4",
	}
	id1 = "de34d771-2c76-44e3-81f9-48c730feef1e"
	id2 = "de34d771-2c76-44e3-81f9-48c730feef1a"
)

func TestIssueCategoryDao_CreateBatch(t *testing.T) {
	type args struct {
		ctx          context.Context
		categoryList []*icPb.IssueCategory
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    []*icPb.IssueCategory
	}{
		{
			name: "invalid request, product category not populated",
			args: args{
				ctx: context.Background(),
				categoryList: []*icPb.IssueCategory{
					{
						ProductCategoryDetails: "abc",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "invalid request, empty list provided",
			args: args{
				ctx:          context.Background(),
				categoryList: []*icPb.IssueCategory{},
			},
			wantErr: true,
		},
		{
			name: "invalid request, L3 provided without L2",
			args: args{
				ctx: context.Background(),
				categoryList: []*icPb.IssueCategory{
					{
						SubCategory:     "abc",
						ProductCategory: "abc",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "duplicate entry: ignore error",
			args: args{
				ctx: context.Background(),
				categoryList: []*icPb.IssueCategory{
					issueCategory4,
				},
			},
			wantErr: false,
			want:    []*icPb.IssueCategory{issueCategory4},
		},
		{
			name: "success: single record",
			args: args{
				ctx: context.Background(),
				categoryList: []*icPb.IssueCategory{
					issueCategory1,
				},
			},
			wantErr: false,
			want:    []*icPb.IssueCategory{issueCategory1},
		},
		{
			name: "success: multiple record",
			args: args{
				ctx: context.Background(),
				categoryList: []*icPb.IssueCategory{
					issueCategory1,
					issueCategory2,
					issueCategory3,
				},
			},
			wantErr: false,
			want:    []*icPb.IssueCategory{issueCategory1, issueCategory2, issueCategory3},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, issueCategoryDTS.db, issueCategoryDTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := issueCategoryDTS.issueCategoryDao.CreateBatch(tt.args.ctx, tt.args.categoryList)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isIssueCategoryListEqual(got, tt.want) {
				t.Errorf("CreateBatch() got = %v, want = %v", got, tt.want)
				return
			}

		})
	}
}

func TestIssueCategoryDao_GetById(t *testing.T) {
	type args struct {
		ctx        context.Context
		categoryId string
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
		want    *icPb.IssueCategory
	}{
		{
			name: "invalid request, category id not passed",
			args: args{
				ctx: context.Background(),
			},
			wantErr: errors.Wrap(epifierrors.ErrInvalidArgument, "category id is mandatory parameter"),
		},
		{
			name: "record not found",
			args: args{
				ctx:        context.Background(),
				categoryId: "de34d771-2c76-44e3-81f9-48c730feef1f",
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "success",
			args: args{
				ctx:        context.Background(),
				categoryId: id1,
			},
			want:    issueCategory4,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, issueCategoryDTS.db, issueCategoryDTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := issueCategoryDTS.issueCategoryDao.GetById(tt.args.ctx, tt.args.categoryId)
			if (err != nil || tt.wantErr != nil) && err.Error() != tt.wantErr.Error() {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && !isIssueCategoryEqual(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
				return
			}
		})
	}
}

func TestIssueCategoryDao_GetAllByFilter(t *testing.T) {
	type args struct {
		ctx    context.Context
		filter *icPb.IssueCategoryFilter
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
		wantCnt int
	}{
		{
			name: "invalid issue category passed in filter",
			args: args{
				ctx:    context.Background(),
				filter: &icPb.IssueCategoryFilter{},
			},
			wantErr: errors.Wrap(epifierrors.ErrInvalidArgument, "invalid issue category, product category is mandatory"),
		},
		{
			name: "error: record not found",
			args: args{
				ctx: context.Background(),
				filter: &icPb.IssueCategoryFilter{
					IssueCategory: &icPb.IssueCategory{
						ProductCategory: "Onbarding",
					},
					FilterDepth: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY,
				},
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "success: filter depth L1",
			args: args{
				ctx: context.Background(),
				filter: &icPb.IssueCategoryFilter{
					IssueCategory: &icPb.IssueCategory{
						ProductCategory: "Onboarding",
					},
					FilterDepth: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY,
				},
			},
			wantCnt: 3,
		},
		{
			name: "success: filter depth L2",
			args: args{
				ctx: context.Background(),
				filter: &icPb.IssueCategoryFilter{
					IssueCategory: &icPb.IssueCategory{
						ProductCategory:        "Onboarding",
						ProductCategoryDetails: "VKYC",
					},
					FilterDepth: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS,
				},
			},
			wantCnt: 2,
		},
		{
			name: "success: filter depth L3",
			args: args{
				ctx: context.Background(),
				filter: &icPb.IssueCategoryFilter{
					IssueCategory: &icPb.IssueCategory{
						ProductCategory:        "Onboarding",
						ProductCategoryDetails: "VKYC",
						SubCategory:            "Issue-2",
					},
					FilterDepth: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
				},
			},
			wantCnt: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, issueCategoryDTS.db, issueCategoryDTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := issueCategoryDTS.issueCategoryDao.GetAllByFilter(tt.args.ctx, tt.args.filter)
			if (err != nil || tt.wantErr != nil) && err.Error() != tt.wantErr.Error() {
				t.Errorf("GetAllByFilter() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && len(got) != tt.wantCnt {
				t.Errorf("GetAllByFilter() got cnt = %v, want cnt %v", len(got), tt.wantCnt)
				return
			}
		})
	}
}

func isIssueCategoryListEqual(got []*icPb.IssueCategory, want []*icPb.IssueCategory) bool {
	if len(got) != len(want) {
		return false
	}
	for idx := range got {
		if !isIssueCategoryEqual(got[idx], want[idx]) {
			return false
		}
	}
	return true
}
func isIssueCategoryEqual(got *icPb.IssueCategory, want *icPb.IssueCategory) bool {
	got.Id = want.Id
	got.CreatedAt = want.CreatedAt
	got.UpdatedAt = want.UpdatedAt
	if diff := deep.Equal(got, want); diff != nil {
		return false
	}
	return true
}

func TestIssueCategoryDao_GetIssueCategoryTree(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    *icPb.IssueCategoryTree
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
			},
			want: &icPb.IssueCategoryTree{
				ProductCategoryNodes: []*icPb.IssueCategoryNode{
					{
						Value: "Account",
						Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY,
						Children: []*icPb.IssueCategoryNode{
							{
								Value: "Delete",
								Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS,
							},
							{
								Value: "Blocked",
								Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS,
								Children: []*icPb.IssueCategoryNode{
									{
										Value: "Issue-1",
										Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
									},
								},
							},
						},
					},
					{
						Value: "Onboarding",
						Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY,
						Children: []*icPb.IssueCategoryNode{
							{
								Value: "VKYC",
								Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS,
								Children: []*icPb.IssueCategoryNode{
									{
										Value: "Issue-2",
										Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
									},
									{
										Value: "Issue-3",
										Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
									},
								},
							},
							{
								Value: "KYC",
								Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_PRODUCT_CATEGORY_DETAILS,
								Children: []*icPb.IssueCategoryNode{
									{
										Value: "Issue-4",
										Level: icPb.IssueCategoryMask_ISSUE_CATEGORY_MASK_SUB_CATEGORY,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := issueCategoryDTS.issueCategoryDao.GetIssueCategoryTree(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetIssueCategoryTree() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetIssueCategoryTree() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIssueCategoryDao_GetByIds(t *testing.T) {
	type args struct {
		categoryIds []string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]*icPb.IssueCategory
		wantErr error
	}{
		{
			name: "invalid request, empty list provided",
			args: args{
				categoryIds: []string{},
			},
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "record not found",
			args: args{
				categoryIds: []string{"de34d771-2c76-44e3-81f9-48c730feef1f"},
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "success: single record",
			args: args{
				categoryIds: []string{id1},
			},
			want: map[string]*icPb.IssueCategory{
				id1: issueCategory4,
			},
		},
		{
			name: "success: multiple records",
			args: args{
				categoryIds: []string{id1, id2},
			},
			want: map[string]*icPb.IssueCategory{
				id1: issueCategory4,
				id2: issueCategory5,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := issueCategoryDTS.issueCategoryDao.GetByIds(context.Background(), tt.args.categoryIds)
			if (err != nil) != (tt.wantErr != nil) && !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isResponseMatching(got, tt.want) {
				t.Errorf("GetByIds() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isResponseMatching(got map[string]*icPb.IssueCategory, want map[string]*icPb.IssueCategory) bool {
	if len(got) != len(want) {
		return false
	}
	for k, v := range got {
		// ignoring the created at, updated at, and id (db generated fields)
		v.CreatedAt = want[k].GetCreatedAt()
		v.UpdatedAt = want[k].GetUpdatedAt()
		v.Id = want[k].GetId()
		if !reflect.DeepEqual(v, want[k]) {
			return false
		}
	}
	return true
}
