//go:generate dao_metrics_gen .
//go:generate mockgen -source=dao.go -destination=../../test/mocks/issue_category/dao/mock_dao.go

package dao

import (
	"context"

	icPb "github.com/epifi/gamma/api/cx/issue_category"
)

type IIssueCategoryDao interface {
	// CreateBatch - method to bulk insert issue category records in db
	// accepts list of IssueCategory objects to be created and returns list of created entries in the db
	CreateBatch(ctx context.Context, req []*icPb.IssueCategory) ([]*icPb.IssueCategory, error)

	// GetById - method to retrieve IssueCategory based on unique id
	// categoryId is the primary key in db, and it is mandatory field
	GetById(ctx context.Context, categoryId string) (*icPb.IssueCategory, error)

	// GetAllByFilter will get all issue categories with given filters applied
	// Record not found error if no record matches given filter
	// will return non-nil error for other errors
	GetAllByFilter(ctx context.Context, filter *icPb.IssueCategoryFilter) ([]*icPb.IssueCategory, error)

	// GetIssueCategoryTree is used to fetch the tree structure for all combinations of L1, L2, L3
	GetIssueCategoryTree(ctx context.Context) (*icPb.IssueCategoryTree, error)

	// GetIssueCategoryLevel will get a particular issue category level with given filters
	// if neither of L1, L2 or L3 are present, will fetch the list of L1s
	// if L1 is present, we will fetch the list of L2s associated with that L1
	// if L1, L2 are present, we will fetch the list of L3s associated with the L1 and L2
	GetIssueCategoryLevel(ctx context.Context, filter *icPb.IssueCategoryFilter) ([]*icPb.IssueCategory, error)

	// GetByIds - method to retrieve IssueCategory based on unique ids
	// maximum 50 records can be fetched at a time
	// it returns a map of issue category id to issue category objects
	GetByIds(ctx context.Context, categoryIds []string) (map[string]*icPb.IssueCategory, error)
}
