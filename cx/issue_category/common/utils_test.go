package common

import (
	"testing"

	"github.com/pkg/errors"

	icPb "github.com/epifi/gamma/api/cx/issue_category"
)

func TestValidateIssueCategory(t *testing.T) {
	t.<PERSON>()
	type args struct {
		category *icPb.IssueCategory
	}
	tests := []struct {
		name	string
		args	args
		wantErr	error
	}{
		{
			name:	"invalid: L1 not populated",
			args: args{
				category: &icPb.IssueCategory{},
			},
			wantErr:	errors.New("invalid issue category, product category is mandatory"),
		},
		{
			name:	"invalid: L3 populated without L2",
			args: args{
				category: &icPb.IssueCategory{
					ProductCategory:	"A",
					SubCategory:		"C",
				},
			},
			wantErr:	errors.New("invalid issue category, cannot populate L3 without L2"),
		},
		{
			name:	"success",
			args: args{
				category: &icPb.IssueCategory{
					ProductCategory: "A",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateIssueCategory(tt.args.category); err != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("ValidateIssueCategory() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
