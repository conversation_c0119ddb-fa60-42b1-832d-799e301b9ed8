package common

import (
	"github.com/pkg/errors"

	icPb "github.com/epifi/gamma/api/cx/issue_category"
)

// ValidateIssueCategory determines whether given issueCategory is valid
func ValidateIssueCategory(category *icPb.IssueCategory) error {
	// Product Category (L1) must not be empty
	if category.GetProductCategory() == "" {
		return errors.New("invalid issue category, product category is mandatory")
	}

	// SubCategory (L3) cannot be populated if ProductCategoryDetails (L2) is empty
	if category.GetSubCategory() != "" && category.GetProductCategoryDetails() == "" {
		return errors.New("invalid issue category, cannot populate L3 without L2")
	}
	return nil
}
