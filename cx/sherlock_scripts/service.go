//nolint:dupl,funlen
package sherlock_scripts

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	ssPb "github.com/epifi/gamma/api/cx/sherlock_scripts"
	"github.com/epifi/gamma/api/typesv2/webui"
	cxLogger "github.com/epifi/gamma/cx/logger"
	helper "github.com/epifi/gamma/cx/sherlock_scripts/script_helper"
	"github.com/epifi/be-common/pkg/epifierrors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	sherlockScriptsPb "github.com/epifi/gamma/api/cx/sherlock_scripts"
	searchPb "github.com/epifi/gamma/api/search"
	cxSearchPb "github.com/epifi/gamma/api/search/cx"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	SCRIPT_NAME   = "Script Name"
	EXIT_CRITERIA = "Exit Criteria"
	L1            = "L1"
	L2            = "L2"
	L3            = "L3"
)

type SherlockScriptsService struct {
	searchClient         searchPb.ActionBarClient
	sherlockScriptHelper helper.ISherlockScriptHelper
}

var _ sherlockScriptsPb.SherlockScriptsServer = &SherlockScriptsService{}

func NewSherlockScriptsService(searchClient searchPb.ActionBarClient, sherlockScriptHelper helper.ISherlockScriptHelper) *SherlockScriptsService {
	return &SherlockScriptsService{
		searchClient:         searchClient,
		sherlockScriptHelper: sherlockScriptHelper,
	}
}

func validateSearchRequest(request *sherlockScriptsPb.GetSearchResultsRequest) error {
	// validations on product category details
	if request.GetProductCategoryDetails() != "" {
		if request.GetProductCategory() == "" {
			return errors.New("product category details filter cannot be applied without product category filter")
		}
	}
	// validations on subcategory
	if request.GetSubCategory() != "" {
		if request.GetProductCategoryDetails() == "" {
			return errors.New("subcategory filter cannot be applied without product category details filter")
		}
		if request.GetProductCategory() == "" {
			return errors.New("subcategory filter cannot be applied without product category filter")
		}
	}
	return nil
}

func validateSearchMetaRequest(request *sherlockScriptsPb.GetSearchResultsMetaRequest) error {
	// validations on product category details
	if request.GetProductCategoryDetails() != "" {
		if request.GetProductCategory() == "" {
			return errors.New("product category details filter cannot be applied without product category filter")
		}
	}
	// validations on subcategory
	if request.GetSubCategory() != "" {
		if request.GetProductCategoryDetails() == "" {
			return errors.New("subcategory filter cannot be applied without product category details filter")
		}
		if request.GetProductCategory() == "" {
			return errors.New("subcategory filter cannot be applied without product category filter")
		}
	}
	return nil
}

func (s SherlockScriptsService) GetSearchResults(ctx context.Context, request *sherlockScriptsPb.GetSearchResultsRequest) (*sherlockScriptsPb.GetSearchResultsResponse, error) {
	validateRequestErr := validateSearchRequest(request)
	if validateRequestErr != nil {
		logger.Error(ctx, "invalid request to get search results", zap.Error(validateRequestErr), zap.String(logger.ES_QUERY, request.GetQuery()), zap.String(logger.PRODUCT_CATEGORY, request.GetProductCategory()),
			zap.String(logger.PRODUCT_CATEGORY_DETAILS, request.GetProductCategoryDetails()), zap.String(logger.PRODUCT_SUB_CATEGORY, request.GetSubCategory()))
		return &sherlockScriptsPb.GetSearchResultsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(validateRequestErr.Error()),
		}, nil
	}
	searchResp, searchErr := s.searchClient.GetSherlockScriptSearchResults(ctx, &searchPb.GetSherlockScriptSearchResultsRequest{
		SherlockScriptSearchParams: &cxSearchPb.SherlockScriptSearchParams{
			Query:                 request.GetQuery(),
			ProductCategory:       request.GetProductCategory(),
			ProductCategoryDetail: request.GetProductCategoryDetails(),
			SubCategory:           request.GetSubCategory(),
		},
		FromBasedPaginationParams: &searchPb.FromBasedPaginationParams{
			From: request.GetPaginationParams().GetFrom(),
			Size: request.GetPaginationParams().GetSize(),
		},
	})
	if te := epifigrpc.RPCError(searchResp, searchErr); te != nil {
		logger.Error(ctx, "error while fetching sherlock script search results", zap.Error(te))
		if searchResp.GetStatus().IsRecordNotFound() {
			return &sherlockScriptsPb.GetSearchResultsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		if searchResp.GetStatus().IsInvalidArgument() {
			return &sherlockScriptsPb.GetSearchResultsResponse{
				Status: rpcPb.StatusInvalidArgument(),
			}, nil
		}
		return &sherlockScriptsPb.GetSearchResultsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching results"),
		}, nil
	}
	return &sherlockScriptsPb.GetSearchResultsResponse{
		Status:        rpcPb.StatusOk(),
		SearchResults: convertToSearchResultTable(searchResp.GetSearchResults()),
		PaginationParams: &sherlockScriptsPb.PaginationParams{
			From: request.GetPaginationParams().GetFrom(),
			Size: request.GetPaginationParams().GetSize(),
		},
	}, nil
}

func (s SherlockScriptsService) GetSherlockScriptsInBulk(ctx context.Context, request *sherlockScriptsPb.GetBulkSherlockScriptRequest) (*sherlockScriptsPb.GetBulkSherlockScriptResponse, error) {
	filter := &helper.SherlockScriptTimeFilter{
		StartTime:         request.GetStartTime(),
		EndTime:           request.GetEndTime(),
		IsCreatedAtFilter: request.GetIsCreatedAtFilter(),
		IsUpdatedAtFilter: request.GetIsUpdatedAtFilter(),
		PageToken: helper.PageToken{
			Page:     uint32(request.GetRequestPageToken().GetFrom()),
			PageSize: uint32(request.GetRequestPageToken().GetSize()),
		},
	}
	sherlockScripts, err := s.sherlockScriptHelper.GetSherlockScriptByTimeStamp(ctx, filter)
	if err != nil {
		cxLogger.Error(ctx, "error while fetching scripts in bulk", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &sherlockScriptsPb.GetBulkSherlockScriptResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &sherlockScriptsPb.GetBulkSherlockScriptResponse{
			Status:          rpcPb.StatusInternalWithDebugMsg("error while fetching results"),
			SherlockScripts: sherlockScripts,
		}, nil
	}
	return &sherlockScriptsPb.GetBulkSherlockScriptResponse{
		Status:          rpcPb.StatusOk(),
		SherlockScripts: sherlockScripts,
	}, nil
}

func (s *SherlockScriptsService) GetSherlockScript(ctx context.Context, request *ssPb.GetSherlockScriptRequest) (*ssPb.GetSherlockScriptResponse, error) {

	sherlockScript, err := s.sherlockScriptHelper.GetSherlockScript(ctx, request.GetScriptId())
	if err != nil {
		cxLogger.Error(ctx, fmt.Sprintf("error fetching sherlock script %v ", request.GetScriptId()), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &ssPb.GetSherlockScriptResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &ssPb.GetSherlockScriptResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &ssPb.GetSherlockScriptResponse{
		Status:         rpcPb.StatusOk(),
		SherlockScript: sherlockScript,
	}, nil
}

func convertToSearchResultTable(searchResults []*searchPb.SherlockScriptSearchResult) *webui.Table {
	table := &webui.Table{}
	table.TableHeaders = []*webui.TableHeader{
		{
			Label:      SCRIPT_NAME,
			HeaderKey:  SCRIPT_NAME,
			IsVisible:  true,
			IsSortable: false,
			Style:      nil,
		},
		{
			Label:      EXIT_CRITERIA,
			HeaderKey:  EXIT_CRITERIA,
			IsVisible:  true,
			IsSortable: false,
			Style:      nil,
		},
	}
	table.TableRows = getSearchResultTableRows(searchResults)
	return table
}

func getSearchResultTableRows(searchResults []*searchPb.SherlockScriptSearchResult) []*webui.TableRow {
	var tableRows []*webui.TableRow
	for _, searchResult := range searchResults {
		tableRows = append(tableRows, &webui.TableRow{
			HeaderKeyToCellValueMap: nil,
			HeaderKeyCellMap: map[string]*webui.TableCell{
				SCRIPT_NAME: {
					DataType: webui.TableCell_DATA_TYPE_STRING,
					ValueV2: &webui.TableCell_StringValue{
						StringValue: searchResult.GetTitle(),
					},
				},
				EXIT_CRITERIA: {
					DataType: webui.TableCell_DATA_TYPE_LABEL_VALUE_MATRIX,
					ValueV2:  getTableLabelMatrixValue(searchResult.GetIssueCategoryDetails().GetProductCategories()),
				},
			},
			Meta: searchResult.GetId(),
		})
	}
	return tableRows
}

func getTableLabelMatrixValue(productCategories []*searchPb.ProductCategory) *webui.TableCell_TableLabelMatrixValue {
	var labelValueList []*webui.TableLabelValueList
	for _, productCategory := range productCategories {
		// if there are no product category details, append the details without any product category details or subcategory
		if len(productCategory.GetProductCategoryDetails()) == 0 {
			// append only if we have non-empty product category
			if productCategory.GetValue() != "" {
				labelValueList = append(labelValueList, &webui.TableLabelValueList{
					LabelValueList: []*webui.TableLabelValue{
						{
							Label:    L1,
							DataType: webui.TableLabelValue_DATA_TYPE_STRING,
							Value: &webui.TableLabelValue_StringValue{
								StringValue: productCategory.GetValue(),
							},
						},
					},
				})
			} else {
				continue
			}
		}
		// other append with product category details
		for _, productCategoryDetails := range productCategory.GetProductCategoryDetails() {
			// if there are no subcategories, append the details without any subcategory
			if len(productCategoryDetails.GetSubCategories()) == 0 {
				// append only if we have non-empty product category details
				if productCategoryDetails.GetValue() != "" {
					labelValueList = append(labelValueList, &webui.TableLabelValueList{
						LabelValueList: []*webui.TableLabelValue{
							{
								Label:    L1,
								DataType: webui.TableLabelValue_DATA_TYPE_STRING,
								Value: &webui.TableLabelValue_StringValue{
									StringValue: productCategory.GetValue(),
								},
							},
							{
								Label:    L2,
								DataType: webui.TableLabelValue_DATA_TYPE_STRING,
								Value: &webui.TableLabelValue_StringValue{
									StringValue: productCategoryDetails.GetValue(),
								},
							},
						},
					})
				} else {
					continue
				}
			}
			// otherwise append with subcategories
			for _, subCategory := range productCategoryDetails.GetSubCategories() {
				// append only if we have non-empty subcategory
				if subCategory.GetValue() != "" {
					labelValueList = append(labelValueList, &webui.TableLabelValueList{
						LabelValueList: []*webui.TableLabelValue{
							{
								Label:    L1,
								DataType: webui.TableLabelValue_DATA_TYPE_STRING,
								Value: &webui.TableLabelValue_StringValue{
									StringValue: productCategory.GetValue(),
								},
							},
							{
								Label:    L2,
								DataType: webui.TableLabelValue_DATA_TYPE_STRING,
								Value: &webui.TableLabelValue_StringValue{
									StringValue: productCategoryDetails.GetValue(),
								},
							},
							{
								Label:    L3,
								DataType: webui.TableLabelValue_DATA_TYPE_STRING,
								Value: &webui.TableLabelValue_StringValue{
									StringValue: subCategory.GetValue(),
								},
							},
						},
					})
				}
			}
		}
	}
	return &webui.TableCell_TableLabelMatrixValue{
		TableLabelMatrixValue: &webui.TableLabelValueMatrix{
			LabelValueMatrix: labelValueList,
		},
	}
}

func (s SherlockScriptsService) GetSearchResultsMeta(ctx context.Context, request *sherlockScriptsPb.GetSearchResultsMetaRequest) (*sherlockScriptsPb.GetSearchResultsMetaResponse, error) {
	validateRequestErr := validateSearchMetaRequest(request)
	if validateRequestErr != nil {
		logger.Error(ctx, "invalid request to get search results", zap.Error(validateRequestErr), zap.String(logger.ES_QUERY, request.GetQuery()), zap.String(logger.PRODUCT_CATEGORY, request.GetProductCategory()),
			zap.String(logger.PRODUCT_CATEGORY_DETAILS, request.GetProductCategoryDetails()), zap.String(logger.PRODUCT_SUB_CATEGORY, request.GetSubCategory()))
		return &sherlockScriptsPb.GetSearchResultsMetaResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(validateRequestErr.Error()),
		}, nil
	}
	searchResp, searchErr := s.searchClient.GetSherlockScriptSearchResultsMeta(ctx, &searchPb.GetSherlockScriptSearchResultsMetaRequest{SherlockScriptSearchParams: &cxSearchPb.SherlockScriptSearchParams{
		Query:                 request.GetQuery(),
		ProductCategory:       request.GetProductCategory(),
		ProductCategoryDetail: request.GetProductCategoryDetails(),
		SubCategory:           request.GetSubCategory(),
	}})
	if te := epifigrpc.RPCError(searchResp, searchErr); te != nil {
		logger.Error(ctx, "error while fetching sherlock script search results", zap.Error(te))
		if searchResp.GetStatus().IsRecordNotFound() {
			return &sherlockScriptsPb.GetSearchResultsMetaResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		if searchResp.GetStatus().IsInvalidArgument() {
			return &sherlockScriptsPb.GetSearchResultsMetaResponse{
				Status: rpcPb.StatusInvalidArgument(),
			}, nil
		}
		return &sherlockScriptsPb.GetSearchResultsMetaResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching results"),
		}, nil
	}

	return &sherlockScriptsPb.GetSearchResultsMetaResponse{
		Status:                         rpcPb.StatusOk(),
		SherlockScriptMetaSearchResult: convertToSearchResultMetaWithLabelValue(searchResp.GetSherlockScriptMetaSearchResult()),
	}, nil
}

func convertToSearchResultMetaWithLabelValue(metaSearchResult *searchPb.SherlockScriptMetaSearchResult) *sherlockScriptsPb.SherlockScriptMetaSearchResult {
	productCategoryMeta := []*sherlockScriptsPb.ProductCategoryMeta{}
	for _, productCategory := range metaSearchResult.GetScriptCategoryParamsMeta().GetProductCategoryMetaList() {
		var productCategoryDetailsMeta []*sherlockScriptsPb.ProductCategoryDetailMeta
		for _, productCategoryDetails := range productCategory.GetProductCategoryDetailMetaList() {
			var subCategoryMeta []*sherlockScriptsPb.SubCategoryMeta
			for _, subCategory := range productCategoryDetails.GetSubCategoryMetaList() {
				subCategoryMeta = append(subCategoryMeta, &sherlockScriptsPb.SubCategoryMeta{
					Label: subCategory.GetValue(),
					Value: subCategory.GetValue(),
					Count: subCategory.GetCount(),
				})
			}
			productCategoryDetailsMeta = append(productCategoryDetailsMeta, &sherlockScriptsPb.ProductCategoryDetailMeta{
				Label:               productCategoryDetails.GetValue(),
				Value:               productCategoryDetails.GetValue(),
				Count:               productCategoryDetails.GetCount(),
				SubCategoryMetaList: subCategoryMeta,
			})
		}
		productCategoryMeta = append(productCategoryMeta, &sherlockScriptsPb.ProductCategoryMeta{
			Label:                         productCategory.GetValue(),
			Value:                         productCategory.GetValue(),
			Count:                         productCategory.GetCount(),
			ProductCategoryDetailMetaList: productCategoryDetailsMeta,
		})
	}

	return &sherlockScriptsPb.SherlockScriptMetaSearchResult{
		ScriptCategoryParamsMeta: &sherlockScriptsPb.ScriptCategoryParamsMeta{
			ProductCategoryMetaList: productCategoryMeta,
		},
		TotalResultCount: metaSearchResult.GetTotalResultCount(),
	}
}
