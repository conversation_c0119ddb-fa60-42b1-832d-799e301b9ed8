package script_helper

import (
	"context"
	"reflect"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"

	ssPb "github.com/epifi/gamma/api/cx/sherlock_scripts"
	strapiPkgPb "github.com/epifi/gamma/api/pkg/strapi"
	"github.com/epifi/gamma/api/pkg/strapi/payload"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/pkg/strapi"
)

const strapiScriptIdField = "slug-id"
const createdAtField = "createdAt"
const updatedAtField = "updatedAt"

type SherlockScriptHelperImpl struct {
	strapiClient *strapi.Strapi
	cxConfig     *config.Config
}

func NewSherlockScriptHelperImpl(strapiClient *strapi.Strapi, cxConfig *config.Config) *SherlockScriptHelperImpl {
	return &SherlockScriptHelperImpl{
		strapiClient: strapiClient,
		cxConfig:     cxConfig,
	}
}

func (s *SherlockScriptHelperImpl) GetSherlockScript(ctx context.Context, scriptId string) (*ssPb.SherlockScript, error) {
	getSherlockScriptQueryParams := s.CreateGetSherlockScriptRequest(scriptId)
	scriptResponse, err := s.strapiClient.Get(ctx, getSherlockScriptQueryParams)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching script")
	}
	var sherlockScript payload.CmsScript
	umErr := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(scriptResponse, &sherlockScript)
	if umErr != nil {
		return nil, errors.Wrap(umErr, "error while unmarshalling strapi response to proto")
	}
	if sherlockScript.GetCmsScriptData() == nil {
		return nil, epifierrors.ErrRecordNotFound
	}
	return s.convertToSherlockScriptProto(sherlockScript.GetCmsScriptData()[0]), nil
}
func (s *SherlockScriptHelperImpl) convertToSherlockScriptProto(scriptData *payload.CmsScriptData) *ssPb.SherlockScript {
	solutions := scriptData.GetCmsScriptAttribute().GetSolutions()
	emptyGuide := &ssPb.Guide{}
	emptyPictureGuide := &ssPb.PictureGuide{}
	emptyPostCallStep := &ssPb.PostCallStep{}
	var sherlockSolutionList []*ssPb.Solution
	var pictureGuide *ssPb.PictureGuide
	for _, sol := range solutions {
		var guides []*ssPb.Guide
		for _, strapiGuide := range sol.GetPictureGuide().GetPictureGuideData().GetPictureGuideAttribute().GetGuides() {
			guide := &ssPb.Guide{
				Instruction: strapiGuide.GetInstruction(),
				Tip:         strapiGuide.GetTip(),
				Warning:     strapiGuide.GetWarning(),
				Url:         strapiGuide.GetPictureUrl().GetPictureUrlData().GetPictureUrlAttribute().GetOriginalPictureFormat(),
			}
			if !reflect.DeepEqual(emptyGuide, guide) {
				guides = append(guides, guide)
			}
		}

		pictureGuide = &ssPb.PictureGuide{
			Title:       sol.GetPictureGuide().GetPictureGuideData().GetPictureGuideAttribute().GetTitle(),
			Description: sol.GetPictureGuide().GetPictureGuideData().GetPictureGuideAttribute().GetDescription(),
			Guides:      guides,
		}
		postCallStep := &ssPb.PostCallStep{
			Notes:                  sol.GetPostCallSteps().GetNotes(),
			ProductCategory:        sol.GetPostCallSteps().GetFreshdeskDetails().GetProductCategory(),
			SubCategory:            sol.GetPostCallSteps().GetFreshdeskDetails().GetSubCategory(),
			ProductCategoryDetails: sol.GetPostCallSteps().GetFreshdeskDetails().GetProductCategoryDetails(),
			Status:                 sol.GetPostCallSteps().GetFreshdeskDetails().GetStatus(),
			Group:                  sol.GetPostCallSteps().GetFreshdeskDetails().GetGroup(),
			Agent:                  sol.GetPostCallSteps().GetFreshdeskDetails().GetAgent(),
			Title:                  sol.GetPostCallSteps().GetTitle(),
		}
		sherlockSolution := &ssPb.Solution{
			Text: sol.GetText(),
		}

		// Added empty object check to handle rendering for nil object on Sherlock
		if !reflect.DeepEqual(postCallStep, emptyPostCallStep) {
			sherlockSolution.PostCallStep = postCallStep
		}
		if !reflect.DeepEqual(pictureGuide, emptyPictureGuide) {
			sherlockSolution.PictureGuide = pictureGuide
		}
		sherlockSolutionList = append(sherlockSolutionList, sherlockSolution)
	}
	var keywords []string
	for _, o := range scriptData.GetCmsScriptAttribute().GetKeywords() {
		keywords = append(keywords, o.GetKeyword())
	}
	return &ssPb.SherlockScript{
		SlugId:           scriptData.GetCmsScriptAttribute().GetSlug(),
		Title:            scriptData.GetCmsScriptAttribute().GetTitle(),
		ShortDescription: scriptData.GetCmsScriptAttribute().GetShortDescription(),
		Solutions:        sherlockSolutionList,
		Keywords:         keywords,
	}
}

func (s *SherlockScriptHelperImpl) CreateGetSherlockScriptRequest(scriptId string) *strapiPkgPb.GetAPIParams {
	var filterParams []*strapiPkgPb.Filter
	var populateParams []*strapiPkgPb.Populate

	for _, filter := range s.cxConfig.StrapiConfig.FilterParamFields {
		filterParam := &strapiPkgPb.Filter{
			Field:          filter,
			FilterOperator: strapiPkgPb.FilterOperator_FilterOperator_EQUAL,
			Value:          scriptId,
		}
		filterParams = append(filterParams, filterParam)
	}

	for _, params := range s.cxConfig.StrapiConfig.PopulateFields {
		populateParam := &strapiPkgPb.Populate{Path: params}
		populateParams = append(populateParams, populateParam)
	}
	return &strapiPkgPb.GetAPIParams{
		FiterParams:    filterParams,
		PopulateParams: populateParams,
		ResourceUrl:    "scripts",
	}
}

type SherlockScriptTimeFilter struct {
	StartTime         string
	EndTime           string
	IsCreatedAtFilter bool
	IsUpdatedAtFilter bool
	PageToken         PageToken
}
type PageToken struct {
	Page     uint32
	PageSize uint32
}

// nolint:funlen
func (s *SherlockScriptHelperImpl) GetSherlockScriptByTimeStamp(ctx context.Context, filter *SherlockScriptTimeFilter) ([]*ssPb.SherlockScript, error) {

	var sherlockScripts []*ssPb.SherlockScript
	var filterParams []*strapiPkgPb.Filter
	var sortParams []*strapiPkgPb.SortField
	if filter.IsCreatedAtFilter {
		startTimeFilter := &strapiPkgPb.Filter{
			Field:          createdAtField,
			FilterOperator: strapiPkgPb.FilterOperator_FilterOperator_GREATER_THAN_OR_EQUAL,
			Value:          filter.StartTime,
		}
		endTimeFilter := &strapiPkgPb.Filter{
			Field:          createdAtField,
			FilterOperator: strapiPkgPb.FilterOperator_FilterOperator_LESS_THAN_OR_EQUAL,
			Value:          filter.EndTime,
		}
		filterParams = append(filterParams, startTimeFilter, endTimeFilter)
		createdAtFieldASCSort := &strapiPkgPb.SortField{
			Field:     updatedAtField,
			SortOrder: strapiPkgPb.SortOrder_SortOrder_ASCENDING,
		}
		sortParams = append(sortParams, createdAtFieldASCSort)
	}
	if filter.IsUpdatedAtFilter {
		startTimeFilter := &strapiPkgPb.Filter{
			Field:          updatedAtField,
			FilterOperator: strapiPkgPb.FilterOperator_FilterOperator_GREATER_THAN_OR_EQUAL,
			Value:          filter.StartTime,
		}
		endTimeFilter := &strapiPkgPb.Filter{
			Field:          updatedAtField,
			FilterOperator: strapiPkgPb.FilterOperator_FilterOperator_LESS_THAN_OR_EQUAL,
			Value:          filter.EndTime,
		}
		filterParams = append(filterParams, startTimeFilter, endTimeFilter)
		updatedAtFieldASCSort := &strapiPkgPb.SortField{
			Field:     updatedAtField,
			SortOrder: strapiPkgPb.SortOrder_SortOrder_ASCENDING,
		}
		sortParams = append(sortParams, updatedAtFieldASCSort)
	}
	var populateParams []*strapiPkgPb.Populate
	for _, params := range s.cxConfig.StrapiConfig.PopulateFields {
		populateParam := &strapiPkgPb.Populate{Path: params}
		populateParams = append(populateParams, populateParam)
	}
	var pageToken *strapiPkgPb.PageToken
	if !reflect.DeepEqual(filter.PageToken, PageToken{}) {
		pageToken = &strapiPkgPb.PageToken{
			PageNumber: filter.PageToken.Page,
			PageSize:   filter.PageToken.PageSize,
		}
	}
	getSherlockScriptQueryParams := &strapiPkgPb.GetAPIParams{
		FiterParams:    filterParams,
		PopulateParams: populateParams,
		SortParams:     sortParams,
		PageToken:      pageToken,
		ResourceUrl:    "scripts",
	}
	scriptResponse, err := s.strapiClient.Get(ctx, getSherlockScriptQueryParams)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching script")
	}
	var sherlockScript payload.CmsScript
	umErr := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(scriptResponse, &sherlockScript)
	if umErr != nil {
		return nil, errors.Wrap(umErr, "error while unmarshalling strapi response to proto")
	}
	if sherlockScript.GetCmsScriptData() == nil {
		return nil, epifierrors.ErrRecordNotFound
	}
	for _, scriptData := range sherlockScript.GetCmsScriptData() {
		sherlockScriptData := s.convertToSherlockScriptProto(scriptData)
		sherlockScripts = append(sherlockScripts, sherlockScriptData)
	}
	return sherlockScripts, nil
}
