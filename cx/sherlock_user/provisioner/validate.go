package provisioner

import (
	"fmt"

	sherlockUserPb "github.com/epifi/gamma/api/cx/sherlock_user"
)

func ValidateRequest(req *sherlockUserPb.SherlockUserInfo) error {
	if req.GetEmailId() == "" {
		return fmt.<PERSON>rrorf("request does not have email")
	}
	if req.GetPhoneNumber() == nil {
		return fmt.Errorf("request does not have phone number")
	}
	if req.GetUserType() == sherlockUserPb.SherlockUserType_SHERLOCK_USER_TYPE_UNSPECIFIED {
		return fmt.Errorf("request does not have valid sherlock user type")
	}
	if len(req.GetSherlockUserRoles()) == 0 {
		return fmt.Errorf("request does not have user roles")
	}
	if req.GetUserStatus() == sherlockUserPb.SherlockUserStatus_SHERLOCK_USER_STATUS_UNSPECIFIED {
		return fmt.Errorf("request does not have valid user status")
	}
	if req.GetName().ToString() == "" {
		return fmt.Errorf("request does not have valid name")
	}
	return nil
}

func ValidateAndGetUpdateFieldMask(user *sherlockUserPb.SherlockUserInfo, mask []sherlockUserPb.SherlockUserFieldMask) ([]sherlockUserPb.SherlockUserFieldMask, error) {
	var updateMask []sherlockUserPb.SherlockUserFieldMask
	for _, field := range mask {
		switch field {
		case sherlockUserPb.SherlockUserFieldMask_ROLES:
			if len(user.GetSherlockUserRoles()) == 0 {
				return nil, fmt.Errorf("update field mask does not have valid sherlock user roles")
			}
			updateMask = append(updateMask, sherlockUserPb.SherlockUserFieldMask_ROLES)

		case sherlockUserPb.SherlockUserFieldMask_USER_TYPE:
			if user.GetUserType() == sherlockUserPb.SherlockUserType_SHERLOCK_USER_TYPE_UNSPECIFIED {
				return nil, fmt.Errorf("update field mask does not have valid sherlock user type")
			}
			updateMask = append(updateMask, sherlockUserPb.SherlockUserFieldMask_USER_TYPE)

		case sherlockUserPb.SherlockUserFieldMask_USER_NAME:
			if user.GetName().ToString() == "" {
				return nil, fmt.Errorf("update field mask does not have valid name")
			}
			updateMask = append(updateMask, sherlockUserPb.SherlockUserFieldMask_USER_NAME)

		case sherlockUserPb.SherlockUserFieldMask_PHONE_NUMBER:
			if user.GetPhoneNumber() == nil {
				return nil, fmt.Errorf("update field mask does not have phone number")
			}
			updateMask = append(updateMask, sherlockUserPb.SherlockUserFieldMask_PHONE_NUMBER)

		case sherlockUserPb.SherlockUserFieldMask_OZONETEL_ID:
			// we allow an ozonetel id to be updated as empty, for example : if an agent is being deactivated
			updateMask = append(updateMask, sherlockUserPb.SherlockUserFieldMask_OZONETEL_ID)

		case sherlockUserPb.SherlockUserFieldMask_STATUS:
			if user.GetUserStatus() == sherlockUserPb.SherlockUserStatus_SHERLOCK_USER_STATUS_UNSPECIFIED {
				return nil, fmt.Errorf("update field mask does not have valid status")
			}
			updateMask = append(updateMask, sherlockUserPb.SherlockUserFieldMask_STATUS)

		default:
			return nil, fmt.Errorf("invalid update field")
		}
	}
	return updateMask, nil
}
