package provisioner

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	sherlockUserPb "github.com/epifi/gamma/api/cx/sherlock_user"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/sherlock_user/dao"
	sherlockUserWrapper "github.com/epifi/gamma/cx/sherlock_user/sherlock_user_wrapper"
	"github.com/epifi/be-common/pkg/epifierrors"
)

type EpifiUserProvisioner struct {
	sherlockUser sherlockUserWrapper.ISherlockUser
}

func NewEpifiUserProvisioner(sherlockUser sherlockUserWrapper.ISherlockUser) *EpifiUserProvisioner {
	return &EpifiUserProvisioner{
		sherlockUser: sherlockUser,
	}
}

var _ IUserProvisioner = &EpifiUserProvisioner{}

func (e *EpifiUserProvisioner) CreateUser(ctx context.Context, request *sherlockUserPb.CreateSherlockUserRequest) *sherlockUserPb.ProvisioningStatus {

	validateErr := ValidateRequest(request.GetSherlockUserInfo())
	if validateErr != nil {
		cxLogger.Error(ctx, "invalid create sherlock user request", zap.Error(validateErr))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInvalidArgumentWithDebugMsg(validateErr.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_EPIFI,
		}
	}

	user, alreadyErr := e.isAlreadyPresent(ctx, request.GetSherlockUserInfo().GetEmailId())
	if alreadyErr != nil && !errors.Is(alreadyErr, epifierrors.ErrRecordNotFound) {
		cxLogger.Error(ctx, "internal error while retrieving sherlock user", zap.Error(alreadyErr))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInternalWithDebugMsg(alreadyErr.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_EPIFI,
		}
	}
	if user != nil {
		cxLogger.Error(ctx, "sherlock user already exists", zap.Any("user_id", user.GetId()))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusAlreadyExistsWithDebugMsg("sherlock user already exits"),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_EPIFI,
		}
	}

	user, err := e.sherlockUser.CreateSherlockUser(ctx, request.GetSherlockUserInfo())
	if err != nil {
		cxLogger.Error(ctx, "internal server error while creating epifi sherlock user", zap.Error(err))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInternalWithDebugMsg(err.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_EPIFI,
		}
	}

	cxLogger.Info(ctx, "epifi sherlock user creation successful", zap.Any("sherlock_user_id", user.GetId()))
	return &sherlockUserPb.ProvisioningStatus{
		Status:                  rpcPb.StatusOk(),
		SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_EPIFI,
	}
}

func (e *EpifiUserProvisioner) UpdateUser(ctx context.Context, sherlockUserInfo *sherlockUserPb.SherlockUserInfo, updateFieldMask []sherlockUserPb.SherlockUserFieldMask) *sherlockUserPb.ProvisioningStatus {

	updateMask, updateErr := ValidateAndGetUpdateFieldMask(sherlockUserInfo, updateFieldMask)
	if updateErr != nil {
		cxLogger.Error(ctx, "error in update field mask", zap.Error(updateErr))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInvalidArgumentWithDebugMsg(updateErr.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_EPIFI,
		}
	}

	user, err := e.sherlockUser.UpdateSherlockUser(ctx, sherlockUserInfo, updateMask)
	if err != nil {
		cxLogger.Error(ctx, "error while updating epifi sherlock user", zap.Error(err))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInternalWithDebugMsg(err.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_EPIFI,
		}
	}

	cxLogger.Info(ctx, "successfully update sherlock user", zap.Any("updated_sherlock_user_id", user.GetId()))
	return &sherlockUserPb.ProvisioningStatus{
		Status:                  rpcPb.StatusOk(),
		SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_EPIFI,
	}
}

func (e *EpifiUserProvisioner) GetUser(ctx context.Context, email string) (*sherlockUserPb.SherlockUserInfo, error) {
	user, err := e.isAlreadyPresent(ctx, email)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (e *EpifiUserProvisioner) GetUsers(ctx context.Context, pageSize int, pageToken *dao.PageToken, filters *sherlockUserPb.GetAllSherlockUsersRequest_Filters) ([]*sherlockUserPb.SherlockUserInfo, error) {
	agentList, err := e.sherlockUser.GetBulkUser(ctx, pageToken, pageSize, filters)
	if err != nil {
		return nil, err
	}
	return agentList, nil
}

func (e *EpifiUserProvisioner) GetUserBySearchId(ctx context.Context, searchId *sherlockUserPb.SearchId) (*sherlockUserPb.SherlockUserInfo, error) {
	user, err := e.sherlockUser.GetSherlockUserBySearchId(ctx, searchId)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching user")
	}
	return user, nil
}

func (e *EpifiUserProvisioner) isAlreadyPresent(ctx context.Context, email string) (*sherlockUserPb.SherlockUserInfo, error) {
	if email == "" {
		return nil, fmt.Errorf("email id cannot be empty")
	}

	user, err := e.sherlockUser.GetSherlockUser(ctx, email)
	if err != nil {
		return nil, err
	}

	return user, nil
}
