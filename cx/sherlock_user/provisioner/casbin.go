package provisioner

import (
	"context"
	"strings"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	casbinPb "github.com/epifi/gamma/api/casbin"
	sherlockUserPb "github.com/epifi/gamma/api/cx/sherlock_user"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type CasbinUserProvisioner struct {
	casbinServiceClient casbinPb.CasbinClient
}

func NewCasbinUserProvisioner(casbinServiceClient casbinPb.CasbinClient) *CasbinUserProvisioner {
	return &CasbinUserProvisioner{
		casbinServiceClient: casbinServiceClient,
	}
}

var _ IUserProvisioner = &CasbinUserProvisioner{}

func (c *CasbinUserProvisioner) CreateUser(ctx context.Context, request *sherlockUserPb.CreateSherlockUserRequest) *sherlockUserPb.ProvisioningStatus {

	validateErr := ValidateRequest(request.GetSherlockUserInfo())
	if validateErr != nil {
		cxLogger.Error(ctx, "invalid request parameters", zap.Error(validateErr))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInvalidArgumentWithDebugMsg(validateErr.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_CASBIN,
		}
	}

	emailID := strings.ToLower(request.GetSherlockUserInfo().GetEmailId())
	accessLevelList := request.GetSherlockUserInfo().GetSherlockUserRoles()

	resp, err := c.casbinServiceClient.AddUserPermissionsList(ctx, &casbinPb.AddUserPermissionsListRequest{
		UserId:      emailID,
		AccessLevel: accessLevelList,
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error in creating user in casbin", zap.Error(te))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInternalWithDebugMsg(te.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_CASBIN,
		}
	}

	return &sherlockUserPb.ProvisioningStatus{
		Status:                  rpcPb.StatusOk(),
		SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_CASBIN,
	}
}

func (c *CasbinUserProvisioner) UpdateUser(ctx context.Context, sherlockUserInfo *sherlockUserPb.SherlockUserInfo, updateFieldMask []sherlockUserPb.SherlockUserFieldMask) *sherlockUserPb.ProvisioningStatus {

	updateMask, updateErr := ValidateAndGetUpdateFieldMask(sherlockUserInfo, updateFieldMask)
	if updateErr != nil {
		cxLogger.Error(ctx, "error in update field mask", zap.Error(updateErr))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInvalidArgumentWithDebugMsg(updateErr.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_CASBIN,
		}
	}

	updateFlag := false
	for _, field := range updateMask {
		if field == sherlockUserPb.SherlockUserFieldMask_ROLES {
			updateFlag = true
			break
		}
	}

	if !updateFlag {
		cxLogger.Info(ctx, "no roles has to be updated in casbin")
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusOkWithDebugMsg("no roles have to be updated"),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_CASBIN,
		}
	}

	// we delete all roles available for user and then insert newly updated roles back to casbin
	// this is done to avoid inconsistency in the database

	removeResp, removeErr := c.casbinServiceClient.RemoveUserPermissions(
		ctx, &casbinPb.RemoveUserPermissionsRequest{UserId: sherlockUserInfo.GetEmailId()},
	)
	if te := epifigrpc.RPCError(removeResp, removeErr); te != nil && !removeResp.GetStatus().IsRecordNotFound() {
		cxLogger.Error(ctx, "error in updating user in casbin", zap.Error(te))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInternalWithDebugMsg(te.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_CASBIN,
		}
	}

	addResp, addErr := c.casbinServiceClient.AddUserPermissionsList(ctx, &casbinPb.AddUserPermissionsListRequest{
		UserId:      sherlockUserInfo.GetEmailId(),
		AccessLevel: sherlockUserInfo.GetSherlockUserRoles(),
	})
	if te := epifigrpc.RPCError(addResp, addErr); te != nil {
		cxLogger.Error(ctx, "error in updating user in casbin", zap.Error(te))
		return &sherlockUserPb.ProvisioningStatus{
			Status:                  rpcPb.StatusInternalWithDebugMsg(te.Error()),
			SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_CASBIN,
		}
	}

	return &sherlockUserPb.ProvisioningStatus{
		Status:                  rpcPb.StatusOk(),
		SherlockUserProvisioner: sherlockUserPb.SherlockUserProvisioner_CASBIN,
	}
}
