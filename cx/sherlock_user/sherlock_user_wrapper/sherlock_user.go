package sherlock_user_wrapper

import (
	"context"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/pkg/errors"

	sherlockUserPb "github.com/epifi/gamma/api/cx/sherlock_user"
	"github.com/epifi/gamma/cx/sherlock_user/dao"
	"github.com/epifi/gamma/cx/sherlock_user/dao/model"
)

type ISherlockUser interface {
	// CreateSherlockUser - creates a sherlock user with roles and info in the respective tables
	CreateSherlockUser(ctx context.Context, sherlockUser *sherlockUserPb.SherlockUserInfo) (*sherlockUserPb.SherlockUserInfo, error)

	// UpdateSherlockUser - updates info and roles of a user in the respective tables
	UpdateSherlockUser(ctx context.Context, sherlockUser *sherlockUserPb.SherlockUserInfo, updateMask []sherlockUserPb.SherlockUserFieldMask) (*sherlockUserPb.SherlockUserInfo, error)

	// GetSherlockUser - fetches info and roles of a user with active status
	GetSherlockUser(ctx context.Context, emailId string) (*sherlockUserPb.SherlockUserInfo, error)

	// GetBulkUser - fetches list of all sherlock users
	GetBulkUser(ctx context.Context, pageToken *dao.PageToken, pageSize int, filters *sherlockUserPb.GetAllSherlockUsersRequest_Filters) ([]*sherlockUserPb.SherlockUserInfo, error)

	// GetSherlockUserBySearchId - fetches info and roles of a user using search filter
	// user email, ozonetel id can be passed in the search id
	GetSherlockUserBySearchId(ctx context.Context, searchId *sherlockUserPb.SearchId) (*sherlockUserPb.SherlockUserInfo, error)
}

type SherlockUser struct {
	sherlockUserInfoDao dao.ISherlockUserInfoDao
	sherlockUserRoleDao dao.ISherlockUserRoleDao
	txnExecutor         storageV2.TxnExecutor
}

func NewSherlockUserDao(sherlockUserInfoDao dao.ISherlockUserInfoDao, sherlockUserRoleDao dao.ISherlockUserRoleDao, txnExecutor storageV2.TxnExecutor) *SherlockUser {
	return &SherlockUser{
		sherlockUserInfoDao: sherlockUserInfoDao,
		sherlockUserRoleDao: sherlockUserRoleDao,
		txnExecutor:         txnExecutor,
	}
}

var _ ISherlockUser = &SherlockUser{}

func (s *SherlockUser) CreateSherlockUser(ctx context.Context, sherlockUser *sherlockUserPb.SherlockUserInfo) (*sherlockUserPb.SherlockUserInfo, error) {

	var sherlockUserProto *sherlockUserPb.SherlockUserInfo

	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		userInfo, err := s.sherlockUserInfoDao.CreateUserInfo(txnCtx, sherlockUser)
		if err != nil {
			return errors.Wrap(err, "error while creation of user info")
		}
		sherlockUserProto = userInfo
		if len(sherlockUser.GetSherlockUserRoles()) > 0 {
			for _, role := range sherlockUser.GetSherlockUserRoles() {
				_, err = s.sherlockUserRoleDao.CreateSherlockUserRole(txnCtx, sherlockUserProto.GetId(), role)
				if err != nil {
					return errors.Wrap(err, "error while creation of user roles")
				}
			}
		}
		return nil
	})
	if txnErr != nil {
		return nil, txnErr
	}

	return sherlockUserProto, nil
}

// UpdateSherlockUser - email id is mandatory for all updates, email id cannot be updated
func (s *SherlockUser) UpdateSherlockUser(ctx context.Context, sherlockUser *sherlockUserPb.SherlockUserInfo, updateMask []sherlockUserPb.SherlockUserFieldMask) (*sherlockUserPb.SherlockUserInfo, error) {
	var userId string
	var userResult *sherlockUserPb.SherlockUserInfo
	// we will remove the ozonetel id of a user, if the user is deactivated
	// this will ensure we don't have an inactive as well as active user with same ozonetel id
	if sherlockUser.GetUserStatus() == sherlockUserPb.SherlockUserStatus_INACTIVE {
		if !lo.Contains(updateMask, sherlockUserPb.SherlockUserFieldMask_OZONETEL_ID) {
			updateMask = append(updateMask, sherlockUserPb.SherlockUserFieldMask_OZONETEL_ID)
		}
		sherlockUser.OzonetelId = ""
	}
	updateColumns, rolesUpdateFlag, updateMaskErr := model.GetSherlockUserColumnsForUpdate(updateMask)
	if updateMaskErr != nil {
		return nil, updateMaskErr
	}
	if len(updateColumns) == 0 && !rolesUpdateFlag {
		return nil, errors.New("update mask cannot be nil")
	}

	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		if len(updateColumns) != 0 {
			_, err := s.sherlockUserInfoDao.UpdateUserInfo(txnCtx, sherlockUser, updateColumns)
			if err != nil {
				return errors.Wrap(err, "error while updating user info")
			}
		}
		// Updating roles is implemented as deleting all the previous roles and adding new roles, as of now
		// need to change this implementation to soft deletion of previous roles in the future
		if rolesUpdateFlag {
			var updatedRoles []*sherlockUserPb.SherlockUserRole
			updatedUserInfo, err := s.sherlockUserInfoDao.GetUserInfo(txnCtx, sherlockUser.GetEmailId())
			if err != nil {
				return err
			}
			userId = updatedUserInfo.GetId()
			err = s.sherlockUserRoleDao.DeleteSherlockUserRoles(txnCtx, userId)
			if err != nil {
				return errors.Wrap(err, "error while deleting prev roles")
			}
			if len(sherlockUser.GetSherlockUserRoles()) > 0 {
				for _, role := range sherlockUser.GetSherlockUserRoles() {
					roleModel, err := s.sherlockUserRoleDao.CreateSherlockUserRole(txnCtx, userId, role)
					if err != nil {
						return errors.Wrap(err, "error while creation of new user roles")
					}
					updatedRoles = append(updatedRoles, roleModel)
				}
				userResult = model.AddRolesToSherlockUserInfoProto(updatedUserInfo, updatedRoles)
			}
		}
		return nil
	})

	if txnErr != nil {
		return nil, txnErr
	}

	return userResult, nil
}

func (s *SherlockUser) GetSherlockUser(ctx context.Context, emailId string) (*sherlockUserPb.SherlockUserInfo, error) {
	var sherlockUser *sherlockUserPb.SherlockUserInfo

	userInfoModel, err := s.sherlockUserInfoDao.GetUserInfo(ctx, emailId)
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch user info")
	}
	if userInfoModel.GetUserStatus() == sherlockUserPb.SherlockUserStatus_INACTIVE {
		return nil, epifierrors.ErrPermissionDenied
	}
	userRolesModel, err := s.sherlockUserRoleDao.GetSherlockUserRoles(ctx, userInfoModel.Id)
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch user roles")
	}

	sherlockUser = model.AddRolesToSherlockUserInfoProto(userInfoModel, userRolesModel)
	return sherlockUser, nil
}

func (s *SherlockUser) GetBulkUser(ctx context.Context, pageToken *dao.PageToken, pageSize int, filters *sherlockUserPb.GetAllSherlockUsersRequest_Filters) ([]*sherlockUserPb.SherlockUserInfo, error) {

	var resultSherlockUser []*sherlockUserPb.SherlockUserInfo

	sherlockUserInfo, err := s.sherlockUserInfoDao.GetBulkUserInfo(ctx, pageToken, pageSize, filters)

	if err != nil {
		return nil, errors.Wrap(err, "unable to get bulk user info from db")
	}

	for _, user := range sherlockUserInfo {
		userRoles, err := s.sherlockUserRoleDao.GetSherlockUserRoles(ctx, user.Id)
		if err != nil {
			return nil, errors.Wrap(err, "unable to get user roles from db")
		}
		userInfoProto := model.AddRolesToSherlockUserInfoProto(user, userRoles)
		resultSherlockUser = append(resultSherlockUser, userInfoProto)
	}

	return resultSherlockUser, nil
}

func (s *SherlockUser) GetSherlockUserBySearchId(ctx context.Context, searchId *sherlockUserPb.SearchId) (*sherlockUserPb.SherlockUserInfo, error) {
	var sherlockUser *sherlockUserPb.SherlockUserInfo

	userInfo, err := s.sherlockUserInfoDao.GetUserInfoBySearchId(ctx, searchId)
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch user info")
	}

	userRoles, err := s.sherlockUserRoleDao.GetSherlockUserRoles(ctx, userInfo.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch user roles")
	}

	sherlockUser = model.AddRolesToSherlockUserInfoProto(userInfo, userRoles)
	return sherlockUser, nil
}
