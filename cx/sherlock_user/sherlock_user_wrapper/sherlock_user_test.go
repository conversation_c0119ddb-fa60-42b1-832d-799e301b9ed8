package sherlock_user_wrapper_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	gormV2 "gorm.io/gorm"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	casbinPb "github.com/epifi/gamma/api/casbin"
	sherlockUserPb "github.com/epifi/gamma/api/cx/sherlock_user"

	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/sherlock_user/dao"
	"github.com/epifi/gamma/cx/sherlock_user/sherlock_user_wrapper"
	"github.com/epifi/gamma/cx/test"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
)

// Test Suite for sherlock user info dao
type SherlockUserTestSuite struct {
	db           *gormV2.DB
	conf         *config.Config
	sherlockUser *sherlock_user_wrapper.SherlockUser
}

var (
	SUTS             SherlockUserTestSuite
	testSherlockUser = &sherlockUserPb.SherlockUserInfo{
		EmailId: "<EMAIL>",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 9999999999,
		},
		Name: &commontypes.Name{
			FirstName:  "test1",
			MiddleName: "test2",
			LastName:   "test3",
		},
		UserType:          sherlockUserPb.SherlockUserType_CONSULTANT,
		UserStatus:        sherlockUserPb.SherlockUserStatus_ACTIVE,
		SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_ADMIN, casbinPb.AccessLevel_RISK_OPS},
	}
	testUserInfoFixture1 = &sherlockUserPb.SherlockUserInfo{
		Id:      "832d7fcf-3a31-484c-96e4-a252f41c0f10",
		EmailId: "<EMAIL>",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 8888888888,
		},
		Name: &commontypes.Name{
			FirstName:  "John",
			MiddleName: "M",
			LastName:   "Wick",
		},
		UserType:          sherlockUserPb.SherlockUserType_CONSULTANT,
		UserStatus:        sherlockUserPb.SherlockUserStatus_ACTIVE,
		OzonetelId:        "12345678",
		SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT, casbinPb.AccessLevel_FEDERAL_AGENT, casbinPb.AccessLevel_RISK_OPS},
	}
	testUserInfoFixture2 = &sherlockUserPb.SherlockUserInfo{
		Id:      "832d7fcf-3a31-484c-96e4-a252f41c0f10",
		EmailId: "<EMAIL>",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 8888888888,
		},
		Name: &commontypes.Name{
			FirstName:  "John",
			MiddleName: "M",
			LastName:   "Wick",
		},
		UserType:          sherlockUserPb.SherlockUserType_CONSULTANT,
		UserStatus:        sherlockUserPb.SherlockUserStatus_ACTIVE,
		OzonetelId:        "12345678",
		SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT},
	}
	testUserInfoFixture3 = &sherlockUserPb.SherlockUserInfo{
		Id:      "832d7fcf-3a31-484c-96e4-a252f41c0f10",
		EmailId: "<EMAIL>",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 1234567810,
		},
		Name:              &commontypes.Name{FirstName: "user"},
		UserType:          sherlockUserPb.SherlockUserType_EMPLOYEE,
		UserStatus:        sherlockUserPb.SherlockUserStatus_ACTIVE,
		SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT, casbinPb.AccessLevel_FEDERAL_AGENT, casbinPb.AccessLevel_RISK_OPS},
		OzonetelId:        "12345678",
	}
	testUserInfoFixture4 = &sherlockUserPb.SherlockUserInfo{
		Id:      "832d7fcf-3a31-484c-96e4-a252f41c0f10",
		EmailId: "<EMAIL>",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 1234567810,
		},
		Name: &commontypes.Name{
			FirstName: "user",
		},
		UserType:          sherlockUserPb.SherlockUserType_EMPLOYEE,
		UserStatus:        sherlockUserPb.SherlockUserStatus_INACTIVE,
		OzonetelId:        "12345678",
		SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT},
	}
	testUserInfoFixture5 = &sherlockUserPb.SherlockUserInfo{
		Id:      "832d7fcf-3a31-484c-96e4-a252f41c0f10",
		EmailId: "<EMAIL>",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: 1234567810,
		},
		Name: &commontypes.Name{
			FirstName: "user",
		},
		UserType:          sherlockUserPb.SherlockUserType_EMPLOYEE,
		UserStatus:        sherlockUserPb.SherlockUserStatus_INACTIVE,
		OzonetelId:        "",
		SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT},
	}
)

func TestSherlockUser_GetBulkUser(t *testing.T) {
	type args struct {
		ctx       context.Context
		pageToken *dao.PageToken
		pageSize  int
		filters   *sherlockUserPb.GetAllSherlockUsersRequest_Filters
	}
	tests := []struct {
		name    string
		args    args
		wantLen int
		wantErr bool
	}{
		{
			name: "get records when the page token is not passed",
			args: args{
				ctx:      context.Background(),
				pageSize: 9,
				filters: &sherlockUserPb.GetAllSherlockUsersRequest_Filters{
					EmailId:   "",
					FirstName: "",
				},
			},
			wantLen: 10,
			wantErr: false,
		},
		{
			name: "get records when the page token, size is passed",
			args: args{
				ctx: context.Background(),
				pageToken: &dao.PageToken{
					Timestamp: timestamppb.Now(),
					IsReverse: true,
				},
				pageSize: 5,
				filters: &sherlockUserPb.GetAllSherlockUsersRequest_Filters{
					EmailId:   "",
					FirstName: "",
				},
			},
			wantLen: 6,
			wantErr: false,
		},
		{
			name: "get records when filter on first name is passed",
			args: args{
				ctx:       context.Background(),
				pageToken: nil,
				pageSize:  5,
				filters: &sherlockUserPb.GetAllSherlockUsersRequest_Filters{
					EmailId:   "",
					FirstName: "John",
				},
			},
			wantLen: 0,
			wantErr: false,
		},
		{
			name: "get records when filter on email id is passed",
			args: args{
				ctx:       context.Background(),
				pageToken: nil,
				pageSize:  5,
				filters: &sherlockUserPb.GetAllSherlockUsersRequest_Filters{
					EmailId:   "<EMAIL>",
					FirstName: "",
				},
			},
			wantLen: 1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, SUTS.db, SUTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := SUTS.sherlockUser.GetBulkUser(tt.args.ctx, tt.args.pageToken, tt.args.pageSize, tt.args.filters)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBulkUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.Equal(t, tt.wantLen, len(got))
			}
		})
	}
}

func TestSherlockUser_GetSherlockUser(t *testing.T) {
	type args struct {
		ctx     context.Context
		emailId string
	}
	tests := []struct {
		name    string
		args    args
		wantLen int
		wantErr bool
	}{
		{
			name:    "failure, email id not passed",
			args:    args{ctx: context.Background()},
			wantLen: 0,
			wantErr: true,
		},
		{
			name: "failure, user doesn't exist",
			args: args{
				ctx:     context.Background(),
				emailId: "<EMAIL>",
			},
			wantLen: 0,
			wantErr: true,
		},
		{
			name: "success: user marked inactive",
			args: args{
				ctx:     context.Background(),
				emailId: "<EMAIL>",
			},
			wantLen: 0,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx:     context.Background(),
				emailId: "<EMAIL>",
			},
			wantLen: 2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, SUTS.db, SUTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := SUTS.sherlockUser.GetSherlockUser(tt.args.ctx, tt.args.emailId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSherlockUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.Equal(t, tt.wantLen, len(got.GetSherlockUserRoles()))
				assert.NotEmpty(t, len(got.GetEmailId()))
			}
		})
	}
}

func TestSherlockUser_CreateSherlockUser(t *testing.T) {
	type args struct {
		ctx          context.Context
		sherlockUser *sherlockUserPb.SherlockUserInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "failed record creation due to email id not present",
			args: args{
				ctx:          context.Background(),
				sherlockUser: &sherlockUserPb.SherlockUserInfo{},
			},
			wantErr: true,
		},
		{
			name: "failed record creation due to invalid user role",
			args: args{
				ctx: context.Background(),
				sherlockUser: &sherlockUserPb.SherlockUserInfo{
					EmailId:           testSherlockUser.GetEmailId(),
					SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED},
				},
			},
			wantErr: true,
		},
		{
			name: "failed record creation due to usage of existing email",
			args: args{
				ctx: context.Background(),
				sherlockUser: &sherlockUserPb.SherlockUserInfo{
					EmailId: "<EMAIL>",
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx:          context.Background(),
				sherlockUser: testSherlockUser,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, SUTS.db, SUTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := SUTS.sherlockUser.CreateSherlockUser(tt.args.ctx, tt.args.sherlockUser)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateSherlockUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotEmpty(t, got.GetId())
			}
		})
	}
}

func TestSherlockUser_UpdateSherlockUser(t *testing.T) {
	type args struct {
		ctx          context.Context
		sherlockUser *sherlockUserPb.SherlockUserInfo
		updateMask   []sherlockUserPb.SherlockUserFieldMask
	}
	tests := []struct {
		name    string
		args    args
		want    *sherlockUserPb.SherlockUserInfo
		wantErr bool
	}{
		{
			name: "bad request, no email id or user id for update",
			args: args{
				ctx:          context.Background(),
				sherlockUser: &sherlockUserPb.SherlockUserInfo{},
				updateMask:   nil,
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "bad request, no update mask",
			args: args{
				ctx: context.Background(),
				sherlockUser: &sherlockUserPb.SherlockUserInfo{
					EmailId: "<EMAIL>",
				},
				updateMask: nil,
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "failure, invalid roles",
			args: args{
				ctx: context.Background(),
				sherlockUser: &sherlockUserPb.SherlockUserInfo{
					EmailId:           "<EMAIL>",
					SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_ACCESS_LEVEL_UNSPECIFIED},
				},
				updateMask: []sherlockUserPb.SherlockUserFieldMask{sherlockUserPb.SherlockUserFieldMask_ROLES},
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "success, update all fields except roles",
			args: args{
				ctx:          context.Background(),
				sherlockUser: testUserInfoFixture2,
				updateMask: []sherlockUserPb.SherlockUserFieldMask{sherlockUserPb.SherlockUserFieldMask_USER_TYPE,
					sherlockUserPb.SherlockUserFieldMask_PHONE_NUMBER, sherlockUserPb.SherlockUserFieldMask_OZONETEL_ID,
					sherlockUserPb.SherlockUserFieldMask_USER_NAME, sherlockUserPb.SherlockUserFieldMask_STATUS},
			},
			want:    testUserInfoFixture2,
			wantErr: false,
		},
		{
			name: "success, update only roles",
			args: args{
				ctx:          context.Background(),
				sherlockUser: testUserInfoFixture3,
				updateMask:   []sherlockUserPb.SherlockUserFieldMask{sherlockUserPb.SherlockUserFieldMask_ROLES},
			},
			want:    testUserInfoFixture3,
			wantErr: false,
		},
		{
			name: "success, update all fields",
			args: args{
				ctx:          context.Background(),
				sherlockUser: testUserInfoFixture1,
				updateMask: []sherlockUserPb.SherlockUserFieldMask{sherlockUserPb.SherlockUserFieldMask_USER_TYPE,
					sherlockUserPb.SherlockUserFieldMask_PHONE_NUMBER, sherlockUserPb.SherlockUserFieldMask_ROLES,
					sherlockUserPb.SherlockUserFieldMask_USER_NAME, sherlockUserPb.SherlockUserFieldMask_STATUS,
					sherlockUserPb.SherlockUserFieldMask_OZONETEL_ID},
			},
			want:    testUserInfoFixture1,
			wantErr: false,
		},
		{
			name: "success, mark user as inactive, ozonetel id is cleared",
			args: args{
				ctx:          context.Background(),
				sherlockUser: testUserInfoFixture4,
				updateMask:   []sherlockUserPb.SherlockUserFieldMask{sherlockUserPb.SherlockUserFieldMask_STATUS},
			},
			want:    testUserInfoFixture5,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, SUTS.db, SUTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			_, err := SUTS.sherlockUser.UpdateSherlockUser(tt.args.ctx, tt.args.sherlockUser, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateSherlockUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				// make a get call to see if update was done
				expected, getErr := SUTS.sherlockUser.GetSherlockUserBySearchId(context.Background(), &sherlockUserPb.SearchId{
					SearchIdType: sherlockUserPb.SearchIdType_SEARCH_ID_TYPE_AGENT_EMAIL_ID,
					SearchKey:    &sherlockUserPb.SearchId_EmailId{EmailId: tt.want.GetEmailId()},
				})
				assert.Nil(t, getErr)
				// hack to make deep equal pass
				// want = expected updated date can change as and when run
				tt.want.CreatedAt = expected.GetCreatedAt()
				tt.want.UpdatedAt = expected.GetUpdatedAt()
				if !reflect.DeepEqual(expected, tt.want) {
					t.Errorf("UpdateSherlockUser() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
		})
	}
}
