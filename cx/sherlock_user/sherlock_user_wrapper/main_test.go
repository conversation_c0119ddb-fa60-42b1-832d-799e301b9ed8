package sherlock_user_wrapper_test

import (
	"flag"
	"os"
	"testing"

	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/cx/sherlock_user/dao"
	"github.com/epifi/gamma/cx/sherlock_user/sherlock_user_wrapper"
	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, db, teardown := test.InitTestServer(true)
	sherlockUserInfoDao := dao.NewSherlockUserInfoDao(db)
	sherlockUserRoleDao := dao.NewSherlockUserRoleDao(db)
	txnExecutor := storageV2.NewGormTxnExecutor(db)
	sherlockUser := sherlock_user_wrapper.NewSherlockUserDao(sherlockUserInfoDao, sherlockUserRoleDao, txnExecutor)
	SUTS = SherlockUserTestSuite{db: db, conf: conf, sherlockUser: sherlockUser}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
