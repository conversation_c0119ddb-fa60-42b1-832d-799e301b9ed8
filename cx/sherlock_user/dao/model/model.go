package model

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"strings"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/casbin"
	sherlockUserPb "github.com/epifi/gamma/api/cx/sherlock_user"
	"github.com/epifi/be-common/pkg/nulltypes"
)

var SherlockUserInfoColumnNames = map[sherlockUserPb.SherlockUserFieldMask]string{
	sherlockUserPb.SherlockUserFieldMask_USER_TYPE:    "user_type",
	sherlockUserPb.SherlockUserFieldMask_PHONE_NUMBER: "phone",
	sherlockUserPb.SherlockUserFieldMask_USER_NAME:    "user_name",
	sherlockUserPb.SherlockUserFieldMask_STATUS:       "status",
	sherlockUserPb.SherlockUserFieldMask_OZONETEL_ID:  "ozonetel_id",
	sherlockUserPb.SherlockUserFieldMask_ROLES:        "roles",
}

// SherlockUser type representing users information in database
type SherlockUser struct {
	// Sherlock user Id as generated
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	// Type of user : employee, consultant, support
	UserType sherlockUserPb.SherlockUserType
	// primary Phone number of user
	Phone *commontypes.PhoneNumber
	// primary Email of user (epifi created id)
	Email string
	// name of user
	UserName *commontypes.Name
	// Status of user that is active/inactive
	Status sherlockUserPb.SherlockUserStatus
	// Ozonetel Id of user
	OzonetelId nulltypes.NullString
	// time of creation of the user
	CreatedAt time.Time
	// last updated time
	UpdatedAt time.Time
}

// SherlockUserRole type represent user roles information in database
type SherlockUserRole struct {
	// Sherlock Id as generated
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	// user Id from the sherlock_users table
	UserId string
	// Role of user, for example : comms_developer
	UserRole casbin.AccessLevel
	// time of creation of the role
	CreatedAt time.Time
	// last updated time
	UpdatedAt time.Time
}

func NewSherlockUser(user *sherlockUserPb.SherlockUserInfo) *SherlockUser {
	return &SherlockUser{
		UserType:   user.GetUserType(),
		Phone:      user.GetPhoneNumber(),
		Email:      strings.ToLower(user.GetEmailId()),
		UserName:   user.GetName(),
		Status:     user.GetUserStatus(),
		OzonetelId: nulltypes.NewNullString(user.GetOzonetelId()),
	}
}

func NewSherlockRole(userId string, role casbin.AccessLevel) *SherlockUserRole {
	return &SherlockUserRole{
		UserId:   userId,
		UserRole: role,
	}
}

func ToSherlockUserInfoProtoWithoutRole(user *SherlockUser) *sherlockUserPb.SherlockUserInfo {
	sherlockUser := &sherlockUserPb.SherlockUserInfo{
		Id:          user.Id,
		EmailId:     user.Email,
		PhoneNumber: user.Phone,
		Name:        user.UserName,
		UserType:    user.UserType,
		UserStatus:  user.Status,
		OzonetelId:  user.OzonetelId.String,
	}
	createdAtTs := timestampPb.New(user.CreatedAt)
	sherlockUser.CreatedAt = createdAtTs
	updatedAtTs := timestampPb.New(user.UpdatedAt)
	sherlockUser.UpdatedAt = updatedAtTs
	return sherlockUser
}

func ToSherlockUserRoleProto(userRole *SherlockUserRole) *sherlockUserPb.SherlockUserRole {
	sherlockUserRole := &sherlockUserPb.SherlockUserRole{
		UserId:           userRole.UserId,
		Id:               userRole.Id,
		SherlockUserRole: userRole.UserRole,
		CreatedAt:        timestampPb.New(userRole.CreatedAt),
		UpdatedAt:        timestampPb.New(userRole.UpdatedAt),
	}
	return sherlockUserRole
}

func AddRolesToSherlockUserInfoProto(userInfoProto *sherlockUserPb.SherlockUserInfo, roleProto []*sherlockUserPb.SherlockUserRole) *sherlockUserPb.SherlockUserInfo {
	sherlockUser := userInfoProto
	var userRoles []casbin.AccessLevel
	for _, role := range roleProto {
		userRoles = append(userRoles, role.GetSherlockUserRole())
	}
	sherlockUser.SherlockUserRoles = userRoles
	return sherlockUser
}

// GetSherlockUserColumnsForUpdate - returns list of columns to be updated
func GetSherlockUserColumnsForUpdate(updateMask []sherlockUserPb.SherlockUserFieldMask) ([]string, bool, error) {
	var selectColumns []string
	// rolesUpdateFlag is set to be true if 'roles' is also present in the update mask
	// if the flag is set to true, user roles are updated
	rolesUpdateFlag := false
	for _, field := range updateMask {
		if field == sherlockUserPb.SherlockUserFieldMask_ROLES {
			rolesUpdateFlag = true
			continue
		}
		column, ok := SherlockUserInfoColumnNames[field]
		if !ok {
			return nil, rolesUpdateFlag, fmt.Errorf("unable to map field %s", field)
		}
		selectColumns = append(selectColumns, column)
	}
	return selectColumns, rolesUpdateFlag, nil
}
