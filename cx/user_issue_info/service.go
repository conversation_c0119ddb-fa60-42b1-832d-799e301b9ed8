package user_issue_info

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	rpcPb "github.com/epifi/be-common/api/rpc"
	userIssueInfoPb "github.com/epifi/gamma/api/cx/user_issue_info"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

type Service struct {
	cacheStorage cache.CacheStorage
}

func NewService(cacheStorage cache.CacheStorage) *Service {
	return &Service{
		cacheStorage: cacheStorage,
	}
}

var _ userIssueInfoPb.UserIssueInfoServiceServer = &Service{}

func (s *Service) GetUserIssueInfo(ctx context.Context, req *userIssueInfoPb.GetUserIssueRequest) (*userIssueInfoPb.GetUserIssueResponse, error) {
	actorId := req.GetHeader().GetActor().GetId()
	infoStr, err := s.cacheStorage.Get(ctx, getIssueKeyForActor(actorId))
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &userIssueInfoPb.GetUserIssueResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching user issue info from db", zap.Error(err), zap.Any(logger.ACTOR_ID, actorId))
		return &userIssueInfoPb.GetUserIssueResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	issueInfo := &userIssueInfoPb.RewardsIssueInfo{}
	err = protojson.Unmarshal([]byte(infoStr), issueInfo)
	if err != nil {
		logger.Error(ctx, "error while unmarshalling issue info", zap.Error(err), zap.Any(logger.ACTOR_ID, actorId))
		return &userIssueInfoPb.GetUserIssueResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while unmarshalling issue info"),
		}, nil
	}

	return &userIssueInfoPb.GetUserIssueResponse{
		Status:        rpcPb.StatusOk(),
		UserIssueData: convertToRewardIssueToDataValueList(issueInfo),
	}, nil

}

func convertToRewardIssueToDataValueList(issueInfo *userIssueInfoPb.RewardsIssueInfo) []*userIssueInfoPb.DataValue {
	var dataValueList []*userIssueInfoPb.DataValue
	dataValueList = append(dataValueList, &userIssueInfoPb.DataValue{
		Label: "User State",
		Value: issueInfo.GetRedemptionState(),
	})
	dataValueList = append(dataValueList, &userIssueInfoPb.DataValue{
		Label: "User Issue",
		Value: issueInfo.GetUserIssue(),
	})
	dataValueList = append(dataValueList, &userIssueInfoPb.DataValue{
		Label: "CX Script",
		Value: issueInfo.GetCxScript(),
	})
	return dataValueList
}

func (s *Service) BulkSetUserIssueInfo(ctx context.Context, req *userIssueInfoPb.BulkSetUserIssueRequest) (*userIssueInfoPb.BulkSetUserIssueResponse, error) {
	goroutine.Run(ctx, 30*time.Minute, func(ctx context.Context) {
		s.setDataInCache(ctx, req)
	})
	return &userIssueInfoPb.BulkSetUserIssueResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func getIssueKeyForActor(actorId string) string {
	return "cx_user_issue_info_" + actorId
}

func (s *Service) setDataInCache(ctx context.Context, req *userIssueInfoPb.BulkSetUserIssueRequest) {
	for _, userInfo := range req.GetUserIssueInfoList() {
		issueBytes, err := protojson.Marshal(userInfo.GetRewardsIssueInfo())
		if err != nil {
			cxLogger.Error(ctx, "error while marshaling ticket for caching", zap.Error(err))
			return
		}
		// 2 weeks
		duration := 14 * 24 * time.Hour
		err = s.cacheStorage.Set(ctx, getIssueKeyForActor(userInfo.GetActorId()), string(issueBytes), duration)
		if err != nil {
			cxLogger.Error(ctx, "error while setting user issue info in cache", zap.Error(err))
			return
		}
		cxLogger.Info(ctx, "set the user issue info in cache successfully")
	}
}
