package liveness_video

import (
	"context"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	s3Sdk "github.com/aws/aws-sdk-go-v2/service/s3"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	cxLvPb "github.com/epifi/gamma/api/cx/liveness_video"
	"github.com/epifi/gamma/cx/audit_log"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/interceptor"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/sherlock_auth"

	"go.uber.org/zap"
)

type Service struct {
	cxLvPb.UnimplementedLivenessVideoServer
	livenessClient         livenessPb.LivenessClient
	authFunc               interceptor.AuthFunction
	accessControlFunc      interceptor.AccessControlFunction
	casbinAuthorizationSvc *sherlock_auth.CasbinAuthorizationService
	cognitoSvc             *sherlock_auth.CognitoAuthService
	auditLogSvc            *audit_log.Service
	cxDesc                 interceptor.GetCxDescriptor
	livenessS3Client       s3.S3Client
	config                 *config.Config
}

func NewLivenessVideoService(livenessClient livenessPb.LivenessClient, authFunc interceptor.AuthFunction,
	accessControlFunc interceptor.AccessControlFunction, cxDesc interceptor.GetCxDescriptor,
	casbinAuthorizationSvc *sherlock_auth.CasbinAuthorizationService, cognitoSvc *sherlock_auth.CognitoAuthService,
	auditLogSvc *audit_log.Service, livenessS3Client s3.S3Client,
	config *config.Config) *Service {
	return &Service{
		livenessClient:         livenessClient,
		authFunc:               authFunc,
		accessControlFunc:      accessControlFunc,
		casbinAuthorizationSvc: casbinAuthorizationSvc,
		cognitoSvc:             cognitoSvc,
		auditLogSvc:            auditLogSvc,
		cxDesc:                 cxDesc,
		livenessS3Client:       livenessS3Client,
		config:                 config,
	}
}

var _ cxLvPb.LivenessVideoServer = &Service{}

func (s *Service) GetFMImage(ctx context.Context, request *cxLvPb.GetFMImageRequest) (*cxLvPb.GetFMImageResponse, error) {
	if request.GetS3LocationPath() == "" {
		cxLogger.Info(ctx, "s3 location path not passed in request ot fetch liveness image")
		return &cxLvPb.GetFMImageResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("s3 location path is mandatory"),
		}, nil
	}
	resp, err := s.livenessClient.GetS3Image(ctx, &livenessPb.GetS3ImageRequest{
		LocationKey: request.GetS3LocationPath(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching image from liveness service", zap.Error(te),
			zap.String("s3_location_path", request.GetS3LocationPath()))
		if resp.GetStatus().IsRecordNotFound() {
			return &cxLvPb.GetFMImageResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("image not found"),
			}, nil
		}
		return &cxLvPb.GetFMImageResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching image from liveness service"),
		}, nil
	}
	return &cxLvPb.GetFMImageResponse{
		Status: rpcPb.StatusOk(),
		Image:  resp.GetImage(),
	}, nil
}

func (s *Service) GetLivenessVideoURL(ctx context.Context, req *cxLvPb.GetLivenessVideoURLRequest) (*cxLvPb.GetLivenessVideoURLResponse, error) {
	if req.GetActorId() == "" || req.GetRequestId() == "" {
		cxLogger.Info(ctx, "actor id or request id both are mandatory")
		return &cxLvPb.GetLivenessVideoURLResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor id or request id both are mandatory"),
		}, nil
	}
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())
	// call liveness service to get live video location
	resp, err := s.livenessClient.GetLivenessStatus(ctx, &livenessPb.GetLivenessStatusRequest{
		ActorId:   req.GetActorId(),
		AttemptId: req.GetRequestId(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching liveness status from liveness service", zap.Error(te))
		return &cxLvPb.GetLivenessVideoURLResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching liveness video url"),
		}, nil
	}
	// pre-sign the s3 url
	signedUrl, err := s.livenessS3Client.GetPreSignedURLFromS3Input(ctx, &s3Sdk.GetObjectInput{
		Bucket: aws.String(s.config.LivenessVideoConfig.S3BucketName),
		Key:    aws.String(resp.GetVideoLocation()),
	}, 10*time.Minute)
	if err != nil {
		cxLogger.Error(ctx, "error while creating pre-signed url", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetRequestId()))
		return &cxLvPb.GetLivenessVideoURLResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while creating pre-signed url for resource"),
		}, nil
	}
	return &cxLvPb.GetLivenessVideoURLResponse{
		Status:   rpcPb.StatusOk(),
		VideoUrl: signedUrl,
	}, nil
}
