package utils

import (
	"fmt"
	"sort"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/datetime"
	moneyPb "github.com/epifi/be-common/pkg/money"
	cxPalPb "github.com/epifi/gamma/api/cx/data_collector/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

const (
	HeaderKeyID                      = "id"
	HeaderKeyVendor                  = "vendor"
	HeaderKeyLoanProgram             = "loan_program"
	HeaderKeyStatus                  = "status"
	HeaderKeySubStatus               = "sub_status"
	HeaderKeyCreatedAt               = "created_at"
	HeaderKeyUpdatedAt               = "updated_at"
	HeaderKeyCompletedAt             = "completed_at"
	HeaderKeyOfferID                 = "offer_id"
	HeaderKeySelectedLoanAmount      = "selected_loan_amount"
	HeaderKeyProcessingFees          = "processing_fees"
	HeaderKeyGst                     = "gst"
	HeaderKeyTenure                  = "tenure"
	HeaderKeyVendorLoanID            = "vendor_loan_id"
	HeaderKeyVendorUserID            = "vendor_user_id"
	HeaderKeyLoanOfferType           = "loan_offer_type"
	HeaderKeyMinLoanAmount           = "min_loan_amount"
	HeaderKeyMaxLoanAmount           = "max_loan_amount"
	HeaderKeyInterest                = "interest"
	HeaderKeyMinTenure               = "min_tenure"
	HeaderKeyMaxTenure               = "max_tenure"
	HeaderKeyStartDate               = "start_date"
	HeaderKeyEndDate                 = "end_date"
	HeaderKeyAccountNumber           = "account_number"
	HeaderKeyAccountStatus           = "account_status"
	HeaderKeyDisbursalDate           = "disbursal_date"
	HeaderKeyDisbursalAmount         = "disbursal_amount"
	HeaderKeyProcessingFeesGst       = "processing_fees_gst"
	HeaderKeyOutstandingAmount       = "outstanding_amount"
	HeaderKeyStepName                = "step_name"
	HeaderKeyIterationNumber         = "iteration_number"
	HeaderKeyTxnDate                 = "txn_date"
	HeaderKeyAmount                  = "amount"
	HeaderKeyTxnType                 = "txn_type"
	HeaderKeyEmiDate                 = "emi_date"
	HeaderKeyEmiAmount               = "emi_amount"
	HeaderKeyMinDue                  = "min_due"
	HeaderKeyPenaltyFees             = "penalty_fees"
	HeaderKeyBounceCharges           = "bounce_charges"
	HeaderKeyOtherCharges            = "other_charges"
	HeaderKeyForeclosureAmountAmount = "fore_closure_amount"
	HeaderKeyNextEmiDate             = "next_emi_date"
)

func GetEligibilityDetailsComponent(loanUserDetailsResp *palCxPb.GetLoanUserDetailsResponse) *cxPalPb.WebComponent {
	reason := loanUserDetailsResp.GetCurrentMonthUserLoanEligibilityDetails().GetReasonForIneligibility()
	if reason == "" {
		reason = "NA"
	}

	var keyValues = []*webui.LabelValueV2{
		{
			Label:    "Is User Being Suggested Loan Feature From Fi?",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: fmt.Sprintf("%v", loanUserDetailsResp.GetCurrentMonthUserLoanEligibilityDetails().GetIsUserSuggestedForLoan()),
			},
		},
		{
			Label:    "Reason for Ineligibility",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: reason,
			},
		},
	}
	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_KeyValueComponent{
			KeyValueComponent: &webui.Section{
				Title:       "User Loan Eligibility",
				LabelValues: keyValues,
			},
		},
	}
}

func GetEligibilityLRComponent(resp *palCxPb.GetLoanUserDetailsResponse) (*cxPalPb.WebComponent, error) {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				{Label: "Id", HeaderKey: HeaderKeyID, IsVisible: true},
				{Label: "Vendor", HeaderKey: HeaderKeyVendor, IsVisible: true},
				{Label: "Loan Program", HeaderKey: HeaderKeyLoanProgram, IsVisible: true},
				{Label: "Status", HeaderKey: HeaderKeyStatus, IsVisible: true},
				{Label: "SubStatus", HeaderKey: HeaderKeySubStatus, IsVisible: true},
				{Label: "CreatedAt", HeaderKey: HeaderKeyCreatedAt, IsVisible: true},
				{Label: "UpdatedAt", HeaderKey: HeaderKeyUpdatedAt, IsVisible: true},
				{Label: "CompletedAt", HeaderKey: HeaderKeyCompletedAt, IsVisible: true},
			},
			Actions: []*webui.CTA{
				{
					Label: "View details",
					Deeplink: &webui.Deeplink{
						ScreenName: webui.ScreenName_SCREEN_NAME_LOANS_VIEW_DETAILS,
					},
				},
			},
			TableName: "Loan Eligibility Requests",
		}
	)

	sort.Slice(resp.GetEligibilityLoanRequests(), func(i, j int) bool {
		return resp.GetEligibilityLoanRequests()[i].GetCreatedAt().AsTime().After(resp.GetEligibilityLoanRequests()[j].GetCreatedAt().AsTime())
	})
	rows, err := getEligiblityLRTableRows(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate table rows: %w", err)
	}
	table.TableRows = rows

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}, nil
}

func getEligiblityLRTableRows(resp *palCxPb.GetLoanUserDetailsResponse) ([]*webui.TableRow, error) {
	var rows []*webui.TableRow
	for idx, lr := range resp.GetEligibilityLoanRequests() {
		if idx >= 15 {
			cxLogger.InfoNoCtx("reached max limit of 15 rows for eligibility loan requests")
			break
		}
		lrStatus := helper.ConvertLrStatusToReadableFormat(lr.GetStatus())
		lrSubStatus := helper.ConvertLrSubStatusToReadableFormat(lr.GetSubStatus())
		createdAt := helper.ConvertTimestampToReadableFormat(lr.GetCreatedAt())
		updatedAt := helper.ConvertTimestampToReadableFormat(lr.GetUpdatedAt())
		completedAt := helper.ConvertTimestampToReadableFormat(lr.GetCompletedAt())
		vendor := lr.GetVendor().String()
		loanProgram := lr.GetLoanProgram().String()

		metadata := &cxPalPb.LoansMetadata{
			Vendor:       lr.GetVendor(),
			LoanProgram:  lr.GetLoanProgram(),
			MetadataType: cxPalPb.LoansMetadataType_LOANS_METADATA_TYPE_ELIGIBILITY_LOAN_REQUEST,
			Details: &cxPalPb.LoansMetadata_LoanRequestMetadata{
				LoanRequestMetadata: &cxPalPb.LoanRequestMetadata{
					LoanRequestId: lr.GetId(),
				},
			},
		}
		metaStr, err := protojson.Marshal(metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal loan reqeuest metadata for id: %s, err: %w", lr.GetId(), err)
		}
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyID:          helper.GetTableCellWithStringValue(lr.GetId()),
				HeaderKeyStatus:      helper.GetTableCellWithStringValue(lrStatus),
				HeaderKeySubStatus:   helper.GetTableCellWithStringValue(lrSubStatus),
				HeaderKeyCreatedAt:   helper.GetTableCellWithStringValue(createdAt),
				HeaderKeyUpdatedAt:   helper.GetTableCellWithStringValue(updatedAt),
				HeaderKeyCompletedAt: helper.GetTableCellWithStringValue(completedAt),
				HeaderKeyVendor:      helper.GetTableCellWithStringValue(vendor),
				HeaderKeyLoanProgram: helper.GetTableCellWithStringValue(loanProgram),
			},
			Meta: string(metaStr),
		})
	}

	return rows, nil
}

func GetApplicationLRComponent(resp *palCxPb.GetLoanUserDetailsResponse) (*cxPalPb.WebComponent, error) {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				{Label: "Id", HeaderKey: HeaderKeyID, IsVisible: true},
				{Label: "Status", HeaderKey: HeaderKeyStatus, IsVisible: true},
				{Label: "SubStatus", HeaderKey: HeaderKeySubStatus, IsVisible: true},
				{Label: "Offer Id", HeaderKey: HeaderKeyOfferID, IsVisible: true},
				{Label: "Loan amount selected", HeaderKey: HeaderKeySelectedLoanAmount, IsVisible: true},
				{Label: "Processing fees", HeaderKey: HeaderKeyProcessingFees, IsVisible: true},
				{Label: "Gst", HeaderKey: HeaderKeyGst, IsVisible: true},
				{Label: "Tenure", HeaderKey: HeaderKeyTenure, IsVisible: true},
				{Label: "Vendor", HeaderKey: HeaderKeyVendor, IsVisible: true},
				{Label: "Loan Program", HeaderKey: HeaderKeyLoanProgram, IsVisible: true},
				{Label: "Vendor Loan id", HeaderKey: HeaderKeyVendorLoanID, IsVisible: true},
				{Label: "Vendor user id", HeaderKey: HeaderKeyVendorUserID, IsVisible: true},
				{Label: "CreatedAt", HeaderKey: HeaderKeyCreatedAt, IsVisible: true},
				{Label: "UpdatedAt", HeaderKey: HeaderKeyUpdatedAt, IsVisible: true},
				{Label: "CompletedAt", HeaderKey: HeaderKeyCompletedAt, IsVisible: true},
			},
			Actions: []*webui.CTA{
				{
					Label: "View details",
					Deeplink: &webui.Deeplink{
						ScreenName: webui.ScreenName_SCREEN_NAME_LOANS_VIEW_DETAILS,
					},
				},
			},
			TableName: "Loan Applications Requests",
		}
	)

	sort.Slice(resp.GetApplicationLoanRequests(), func(i, j int) bool {
		return resp.GetApplicationLoanRequests()[i].GetCreatedAt().AsTime().After(resp.GetApplicationLoanRequests()[j].GetCreatedAt().AsTime())
	})
	rows, err := getApplicationLRTableRows(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate table rows for loan applications table: %w", err)
	}
	table.TableRows = rows

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}, nil
}

//nolint:funlen
func getApplicationLRTableRows(resp *palCxPb.GetLoanUserDetailsResponse) ([]*webui.TableRow, error) {
	var (
		rows        []*webui.TableRow
		applicantId string
	)
	if len(resp.GetApplicants()) > 0 {
		applicantId = resp.GetApplicants()[0].GetVendorApplicantId()
	}
	for idx, lr := range resp.GetApplicationLoanRequests() {
		if idx >= 15 {
			cxLogger.InfoNoCtx("reached max limit of 15 rows for application loan requests")
			break
		}
		loanAmountSelected := helper.ConvertMoneyToReadableFormat(lr.GetDetails().GetLoanInfo().GetAmount())
		tenure := fmt.Sprintf("%d months", lr.GetDetails().GetLoanInfo().GetTenureInMonths())
		gst := helper.ConvertMoneyToReadableFormat(lr.GetDetails().GetLoanInfo().GetDeductions().GetGst())
		processingFees := helper.ConvertMoneyToReadableFormat(lr.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee())
		vendorLoanId := lr.GetVendorRequestId()
		lrStatus := helper.ConvertLrStatusToReadableFormat(lr.GetStatus())
		lrSubStatus := helper.ConvertLrSubStatusToReadableFormat(lr.GetSubStatus())
		createdAt := helper.ConvertTimestampToReadableFormat(lr.GetCreatedAt())
		updatedAt := helper.ConvertTimestampToReadableFormat(lr.GetUpdatedAt())
		completedAt := helper.ConvertTimestampToReadableFormat(lr.GetCompletedAt())
		offerId := lr.GetOfferId()
		vendor := lr.GetVendor().String()
		loanProgram := lr.GetLoanProgram().String()

		metadata := &cxPalPb.LoansMetadata{
			Vendor:       lr.GetVendor(),
			LoanProgram:  lr.GetLoanProgram(),
			MetadataType: cxPalPb.LoansMetadataType_LOANS_METADATA_TYPE_APPLICATION_LOAN_REQUEST,
			Details: &cxPalPb.LoansMetadata_LoanRequestMetadata{
				LoanRequestMetadata: &cxPalPb.LoanRequestMetadata{
					LoanRequestId: lr.GetId(),
				},
			},
		}
		metaStr, err := protojson.Marshal(metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal loan reqeuest metadata for id: %s, err: %w", lr.GetId(), err)
		}
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyID:                 helper.GetTableCellWithStringValue(lr.GetId()),
				HeaderKeyStatus:             helper.GetTableCellWithStringValue(lrStatus),
				HeaderKeySubStatus:          helper.GetTableCellWithStringValue(lrSubStatus),
				HeaderKeySelectedLoanAmount: helper.GetTableCellWithStringValue(loanAmountSelected),
				HeaderKeyProcessingFees:     helper.GetTableCellWithStringValue(processingFees),
				HeaderKeyGst:                helper.GetTableCellWithStringValue(gst),
				HeaderKeyTenure:             helper.GetTableCellWithStringValue(tenure),
				HeaderKeyVendorLoanID:       helper.GetTableCellWithStringValue(vendorLoanId),
				HeaderKeyVendorUserID:       helper.GetTableCellWithStringValue(applicantId),
				HeaderKeyCreatedAt:          helper.GetTableCellWithStringValue(createdAt),
				HeaderKeyUpdatedAt:          helper.GetTableCellWithStringValue(updatedAt),
				HeaderKeyCompletedAt:        helper.GetTableCellWithStringValue(completedAt),
				HeaderKeyOfferID:            helper.GetTableCellWithStringValue(offerId),
				HeaderKeyVendor:             helper.GetTableCellWithStringValue(vendor),
				HeaderKeyLoanProgram:        helper.GetTableCellWithStringValue(loanProgram),
			},
			Meta: string(metaStr),
		})
	}

	return rows, nil
}

func GetOfferDetailsComponent(resp *palCxPb.GetLoanUserDetailsResponse) *cxPalPb.WebComponent {
	var (
		table = &webui.Table{
			TableName: "Loan Offer Details",
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader(HeaderKeyVendor, "Vendor"),
				helper.GetTableHeader(HeaderKeyLoanProgram, "Loan Program"),
				helper.GetTableHeader(HeaderKeyLoanOfferType, "Loan Offer Type"),
				helper.GetTableHeader(HeaderKeyMinLoanAmount, "Min Loan Amount"),
				helper.GetTableHeader(HeaderKeyMaxLoanAmount, "Max Loan Amount"),
				helper.GetTableHeader(HeaderKeyInterest, "Interest"),
				helper.GetTableHeader(HeaderKeyMinTenure, "Min Tenure"),
				helper.GetTableHeader(HeaderKeyMaxTenure, "Max Tenure"),
				helper.GetTableHeader(HeaderKeyStartDate, "Offer Start date"),
				helper.GetTableHeader(HeaderKeyEndDate, "Expiry date"),
			},
		}
	)
	for _, offer := range resp.GetLoanOfferDetails() {
		table.TableRows = append(table.GetTableRows(), &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyMinLoanAmount: helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(offer.GetMinLoanAmount())),
				HeaderKeyMaxLoanAmount: helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(offer.GetMaxLoanAmount())),
				HeaderKeyInterest:      helper.GetTableCellWithStringValue(fmt.Sprint(offer.GetInterest())),
				HeaderKeyMinTenure:     helper.GetTableCellWithStringValue(fmt.Sprintf("%d months", offer.GetMinTenure())),
				HeaderKeyMaxTenure:     helper.GetTableCellWithStringValue(fmt.Sprintf("%d months", offer.GetMaxTenure())),
				HeaderKeyStartDate:     helper.GetTableCellWithStringValue(helper.ConvertDateToReadableFormat(offer.GetOfferStartDate())),
				HeaderKeyEndDate:       helper.GetTableCellWithStringValue(helper.ConvertDateToReadableFormat(offer.GetOfferEndDate())),
				HeaderKeyVendor:        helper.GetTableCellWithStringValue(offer.GetVendor()),
				HeaderKeyLoanProgram:   helper.GetTableCellWithStringValue(offer.GetLoanProgram()),
				HeaderKeyLoanOfferType: helper.GetTableCellWithStringValue(offer.GetLoanOfferType().String()),
			},
		})
	}
	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}
}

func GetLoanAccountsComponent(resp *palCxPb.GetLoanDetailsResponse) (*cxPalPb.WebComponent, error) {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader(HeaderKeyAccountNumber, "Account number"),
				helper.GetTableHeader(HeaderKeyAccountStatus, "Account status"),
				helper.GetTableHeader(HeaderKeyDisbursalDate, "Disbursal date"),
				helper.GetTableHeader(HeaderKeyDisbursalAmount, "Loan amount"),
				helper.GetTableHeader(HeaderKeyProcessingFeesGst, "Processing fees + Gst"),
				helper.GetTableHeader(HeaderKeyInterest, "Interest(%)"),
				helper.GetTableHeader(HeaderKeyTenure, "Tenure"),
				helper.GetTableHeader(HeaderKeyOutstandingAmount, "Outstanding amount"),
				helper.GetTableHeader(HeaderKeyForeclosureAmountAmount, "Foreclosure amount"),
			},
			Actions: []*webui.CTA{
				{
					Label: "View details",
					Deeplink: &webui.Deeplink{
						ScreenName: webui.ScreenName_SCREEN_NAME_LOANS_VIEW_DETAILS,
					},
				},
			},
			TableName: "Loan accounts",
		}
	)

	sort.Slice(resp.GetLoanDetails(), func(i, j int) bool {
		return resp.GetLoanDetails()[i].GetLoanOpenDate().AsTime().After(resp.GetLoanDetails()[j].GetLoanOpenDate().AsTime())
	})
	rows, err := getLoanAccountTableRows(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate loan account table rows: %w", err)
	}
	table.TableRows = rows

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}, nil
}

func getLoanAccountTableRows(resp *palCxPb.GetLoanDetailsResponse) ([]*webui.TableRow, error) {
	var rows []*webui.TableRow
	for _, loanAccountDetails := range resp.GetLoanDetails() {
		loanAccount := loanAccountDetails.GetLoanAccount()
		accountNumber := loanAccount.GetAccountNumber()
		accountStatus := helper.ConvertLoanAccountStatusToReadableFormat(loanAccount.GetStatus())
		disbursalDate := helper.ConvertTimestampToReadableFormat(loanAccount.GetCreatedAt())
		disbursalAmount := helper.ConvertMoneyToReadableFormat(loanAccountDetails.GetLoanAmount())

		processingFeesGst, err := moneyPb.Sum(loanAccountDetails.GetProcessingFee(), loanAccountDetails.GetGst())
		if err != nil {
			return nil, fmt.Errorf("failed to add processing fess and gst: %w", err)
		}
		processingFeesGstStr := helper.ConvertMoneyToReadableFormat(processingFeesGst)
		interestRate := loanAccount.GetDetails().GetInterestRate()
		// adding this in case interest rate is in decimal format
		if interestRate < 1 {
			interestRate *= 100
		}
		interest := fmt.Sprintf("%.2f%%", interestRate)
		tenure := fmt.Sprintf("%d months", loanAccount.GetDetails().GetTenureInMonths())
		outstandingAmount := helper.ConvertMoneyToReadableFormat(loanAccountDetails.GetOutstandingAmount())
		foreclosureAmount := helper.ConvertMoneyToReadableFormat(loanAccountDetails.GetForeClosureDetails().GetTotalOutstandingAmount())

		metadata := &cxPalPb.LoansMetadata{
			Vendor:       loanAccount.GetVendor(),
			LoanProgram:  loanAccount.GetLoanProgram(),
			MetadataType: cxPalPb.LoansMetadataType_LOANS_METADATA_TYPE_LOAN_ACCOUNT,
			Details: &cxPalPb.LoansMetadata_LoanAccountMetadata{
				LoanAccountMetadata: &cxPalPb.LoanAccountMetadata{
					LoanAccountId: loanAccount.GetId(),
				},
			},
		}
		metaStr, err := protojson.Marshal(metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal loan account metadata for id: %s, err: %w", loanAccount.GetId(), err)
		}
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyAccountNumber:           helper.GetTableCellWithStringValue(accountNumber),
				HeaderKeyAccountStatus:           helper.GetTableCellWithStringValue(accountStatus),
				HeaderKeyDisbursalDate:           helper.GetTableCellWithStringValue(disbursalDate),
				HeaderKeyDisbursalAmount:         helper.GetTableCellWithStringValue(disbursalAmount),
				HeaderKeyProcessingFeesGst:       helper.GetTableCellWithStringValue(processingFeesGstStr),
				HeaderKeyInterest:                helper.GetTableCellWithStringValue(interest),
				HeaderKeyTenure:                  helper.GetTableCellWithStringValue(tenure),
				HeaderKeyOutstandingAmount:       helper.GetTableCellWithStringValue(outstandingAmount),
				HeaderKeyForeclosureAmountAmount: helper.GetTableCellWithStringValue(foreclosureAmount),
			},
			Meta: string(metaStr),
		})
	}

	return rows, nil
}

func GetLoanStepsComponent(resp *palCxPb.GetLoanRequestAdditionalDetailsResponse) *cxPalPb.WebComponent {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader(HeaderKeyStepName, "Loan step name"),
				helper.GetTableHeader(HeaderKeyStatus, "Status"),
				helper.GetTableHeader(HeaderKeySubStatus, "Sub Status"),
				helper.GetTableHeader(HeaderKeyCreatedAt, "Created at"),
				helper.GetTableHeader(HeaderKeyCompletedAt, "Completed at"),
				helper.GetTableHeader(HeaderKeyIterationNumber, "Iteration number"),
			},
			TableName: "Loan step executions",
		}
	)

	sort.Slice(resp.GetLoanSteps(), func(i, j int) bool {
		return resp.GetLoanSteps()[i].GetCreatedAt().AsTime().Before(resp.GetLoanSteps()[j].GetCreatedAt().AsTime())
	})
	table.TableRows = getLoanStepsTableRows(resp)

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}
}

func getLoanStepsTableRows(resp *palCxPb.GetLoanRequestAdditionalDetailsResponse) []*webui.TableRow {
	var (
		rows             []*webui.TableRow
		stepIterationMap = make(map[palPb.LoanStepExecutionStepName]int)
	)
	for _, loanStep := range resp.GetLoanSteps() {
		loanStepName := helper.ConvertLoanStepNameToReadableFormat(loanStep.GetStepName())
		status := helper.ConvertLseStatusToReadableFormat(loanStep.GetStatus())
		subStatus := helper.ConvertLseSubStatusToReadableFormat(loanStep.GetSubStatus())
		createdAt := helper.ConvertTimestampToReadableFormat(loanStep.GetCreatedAt())
		completedAt := helper.ConvertTimestampToReadableFormat(loanStep.GetCompletedAt())

		iterationNum, ok := stepIterationMap[loanStep.GetStepName()]
		if !ok {
			iterationNum = 1
			stepIterationMap[loanStep.GetStepName()] = 1
		} else {
			stepIterationMap[loanStep.GetStepName()] = iterationNum + 1
		}
		iterationNumStr := fmt.Sprintf("%d", iterationNum)

		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyStepName:        helper.GetTableCellWithStringValue(loanStepName),
				HeaderKeyStatus:          helper.GetTableCellWithStringValue(status),
				HeaderKeySubStatus:       helper.GetTableCellWithStringValue(subStatus),
				HeaderKeyCreatedAt:       helper.GetTableCellWithStringValue(createdAt),
				HeaderKeyCompletedAt:     helper.GetTableCellWithStringValue(completedAt),
				HeaderKeyIterationNumber: helper.GetTableCellWithStringValue(iterationNumStr),
			},
		})
	}

	return rows
}

func GetLoanAccountKeyValueComponent(resp *palCxPb.GetLoanAccountAdditionalDetailsResponse) *cxPalPb.WebComponent {
	isLoanClosureInitiated := isLoanAccountClosureWasInitiatedByUser(resp)
	isLAClosedOnVendor := false
	if resp.GetLoanAccount().GetVendor() == palPb.Vendor_FIFTYFIN {
		isLAClosedOnVendor = resp.GetFiftyfinLamfDetails().GetIsLoanAccountClosedOnVendor()
	} else {
		isLAClosedOnVendor = resp.GetLoanAccount().GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED
	}
	var keyValues = []*webui.LabelValueV2{
		{
			Label:    "Loan Account Number",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: resp.GetLoanAccount().GetAccountNumber(),
			},
		},
		{
			Label:    "Loan Account Status (In Fi)",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: helper.ConvertLoanAccountStatusToReadableFormat(resp.GetLoanAccount().GetStatus()),
			},
		},
		{
			Label:    "Is loan account closure initiated by user on Fi?",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: fmt.Sprintf("%v", isLoanClosureInitiated),
			},
		},
		{
			Label:    "Is loan account closed on vendor side?",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: fmt.Sprintf("%v", isLAClosedOnVendor),
			},
		},
		{
			Label:    "Vendor",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: fmt.Sprintf("%v", resp.GetLoanAccount().GetVendor().String()),
			},
		},
		{
			Label:    "Loan Program",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: fmt.Sprintf("%v", resp.GetLoanAccount().GetLoanProgram()),
			},
		},
		{
			Label:    "Disbursal Bank",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: fmt.Sprintf("%v", resp.GetBankAccountDetails().GetBankName()),
			},
		},
		{
			Label:    "Bank A/C No.",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value: &webui.LabelValueV2_StringValue{
				StringValue: fmt.Sprintf("XXX%v", resp.GetBankAccountDetails().GetAccountNumber()),
			},
		},
	}
	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_KeyValueComponent{
			KeyValueComponent: &webui.Section{
				Title:       "User Account details",
				LabelValues: keyValues,
			},
		},
	}
}

func isLoanAccountClosureWasInitiatedByUser(resp *palCxPb.GetLoanAccountAdditionalDetailsResponse) bool {
	var closureLprExists bool
	for _, lpr := range resp.GetLoanPaymentRequests() {
		if lpr.GetStatus() == palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS && lpr.GetType() == palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE {
			closureLprExists = true
			break
		}
	}
	if closureLprExists || len(resp.GetLoanAccountClosureRequests()) > 0 {
		return true
	}
	return false
}

// show only txns that are paid by users
func GetLprRequestsComponent(loanPaymentRequests []*palPb.LoanPaymentRequest) *cxPalPb.WebComponent {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader(HeaderKeyTxnDate, "Txn Date"),
				helper.GetTableHeader(HeaderKeyAmount, "Amount"),
				helper.GetTableHeader(HeaderKeyStatus, "Status"),
				helper.GetTableHeader(HeaderKeyTxnType, "Txn type"),
			},
			TableName: "Loan transactions (Fi)",
		}
	)

	var rows []*webui.TableRow
	for _, lpr := range loanPaymentRequests {
		if lpr.GetType() == palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_BATCH_COLLECTION {
			continue
		}
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyTxnDate: helper.GetTableCellWithStringValue(helper.ConvertTimestampToReadableFormat(lpr.GetCreatedAt())),
				HeaderKeyAmount:  helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(lpr.GetAmount())),
				HeaderKeyStatus:  helper.GetTableCellWithStringValue(helper.ConvertLoanPaymentRequestStatusToReadableFormat(lpr.GetStatus())),
				HeaderKeyTxnType: helper.GetTableCellWithStringValue(helper.ConvertLoanPaymentRequestTypeToReadableFormat(lpr.GetType())),
			},
		})
	}
	table.TableRows = rows

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}
}

func GetEmiDetailsComponent(loanPayouts []*palPb.LoanInstallmentPayout) *cxPalPb.WebComponent {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader(HeaderKeyEmiDate, "Emi date"),
				helper.GetTableHeader(HeaderKeyEmiAmount, "Emi amount"),
				helper.GetTableHeader(HeaderKeyMinDue, "min due"),
			},
			TableName: "Emi details",
		}
	)

	sort.Slice(loanPayouts, func(i, j int) bool {
		return datetime.IsDateAfter(loanPayouts[j].GetDueDate(), loanPayouts[i].GetDueDate())
	})
	var rows []*webui.TableRow
	for _, loanPayout := range loanPayouts {
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyEmiDate:   helper.GetTableCellWithStringValue(helper.ConvertDateToReadableFormat(loanPayout.GetDueDate())),
				HeaderKeyEmiAmount: helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(loanPayout.GetDueAmount())),
				HeaderKeyMinDue:    helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(loanPayout.GetInterest())),
			},
		})
	}
	table.TableRows = rows

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}
}

func GetPenaltyFeesComponent(loanPayouts []*palPb.LoanInstallmentPayout) *cxPalPb.WebComponent {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader(HeaderKeyPenaltyFees, "Penalty fees"),
				helper.GetTableHeader(HeaderKeyBounceCharges, "Bounce Charges"),
				helper.GetTableHeader(HeaderKeyOtherCharges, "Other Charges"),
			},
			TableName: "Penalty fees and Bounce Charges",
		}
	)

	var rows []*webui.TableRow
	for _, loanPayout := range loanPayouts {
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyPenaltyFees:   helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(loanPayout.GetDetails().GetChargesApplied().GetLatePaymentInterest())),
				HeaderKeyBounceCharges: helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(loanPayout.GetDetails().GetChargesApplied().GetBounceCharges())),
				HeaderKeyOtherCharges:  helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(loanPayout.GetDetails().GetChargesApplied().GetOtherCharges())),
			},
		})
	}
	table.TableRows = rows
	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}
}

func GetEmiDetailsComponentPL(upcomingEMIs []*palCxPb.LoanUpcomingEmi) *cxPalPb.WebComponent {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader(HeaderKeyAccountNumber, "Loan Acct ID"),
				helper.GetTableHeader(HeaderKeyEmiAmount, "EMI amount"),
				helper.GetTableHeader(HeaderKeyNextEmiDate, "Next EMI Date"),
			},
			TableName: "Upcoming EMI details",
		}
	)

	var rows []*webui.TableRow
	for _, upcomingEMI := range upcomingEMIs {
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				HeaderKeyAccountNumber: helper.GetTableCellWithStringValue(upcomingEMI.GetLoanAccountNumber()),
				HeaderKeyEmiAmount:     helper.GetTableCellWithStringValue(helper.ConvertMoneyToReadableFormat(upcomingEMI.GetAmount())),
				HeaderKeyNextEmiDate:   helper.GetTableCellWithStringValue(helper.ConvertDateToReadableFormat(upcomingEMI.GetNextEmiDate())),
			},
		})
	}
	table.TableRows = rows

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}
}
