package impl

import (
	"context"
	"fmt"
	"sort"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	cxPalPb "github.com/epifi/gamma/api/cx/data_collector/preapprovedloan"
	"github.com/epifi/gamma/api/investment/mutualfund"
	catalogManagerPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	"github.com/epifi/gamma/api/typesv2/webui"
	vendorsFiftyfin "github.com/epifi/gamma/api/vendors/fiftyfin"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator/utils"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/helper"
)

type WebViewGenerator struct {
	mfCatalogClient catalogManagerPb.CatalogManagerClient
}

var _ generator.LoansWebViewGenerator = &WebViewGenerator{}

func NewLamfWebViewGenerator(mfCatalogClient catalogManagerPb.CatalogManagerClient) *WebViewGenerator {
	return &WebViewGenerator{
		mfCatalogClient: mfCatalogClient,
	}
}

func (w *WebViewGenerator) GenerateLOSWebView(ctx context.Context, loanUserDetailsResp *palCxPb.GetLoanUserDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	eligibilityLrComp, err := utils.GetEligibilityLRComponent(loanUserDetailsResp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate lr eligibility web component: %w", err)
	}
	applicationLrComp, err := utils.GetApplicationLRComponent(loanUserDetailsResp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate lr application web component: %w", err)
	}
	mfHoldingsTable, err := w.getOfferHoldingsComponent(ctx, loanUserDetailsResp)
	if err != nil {
		return nil, fmt.Errorf("faied to generate offer holdings component: %w", err)
	}

	components := []*cxPalPb.WebComponent{
		utils.GetEligibilityDetailsComponent(loanUserDetailsResp),
		eligibilityLrComp,
		applicationLrComp,
		utils.GetOfferDetailsComponent(loanUserDetailsResp),
		mfHoldingsTable,
	}
	return components, nil
}

func (w *WebViewGenerator) GenerateLMSWebView(_ context.Context, resp *palCxPb.GetLoanDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	loanAccountComponent, err := utils.GetLoanAccountsComponent(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate loan acocunt component: %w", err)
	}
	return []*cxPalPb.WebComponent{
		loanAccountComponent,
	}, nil
}

func (w *WebViewGenerator) GenerateEligiblityLoanRequestPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanRequestAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	lseComponent := utils.GetLoanStepsComponent(resp)
	return []*cxPalPb.WebComponent{
		lseComponent,
	}, nil
}

func (w *WebViewGenerator) GenerateApplicationLoanRequestPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanRequestAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	lseComponent := utils.GetLoanStepsComponent(resp)
	pledgedMfComponent, err := w.getPledgedMfComponent(ctx, resp.GetLoanRequest().GetDetails().GetLoanInfo().GetPledgeDetails().GetMutualFunds())
	if err != nil {
		return nil, fmt.Errorf("failed to get pledged mf component: %w", err)
	}
	return []*cxPalPb.WebComponent{
		pledgedMfComponent,
		lseComponent,
	}, nil
}

func (w *WebViewGenerator) GenerateLoanAccountPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanAccountAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	loanAccountDetailsComponent := utils.GetLoanAccountKeyValueComponent(resp)
	loanPaymentRequestComponent := utils.GetLprRequestsComponent(resp.GetLoanPaymentRequests())
	soaComponent := w.getSOAComponent(resp.GetFiftyfinLamfDetails().GetSoa().GetTransactions())
	emiScheduleComponent := utils.GetEmiDetailsComponent(resp.GetInstallmentPayouts())
	pledgedMfComponent, err := w.getPledgedMfComponent(ctx, resp.GetLoanAccount().GetDetails().GetCollateralDetails().GetMfPledgeDetails().GetMutualFunds())
	penaltyFeesComponent := utils.GetPenaltyFeesComponent(resp.GetInstallmentPayouts())
	if err != nil {
		return nil, fmt.Errorf("failed to get pledged mf component: %w", err)
	}
	return []*cxPalPb.WebComponent{
		loanAccountDetailsComponent,
		pledgedMfComponent,
		emiScheduleComponent,
		loanPaymentRequestComponent,
		soaComponent,
		penaltyFeesComponent,
	}, nil
}

func (w *WebViewGenerator) getOfferHoldingsComponent(ctx context.Context, resp *palCxPb.GetLoanUserDetailsResponse) (*cxPalPb.WebComponent, error) {
	var (
		table = &webui.Table{
			TableName: "Offer Holdings",
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader("fundName", "Fund Name"),
				helper.GetTableHeader("folioNumber", "Folio Number"),
				helper.GetTableHeader("units", "Units"),
				helper.GetTableHeader("phone", "Phone number"),
				helper.GetTableHeader("email", "Email"),
				helper.GetTableHeader("eligibleForLienMarking", "Eligible for lien marking"),
				helper.GetTableHeader("reason", "Reason"),
			},
		}
	)
	sort.Slice(resp.GetLoanOfferDetails(), func(i, j int) bool {
		return datetime.IsDateAfter(resp.GetLoanOfferDetails()[i].GetOfferEndDate(), resp.GetLoanOfferDetails()[j].GetOfferEndDate())
	})
	if len(resp.GetLoanOfferDetails()) > 0 {
		offer := resp.GetLoanOfferDetails()[0]
		mfDetails, err := w.getMutualFundDetails(ctx, w.getAllIsins(offer.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint()))
		if err != nil {
			return nil, fmt.Errorf("error while building offer holding details: %w", err)
		}
		table.TableRows = append(table.GetTableRows(), w.getMfHoldingsTableRows(offer.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint().GetApprovedHoldings(), mfDetails, "Yes")...)
		table.TableRows = append(table.GetTableRows(), w.getMfHoldingsTableRows(offer.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint().GetLockedHoldings(), mfDetails, "No")...)
		table.TableRows = append(table.GetTableRows(), w.getMfHoldingsTableRows(offer.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint().GetUnapprovedHoldings(), mfDetails, "No")...)
	}
	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}, nil
}

func (w *WebViewGenerator) getMfHoldingsTableRows(holdings []*palPb.MfPortfolioConstraint_Holding, mfDetails map[string]*mutualfund.MutualFund, isEligibleForLienMarking string) []*webui.TableRow {
	var tableRows []*webui.TableRow
	for _, holding := range holdings {
		tableRows = append(tableRows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				"fundName":               helper.GetTableCellWithStringValue(getFundName(mfDetails, holding.GetIsin())),
				"folioNumber":            helper.GetTableCellWithStringValue(holding.GetFolioNumber()),
				"units":                  helper.GetTableCellWithStringValue(fmt.Sprint(holding.GetQuantity())),
				"phone":                  helper.GetTableCellWithStringValue(helper.MaskPhoneNumber(holding.GetPhoneNumber())),
				"email":                  helper.GetTableCellWithStringValue(helper.MaskEmail(holding.GetEmail())),
				"eligibleForLienMarking": helper.GetTableCellWithStringValue(isEligibleForLienMarking),
				"reason":                 helper.GetTableCellWithStringValue(helper.ConvertMfOfferApprovalStatusToReadableFormat(holding.GetApprovalStatus())),
			},
		})
	}
	return tableRows
}

func (w *WebViewGenerator) getMutualFundDetails(ctx context.Context, isinArr []string) (map[string]*mutualfund.MutualFund, error) {
	res, err := w.mfCatalogClient.GetMutualFunds(ctx, &catalogManagerPb.GetMutualFundsRequest{
		FundIdentifier: mutualfund.MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ISIN,
		Ids:            isinArr,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, fmt.Errorf("error while fetching mutual fund details : %w", rpcErr)
	}

	return res.GetMutualFunds(), nil
}

func (w *WebViewGenerator) getAllIsins(mfPortfolio *palPb.MfPortfolioConstraint) []string {
	approvedIsin := lo.Map(mfPortfolio.GetApprovedHoldings(), func(item *palPb.MfPortfolioConstraint_Holding, index int) string {
		return item.GetIsin()
	})
	unapprovedIsins := lo.Map(mfPortfolio.GetUnapprovedHoldings(), func(item *palPb.MfPortfolioConstraint_Holding, index int) string {
		return item.GetIsin()
	})
	allIsins := make([]string, 0)
	allIsins = append(allIsins, approvedIsin...)
	allIsins = append(allIsins, unapprovedIsins...)
	return allIsins
}

func getFundName(mfDetails map[string]*mutualfund.MutualFund, isin string) string {
	detail, ok := mfDetails[isin]
	if !ok {
		return ""
	}
	return detail.GetNameData().GetShortName()
}

// show only txns that are paid by users
func (w *WebViewGenerator) getSOAComponent(soaTxns []*vendorsFiftyfin.LoanStatementTxn) *cxPalPb.WebComponent {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader("date", "Txn Date"),
				helper.GetTableHeader("amount", "Amount"),
				helper.GetTableHeader("particulars", "Particulars"),
			},
			TableName: "Loan transactions (by vendor)",
		}
	)

	var rows []*webui.TableRow
	for _, soaTxn := range soaTxns {
		if soaTxn.GetCreditAmount() > 0 {
			rows = append(rows, &webui.TableRow{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					"date":        helper.GetTableCellWithStringValue(soaTxn.GetDate()),
					"amount":      helper.GetTableCellWithStringValue(fmt.Sprintf("%.2f", soaTxn.GetCreditAmount())),
					"particulars": helper.GetTableCellWithStringValue(soaTxn.GetParticulars()),
				},
			})
		}
	}
	table.TableRows = rows

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}
}

func (w *WebViewGenerator) getPledgedMfComponent(ctx context.Context, mutualFunds *palPb.PledgeDetails_MutualFunds) (*cxPalPb.WebComponent, error) {
	var (
		table = &webui.Table{
			TableHeaders: []*webui.TableHeader{
				helper.GetTableHeader("fund_name", "Fund Name"),
				helper.GetTableHeader("units", "Units"),
			},
			TableName: "Pledged mutual funds",
		}
	)

	var isinArr []string
	for _, pledgedMutualFund := range mutualFunds.GetSchemes() {
		isinArr = append(isinArr, pledgedMutualFund.GetIsin())
	}
	isinArr = lo.Uniq(isinArr)
	mutualFundDetails, err := w.getMutualFundDetails(ctx, isinArr)
	if err != nil {
		return nil, fmt.Errorf("failed to get mutual fund details: %w", err)
	}

	var rows []*webui.TableRow
	for _, pledgedMutualFund := range mutualFunds.GetSchemes() {
		fundName := getFundName(mutualFundDetails, pledgedMutualFund.GetIsin())
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				"fund_name": helper.GetTableCellWithStringValue(fundName),
				"units":     helper.GetTableCellWithStringValue(fmt.Sprintf("%.2f", pledgedMutualFund.GetQuantity())),
			},
		})
	}
	table.TableRows = rows

	return &cxPalPb.WebComponent{
		Details: &cxPalPb.WebComponent_Table{
			Table: table,
		},
	}, nil
}
