package generator

import (
	"context"
	"fmt"
	cxPalPb "github.com/epifi/gamma/api/cx/data_collector/preapprovedloan"
	palCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator/utils"
)

type LoansWebViewGenerator interface {
	GenerateLOSWebView(ctx context.Context, resp *palCxPb.GetLoanUserDetailsResponse) ([]*cxPalPb.WebComponent, error)
	GenerateLMSWebView(ctx context.Context, resp *palCxPb.GetLoanDetailsResponse) ([]*cxPalPb.WebComponent, error)
	GenerateEligiblityLoanRequestPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanRequestAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error)
	GenerateApplicationLoanRequestPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanRequestAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error)
	GenerateLoanAccountPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanAccountAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error)
}

type BaseWebViewGenerator struct{}

var _ LoansWebViewGenerator = &BaseWebViewGenerator{}

func NewBaseWebViewGenerator() *BaseWebViewGenerator {
	return &BaseWebViewGenerator{}
}

func (b *BaseWebViewGenerator) GenerateLOSWebView(ctx context.Context, resp *palCxPb.GetLoanUserDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	eligibilityLrComp, err := utils.GetEligibilityLRComponent(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate lr eligibility web component: %w", err)
	}
	applicationLrComp, err := utils.GetApplicationLRComponent(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate lr application web component: %w", err)
	}

	offerDetails := utils.GetOfferDetailsComponent(resp)
	eligibilityDetails := utils.GetEligibilityDetailsComponent(resp)

	components := []*cxPalPb.WebComponent{
		eligibilityDetails,
		eligibilityLrComp,
		applicationLrComp,
		offerDetails,
	}

	return components, nil
}

func (b *BaseWebViewGenerator) GenerateLMSWebView(ctx context.Context, resp *palCxPb.GetLoanDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	loanAccountComponent, err := utils.GetLoanAccountsComponent(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to generate loan acocunt component: %w", err)
	}
	return []*cxPalPb.WebComponent{
		loanAccountComponent,
	}, nil
}

func (b *BaseWebViewGenerator) GenerateEligiblityLoanRequestPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanRequestAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	lseComponent := utils.GetLoanStepsComponent(resp)
	return []*cxPalPb.WebComponent{
		lseComponent,
	}, nil
}

func (b *BaseWebViewGenerator) GenerateApplicationLoanRequestPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanRequestAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	lseComponent := utils.GetLoanStepsComponent(resp)
	return []*cxPalPb.WebComponent{
		lseComponent,
	}, nil
}

func (b *BaseWebViewGenerator) GenerateLoanAccountPopUpWebView(ctx context.Context, resp *palCxPb.GetLoanAccountAdditionalDetailsResponse) ([]*cxPalPb.WebComponent, error) {
	loanAccountDetailsComponent := utils.GetLoanAccountKeyValueComponent(resp)
	loanPaymentRequestComponent := utils.GetLprRequestsComponent(resp.GetLoanPaymentRequests())
	emiScheduleComponent := utils.GetEmiDetailsComponentPL(resp.GetLoanUpcomingEmis())
	penaltyFeesComponent := utils.GetPenaltyFeesComponent(resp.GetInstallmentPayouts())
	return []*cxPalPb.WebComponent{
		loanAccountDetailsComponent,
		emiScheduleComponent,
		loanPaymentRequestComponent,
		penaltyFeesComponent,
	}, nil
}
