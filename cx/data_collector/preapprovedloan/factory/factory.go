package factory

import (
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator/impl"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/providers"
)

type ViewFactory struct {
	baseWebViewGenerator *generator.BaseWebViewGenerator
	lamfWebViewGenerator *impl.WebViewGenerator
	baseDataProvider     *providers.BaseDataProvider
}

func NewViewFactory(lamfWebViewGenerator *impl.WebViewGenerator, baseWebViewGenerator *generator.BaseWebViewGenerator, baseDataProvider *providers.BaseDataProvider) *ViewFactory {
	return &ViewFactory{
		baseWebViewGenerator: baseWebViewGenerator,
		lamfWebViewGenerator: lamfWebViewGenerator,
		baseDataProvider:     baseDataProvider,
	}
}

func (p *ViewFactory) GetLoansWebViewGenerator(vendor palPb.Vendor, loanProgram palPb.LoanProgram) generator.LoansWebViewGenerator {
	switch {
	case vendor == palPb.Vendor_FIFTYFIN && loanProgram == palPb.LoanProgram_LOAN_PROGRAM_LAMF:
		return p.lamfWebViewGenerator
	default:
		return p.baseWebViewGenerator
	}
}

func (p *ViewFactory) GetDataProvider(_ palPb.Vendor, _ palPb.LoanProgram) providers.DataProvider {
	return p.baseDataProvider
}
