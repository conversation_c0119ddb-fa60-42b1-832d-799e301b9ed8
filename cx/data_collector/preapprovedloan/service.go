package preapprovedloan

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cxPalPb "github.com/epifi/gamma/api/cx/data_collector/preapprovedloan"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/investment/mutualfund"
	catalogManagerPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	preapprovedloanCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	userPb "github.com/epifi/gamma/api/user"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/factory"
	cxLogger "github.com/epifi/gamma/cx/logger"
	pkgLoans "github.com/epifi/gamma/pkg/loans"
	palPkg "github.com/epifi/gamma/preapprovedloan/pkg"
)

type Service struct {
	cxPalPb.UnimplementedPreApprovedLoanServer
	authEngine              auth_engine.IAuthEngine
	preapprovedloanCxClient preapprovedloanCxPb.CxClient
	mfCatalogClient         catalogManagerPb.CatalogManagerClient
	viewFactory             *factory.ViewFactory
	ticketClient            ticketPb.TicketClient
	userClient              userPb.UsersClient
	fdClient                freshdeskPb.FreshdeskClient
}

func NewService(authEngine auth_engine.IAuthEngine, preapprovedloanCxClient preapprovedloanCxPb.CxClient,
	mfCatalogClient catalogManagerPb.CatalogManagerClient, viewFactory *factory.ViewFactory,
	ticketClient ticketPb.TicketClient, userClient userPb.UsersClient, fdClient freshdeskPb.FreshdeskClient) *Service {
	return &Service{
		authEngine:              authEngine,
		preapprovedloanCxClient: preapprovedloanCxClient,
		mfCatalogClient:         mfCatalogClient,
		viewFactory:             viewFactory,
		ticketClient:            ticketClient,
		userClient:              userClient,
		fdClient:                fdClient,
	}
}

var _ cxPalPb.PreApprovedLoanServer = &Service{}

const (
	fundName               = "Fund Name"
	folioNumber            = "Folio Number"
	units                  = "Units"
	eligibleForLienMarking = "Eligible for lien marking"
	reason                 = "Reason"
	ticketId               = "Ticket Id"
	ticketSubject          = "Ticket Subject"
	loanVendor             = "Loan Vendor"
	loanProgram            = "Loan Program"
	disposition1           = "Disposition 1"
	disposition2           = "Disposition 2"
	disposition3           = "Disposition 3"
	dropOffStage           = "Drop Off Stage"
	acquisitionChannel     = "Acquisition Channel"
)

func (s *Service) GetLoanUserDetails(ctx context.Context, req *cxPalPb.GetLoanUserDetailsRequest) (*cxPalPb.GetLoanUserDetailsResponse, error) {
	// isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	// if isActionRequired {
	//	return &cxPalPb.GetLoanUserDetailsResponse{
	//		Status:           rpcPb.StatusOk(),
	//		SherlockDeepLink: sherlockDeepLink,
	//	}, nil
	// }
	loansHeader := pkgLoans.ConvertToPalLoanHeader(req.GetLoanHeader())
	palRes, err := s.preapprovedloanCxClient.GetLoanUserDetails(ctx, &preapprovedloanCxPb.GetLoanUserDetailsRequest{
		ActorId:    req.GetHeader().GetActor().GetId(),
		LoanHeader: loansHeader,
	})
	if te := epifigrpc.RPCError(palRes, err); te != nil {
		cxLogger.Error(ctx, "error while fetching preapprovedloan user details", zap.Error(te))
		return &cxPalPb.GetLoanUserDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch preapprovedloan user details"),
		}, nil
	}

	webViewGenerator := s.viewFactory.GetLoansWebViewGenerator(loansHeader.GetVendor(), loansHeader.GetLoanProgram())
	losWebViewComponents, err := webViewGenerator.GenerateLOSWebView(ctx, palRes)
	if err != nil {
		cxLogger.Error(ctx, "error while generating web view components", zap.Error(err))
		return &cxPalPb.GetLoanUserDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while generating web view components"),
		}, nil
	}

	res := &cxPalPb.GetLoanUserDetailsResponse{
		Status: rpcPb.StatusOk(),
		LoanUserDetails: &cxPalPb.LoanUserDetails{
			IsUserSuggestedForLoan:     palRes.GetCurrentMonthUserLoanEligibilityDetails().GetIsUserSuggestedForLoan(),
			LoanApplicationStatus:      palRes.GetCurrentUserLoanApplicationDetails().GetLoanApplicationStatus(),
			ReasonForIneligibility:     palRes.GetCurrentMonthUserLoanEligibilityDetails().GetReasonForIneligibility(),
			LoanApplicationSubStatus:   palRes.GetCurrentUserLoanApplicationDetails().GetLoanApplicationSubStatus(),
			LoanAmountApplied:          palRes.GetCurrentUserLoanApplicationDetails().GetLoanAmountApplied(),
			TenureAppliedMonths:        palRes.GetCurrentUserLoanApplicationDetails().GetTenureAppliedMonths(),
			LoanOfferDetails:           s.fillLoanOfferDetails(ctx, palRes.GetLoanOfferDetails()),
			LoanApplicationVendor:      palRes.GetCurrentUserLoanApplicationDetails().GetVendor(),
			LoanApplicationLoanProgram: palRes.GetCurrentUserLoanApplicationDetails().GetLoanProgram(),
			Components:                 losWebViewComponents,
		},
	}
	return res, nil
}

func (s *Service) fillLoanOfferDetails(ctx context.Context, offerDetails []*preapprovedloanCxPb.GetLoanUserDetailsResponse_LoanOfferDetails) []*cxPalPb.LoanUserDetails_LoanOfferDetails {
	res := make([]*cxPalPb.LoanUserDetails_LoanOfferDetails, 0)
	for _, od := range offerDetails {
		loanOffer := &cxPalPb.LoanUserDetails_LoanOfferDetails{
			LoanOfferId:            od.GetLoanOfferId(),
			MinLoanAmount:          od.GetMinLoanAmount(),
			MaxLoanAmount:          od.GetMaxLoanAmount(),
			MaxEmiAmount:           od.GetMaxEmiAmount(),
			Interest:               od.GetInterest(),
			MinTenure:              od.GetMinTenure(),
			MaxTenure:              od.GetMaxTenure(),
			OfferStartDate:         od.GetOfferStartDate(),
			OfferEndDate:           od.GetOfferEndDate(),
			AgentInputUserFeedback: od.GetAgentInputUserFeedback(),
			Vendor:                 od.GetVendor(),
			LoanProgram:            od.GetLoanProgram(),
		}
		if od.GetFiftyfinLamfConstraintInfo() != nil {
			offerHoldingsTable, err := s.getOfferHoldingsTable(ctx, od)
			if err != nil {
				cxLogger.Error(ctx, "error while building offer holdings table", zap.Error(err))
			} else {
				loanOffer.MfHoldings = offerHoldingsTable
			}

		}
		res = append(res, loanOffer)
	}
	return res
}

func (s *Service) getOfferHoldingsTable(ctx context.Context, offer *preapprovedloanCxPb.GetLoanUserDetailsResponse_LoanOfferDetails) (*webui.Table, error) {
	var rows []*webui.TableRow
	mfDetails, err := s.getMutualFundDetails(ctx, s.getAllIsins(offer.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint()))
	if err != nil {
		return nil, errors.Wrap(err, "error while building offer holding details")
	}
	for _, holding := range offer.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint().GetApprovedHoldings() {
		rows = append(rows, &webui.TableRow{
			HeaderKeyToCellValueMap: map[string]string{
				fundName:               getFundName(mfDetails, holding.GetIsin()),
				folioNumber:            holding.GetFolioNumber(),
				units:                  fmt.Sprint(holding.GetQuantity()),
				eligibleForLienMarking: "Yes",
				reason:                 "NA",
			},
		})
	}

	for _, holding := range offer.GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint().GetUnapprovedHoldings() {
		rows = append(rows, &webui.TableRow{
			HeaderKeyToCellValueMap: map[string]string{
				fundName:               getFundName(mfDetails, holding.GetIsin()),
				folioNumber:            holding.GetFolioNumber(),
				units:                  fmt.Sprint(holding.GetQuantity()),
				eligibleForLienMarking: "No",
				reason:                 "NA",
			},
		})
	}

	return &webui.Table{
		TableName:    "Offer Holdings",
		TableHeaders: getOfferHoldingsTableHeaders(),
		TableRows:    rows,
	}, nil
}

func getOfferHoldingsTableHeaders() []*webui.TableHeader {
	return []*webui.TableHeader{
		{
			Label:     "Fund Name",
			HeaderKey: fundName,
			IsVisible: true,
		},
		{
			Label:     "Folio Number",
			HeaderKey: folioNumber,
			IsVisible: true,
		},
		{
			Label:     "Units",
			HeaderKey: units,
			IsVisible: true,
		},
		{
			Label:     "Eligible for lien marking",
			HeaderKey: eligibleForLienMarking,
			IsVisible: true,
		},
		{
			Label:     "Reason",
			HeaderKey: eligibleForLienMarking,
			IsVisible: true,
		},
	}
}

func (s *Service) getMutualFundDetails(ctx context.Context, isinArr []string) (map[string]*mutualfund.MutualFund, error) {
	res, err := s.mfCatalogClient.GetMutualFunds(ctx, &catalogManagerPb.GetMutualFundsRequest{
		FundIdentifier: mutualfund.MutualFundIdentifier_MUTUAL_FUND_IDENTIFIER_ISIN,
		Ids:            isinArr,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, fmt.Errorf("error while fetching mutual fund details : %w", rpcErr)
	}

	return res.GetMutualFunds(), nil
}

func (s *Service) getAllIsins(mfPortfolio *palPb.MfPortfolioConstraint) []string {
	approvedIsin := lo.Map(mfPortfolio.GetApprovedHoldings(), func(item *palPb.MfPortfolioConstraint_Holding, index int) string {
		return item.GetIsin()
	})
	unapprovedIsins := lo.Map(mfPortfolio.GetUnapprovedHoldings(), func(item *palPb.MfPortfolioConstraint_Holding, index int) string {
		return item.GetIsin()
	})
	allIsins := make([]string, 0)
	allIsins = append(allIsins, approvedIsin...)
	allIsins = append(allIsins, unapprovedIsins...)
	return allIsins
}

func getFundName(mfDetails map[string]*mutualfund.MutualFund, isin string) string {
	detail, ok := mfDetails[isin]
	if !ok {
		return ""
	}
	return detail.GetNameData().GetShortName()
}

func (s *Service) GetLoanDetails(ctx context.Context, req *cxPalPb.GetLoanDetailsRequest) (*cxPalPb.GetLoanDetailsResponse, error) {
	// isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	// if isActionRequired {
	//	return &cxPalPb.GetLoanDetailsResponse{
	//		Status:           rpcPb.StatusOk(),
	//		SherlockDeepLink: sherlockDeepLink,
	//	}, nil
	// }

	loansHeader := pkgLoans.ConvertToPalLoanHeader(req.GetLoanHeader())
	palRes, err := s.preapprovedloanCxClient.GetLoanDetails(ctx, &preapprovedloanCxPb.GetLoanDetailsRequest{
		ActorId:    req.GetHeader().GetActor().GetId(),
		LoanHeader: loansHeader,
	})
	if te := epifigrpc.RPCError(palRes, err); te != nil {
		cxLogger.Error(ctx, "error while fetching preapprovedloan user details", zap.Error(te))
		return &cxPalPb.GetLoanDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch preapprovedloan user details"),
		}, nil
	}
	loanDetailsV2 := s.fillLoanDetails(ctx, palRes.GetLoanDetails())
	for idx, loanDetail := range loanDetailsV2 {
		palRes.GetLoanDetails()[idx].ForeClosureDetails = &preapprovedloanCxPb.ForeclosureDetails{
			TotalOutstandingAmount:     loanDetail.GetForeClosureDetails().GetTotalOutstandingAmount(),
			PrincipalOutstandingAmount: loanDetail.GetForeClosureDetails().GetPrincipalOutstandingAmount(),
			InterestOutstandingAmount:  loanDetail.GetForeClosureDetails().GetInterestOutstandingAmount(),
			PenaltyAmt:                 loanDetail.GetForeClosureDetails().GetPenaltyAmt(),
			FeesAmt:                    loanDetail.GetForeClosureDetails().GetFeesAmt(),
			OtherCharges:               loanDetail.GetForeClosureDetails().GetOtherCharges(),
		}
	}
	webViewGenerator := s.viewFactory.GetLoansWebViewGenerator(loansHeader.GetVendor(), loansHeader.GetLoanProgram())
	lmsWebViewComponents, err := webViewGenerator.GenerateLMSWebView(ctx, palRes)
	if err != nil {
		cxLogger.Error(ctx, "error while generating web view components", zap.Error(err))
		return &cxPalPb.GetLoanDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while generating web view components"),
		}, nil
	}

	res := &cxPalPb.GetLoanDetailsResponse{
		Status:        rpcPb.StatusOk(),
		LoanDetails:   &cxPalPb.LoanDetails{},
		LoanDetailsV2: loanDetailsV2,
		Components:    lmsWebViewComponents,
	}
	return res, nil
}

func (s *Service) fillLoanDetails(ctx context.Context, loanDetails []*preapprovedloanCxPb.GetLoanDetailsResponse_LoanDetailsForCx) []*cxPalPb.LoanDetailsV2 {
	res := make([]*cxPalPb.LoanDetailsV2, 0)
	for _, ld := range loanDetails {
		foreClosureResp, foreclosureErr := s.GetForeClosureDetails(ctx, &cxPalPb.GetForeclosureRequest{
			LoanAccountNumber: ld.GetLoanAccountNumber(),
			LoanHeader: &types.LoanHeader{
				LoanProgram: types.LoanProgram(types.LoanProgram_value[ld.GetLoanProgram()]),
				Vendor:      types.Vendor(types.Vendor_value["VENDOR_"+ld.GetVendor()]),
			},
		})
		if te := epifigrpc.RPCError(foreClosureResp, foreclosureErr); te != nil {
			// not returning error in order to best effort
			cxLogger.Error(ctx, "unable to get the foreclosure details from api", zap.Error(te))
		}

		pastTransactions := fillPastTransactions(ld.GetLoanPastTransactions())
		upcomingEmis := fillUpcomingEmis(ld.GetLoanUpcomingEmis())
		details := &cxPalPb.LoanDetailsV2{
			LoanAccountNumber:    ld.GetLoanAccountNumber(),
			LoanOpenDate:         ld.GetLoanOpenDate(),
			LoanAmount:           ld.GetLoanAmount(),
			InterestRate:         ld.GetInterestRate(),
			TenureInMonths:       ld.GetTenureInMonths(),
			ProcessingFee:        ld.GetProcessingFee(),
			OutstandingAmount:    ld.GetOutstandingAmount(),
			Gst:                  ld.GetGst(),
			BrokenPeriodInterest: ld.GetBrokenPeriodInterest(),
			PreClosureFee:        ld.GetPreClosureFee(),
			Vendor:               ld.GetVendor(),
			LoanProgram:          ld.GetLoanProgram(),
			LoanPastTransactions: pastTransactions,
			LoanUpcomingEmis:     upcomingEmis,
			MandateAccount: &cxPalPb.MandateAccount{
				MaskedAccNo: ld.GetMandateAccount().GetMaskedAccNo(),
				BankName:    ld.GetMandateAccount().GetBankName(),
			},
			BankAccountDetails: &cxPalPb.BankAccountDetails{
				AccountNumber: ld.GetBankAccountDetails().GetAccountNumber(),
				Ifsc:          ld.GetBankAccountDetails().GetIfsc(),
				BankName:      ld.GetBankAccountDetails().GetBankName(),
			},
			ForeClosureDetails: &cxPalPb.ForeclosureDetails{
				TotalOutstandingAmount:     foreClosureResp.GetForeClosureDetails().GetTotalOutstandingAmount(),
				PrincipalOutstandingAmount: foreClosureResp.GetForeClosureDetails().GetPrincipalOutstandingAmount(),
				InterestOutstandingAmount:  foreClosureResp.GetForeClosureDetails().GetInterestOutstandingAmount(),
				PenaltyAmt:                 foreClosureResp.GetForeClosureDetails().GetPenaltyAmt(),
				FeesAmt:                    foreClosureResp.GetForeClosureDetails().GetFeesAmt(),
				OtherCharges:               foreClosureResp.GetForeClosureDetails().GetOtherCharges(),
			},
			Status: ld.GetLoanAccount().GetStatus().String(),
		}
		if ld.GetMfPledgeDetails() != nil {
			holdings, err := s.getMfPledgedHoldingDetails(ctx, ld)
			if err != nil {
				cxLogger.Error(ctx, "error while building pledged holdings table", zap.Error(err))
			} else {
				details.MfPledgedHoldings = holdings
			}
		}
		res = append(res, details)
	}
	return res
}

func (s *Service) getMfPledgedHoldingDetails(ctx context.Context, loanDetail *preapprovedloanCxPb.GetLoanDetailsResponse_LoanDetailsForCx) (*webui.Table, error) {
	var isinList []string
	for _, detail := range loanDetail.GetMfPledgeDetails() {
		isinList = append(isinList, detail.GetIsin())
	}
	mfDetails, err := s.getMutualFundDetails(ctx, isinList)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching mutual fund using mf catalog")
	}
	var rows []*webui.TableRow
	for _, detail := range loanDetail.GetMfPledgeDetails() {
		rows = append(rows, &webui.TableRow{
			HeaderKeyToCellValueMap: map[string]string{
				fundName: getFundName(mfDetails, detail.GetIsin()),
				units:    fmt.Sprint(detail.GetUnits()),
			},
		})
	}

	return &webui.Table{
		TableName:    "Pledged Mutual Funds",
		TableHeaders: getMfPledgedHoldingTableHeaders(),
		TableRows:    rows,
	}, nil
}

func getMfPledgedHoldingTableHeaders() []*webui.TableHeader {
	return []*webui.TableHeader{
		{
			Label:     "Fund Name",
			HeaderKey: fundName,
			IsVisible: true,
		},
		{
			Label:     "Units",
			HeaderKey: units,
			IsVisible: true,
		},
	}
}

func fillPastTransactions(palEmiTxns []*preapprovedloanCxPb.GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) []*cxPalPb.LoanDetailsV2_LoanPastTransaction {
	cxEmiTxns := make([]*cxPalPb.LoanDetailsV2_LoanPastTransaction, 0)
	for _, emiTxn := range palEmiTxns {
		cxEmiTxns = append(cxEmiTxns, &cxPalPb.LoanDetailsV2_LoanPastTransaction{
			Timestamp:         emiTxn.GetTimestamp(),
			TransactionType:   emiTxn.GetTransactionType(),
			Status:            emiTxn.GetStatus(),
			OrderId:           emiTxn.GetOrderId(),
			FiUtrNumber:       emiTxn.GetFiUtrNumber(),
			LoanAccountNumber: emiTxn.GetLoanAccountNumber(),
			Amount:            emiTxn.GetAmount(),
			ModeOfPayment:     emiTxn.GetModeOfPayment(),
			Charges:           emiTxn.GetCharges(),
		})
	}
	return cxEmiTxns
}

func fillUpcomingEmis(palEmis []*preapprovedloanCxPb.GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) []*cxPalPb.LoanDetailsV2_LoanUpcomingEmi {
	cxEmis := make([]*cxPalPb.LoanDetailsV2_LoanUpcomingEmi, 0)
	for _, emis := range palEmis {
		cxEmis = append(cxEmis, &cxPalPb.LoanDetailsV2_LoanUpcomingEmi{
			NextEmiDate:       emis.GetNextEmiDate(),
			LoanAccountNumber: emis.GetLoanAccountNumber(),
			Amount:            emis.GetAmount(),
		})
	}
	return cxEmis
}

func (s *Service) GetForeClosureDetails(ctx context.Context, req *cxPalPb.GetForeclosureRequest) (*cxPalPb.GetForeclosureResponse, error) {
	foreClosureDetails, foreClosureDetailsErr := s.preapprovedloanCxClient.GetForeclosureDetails(ctx, &preapprovedloanCxPb.GetForeclosureRequest{
		LoanAccountNumber: req.GetLoanAccountNumber(),
		LoanHeader:        pkgLoans.ConvertToPalLoanHeader(req.GetLoanHeader()),
	})
	if te := epifigrpc.RPCError(foreClosureDetails, foreClosureDetailsErr); te != nil {
		cxLogger.Error(ctx, "error while fetching foreClosure details", zap.Error(te))
		return &cxPalPb.GetForeclosureResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch foreClosure details"),
		}, nil
	}

	return &cxPalPb.GetForeclosureResponse{
		Status: rpcPb.StatusOk(),
		ForeClosureDetails: &cxPalPb.ForeclosureDetails{
			TotalOutstandingAmount:     foreClosureDetails.GetTotalOutstandingAmt(),
			PrincipalOutstandingAmount: foreClosureDetails.GetPrincipalOutstandingAmt(),
			InterestOutstandingAmount:  foreClosureDetails.GetInterestOutstandingAmt(),
			PenaltyAmt:                 foreClosureDetails.GetPenaltyAmt(),
			FeesAmt:                    foreClosureDetails.GetFeesAmt(),
			OtherCharges:               foreClosureDetails.GetOtherCharges(),
		},
	}, nil
}

//nolint:funlen
func (s *Service) GetLoanAdditionalDetails(ctx context.Context, req *cxPalPb.GetLoanAdditionalDetailsRequest) (*cxPalPb.GetLoanAdditionalDetailsResponse, error) {
	var loansMetadata = &cxPalPb.LoansMetadata{}
	err := protojson.Unmarshal([]byte(req.GetMetadata()), loansMetadata)
	if err != nil {
		cxLogger.Error(ctx, "failed to unmarshal loans metadata", zap.Error(err))
		return &cxPalPb.GetLoanAdditionalDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	dataProvider := s.viewFactory.GetDataProvider(loansMetadata.GetVendor(), loansMetadata.GetLoanProgram())
	webViewGenerator := s.viewFactory.GetLoansWebViewGenerator(loansMetadata.GetVendor(), loansMetadata.GetLoanProgram())

	var components []*cxPalPb.WebComponent
	switch loansMetadata.GetMetadataType() {
	case cxPalPb.LoansMetadataType_LOANS_METADATA_TYPE_ELIGIBILITY_LOAN_REQUEST:
		loanRequestDetails, dataErr := dataProvider.GetLoanRequestDetails(ctx, loansMetadata.GetVendor(), loansMetadata.GetLoanProgram(), loansMetadata.GetLoanRequestMetadata().GetLoanRequestId())
		if dataErr != nil {
			cxLogger.Error(ctx, "error while getting loan request details", zap.Error(dataErr))
			return &cxPalPb.GetLoanAdditionalDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while getting loan request details"),
			}, nil
		}
		components, err = webViewGenerator.GenerateEligiblityLoanRequestPopUpWebView(ctx, loanRequestDetails)
		if err != nil {
			cxLogger.Error(ctx, "error while generating loan eligibility web view components", zap.Error(err))
			return &cxPalPb.GetLoanAdditionalDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while generating loan eligibility web view components"),
			}, nil
		}
	case cxPalPb.LoansMetadataType_LOANS_METADATA_TYPE_APPLICATION_LOAN_REQUEST:
		loanRequestDetails, dataErr := dataProvider.GetLoanRequestDetails(ctx, loansMetadata.GetVendor(), loansMetadata.GetLoanProgram(), loansMetadata.GetLoanRequestMetadata().GetLoanRequestId())
		if dataErr != nil {
			cxLogger.Error(ctx, "error while getting loan request details", zap.Error(dataErr))
			return &cxPalPb.GetLoanAdditionalDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while getting loan request details"),
			}, nil
		}
		components, err = webViewGenerator.GenerateApplicationLoanRequestPopUpWebView(ctx, loanRequestDetails)
		if err != nil {
			cxLogger.Error(ctx, "error while generating loan application web view components", zap.Error(err))
			return &cxPalPb.GetLoanAdditionalDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while generating loan application web view components"),
			}, nil
		}
	case cxPalPb.LoansMetadataType_LOANS_METADATA_TYPE_LOAN_ACCOUNT:
		loanAccountDetails, dataErr := dataProvider.GetLoanAccountDetails(ctx, loansMetadata.GetVendor(), loansMetadata.GetLoanProgram(), loansMetadata.GetLoanAccountMetadata().GetLoanAccountId())
		if dataErr != nil {
			cxLogger.Error(ctx, "error while getting loan account details", zap.Error(dataErr))
			return &cxPalPb.GetLoanAdditionalDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while getting loan account details"),
			}, nil
		}
		components, err = webViewGenerator.GenerateLoanAccountPopUpWebView(ctx, loanAccountDetails)
		if err != nil {
			cxLogger.Error(ctx, "error while generating loan account web view components", zap.Error(err))
			return &cxPalPb.GetLoanAdditionalDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while generating loan account web view components"),
			}, nil
		}
	default:
		cxLogger.Error(ctx, fmt.Sprintf("Unknown loans metadata type: %s", loansMetadata.GetMetadataType()))
		return &cxPalPb.GetLoanAdditionalDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &cxPalPb.GetLoanAdditionalDetailsResponse{
		Status:     rpcPb.StatusOk(),
		Components: components,
	}, nil
}

// GetLoanOutCallAgentAssignedTickets RPC to get all loans out calling ticket assigned to the agent
func (s *Service) GetLoanOutCallAgentAssignedTickets(ctx context.Context, req *cxPalPb.GetLoanOutCallAgentAssignedTicketsRequest) (*cxPalPb.GetLoanOutCallAgentAssignedTicketsResponse, error) {
	// Get agent id from agent email
	agent, err := s.fdClient.GetAgent(ctx, &freshdeskPb.GetAgentRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FRESHDESK},
		Email:  req.GetHeader().GetAgentEmail(),
	})
	if te := epifigrpc.RPCError(agent, err); te != nil {
		cxLogger.Error(ctx, "error fetching agent info for given agent email", zap.Any(logger.AGENT_EMAIL, req.GetHeader().GetAgentEmail()), zap.Error(te))
		return &cxPalPb.GetLoanOutCallAgentAssignedTicketsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch agent info"),
		}, nil
	}

	// Get tickets assigned to the agent
	ticketReq := &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			ResponderId:    agent.GetAgent().GetId(),
			AgentGroupList: []ticketPb.Group{ticketPb.Group_GROUP_LOAN_OUTBOUND_CALL},
			FromTime:       timestampPb.New(time.Now().Add(-time.Hour * 24 * 30)),
			StatusList:     ticketPb.GetActiveStatusList(),
		},
		PageContextRequest: req.GetPageContext(),
	}

	if req.GetTicketFilters() != nil {
		res := []*ticketPb.CustomFieldFilter{}
		for _, filter := range req.GetTicketFilters() {
			switch filter.GetTicketFilterKey() {
			case loanVendor:
				res = append(res, &ticketPb.CustomFieldFilter{
					FilterKey: ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_VENDOR,
					FilterValue: &ticketPb.CustomFieldFilter_TicketFiltersCustomFieldFilterLoanVendor{
						TicketFiltersCustomFieldFilterLoanVendor: filter.GetTicketFilterValue(),
					},
				})
			case loanProgram:
				res = append(res, &ticketPb.CustomFieldFilter{
					FilterKey: ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_PROGRAM,
					FilterValue: &ticketPb.CustomFieldFilter_TicketFiltersCustomFieldFilterLoanProgram{
						TicketFiltersCustomFieldFilterLoanProgram: filter.GetTicketFilterValue(),
					},
				})
			case disposition1:
				res = append(res, &ticketPb.CustomFieldFilter{
					FilterKey: ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION1,
					FilterValue: &ticketPb.CustomFieldFilter_TicketFiltersCustomFieldFilterDisposition1{
						TicketFiltersCustomFieldFilterDisposition1: filter.GetTicketFilterValue(),
					},
				})
			case disposition2:
				res = append(res, &ticketPb.CustomFieldFilter{
					FilterKey: ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION2,
					FilterValue: &ticketPb.CustomFieldFilter_TicketFiltersCustomFieldFilterDisposition2{
						TicketFiltersCustomFieldFilterDisposition2: filter.GetTicketFilterValue(),
					},
				})
			case disposition3:
				res = append(res, &ticketPb.CustomFieldFilter{
					FilterKey: ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION3,
					FilterValue: &ticketPb.CustomFieldFilter_TicketFiltersCustomFieldFilterDisposition3{
						TicketFiltersCustomFieldFilterDisposition3: filter.GetTicketFilterValue(),
					},
				})
			case dropOffStage:
				res = append(res, &ticketPb.CustomFieldFilter{
					FilterKey: ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DROP_OFF_STAGE,
					FilterValue: &ticketPb.CustomFieldFilter_TicketFiltersCustomFieldFilterDropoffStage{
						TicketFiltersCustomFieldFilterDropoffStage: filter.GetTicketFilterValue(),
					},
				})
			case acquisitionChannel:
				res = append(res, &ticketPb.CustomFieldFilter{
					FilterKey: ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_ACQUISITION_CHANNEL,
					FilterValue: &ticketPb.CustomFieldFilter_TicketFiltersCustomFieldFilterAcquisitionChannel{
						TicketFiltersCustomFieldFilterAcquisitionChannel: filter.GetTicketFilterValue(),
					},
				})
			}
		}
		ticketReq.GetTicketFilters().CustomFieldDbFilters = res
	}
	ticketsResp, err := s.ticketClient.GetSupportTickets(ctx, ticketReq)
	if te := epifigrpc.RPCError(ticketsResp, err); te != nil && !rpcPb.StatusFromError(te).IsRecordNotFound() {
		cxLogger.Error(ctx, "error while fetching assigned loan out call tickets", zap.Error(te))
		return &cxPalPb.GetLoanOutCallAgentAssignedTicketsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch assigned tickets"),
		}, nil
	}

	ticketsAssignedToAgent := ticketsResp.GetTickets()

	// Convert these tickets to table rows
	tableRows := make([]*webui.TableRow, 0)
	for _, ticket := range ticketsAssignedToAgent {
		tableRows = append(tableRows, &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				ticketId:           {ValueV2: &webui.TableCell_StringValue{StringValue: strconv.Itoa(int(ticket.GetId()))}},
				ticketSubject:      {ValueV2: &webui.TableCell_StringValue{StringValue: ticket.GetSubject()}},
				loanProgram:        {ValueV2: &webui.TableCell_StringValue{StringValue: ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanProgram()}},
				loanVendor:         {ValueV2: &webui.TableCell_StringValue{StringValue: ticket.GetCustomFields().GetLoanOutcallMetadata().GetLoanVendor()}},
				disposition1:       {ValueV2: &webui.TableCell_StringValue{StringValue: ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition1()}},
				disposition2:       {ValueV2: &webui.TableCell_StringValue{StringValue: ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition2()}},
				disposition3:       {ValueV2: &webui.TableCell_StringValue{StringValue: ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition3()}},
				dropOffStage:       {ValueV2: &webui.TableCell_StringValue{StringValue: ticket.GetCustomFields().GetLoanOutcallMetadata().GetDropOffStage()}},
				acquisitionChannel: {ValueV2: &webui.TableCell_StringValue{StringValue: ticket.GetCustomFields().GetLoanOutcallMetadata().GetAcquisitionChannel()}},
			},
			Actions: []*webui.CTA{
				{
					Label:        "View",
					DisplayTheme: webui.CTA_DISPLAY_THEME_PRIMARY,
				},
			},
			Meta: strconv.Itoa(int(ticket.GetId())),
		})
	}

	ticketsTable := &webui.Table{
		TableName: "All Tickets Queue",
		TableHeaders: []*webui.TableHeader{
			{
				Label:     "Ticket Id",
				HeaderKey: ticketId,
				IsVisible: true,
			},
			{
				Label:     "Ticket Subject",
				HeaderKey: ticketSubject,
				IsVisible: true,
			},
			{
				Label:     "Loan Program",
				HeaderKey: loanProgram,
				IsVisible: true,
				ColumnFilter: &webui.ColumnFilter{
					FilterType: webui.ColumnFilterType_COLUMN_FILTER_TYPE_SINGLE_SELECT,
					OptionsConfig: &webui.ColumnFilter_SelectOptionsConfig{
						SelectOptionsConfig: &webui.SelectOptionsConfig{
							SelectOptions: palPkg.GetLoanPrograms(),
						},
					},
				},
			},
			{
				Label:     "Loan Vendor",
				HeaderKey: loanVendor,
				IsVisible: true,
				ColumnFilter: &webui.ColumnFilter{
					FilterType: webui.ColumnFilterType_COLUMN_FILTER_TYPE_SINGLE_SELECT,
					OptionsConfig: &webui.ColumnFilter_SelectOptionsConfig{
						SelectOptionsConfig: &webui.SelectOptionsConfig{
							SelectOptions: palPkg.GetLoanVendors(),
						},
					},
				},
			},
			{
				Label:     "Disposition 1",
				HeaderKey: disposition1,
				IsVisible: true,
				ColumnFilter: &webui.ColumnFilter{
					FilterType: webui.ColumnFilterType_COLUMN_FILTER_TYPE_SINGLE_SELECT,
					OptionsConfig: &webui.ColumnFilter_SelectOptionsConfig{
						SelectOptionsConfig: &webui.SelectOptionsConfig{
							SelectOptions: palPkg.GetDisposition1Categories(),
						},
					},
				},
			},
			{
				Label:     "Disposition 2",
				HeaderKey: disposition2,
				IsVisible: true,
				ColumnFilter: &webui.ColumnFilter{
					FilterType: webui.ColumnFilterType_COLUMN_FILTER_TYPE_SINGLE_SELECT,
					OptionsConfig: &webui.ColumnFilter_SelectOptionsConfig{
						SelectOptionsConfig: &webui.SelectOptionsConfig{
							SelectOptions: palPkg.GetDisposition2Categories(),
						},
					},
				},
			},
			{
				Label:     "Disposition 3",
				HeaderKey: disposition3,
				IsVisible: true,
				ColumnFilter: &webui.ColumnFilter{
					FilterType: webui.ColumnFilterType_COLUMN_FILTER_TYPE_SINGLE_SELECT,
					OptionsConfig: &webui.ColumnFilter_SelectOptionsConfig{
						SelectOptionsConfig: &webui.SelectOptionsConfig{
							SelectOptions: palPkg.GetDisposition3Categories(),
						},
					},
				},
			},
			{
				Label:     "Stage",
				HeaderKey: dropOffStage,
				IsVisible: true,
				ColumnFilter: &webui.ColumnFilter{
					FilterType: webui.ColumnFilterType_COLUMN_FILTER_TYPE_SINGLE_SELECT,
					OptionsConfig: &webui.ColumnFilter_SelectOptionsConfig{
						SelectOptionsConfig: &webui.SelectOptionsConfig{
							SelectOptions: palPkg.GetDropOffStages(),
						},
					},
				},
			},
			{
				Label:     "Acquisition Channel",
				HeaderKey: acquisitionChannel,
				IsVisible: true,
				ColumnFilter: &webui.ColumnFilter{
					FilterType: webui.ColumnFilterType_COLUMN_FILTER_TYPE_SINGLE_SELECT,
					OptionsConfig: &webui.ColumnFilter_SelectOptionsConfig{
						SelectOptionsConfig: &webui.SelectOptionsConfig{
							SelectOptions: palPkg.GetAcquisitionChannels(),
						},
					},
				},
			},
		},
		TableRows: tableRows,
	}

	return &cxPalPb.GetLoanOutCallAgentAssignedTicketsResponse{
		Status:      rpcPb.StatusOk(),
		Tickets:     ticketsTable,
		PageContext: ticketsResp.GetPageContextResponse(),
	}, nil
}

// GetLoanOutCallTicketHistory RPC to get loans out calling ticket history of the user
func (s *Service) GetLoanOutCallTicketHistory(ctx context.Context, req *cxPalPb.GetLoanOutCallTicketHistoryRequest) (*cxPalPb.GetLoanOutCallTicketHistoryResponse, error) {
	// Get all tickets for the user
	ticketsResp, err := s.ticketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			IdentifierType:  ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_EMAIL,
			IdentifierValue: req.GetHeader().GetTicket().GetRequester().GetEmail(),
			FromTime:        timestampPb.New(time.Now().Add(-time.Hour * 24 * 30)),
			StatusList:      ticketPb.GetActiveStatusList(),
		},
	})
	if te := epifigrpc.RPCError(ticketsResp, err); te != nil && !rpcPb.StatusFromError(te).IsRecordNotFound() {
		cxLogger.Error(ctx, "error while fetching ticket history", zap.Error(te))
		return &cxPalPb.GetLoanOutCallTicketHistoryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch ticket history"),
		}, nil
	}

	// Convert these tickets to table rows
	tickets := make([]*cxPalPb.WebComponent, 0)
	for _, ticket := range ticketsResp.GetTickets() {
		tickets = append(tickets, &cxPalPb.WebComponent{
			Details: &cxPalPb.WebComponent_KeyValueComponent{
				KeyValueComponent: &webui.Section{
					Title: "Ticket History",
					LabelValues: []*webui.LabelValueV2{
						{
							Label:    "Ticket Id",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetId()),
							},
						},
						{
							Label:    "Ticket Created At",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetCreatedAt().AsTime().In(datetime.IST).String()),
							},
						},
						{
							Label:    "Ticket Status",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetStatus().String()),
							},
						},
						{
							Label:    "Ticket Subject",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetSubject()),
							},
						},
						{
							Label:    "Ticket Description",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetDescription()),
							},
						},
						{
							Label:    "Disposition 1",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition1()),
							},
						},
						{
							Label:    "Disposition 2",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition2()),
							},
						},
						{
							Label:    "Disposition 3",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition3()),
							},
						},
						{
							Label:    "Disposition 4",
							DataType: webui.LabelValueV2_DATA_TYPE_STRING,
							Value: &webui.LabelValueV2_StringValue{
								StringValue: fmt.Sprintf("%v", ticket.GetCustomFields().GetLoanOutcallMetadata().GetDisposition4()),
							},
						},
					},
				},
			},
		})
	}

	return &cxPalPb.GetLoanOutCallTicketHistoryResponse{
		Status:  rpcPb.StatusOk(),
		Tickets: tickets,
	}, nil
}

// GetUserProfileInfo RPC to get the user profile info in unmasked form
func (s *Service) GetUserProfileInfo(ctx context.Context, req *cxPalPb.GetUserProfileInfoRequest) (*cxPalPb.GetUserProfileInfoResponse, error) {
	// Get user details
	userResp, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: req.GetHeader().GetActor().GetId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching user details", zap.Error(te))
		return &cxPalPb.GetUserProfileInfoResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch user details"),
		}, nil
	}

	return &cxPalPb.GetUserProfileInfoResponse{
		Components: []*cxPalPb.WebComponent{
			{
				Details: &cxPalPb.WebComponent_KeyValueComponent{
					KeyValueComponent: &webui.Section{
						Title: "User Information",
						LabelValues: []*webui.LabelValueV2{
							{
								Label:    "Name",
								DataType: webui.LabelValueV2_DATA_TYPE_STRING,
								Value: &webui.LabelValueV2_StringValue{
									StringValue: fmt.Sprintf("%v", userResp.GetUser().GetProfile().GetGivenName().ToStringWithHonorific()),
								},
							},
							{
								Label:    "Mobile Number",
								DataType: webui.LabelValueV2_DATA_TYPE_STRING,
								Value: &webui.LabelValueV2_StringValue{
									StringValue: fmt.Sprintf("%v", userResp.GetUser().GetProfile().GetPhoneNumber().ToStringNationalNumber()),
								},
							},
							{
								Label:    "Email ID",
								DataType: webui.LabelValueV2_DATA_TYPE_STRING,
								Value: &webui.LabelValueV2_StringValue{
									StringValue: fmt.Sprintf("%v", userResp.GetUser().GetProfile().GetEmail()),
								},
							},
						},
					},
				},
			},
		},
	}, nil
}
