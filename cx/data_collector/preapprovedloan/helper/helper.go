package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"strings"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	moneyPb "github.com/epifi/be-common/pkg/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2/webui"
)

func GetTableCellWithStringValue(value string) *webui.TableCell {
	return &webui.TableCell{DataType: webui.TableCell_DATA_TYPE_STRING, ValueV2: &webui.TableCell_StringValue{StringValue: value}}
}

func GetTableHeader(header, label string) *webui.TableHeader {
	return &webui.TableHeader{
		Label:     label,
		HeaderKey: header,
		IsVisible: true,
	}
}

func ConvertLrStatusToReadableFormat(status palPb.LoanRequestStatus) string {
	return strings.Replace(status.String(), "LOAN_REQUEST_STATUS_", "", 1)
}

func ConvertLrSubStatusToReadableFormat(subStatus palPb.LoanRequestSubStatus) string {
	if subStatus == palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED {
		return "-"
	}
	return strings.Replace(subStatus.String(), "LOAN_REQUEST_SUB_STATUS_", "", 1)
}

func ConvertMfOfferApprovalStatusToReadableFormat(approvalStatus palPb.MfOfferApprovalStatus) string {
	return strings.Replace(approvalStatus.String(), "MF_OFFER_APPROVAL_STATUS_", "", 1)
}

func ConvertLoanAccountStatusToReadableFormat(accountStatus palPb.LoanAccountStatus) string {
	if accountStatus == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED {
		return "-"
	}
	return strings.Replace(accountStatus.String(), "LOAN_ACCOUNT_STATUS_", "", 1)
}

func ConvertLoanStepNameToReadableFormat(loanStepName palPb.LoanStepExecutionStepName) string {
	return strings.Replace(loanStepName.String(), "LOAN_STEP_EXECUTION_STEP_NAME_", "", 1)
}

func ConvertLseStatusToReadableFormat(lseStatus palPb.LoanStepExecutionStatus) string {
	return strings.Replace(lseStatus.String(), "LOAN_STEP_EXECUTION_STATUS_", "", 1)
}

func ConvertLseSubStatusToReadableFormat(lseSubStatus palPb.LoanStepExecutionSubStatus) string {
	if lseSubStatus == palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED {
		return "-"
	}
	return strings.Replace(lseSubStatus.String(), "LOAN_STEP_EXECUTION_SUB_STATUS_", "", 1)
}

func ConvertLoanPaymentRequestStatusToReadableFormat(lprStatus palPb.LoanPaymentRequestStatus) string {
	return strings.Replace(lprStatus.String(), "LOAN_PAYMENT_REQUEST_STATUS_", "", 1)
}

func ConvertLoanPaymentRequestTypeToReadableFormat(lprType palPb.LoanPaymentRequestType) string {
	return strings.Replace(lprType.String(), "LOAN_PAYMENT_REQUEST_TYPE_", "", 1)
}

func ConvertTimestampToReadableFormat(timestamp *timestampPb.Timestamp) string {
	if timestamp == nil {
		return "-"
	}
	return datetime.TimestampToString(timestamp, "02-Jan-2006 15:04:05", datetime.IST)
}

func ConvertDateToReadableFormat(date *date.Date) string {
	return datetime.DateToString(date, "02-Jan-2006", datetime.IST)
}

func ConvertMoneyToReadableFormat(val *money.Money) string {
	return moneyPb.ToDisplayStringInIndianFormat(val, 2, false)
}

func MaskPhoneNumber(phone *commontypes.PhoneNumber) string {
	if phone == nil {
		return "NA"
	}
	first6Digits := phone.GetNationalNumber() / 10000
	return fmt.Sprintf("%dxxxx", first6Digits)
}

func MaskEmail(email string) string {
	if email == "" {
		return "NA"
	}
	emailArr := strings.Split(email, "@")
	if len(emailArr) == 1 {
		// invalid email id
		return email
	}
	local := emailArr[0]
	if len(local) > 2 {
		local = string(local[0]) + strings.Repeat("x", len(local)-2) + string(local[len(local)-1])
	}
	domain := emailArr[1]
	domainParts := strings.Split(domain, ".")
	if len(domainParts) < 2 {
		// invalid domain part
		return local + "@" + domain
	}

	domainName := domainParts[0]
	domainLen := len(domainName)
	if domainLen > 2 {
		domainName = string(domainName[0]) + strings.Repeat("x", domainLen-2) + string(domainName[domainLen-1])
	}

	return local + "@" + domainName + "." + strings.Join(domainParts[1:], ".")
}
