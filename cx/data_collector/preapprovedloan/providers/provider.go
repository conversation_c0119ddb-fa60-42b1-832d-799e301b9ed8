package providers

import (
	"context"
	"fmt"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type DataProvider interface {
	GetLoanRequestDetails(ctx context.Context, vendor palPb.Vendor, loanProgram palPb.LoanProgram, loanRequestId string) (*palCxPb.GetLoanRequestAdditionalDetailsResponse, error)
	GetLoanAccountDetails(ctx context.Context, vendor palPb.Vendor, loanProgram palPb.LoanProgram, loanAccountId string) (*palCxPb.GetLoanAccountAdditionalDetailsResponse, error)
}

type BaseDataProvider struct {
	palCxClient palCxPb.CxClient
}

func NewBaseDataProvider(palCxClient palCxPb.CxClient) *BaseDataProvider {
	return &BaseDataProvider{
		palCxClient: palCxClient,
	}
}

func (b *BaseDataProvider) GetLoanRequestDetails(ctx context.Context, vendor palPb.Vendor, loanProgram palPb.LoanProgram, loanRequestId string) (*palCxPb.GetLoanRequestAdditionalDetailsResponse, error) {
	lrResp, err := b.palCxClient.GetLoanRequestAdditionalDetails(ctx, &palCxPb.GetLoanRequestAdditionalDetailsRequest{
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: loanProgram,
			Vendor:      vendor,
		},
		LoanRequestId: loanRequestId,
		FieldMasks: []palCxPb.LoanRequestAdditionalDetailsFieldMask{
			palCxPb.LoanRequestAdditionalDetailsFieldMask_LOAN_REQUEST_ADDITIONAL_DETAILS_FIELD_MASK_LOAN_STEP_EXECUTIONS,
		},
	})
	if rpcErr := epifigrpc.RPCError(lrResp, err); rpcErr != nil {
		return nil, fmt.Errorf("error in GetLoanRequestAdditionalDetails rpc: %w", err)
	}

	return lrResp, nil
}

func (b *BaseDataProvider) GetLoanAccountDetails(ctx context.Context, vendor palPb.Vendor, loanProgram palPb.LoanProgram, loanAccountId string) (*palCxPb.GetLoanAccountAdditionalDetailsResponse, error) {
	laResp, err := b.palCxClient.GetLoanAccountAdditionalDetails(ctx, &palCxPb.GetLoanAccountAdditionalDetailsRequest{
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: loanProgram,
			Vendor:      vendor,
		},
		AccountId: loanAccountId,
		FieldMasks: []palCxPb.LoanAccountAdditionalDetailsFieldMask{
			palCxPb.LoanAccountAdditionalDetailsFieldMask_LOAN_ACCOUNT_ADDITIONAL_DETAILS_FIELD_MASK_LOAN_INSTALLMENT_PAYOUTS,
			palCxPb.LoanAccountAdditionalDetailsFieldMask_LOAN_ACCOUNT_ADDITIONAL_DETAILS_FIELD_MASK_LOAN_PAYMENT_REQUESTS,
			palCxPb.LoanAccountAdditionalDetailsFieldMask_LOAN_ACCOUNT_ADDITIONAL_DETAILS_FIELD_MASK_LOAN_ACCOUNT_CLOSURE_REQUESTS,
		},
	})
	if rpcErr := epifigrpc.RPCError(laResp, err); rpcErr != nil {
		return nil, fmt.Errorf("error in GetLoanAccountAdditionalDetails rpc: %w", rpcErr)
	}

	return laResp, nil
}
