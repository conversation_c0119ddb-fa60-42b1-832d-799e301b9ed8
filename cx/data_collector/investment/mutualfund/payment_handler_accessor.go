package mutualfund

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	"github.com/epifi/be-common/pkg/logger"
)

type GetPaymentDetailsRequest struct {
	OrderId     string
	PaymentMode payment_handler.PaymentMode
}

func (s *Service) GetPaymentDetails(ctx context.Context, getPaymentDetailsRequests []*GetPaymentDetailsRequest) (map[string]*payment_handler.GetPaymentDetailsResponse, error) {

	/*
		ToDo(Junaid): Use a batch rpc to fetch payment details for an array of orders once pay team exposes a batch rpc.
			Tracked here -> https://monorail.pointz.in/p/fi-app/issues/detail?id=15661
	*/

	var paymentDetailsMap = make(map[string]*payment_handler.GetPaymentDetailsResponse)

	for _, getPaymentDetailsRequest := range getPaymentDetailsRequests {
		response, err := s.paymentHandlerClient.GetPaymentDetails(ctx, &payment_handler.GetPaymentDetailsRequest{
			OrderId:     getPaymentDetailsRequest.OrderId,
			PaymentMode: getPaymentDetailsRequest.PaymentMode,
		})
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error while invoking GetPaymentDetails for orderId: %s with err: %s", getPaymentDetailsRequest.OrderId, err))
			paymentDetailsMap[getPaymentDetailsRequest.OrderId] = nil
			continue
		}

		if !response.Status.IsSuccess() {
			logger.Error(ctx, fmt.Sprintf("error while invoking GetPaymentDetails for orderId: %s with code: %d, shortmessage: %s and debugMessage: %s",
				getPaymentDetailsRequest.OrderId, response.Status.Code, response.Status.ShortMessage,
				response.Status.DebugMessage))
			paymentDetailsMap[getPaymentDetailsRequest.OrderId] = nil
			continue
		}

		paymentDetailsMap[getPaymentDetailsRequest.OrderId] = response
	}

	return paymentDetailsMap, nil
}
