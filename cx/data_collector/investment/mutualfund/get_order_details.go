package mutualfund

import (
	"context"
	"fmt"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxMfPb "github.com/epifi/gamma/api/cx/data_collector/investment/mutualfund"
	beSvc "github.com/epifi/gamma/api/investment/mutualfund/order"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/pkg/investment"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	ISTTimeZoneLocation = "Asia/Kolkata"
)

var rtaRejectionFailureReasonToStringMap = map[beSvc.FailureReason]string{
	beSvc.FailureReason_RTA_REJECTION_PAYMENT_FAILURE:                    "Payment failed",
	beSvc.FailureReason_RTA_REJECTION_ORDER_CANCELLED_VIA_REQUEST:        "No amount deducted, some other issue so order was cancelled",
	beSvc.FailureReason_RTA_REJECTION_KYC_NOT_PROVIDED:                   "Issue with KYC, please contact devs",
	beSvc.FailureReason_RTA_REJECTION_KYC_NOT_VERIFIED:                   "Issue with KYC, please contact devs",
	beSvc.FailureReason_RTA_REJECTION_UNITS_UNAVAILABLE:                  "No units available in the folio",
	beSvc.FailureReason_RTA_REJECTION_THIRD_PART_VALIDATION_FAILURE:      "Could not verify the destination account for withdrawal",
	beSvc.FailureReason_RTA_REJECTION_DOCUMENTS_NOT_RECEIVED:             "Elog/FATCA not provided, contact devs to ensure its provided. This order cannot be retried",
	beSvc.FailureReason_RTA_REJECTION_UNITS_UNDER_LOCK_IN_PERIOD:         "Units are under lock in period",
	beSvc.FailureReason_RTA_REJECTION_PARTIAL_UNITS_UNDER_LOCK_IN_PERIOD: "No units available in the folio",
	beSvc.FailureReason_RTA_REJECTION_TRANSACTION_AMOUNT_BELOW_LIMITS:    "Transaction amount is below minimum allowed value as per fund house",
}

var rtaValidationFailureReasonToStringMap = map[beSvc.FailureReason]bool{
	beSvc.FailureReason_RTA_VALIDATION_FAILURE:                                                                    true,
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_INVESTOR_DOB_LESS_THAN_18_YEARS:                  true,
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_NOMINEE_AGE_IS_LESS_THAN_18:                      true,
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_MINIMUM_AMOUNT_VALIDATION_FAILED:                 true,
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_MISSING_UNITS_OR_AMOUNT_FOR_REDEMPTION_OR_SWITCH: true,
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_SIP_NEW_FLAG_MISSING:                             true,
	beSvc.FailureReason_RTA_REJECTION_DOCUMENTS_NOT_RECEIVED:                                                      true,
	beSvc.FailureReason_RTA_REJECTION_UNITS_UNDER_LOCK_IN_PERIOD:                                                  true,
	beSvc.FailureReason_RTA_REJECTION_PARTIAL_UNITS_UNDER_LOCK_IN_PERIOD:                                          true,
	beSvc.FailureReason_RTA_REJECTION_TRANSACTION_AMOUNT_BELOW_LIMITS:                                             true,
}

var orderFailureReasonToStringMap = map[beSvc.FailureReason]string{
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_DISALLOWED_TRANSACTION:                   "Payment Failure: Disallowed Transaction",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_MAXIMUM_TXN_AMOUNT_LIMIT_BREACHED:        "Payment Failure: Transaction Amount Limit Breached",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_PAYER_ACCOUNT_FROZEN:                     "Payment Failure: Payer Account Frozen",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_PAYER_ACCOUNT_CLOSED:                     "Payment Failure: Payer Account Closed",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_PAYER_DEBIT_FROZEN:                       "Payment Failure: Payer Account Frozen",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_PAYER_ACCOUNT_DORMANT:                    "Payment Failure: Payer Account Dormant",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_PAYER_ACCOUNT_INACTIVE:                   "Payment Failure: Payer Account Inactive",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_TXN_AMOUNT_NOT_ALLOWED:                   "Payment Failure: Transaction Amount Not Allowed",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_AMOUNT_LIMIT_PER_PERIOD_BREACHED:         "Payment Failure: Amount Limit Breached",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_INVALID_OTP:                              "Payment Failure: Invalid OTP",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_AMC_ACCOUNT_SERVER_DOWN:                  "Payment Failure: Amc Account Server Down",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_INVALID_AMC_ACCOUNT:                      "Payment Failure: Invalid AMC Account",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_PAYMENT_REJECTED_BY_RBI:                  "Payment Failure: Payment Rejected By RBI",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_NEFT_PROCESS_CLOSED:                      "Payment Failure: NEFT Process Closed",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_INTERNAL_SERVER_ERROR:                    "Payment Failure: Internal Server Error",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_INVALID_CRED_BLOCK:                       "Payment Failure: Invalid Cred Block",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_TRANSACTION_PIN_NOT_SET:                  "Payment Failure: Transaction PIN Not Set",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_INVALID_PIN:                              "Payment Failure: Invalid PIN",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_TRANSACTION_NUMBER_OF_PIN_TRIES_EXCEEDED: "Payment Failure: Number Of PIN Retries Exceeded",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_NO_DATA_FOUND:                            "Payment Failure: No Data Found",
	beSvc.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_INSUFFICIENT_BALANCE:                                   "Payment Failure: Insufficient Balance",
}

func (s *Service) GetOrderDetails(ctx context.Context, req *cxMfPb.GetOrderDetailsRequest) (
	*cxMfPb.GetOrderDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxMfPb.GetOrderDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	actorId := req.GetHeader().GetActor().GetId()
	orderId := req.GetOrderId()
	getOrderReq := &beSvc.GetOrderDetailsRequest{
		Id: &beSvc.GetOrderDetailsRequest_OrderId{OrderId: orderId},
	}
	beOrder, beErr := s.orderManagerClient.GetOrderDetails(ctx, getOrderReq)
	if err2 := epifigrpc.RPCError(beOrder, beErr); err2 != nil {
		logger.Error(ctx, "Error while getting order details ",
			zap.String(logger.ORDER_ID, orderId),
			zap.String(logger.ACTOR_ID, actorId), zap.Error(err2))
		return &cxMfPb.GetOrderDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("Error while getting order details")}, nil
	}
	return s.getOrderDetailsResponse(ctx, beOrder), nil
}

func (s *Service) getOrderDetailsResponse(ctx context.Context, beOrder *beSvc.GetOrderDetailsResponse) *cxMfPb.GetOrderDetailsResponse {
	res := &cxMfPb.GetOrderDetailsResponse{
		Status:           rpcPb.StatusOk(),
		OrderDetails:     nil,
		SherlockDeepLink: nil,
	}
	timeline, err := s.getOrderTimeLine(ctx, beOrder)
	if err != nil {
		logger.Error(ctx, "Error while getting order timeline ",
			zap.String(logger.ORDER_ID, beOrder.Order.Id), zap.Error(err))
	}
	res.OrderDetails = &cxMfPb.OrderDetails{OrderTimeline: timeline}
	return res
}

func (s *Service) getOrderTimeLine(ctx context.Context, beResp *beSvc.GetOrderDetailsResponse) ([]*cxMfPb.OrderStatusUpdate, error) {
	orderTimeline := investment.NewOrderTimeline(beResp.Order, beResp.OrderStatusTimeline, beResp.PaymentStatus,
		beResp.TransactionTime, beResp.MutualFund)
	timeline, err := orderTimeline.GetTimelineFromStatusUpdates()
	if err != nil {
		logger.Error(ctx, "unable to get user timeline", zap.Error(err))
		return nil, err
	}
	return s.convertToCxTimeline(ctx, timeline, beResp.Order)
}

func (s *Service) convertToCxTimeline(ctx context.Context, orderTimeLine investment.Timeline, order *beSvc.Order) ([]*cxMfPb.OrderStatusUpdate, error) {
	timeline := make([]*cxMfPb.OrderStatusUpdate, len(orderTimeLine))
	for i := 0; i < len(orderTimeLine); i++ {
		stepTimestamp, err := getTimeStampFromOrderTimeline(orderTimeLine[i], order)
		if err != nil {
			logger.Error(ctx, "unable to get timestamp for step",
				zap.String(logger.ORDER_ID, order.Id))
			return nil, err
		}
		timeline[i] = &cxMfPb.OrderStatusUpdate{
			Timestamp:          stepTimestamp,
			TimelineStep:       orderTimeLine[i].OrderStepName,
			StepStatus:         orderTimeLine[i].StepStatus.String(),
			InternalStatus:     orderTimeLine[i].InternalOrderStatus.String(),
			OrderFailureReason: s.getFailureReasonFromOrderTimeline(ctx, orderTimeLine[i], order),
		}
	}
	return timeline, nil
}

func (s *Service) getFailureReason(ctx context.Context, timelineEntry *investment.OrderTimelineEntry, order *beSvc.Order) string {
	if order.OrderStatus == beSvc.OrderStatus_PRE_PROCESSING || order.FailureReason == beSvc.FailureReason_PRE_REQUISITE_KRA_FAILED {
		res, err := s.wobClient.GetInvestmentDataV2(ctx, &wob.GetInvestmentDataV2Request{
			ActorIds: []string{order.ActorId}})
		if te := epifigrpc.RPCError(res, err); te != nil {
			logger.Error(ctx, "error in GetInvestmentDataV2", zap.String(logger.ORDER_ID, order.Id), zap.Error(te))
			return ""
		}
		if res.InvestmentDetailInfo[order.ActorId] != nil {
			return res.InvestmentDetailInfo[order.ActorId].FailureReason.String()
		} else {
			return ""
		}
	}

	// For In Payment orders, we need to check if payment is stuck in manual intervention and return  appropriate reason if needed.
	// Add support to autoInvest/SIP orders as well once recurring payment service provides failure status.
	if order.OrderStatus == beSvc.OrderStatus_IN_PAYMENT && order.OrderSubType == beSvc.OrderSubType_BUY_ONE_TIME_INVEST {
		res, err := s.paymentHandlerClient.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
			OrderId:     order.Id,
			PaymentMode: phPb.PaymentMode_P2P_TRANSFER,
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			logger.Error(ctx, "error in GetPaymentDetails", zap.String(logger.ORDER_ID, order.Id), zap.Error(te))
			return ""
		}
		return res.FailureMessage
	}

	if order.OrderStatus == beSvc.OrderStatus_FAILURE {
		// Display message if rta rejection failure
		reason, ok := rtaRejectionFailureReasonToStringMap[order.FailureReason]
		if ok {
			return reason
		}

		// if order failed because of rta validation failure, then return the debug reason directly.
		_, ok = rtaValidationFailureReasonToStringMap[order.FailureReason]
		if ok {
			return order.FailureDebugReason
		}

		reason, ok = orderFailureReasonToStringMap[order.FailureReason]
		if ok {
			return reason
		}
	}

	return s.getFailureReasonFromOrderTimeline(ctx, timelineEntry, order)
}

func (s *Service) getFailureReasonFromOrderTimeline(ctx context.Context, timelineEntry *investment.OrderTimelineEntry, order *beSvc.Order) string {

	if timelineEntry.FailedInfo == nil {
		return ""
	}

	if len(timelineEntry.FailedInfo.FailureReason.String()) > 0 {
		return timelineEntry.FailedInfo.FailureReason.String()
	}
	return order.FailureReason.String()
}

func getTimeStampFromOrderTimeline(timeLine *investment.OrderTimelineEntry, order *beSvc.Order) (*timestamp.Timestamp, error) {
	switch timeLine.StepStatus {
	case investment.OrderStepStatus_SUCCESS:
		if timeLine.SuccessInfo == nil {
			return nil, fmt.Errorf("SuccessInfo nil for successful step")
		}
		return timeLine.SuccessInfo.SuccessTimestamp, nil
	case investment.OrderStepStatus_PENDING:
		timeZoneLocation, err := time.LoadLocation(ISTTimeZoneLocation)
		if err != nil {
			return nil, fmt.Errorf("error while trying to load IST time zone: %w", err)
		}
		if timeLine.PendingInfo == nil {
			return nil, fmt.Errorf("PendingInfo nil for pending step")
		}
		return timestampPb.New(order.UpdatedAt.AsTime().In(timeZoneLocation)), nil
	case investment.OrderStepStatus_FAILED:
		if timeLine.FailedInfo == nil {
			return nil, fmt.Errorf("FailedInfo nil for failed step")
		}
		return timeLine.FailedInfo.FailedTimestamp, nil
	default:
		return nil, fmt.Errorf("invalid step status: %v", timeLine.StepStatus)
	}
}
