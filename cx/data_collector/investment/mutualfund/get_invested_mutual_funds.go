package mutualfund

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
	cxMfPb "github.com/epifi/gamma/api/cx/data_collector/investment/mutualfund"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	fitttRulePb "github.com/epifi/gamma/api/rms/manager"
	types "github.com/epifi/gamma/api/typesv2"
	cxLogger "github.com/epifi/gamma/cx/logger"
	rmsPkg "github.com/epifi/gamma/pkg/rms"
)

const (
	RmsPurchaseAmount      = "purchaseAmount"
	RmsConfiguredDayOfWeek = "configuredDayOfWeek"
	RmsMutualFundVal       = "mutualFundVal"
)

func (s *Service) GetInvestedMutualFunds(ctx context.Context, req *cxMfPb.GetInvestedMutualFundsRequest) (
	*cxMfPb.GetInvestedMutualFundsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxMfPb.GetInvestedMutualFundsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	actorId := req.GetHeader().GetActor().GetId()
	pageCtx := req.GetPageContext()
	filters := getFiltersFromGetInvestedMutualFundsRequest(req)
	invDigestReq := &catalogPb.GetInvestmentsRequest{
		ActorId:     actorId,
		PageContext: pageCtx,
		Filters:     filters,
	}
	beResp, beErr := s.catalogClient.GetInvestments(ctx, invDigestReq)
	if err2 := epifigrpc.RPCError(beResp, beErr); err2 != nil {
		logger.Error(ctx, "Error while getting investment digest for actor", zap.String(logger.ACTOR_ID, actorId), zap.Error(err2))
		return &cxMfPb.GetInvestedMutualFundsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching investments for actor")}, nil
	}

	fundIDToTimeStampMap, err := s.getFirstInvestmentDateForFunds(ctx, actorId, beResp.MutualFundsInvestmentInfos)
	if err != nil {
		logger.Error(ctx, "Error in getFirstInvestmentDateForFunds", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		return &cxMfPb.GetInvestedMutualFundsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching investments for actor")}, nil
	}

	allActiveRuleSubscriptions, actRuleErr := s.GetAllFitRuleSubscriptionsForMutualFundsByActor(ctx, actorId)
	if actRuleErr != nil {
		cxLogger.Error(ctx, "error while fetching fit rule subscriptions", zap.String(logger.ACTOR_ID, actorId), zap.Error(actRuleErr))
		return &cxMfPb.GetInvestedMutualFundsResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching fit rule subscriptions")}, nil
	}

	mfRuleSubMap := s.getMutualFundSubscriptions(allActiveRuleSubscriptions)

	return s.getInvestedMutualFundsResponse(beResp.GetMutualFundsInvestmentInfos(), mfRuleSubMap, beResp.GetPageContext(), fundIDToTimeStampMap), nil

}

func getFiltersFromGetInvestedMutualFundsRequest(req *cxMfPb.GetInvestedMutualFundsRequest) []*catalogPb.Filter {
	filters := make([]*catalogPb.Filter, 0)
	if req.Amc != cxMfPb.Amc_AMC_UNSPECIFIED {
		filters = append(filters, &catalogPb.Filter{
			Comparator:  catalogPb.Filter_EQUAL,
			FilterField: catalogPb.FilterFieldMask_AMC,
			FilterValue: &catalogPb.Filter_AmcVal{
				AmcVal: mfPb.Amc(mfPb.Amc_value[req.Amc.String()]),
			},
		})
	}
	if req.FromDate != nil {
		filters = append(filters, &catalogPb.Filter{
			Comparator:  catalogPb.Filter_GREATER_OR_EQUAL,
			FilterField: catalogPb.FilterFieldMask_CREATED_AT,
			FilterValue: &catalogPb.Filter_TimeVal{TimeVal: req.FromDate},
		})
	}
	if req.ToDate != nil {
		filters = append(filters, &catalogPb.Filter{
			Comparator:  catalogPb.Filter_SMALLER_OR_EQUAL,
			FilterField: catalogPb.FilterFieldMask_CREATED_AT,
			FilterValue: &catalogPb.Filter_TimeVal{TimeVal: req.ToDate},
		})
	}
	return filters
}

func (s *Service) getMutualFundSubscriptions(subscriptions map[string]*fitttRulePb.Subscriptions) map[string][]*fitttRulePb.RuleSubscription {
	mfRuleSubMap := make(map[string][]*fitttRulePb.RuleSubscription)
	for _, subs := range subscriptions {
		for _, sub := range subs.GetRuleSubscriptions() {
			if sub.GetState() == fitttRulePb.RuleSubscriptionState_ACTIVE {
				for _, val := range sub.GetRuleParamValues().RuleParamValues {
					mfVal := val.GetMutualFundVal()
					if mfVal != nil {
						mfRuleSubMap[mfVal.GetMfId()] = append(mfRuleSubMap[mfVal.GetMfId()], sub)
					}
				}
			}
		}
	}
	return mfRuleSubMap
}

func (s *Service) getSubscriptionText(sub *fitttRulePb.RuleSubscription) string {
	var purchaseAmt *types.Money
	if _, ok := sub.GetRuleParamValues().GetRuleParamValues()[RmsMutualFundVal]; ok {
		if _, ok := sub.GetRuleParamValues().GetRuleParamValues()[RmsPurchaseAmount]; ok {
			purchaseAmt = sub.GetRuleParamValues().GetRuleParamValues()[RmsPurchaseAmount].GetMoneyVal()
		}
		if _, ok := sub.GetRuleParamValues().GetRuleParamValues()[rmsPkg.RmsConfiguredDateOfMonth]; ok {
			configuredDate := sub.GetRuleParamValues().GetRuleParamValues()[rmsPkg.RmsConfiguredDateOfMonth].GetIntVal()
			return fmt.Sprintf("Invest %v on %v of every month", money.ToDisplayStringWithPrecision(purchaseAmt.GetBeMoney(), 0), configuredDate)
		}
		if _, ok := sub.GetRuleParamValues().GetRuleParamValues()[RmsConfiguredDayOfWeek]; ok {
			configuredDay := sub.GetRuleParamValues().GetRuleParamValues()[RmsConfiguredDayOfWeek].GetStrVal()
			return fmt.Sprintf("Invest %v on every %v", money.ToDisplayStringWithPrecision(purchaseAmt.GetBeMoney(), 0), configuredDay)
		}
		return fmt.Sprintf("Invest %v Daily", money.ToDisplayStringWithPrecision(purchaseAmt.GetBeMoney(), 0))
	}
	return ""
}

func (s *Service) getInvestedMutualFundsResponse(infos []*catalogPb.MutualFundInvestmentInfo,
	subMap map[string][]*fitttRulePb.RuleSubscription, pageContext *rpcPb.PageContextResponse, fundIDToTimeStampMap map[string]*timestampPb.Timestamp) *cxMfPb.GetInvestedMutualFundsResponse {
	feResp := &cxMfPb.GetInvestedMutualFundsResponse{
		Status:           rpcPb.StatusOk(),
		InvestedFunds:    nil,
		SherlockDeepLink: nil,
		PageContext:      pageContext,
	}
	investedFunds := make([]*cxMfPb.InvestedFund, len(infos))
	for i, info := range infos {
		investedFunds[i] = &cxMfPb.InvestedFund{
			MutualFundName:       info.GetMutualFundName(),
			AssetClass:           s.getCXAssetClass(info),
			AutoInvestDetails:    s.getAutoInvestDetails(subMap, info.GetMutualFundId()),
			LastTransactionDate:  info.GetLastTransactionDate(),
			FundStatus:           cxMfPb.MutualFundInternalStatus(cxMfPb.MutualFundInternalStatus_value[info.GetInternalFundStatus().String()]),
			MutualFundId:         info.GetMutualFundId(),
			FirstTransactionDate: fundIDToTimeStampMap[info.GetMutualFundId()],
		}
	}
	feResp.InvestedFunds = investedFunds
	return feResp
}

func (s *Service) getCXAssetClass(info *catalogPb.MutualFundInvestmentInfo) cxMfPb.AssetClass {
	if info.GetCategoryName() == mfPb.MutualFundCategoryName_ELSS_TAX_SAVING {
		return cxMfPb.AssetClass_ELSS_TAX_SAVING
	}
	switch info.GetAssetClass() {
	case mfPb.AssetClass_DEBT:
		return cxMfPb.AssetClass_DEBT
	case mfPb.AssetClass_EQUITY:
		return cxMfPb.AssetClass_EQUITY
	case mfPb.AssetClass_CASH:
		return cxMfPb.AssetClass_CASH
	case mfPb.AssetClass_HYBRID:
		return cxMfPb.AssetClass_HYBRID
	default:
		logger.ErrorNoCtx("invalid asset class", zap.String(logger.MF_ASSET_CLASS, info.GetAssetClass().String()))
		return cxMfPb.AssetClass_ASSET_CLASS_UNSPECIFIED
	}
}

func (s *Service) getAutoInvestDetails(mfSubMap map[string][]*fitttRulePb.RuleSubscription, mfId string) string {
	resStr := ""
	for _, ruleSub := range mfSubMap[mfId] {
		resStr += s.getSubscriptionText(ruleSub) + ", "
	}
	if len(resStr) > 2 {
		resStr = resStr[:len(resStr)-2]
	}
	return resStr
}

func (s *Service) getFirstInvestmentDateForFunds(ctx context.Context, actorID string, fundInfos []*catalogPb.MutualFundInvestmentInfo) (map[string]*timestampPb.Timestamp, error) {
	token := &pagination.PageToken{IsReverse: true, Offset: 0, Timestamp: nil}
	tokenStr, err := token.Marshal()
	if err != nil {
		return nil, err
	}

	fundIDToTimeStampMap := make(map[string]*timestampPb.Timestamp)

	for _, val := range fundInfos {
		beRes, beErr := s.orderManagerClient.GetOrdersByFundIDAndActorID(ctx, &orderPb.GetOrdersByFundIDAndActorIDRequest{
			ActorId:      actorID,
			MutualFundId: val.MutualFundId,
			PageContext: &rpcPb.PageContextRequest{
				Token:    &rpcPb.PageContextRequest_AfterToken{AfterToken: tokenStr},
				PageSize: 1,
			},
			Filters: nil,
		})
		if err2 := epifigrpc.RPCError(beRes, beErr); err2 != nil {
			logger.Error(ctx, "error in GetOrdersByFundIDAndActorID", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorID), zap.String(logger.MF_ID, val.MutualFundId))
			fundIDToTimeStampMap[val.MutualFundId] = nil
			continue
		}
		if len(beRes.Orders) > 0 {
			fundIDToTimeStampMap[val.MutualFundId] = beRes.Orders[0].CreatedAt
		} else {
			// This should never happen
			logger.Error(ctx, "no order found in GetOrdersByFundIDAndActorID", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorID), zap.String(logger.MF_ID, val.MutualFundId))
			fundIDToTimeStampMap[val.MutualFundId] = nil
		}
	}
	return fundIDToTimeStampMap, nil

}
