package mutualfund

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxMfPb "github.com/epifi/gamma/api/cx/data_collector/investment/mutualfund"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"

	fitttRulePb "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
)

type Service struct {
	cxMfPb.UnimplementedInvestmentServer
	catalogClient        catalogPb.CatalogManagerClient
	fitRmsClient         fitttRulePb.RuleManagerClient
	orderManagerClient   orderPb.OrderManagerClient
	authEngine           auth_engine.IAuthEngine
	paymentHandlerClient phPb.PaymentHandlerClient
	wobClient            wob.WealthOnboardingClient
}

func NewInvestmentService(catalogClient catalogPb.CatalogManagerClient, fitRmsClient fitttRulePb.RuleManagerClient,
	orderManagerClient orderPb.OrderManagerClient, authEngine auth_engine.IAuthEngine, paymentHandlerClient phPb.PaymentHandlerClient,
	wobClient wob.WealthOnboardingClient) *Service {
	return &Service{
		catalogClient:        catalogClient,
		fitRmsClient:         fitRmsClient,
		orderManagerClient:   orderManagerClient,
		authEngine:           authEngine,
		paymentHandlerClient: paymentHandlerClient,
		wobClient:            wobClient,
	}
}

func (s *Service) GetFolioLedgerDetails(ctx context.Context, req *cxMfPb.GetFolioLedgerDetailsRequest) (*cxMfPb.GetFolioLedgerDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxMfPb.GetFolioLedgerDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	mfId := req.GetMutualFundId()
	res, err := s.catalogClient.GetFolioWiseBalance(ctx, &catalogPb.GetFolioWiseBalanceRequest{
		MutualFundId: mfId,
		ActorId:      actorId,
	})
	if err != nil {
		logger.Error(ctx, "error in GetWithdrawalConstraints when called from GetFolioLedgerDetails rpc", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.MF_ID, mfId))
		return &cxMfPb.GetFolioLedgerDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	folioDetailsBE := res.GetFolioDetails()
	var folioDetails []*cxMfPb.FolioDetails
	for _, folio := range folioDetailsBE {
		folioDetails = append(folioDetails, &cxMfPb.FolioDetails{
			MaskedFolioNumber: mask.GetMaskedString(mask.DontMaskLastFourChars, folio.GetFolioNumber()),
			BalanceUnits:      folio.GetBalanceUnits(),
			WithdrawableUnits: folio.GetWithdrawableUnits(),
		})
	}
	return &cxMfPb.GetFolioLedgerDetailsResponse{
		Status:       rpcPb.StatusOk(),
		FolioDetails: folioDetails,
	}, nil
}
