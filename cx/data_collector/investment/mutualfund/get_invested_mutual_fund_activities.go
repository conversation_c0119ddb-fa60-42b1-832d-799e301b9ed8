package mutualfund

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/money"
	cxMfPb "github.com/epifi/gamma/api/cx/data_collector/investment/mutualfund"
	"github.com/epifi/gamma/api/investment/mutualfund"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	cxLogger "github.com/epifi/gamma/cx/logger"
	invPkg "github.com/epifi/gamma/pkg/investment"
)

var (
	paidOrderStates = map[orderPb.OrderStatus]bool{
		orderPb.OrderStatus_NOTIFYING_PAYMENT_CREDIT: true,
		orderPb.OrderStatus_CONFIRMED_BY_RTA:         true,
		orderPb.OrderStatus_PAID:                     true,
		orderPb.OrderStatus_EXPIRED:                  true,
		orderPb.OrderStatus_MANUAL_INTERVENTION:      true,
		orderPb.OrderStatus_FAILURE:                  true,
		orderPb.OrderStatus_IN_FULFILLMENT:           true,
	}
)

func (s *Service) GetInvestedMutualFundActivities(ctx context.Context, req *cxMfPb.GetInvestedMutualFundActivitiesRequest) (
	*cxMfPb.GetInvestedMutualFundActivitiesResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxMfPb.GetInvestedMutualFundActivitiesResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	actorId := req.GetHeader().GetActor().GetId()
	orders := make([]*orderPb.Order, 0)
	var mutualFund *mutualfund.MutualFund
	respPageCtx := &rpcPb.PageContextResponse{
		BeforeToken: "",
		HasBefore:   false,
		AfterToken:  "",
		HasAfter:    false,
	}
	pageCtx := req.GetPageContext()
	switch req.Id.(type) {
	case *cxMfPb.GetInvestedMutualFundActivitiesRequest_OrderId:
		order, mf, err := s.GetOrderAndMutualFundByExtOrderId(ctx, req.GetOrderId())
		if err != nil {
			return &cxMfPb.GetInvestedMutualFundActivitiesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in fetching mutual fund and order"),
			}, nil
		}
		if order != nil {
			orders = append(orders, order)
		}
		mutualFund = mf
	case *cxMfPb.GetInvestedMutualFundActivitiesRequest_MutualFundId:
		mutualFundId := req.GetMutualFundId()
		filters, err := getFiltersFromFundActivitiesRequest(req)
		if err != nil {
			logger.Error(ctx, "error in checking filters from request",
				zap.String(logger.ACTOR_ID, actorId),
				zap.Error(err))
			return &cxMfPb.GetInvestedMutualFundActivitiesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in checking filters from request"),
			}, nil
		}
		odrs, mf, pgCtx, err := s.getOrdersAndMutualFundByFilters(ctx, filters, actorId, mutualFundId, pageCtx)
		if err != nil {
			return &cxMfPb.GetInvestedMutualFundActivitiesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in fetching mutual fund and orders"),
			}, nil
		}
		orders = odrs
		mutualFund = mf
		respPageCtx = pgCtx
	default:
		return &cxMfPb.GetInvestedMutualFundActivitiesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("wrong id type"),
		}, nil
	}

	resp, err := s.getInvestedMutualFundActivitiesResponse(ctx, orders, mutualFund, respPageCtx)
	if err != nil {
		logger.Error(ctx, "error in getting response for GetInvestedMutualFundActivities",
			zap.String(logger.ACTOR_ID, actorId),
			zap.Error(err))
		return &cxMfPb.GetInvestedMutualFundActivitiesResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while generating response")}, nil
	}
	return resp, nil
}
func (s *Service) getOrdersAndMutualFundByFilters(ctx context.Context, filters []*orderPb.Filter, actorId string, fundId string,
	pageContext *rpcPb.PageContextRequest) ([]*orderPb.Order, *mutualfund.MutualFund, *rpcPb.PageContextResponse, error) {
	ordersResponse, err := s.orderManagerClient.GetOrdersByFundIDAndActorID(ctx, &orderPb.GetOrdersByFundIDAndActorIDRequest{
		ActorId:      actorId,
		MutualFundId: fundId,
		PageContext:  pageContext,
		Filters:      filters,
	})
	if err2 := epifigrpc.RPCError(ordersResponse, err); err2 != nil {
		logger.Error(ctx, "error in invoking GetOrdersByFundIDAndActorID",
			zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.MF_ID, fundId),
			zap.Error(err2))
		return nil, nil, nil, err2
	}
	return ordersResponse.GetOrders(), ordersResponse.GetMutualFund(), ordersResponse.GetPageContext(), nil
}

func (s *Service) GetOrderAndMutualFundByExtOrderId(ctx context.Context, extOrderId string) (*orderPb.Order, *mutualfund.MutualFund, error) {
	ordersResponse, err := s.orderManagerClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Id: &orderPb.GetOrderRequest_ExternalOrderId{ExternalOrderId: extOrderId},
	})
	if err2 := epifigrpc.RPCError(ordersResponse, err); err2 != nil {
		logger.Error(ctx, "error in invoking GetOrder",
			zap.String(logger.EXTERNAL_ID, extOrderId),
			zap.Error(err2))
		return nil, nil, err2
	}
	order := ordersResponse.GetOrder()

	mfResp, err := s.catalogClient.GetMutualFund(ctx, &catalogPb.GetMutualFundRequest{
		Id: order.GetMutualFundId(),
	})
	if err2 := epifigrpc.RPCError(mfResp, err); err2 != nil {
		logger.Error(ctx, "error in invoking GetMutualFund",
			zap.String(logger.EXTERNAL_ID, extOrderId),
			zap.String(logger.MF_ID, order.MutualFundId),
			zap.Error(err2))
		return nil, nil, err2
	}
	return order, mfResp.GetMutualFund(), nil
}

func getFiltersFromFundActivitiesRequest(req *cxMfPb.GetInvestedMutualFundActivitiesRequest) ([]*orderPb.Filter, error) {
	filters := make([]*orderPb.Filter, 0)
	switch req.Id.(type) {
	case *cxMfPb.GetInvestedMutualFundActivitiesRequest_OrderId:
		filters = append(filters, &orderPb.Filter{
			Comparator:  orderPb.Filter_EQUAL,
			FilterField: orderPb.FilterFieldMask_EXTERNAL_ORDER_ID,
			FilterValue: &orderPb.Filter_StringVal{StringVal: req.GetOrderId()},
		})
		return filters, nil
	case *cxMfPb.GetInvestedMutualFundActivitiesRequest_MutualFundId:
		if req.FromDate != nil {
			filters = append(filters, &orderPb.Filter{
				Comparator:  orderPb.Filter_GREATER_OR_EQUAL,
				FilterField: orderPb.FilterFieldMask_CREATED_AT,
				FilterValue: &orderPb.Filter_TimeVal{TimeVal: req.FromDate},
			})
		}
		if req.ToDate != nil {
			filters = append(filters, &orderPb.Filter{
				Comparator:  orderPb.Filter_SMALLER_OR_EQUAL,
				FilterField: orderPb.FilterFieldMask_CREATED_AT,
				FilterValue: &orderPb.Filter_TimeVal{TimeVal: req.ToDate},
			})
		}
		if req.TransactionType != cxMfPb.TransactionType_TransactionType_UNSPECIFIED && req.TransactionType != cxMfPb.TransactionType_ALL_TRANSACTIONS {
			txnType, err := getClientTypeFromTransactions(req.TransactionType)
			if err != nil {
				return nil, err
			}
			if req.TransactionType == cxMfPb.TransactionType_WITHDRAWAL {
				filters = append(filters, &orderPb.Filter{
					Comparator:  orderPb.Filter_EQUAL,
					FilterField: orderPb.FilterFieldMask_ORDER_TYPE,
					FilterValue: &orderPb.Filter_OrderTypeVal{OrderTypeVal: orderPb.OrderType_SELL},
				})
			} else {
				filters = append(filters, &orderPb.Filter{
					Comparator:  orderPb.Filter_EQUAL,
					FilterField: orderPb.FilterFieldMask_ORDER_TYPE,
					FilterValue: &orderPb.Filter_OrderTypeVal{OrderTypeVal: orderPb.OrderType_BUY},
				})
			}
			filters = append(filters, &orderPb.Filter{
				Comparator:  orderPb.Filter_EQUAL,
				FilterField: orderPb.FilterFieldMask_ORDER_CLIENT,
				FilterValue: &orderPb.Filter_OrderClientVal{OrderClientVal: txnType},
			})
		}

		if req.GetOrderFilterStatus() != cxMfPb.OrderFilterStatus_OFS_UNSPECIFIED && req.GetOrderFilterStatus() != cxMfPb.OrderFilterStatus_OFS_ALL {
			filters = append(filters, &orderPb.Filter{
				Comparator:  orderPb.Filter_EQUAL,
				FilterField: orderPb.FilterFieldMask_ORDER_CLIENT,
				FilterValue: &orderPb.Filter_OrderStatusVal{OrderStatusVal: getOrderStatusFromFilterStatus(req.OrderFilterStatus)},
			})
		}
	default:
		return nil, fmt.Errorf("request does not have valid inputs to get Id")
	}
	return filters, nil
}

func getClientTypeFromTransactions(transactionType cxMfPb.TransactionType) (orderPb.OrderClient, error) {
	switch transactionType {
	case cxMfPb.TransactionType_AUTO_INVEST:
		return orderPb.OrderClient_FIT, nil
	case cxMfPb.TransactionType_ONE_TIME:
		return orderPb.OrderClient_USER_APP, nil
	case cxMfPb.TransactionType_WITHDRAWAL:
		return orderPb.OrderClient_USER_APP, nil
	default:
		return orderPb.OrderClient_ORDER_CLIENT_UNSPECIFIED, fmt.Errorf("invalid transaction type")
	}
}

func (s *Service) getInvestedMutualFundActivitiesResponse(ctx context.Context, orders []*orderPb.Order, fund *mutualfund.MutualFund, pageContext *rpcPb.PageContextResponse) (*cxMfPb.GetInvestedMutualFundActivitiesResponse, error) {
	res := &cxMfPb.GetInvestedMutualFundActivitiesResponse{
		Status:           rpcPb.StatusOk(),
		Orders:           nil,
		SherlockDeepLink: nil,
		PageContext:      pageContext,
		FundName:         fund.GetNameData().GetDisplayName(),
	}
	paymentRequests := s.getPaymentRequestsFromOrders(orders)
	paymentDetails, err := s.GetPaymentDetails(ctx, paymentRequests)
	if err != nil {
		logger.Error(ctx, "error while fetching payment details", zap.Error(err))
		return nil, err
	}
	fundOrders := make([]*cxMfPb.FundOrder, len(orders))
	for i, order := range orders {
		utrNumber := ""
		if payDetails := paymentDetails[order.Id]; payDetails != nil {
			utrNumber = paymentDetails[order.Id].UtrRefNumber
		}

		rtaConfirmedAmount := ""
		if order.RtaConfirmedAmount != nil {
			rtaConfirmedAmount = money.ToDisplayStringWithoutSymbol(order.RtaConfirmedAmount)
		}

		completionETA := ""
		if order.OrderStatus != orderPb.OrderStatus_CONFIRMED_BY_RTA && order.OrderStatus != orderPb.OrderStatus_SETTLED && order.OrderStatus != orderPb.OrderStatus_FAILURE {
			etaDate, err := invPkg.GetETADate(invPkg.ETAParams{
				PendingOrder:  order,
				AssetClass:    fund.GetAssetClass(),
				PaymentStatus: paymentDetails[order.GetMutualFundId()].GetPaymentStatus(),
				PaymentTime:   paymentDetails[order.GetMutualFundId()].GetTransactionTime(),
				CategoryName:  fund.GetCategoryName(),
			})
			if err != nil {
				logger.Error(ctx, "error while calculating eta", zap.Error(err), zap.String(logger.ORDER_ID, order.GetId()))
				completionETA = "error while calculating eta"
			}
			if etaDate.Before(time.Now()) {
				completionETA = "Processing Delayed"
			} else {
				completionETA = fmt.Sprintf("%s", etaDate.Format("02 January 2006"))
			}
		}

		fundOrders[i] = &cxMfPb.FundOrder{
			CreatedAt:               order.GetCreatedAt(),
			UpdatedAt:               order.GetUpdatedAt(),
			Units:                   order.Units,
			FolioNumber:             mask.GetMaskedString(mask.DontMaskLastFourChars, order.FolioId),
			TransactionType:         s.getTransactionType(order),
			ActivityStatus:          s.getCxActivityStatus(order.OrderStatus, order.OrderType),
			UtrNumber:               utrNumber,
			ExternalOrderId:         order.GetExternalOrderId(),
			VendorOrderId:           order.GetVendorOrderId(),
			OrderId:                 order.GetId(),
			Amount:                  getAmountForCustomCases(order),
			RtaConfirmedAmount:      rtaConfirmedAmount,
			EstimatedCompletionDate: completionETA,
		}
	}
	res.Orders = fundOrders
	return res, nil
}

func (s *Service) getPaymentRequestsFromOrders(orders []*orderPb.Order) []*GetPaymentDetailsRequest {
	var getPaymentDetailsRequests []*GetPaymentDetailsRequest

	for _, order := range orders {
		if order.OrderType == orderPb.OrderType_BUY && isOrderPaid(order.OrderStatus) {
			getPaymentDetailsRequests = append(getPaymentDetailsRequests, &GetPaymentDetailsRequest{OrderId: order.Id,
				PaymentMode: s.convertToPaymentHandlerPaymentMode(order.PaymentMode)})
		}
	}

	return getPaymentDetailsRequests
}

func isOrderPaid(status orderPb.OrderStatus) bool {
	return paidOrderStates[status]
}

func (s *Service) convertToPaymentHandlerPaymentMode(mode orderPb.PaymentMode) payment_handler.PaymentMode {
	switch mode {
	case orderPb.PaymentMode_SI:
		return payment_handler.PaymentMode_PAYMENT_MODE_SI
	case orderPb.PaymentMode_P2P_TRANSFER:
		return payment_handler.PaymentMode_P2P_TRANSFER
	default:
		return payment_handler.PaymentMode_PAYMENT_MODE_UNSPECIFIED
	}
}

func (s *Service) getTransactionType(order *orderPb.Order) cxMfPb.TransactionType {
	switch order.OrderType {
	case orderPb.OrderType_BUY:
		switch order.Client {
		case orderPb.OrderClient_FIT:
			return cxMfPb.TransactionType_AUTO_INVEST
		default:
			return cxMfPb.TransactionType_ONE_TIME
		}
	case orderPb.OrderType_SELL:
		return cxMfPb.TransactionType_WITHDRAWAL
	default:
		return cxMfPb.TransactionType_TransactionType_UNSPECIFIED
	}
}

// TODO(MIHIR): revisit this logic
// nolint
func (s *Service) getCxActivityStatus(orderStatus orderPb.OrderStatus, orderType orderPb.OrderType) cxMfPb.OrderStatus {
	switch orderStatus {
	case orderPb.OrderStatus_CREATED,
		orderPb.OrderStatus_PRE_PROCESSING,
		orderPb.OrderStatus_PRE_PROCESSING_COMPLETE,
		orderPb.OrderStatus_INITIATED,
		orderPb.OrderStatus_SENT_TO_RTA,
		orderPb.OrderStatus_ACCEPTED_BY_RTA,
		orderPb.OrderStatus_IN_PAYMENT:
		return cxMfPb.OrderStatus_PROCESSING
	case orderPb.OrderStatus_PAID, orderPb.OrderStatus_NOTIFYING_PAYMENT_CREDIT:
		return cxMfPb.OrderStatus_PAYMENT_RECEIVED
	case orderPb.OrderStatus_IN_FULFILLMENT:
		switch orderType {
		case orderPb.OrderType_BUY:
			return cxMfPb.OrderStatus_ORDER_PLACED_WITH_AMC
		case orderPb.OrderType_SELL:
			return cxMfPb.OrderStatus_WITHDRAWAL_SENT_TO_AMC
		default:
			return cxMfPb.OrderStatus_OrderStatus_UNSPECIFIED
		}
	case orderPb.OrderStatus_IN_SETTLEMENT:
		return cxMfPb.OrderStatus_WITHDRAWAL_CONFIRMATION
	case orderPb.OrderStatus_SETTLED:
		return cxMfPb.OrderStatus_WITHDRAWAL_CREDITED
	case orderPb.OrderStatus_FAILURE:
		return cxMfPb.OrderStatus_ORDER_FAILED
	case orderPb.OrderStatus_EXPIRED:
		return cxMfPb.OrderStatus_ORDER_EXPIRED
	case orderPb.OrderStatus_MANUAL_INTERVENTION:
		return cxMfPb.OrderStatus_MANUAL_INTERVENTION
	case orderPb.OrderStatus_CONFIRMED_BY_RTA:
		return cxMfPb.OrderStatus_UNITS_ALLOCATED
	default:
		return cxMfPb.OrderStatus_OrderStatus_UNSPECIFIED
	}

}

// this func contains custom cases for which the amount gets fetched
func getAmountForCustomCases(order *orderPb.Order) string {
	var amount *moneyPb.Money
	switch order.OrderType {
	case orderPb.OrderType_SELL:
		amount = order.GetAmount()
	case orderPb.OrderType_BUY:
		switch order.OrderStatus {
		case orderPb.OrderStatus_FAILURE:
			amount = order.GetAmount()
		case orderPb.OrderStatus_MANUAL_INTERVENTION:
			amount = order.GetAmount()
		default:
			return "-"
		}
	default:
		return "-"
	}
	return money.ToDisplayStringWithoutSymbol(amount)
}

func getOrderStatusFromFilterStatus(orderFilterStatus cxMfPb.OrderFilterStatus) orderPb.OrderStatus {
	switch orderFilterStatus {
	case cxMfPb.OrderFilterStatus_OFS_INVESTMENT_CONFIRMED:
		return orderPb.OrderStatus_CONFIRMED_BY_RTA
	case cxMfPb.OrderFilterStatus_OFS_WITHDRAWAL_CONFIRMED:
		return orderPb.OrderStatus_IN_SETTLEMENT
	case cxMfPb.OrderFilterStatus_OFS_WITHDRAWAL_CREDITED:
		return orderPb.OrderStatus_SETTLED
	}
	return orderPb.OrderStatus_ORDER_STATUS_UNSPECIFIED
}
