package mutualfund

import (
	"context"

	"go.uber.org/zap"

	rmsPb "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

// GetAllFitRulesForMutualFunds returns map of all fit rules available fo a mutual fund
// for now we have 4 rules with 4 constant rule-ids across envs
func (s *Service) GetAllFitRulesForMutualFunds(ctx context.Context) (map[string]*rmsPb.Rule, error) {
	ruleIds := constants.FitAutoInvestRuleIds
	ruleIds = append(ruleIds, constants.FitRoundUpAndInvest)
	ruleResp, beErr := s.fitRmsClient.GetRulesByIds(ctx, &rmsPb.GetRulesByIdsRequest{RuleIds: ruleIds})
	if err2 := epifigrpc.RPCError(ruleResp, beErr); err2 != nil {
		logger.Error(ctx, "Error while getting all fit rules available for mutual funds", zap.Error(beErr))
		return nil, err2
	}
	return ruleResp.Rules, nil
}

// GetAllFitRuleSubscriptionsForMutualFundsByActor returns map of all fit rule subscriptions available for an actor
// for now we have 4 rules with 4 constant rule-ids across envs
func (s *Service) GetAllFitRuleSubscriptionsForMutualFundsByActor(ctx context.Context, actorId string) (map[string]*rmsPb.Subscriptions, error) {
	ruleIds := constants.FitAutoInvestRuleIds
	ruleIds = append(ruleIds, constants.FitRoundUpAndInvest)
	ruleResp, beErr := s.fitRmsClient.GetSubscriptionsByActorForRules(ctx,
		&rmsPb.GetSubscriptionsByActorForRulesRequest{
			ActorId:                actorId,
			RuleIds:                ruleIds,
			ShouldNotUsePagination: true,
		})
	if err2 := epifigrpc.RPCError(ruleResp, beErr); err2 != nil {
		logger.Error(ctx, "error while getting all active fit rules subscribed by actor", zap.Error(err2))
		return nil, err2
	}
	logger.Debug(ctx, "response from 'GetSubscriptionsByActorForRules'", zap.Any("fittt_response", ruleResp))
	return ruleResp.RuleSubscriptions, nil
}
