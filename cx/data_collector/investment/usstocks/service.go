package usstocks

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/genproto/protobuf/field_mask"

	rpcPb "github.com/epifi/be-common/api/rpc"
	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/gamma/api/cx/data_collector/investment/usstocks"
	"github.com/epifi/gamma/api/usstocks/account"
	order2 "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type Service struct {
	usstocks.UnimplementedUsStocksInvestmentServer
	authEngine             auth_engine.IAuthEngine
	celestialClient        celestialPb.CelestialClient
	accountManagerClient   account.AccountManagerClient
	OrderManagerClient     order2.OrderManagerClient
	PortfolioManagerClient portfolio.PortfolioManagerClient
	usStocksOpsConfig      *config.UsStocksOpsConfig
}

func NewUsStocksInvestmentService(authEngine auth_engine.IAuthEngine, celestialClient celestialPb.CelestialClient, accountManagerClient account.AccountManagerClient,
	orderManagerClient order2.OrderManagerClient, PortfolioManagerClient portfolio.PortfolioManagerClient, usStocksOpsConfig *config.UsStocksOpsConfig) *Service {
	return &Service{
		authEngine:             authEngine,
		celestialClient:        celestialClient,
		accountManagerClient:   accountManagerClient,
		OrderManagerClient:     orderManagerClient,
		PortfolioManagerClient: PortfolioManagerClient,
		usStocksOpsConfig:      usStocksOpsConfig,
	}
}

func (s *Service) GetAccountBasicDetails(ctx context.Context, req *usstocks.GetAccountBasicDetailsRequest) (*usstocks.GetAccountBasicDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &usstocks.GetAccountBasicDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()

	accResp, accErr := s.accountManagerClient.GetAccount(ctx, &account.GetAccountRequest{
		Vendor:    commonvgpb.Vendor_ALPACA,
		ActorId:   actorId,
		FieldMask: &field_mask.FieldMask{Paths: []string{(&account.Account{}).GetAccountIdPath(), (&account.Account{}).GetAccountExternalAccountIdPath()}},
	})
	if te := epifigrpc.RPCError(accResp, accErr); te != nil {
		cxLogger.Error(ctx, "error while get account of user", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		return &usstocks.GetAccountBasicDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}

	accountId := accResp.GetAccount().GetId()
	// fetch current stage and status for given account id
	wfsResp, wfsErr := s.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{ClientRequestId: &celestialPb.ClientReqId{
			Id:     accountId,
			Client: workflow.Client_US_STOCKS,
		}},
		Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
	})
	if te := epifigrpc.RPCError(wfsResp, wfsErr); te != nil {
		cxLogger.Error(ctx, "error while get work flow status", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ACCOUNT_ID, accountId))
		return &usstocks.GetAccountBasicDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}

	accountStage := wfsResp.GetWorkflowRequest().GetStage().String()
	accountStatus := getStageStatusString(wfsResp.GetWorkflowRequest().GetStatus())
	return &usstocks.GetAccountBasicDetailsResponse{
		Status:                rpcPb.StatusOk(),
		SherlockDeepLink:      nil,
		AccountId:             accResp.GetAccount().GetExternalAccountId(),
		AccountStage:          accountStage,
		AccountStatus:         accountStatus,
		Rca:                   "-",
		TroubleshootingAdvice: s.usStocksOpsConfig.OrderTypeStageStatusMap[accountStage+"-"+accountStatus],
		AdditionalDetails:     "-",
	}, nil
}

func (s *Service) GetAccountStagesDetails(ctx context.Context, req *usstocks.GetAccountStagesDetailsRequest) (*usstocks.GetAccountStagesDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &usstocks.GetAccountStagesDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()

	accResp, accErr := s.accountManagerClient.GetAccount(ctx, &account.GetAccountRequest{
		Vendor:    commonvgpb.Vendor_ALPACA,
		ActorId:   actorId,
		FieldMask: &field_mask.FieldMask{Paths: []string{"id"}},
	})
	if te := epifigrpc.RPCError(accResp, accErr); te != nil {
		cxLogger.Error(ctx, "error while get account of user", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		return &usstocks.GetAccountStagesDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}

	accountId := accResp.GetAccount().GetId()
	// fetch current stage and status for given account id
	wfsResp, wfsErr := s.celestialClient.GetWorkflowStatus(ctx, &celestialPb.GetWorkflowStatusRequest{
		Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{ClientRequestId: &celestialPb.ClientReqId{
			Id:     accountId,
			Client: workflow.Client_US_STOCKS,
		}},
		Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
	})
	if te := epifigrpc.RPCError(wfsResp, wfsErr); te != nil {
		cxLogger.Error(ctx, "error while get work flow status", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ACCOUNT_ID, accountId))
		return &usstocks.GetAccountStagesDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}

	// fetch all stages and their status for given account id
	wfhResp, wfhErr := s.celestialClient.GetWorkflowHistory(ctx, &celestialPb.GetWorkflowHistoryRequest{
		Identifier: &celestialPb.GetWorkflowHistoryRequest_ClientReqId{ClientReqId: &celestialPb.ClientReqId{
			Id:     accountId,
			Client: workflow.Client_US_STOCKS,
		}},
		Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
	})
	if te := epifigrpc.RPCError(wfhResp, wfhErr); te != nil {
		cxLogger.Error(ctx, "error while get work flow history", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ACCOUNT_ID, accountId))
		return &usstocks.GetAccountStagesDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}

	var stageInfoList []*usstocks.AccountStageInfo
	for _, stageResp := range wfhResp.GetWorkflowHistoryList() {
		stageInfoList = append(stageInfoList, &usstocks.AccountStageInfo{
			CurrentStage:    stageResp.GetStage().String(),
			CurrentStatus:   getStageStatusString(stageResp.GetStatus()),
			UpdatedAt:       stageResp.GetUpdatedAt(),
			Description:     stageResp.GetFailureDescription(),
			MetaInformation: "-", // TODO(satyam) add this after getting details from aditya D
		})
	}

	return &usstocks.GetAccountStagesDetailsResponse{
		Status:     rpcPb.StatusOk(),
		StageInfos: stageInfoList,
	}, nil
}

func (s *Service) GetPortfolioDetails(ctx context.Context, req *usstocks.GetPortfolioDetailsRequest) (*usstocks.GetPortfolioDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &usstocks.GetPortfolioDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	resp, err := s.PortfolioManagerClient.GetPortfolioDetailsForCx(ctx, &portfolio.GetPortfolioDetailsForCxRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching portfolio details using GetPortfolioDetailsForCx", zap.Error(te))
		return &usstocks.GetPortfolioDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}

	var investedAsset []*usstocks.InvestedAsset
	for _, val := range resp.GetAssetsDetails() {
		investedAsset = append(investedAsset, &usstocks.InvestedAsset{
			AssetName:          val.GetSymbol(),
			AssetType:          val.GetStockType().String(),
			FirstTransactionAt: val.GetFirstTransactionAt(),
			LastTransactionAt:  val.GetLastTransactionAt(),
			SellLockUnits:      val.GetSellLockUnits(),
		})
	}

	return &usstocks.GetPortfolioDetailsResponse{
		Status:         rpcPb.StatusOk(),
		InvestedAssets: investedAsset,
	}, nil
}

func (s *Service) GetReviewItems(ctx context.Context, req *usstocks.GetReviewItemsRequest) (*usstocks.GetReviewItemsResponse, error) {
	reviewItemType, rErr := getReviewItemType(req.GetReviewPayloadType())
	if rErr != nil {
		cxLogger.Error(ctx, "error in getting review item type", zap.Error(rErr))
		return &usstocks.GetReviewItemsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	beRes, beErr := s.accountManagerClient.GetPaginatedReviewItems(ctx, &account.GetPaginatedReviewItemsRequest{
		ReviewItemType: reviewItemType,
		PageContext:    req.GetPageContext(),
	})
	if te := epifigrpc.RPCError(beRes, beErr); te != nil {
		cxLogger.Error(ctx, "error while fetching review items", zap.Error(te))
		return &usstocks.GetReviewItemsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	cxElements, convErr := convertToCxReviewElements(beRes.GetManualReviews())
	if convErr != nil {
		cxLogger.Error(ctx, "error in converting to cx review element", zap.Error(convErr))
		return &usstocks.GetReviewItemsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &usstocks.GetReviewItemsResponse{
		Status:         rpcPb.StatusOk(),
		ReviewElements: cxElements,
		PageContext:    beRes.GetPageContext(),
	}, nil
}

func getReviewItemType(payloadType usstocks.ReviewPayloadType) (account.ReviewItemType, error) {
	switch payloadType {
	case usstocks.ReviewPayloadType_REVIEW_PAYLOAD_TYPE_PAN_IMAGE:
		return account.ReviewItemType_REVIEW_ITEM_TYPE_PAN_IMAGE, nil
	default:
		return account.ReviewItemType_REVIEW_ITEM_TYPE_UNSPECIFIED, fmt.Errorf("unhandled payload type: %v", payloadType.String())
	}
}

func convertToCxReviewElements(beElements []*account.ManualReview) ([]*usstocks.ReviewElement, error) {
	var cxElements []*usstocks.ReviewElement
	for _, beElement := range beElements {
		switch beElement.GetReviewItemType() {
		case account.ReviewItemType_REVIEW_ITEM_TYPE_PAN_IMAGE:
			cxElements = append(cxElements, &usstocks.ReviewElement{
				ReviewId: beElement.GetId(),
				Payload: &usstocks.ReviewElement_PanReviewElement{
					PanReviewElement: &usstocks.PanReviewElement{
						ActorId:   beElement.GetActorId(),
						CreatedAt: beElement.GetCreatedAt(),
					},
				},
			})
		default:
			return nil, fmt.Errorf("unhandled review item type: %v", beElement.GetReviewItemType())
		}
	}
	return cxElements, nil
}

func (s *Service) GetReviewItemDetails(ctx context.Context, req *usstocks.GetReviewItemDetailsRequest) (*usstocks.GetReviewItemDetailsResponse, error) {
	beRes, beErr := s.accountManagerClient.GetReviewItemDetails(ctx, &account.GetReviewItemDetailsRequest{
		ReviewId: req.GetReviewId(),
	})
	if te := epifigrpc.RPCError(beRes, beErr); te != nil && !beRes.GetStatus().IsAborted() {
		cxLogger.Error(ctx, "error while fetching review item details", zap.Error(te), zap.String(logger.REQUEST_ID, req.GetReviewId()))
		return &usstocks.GetReviewItemDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if beRes.GetStatus().IsAborted() {
		return &usstocks.GetReviewItemDetailsResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}
	switch beRes.GetReviewItemDetails().(type) {
	case *account.GetReviewItemDetailsResponse_PanReviewDetails:
		return &usstocks.GetReviewItemDetailsResponse{
			Status: rpcPb.StatusOk(),
			ReviewItemDetails: &usstocks.GetReviewItemDetailsResponse_PanReviewDetails{
				PanReviewDetails: &usstocks.PanReviewDetails{
					ReviewId:       req.GetReviewId(),
					Name:           beRes.GetPanReviewDetails().GetName(),
					PanImage:       beRes.GetPanReviewDetails().GetPanImage(),
					LivenessImage:  beRes.GetPanReviewDetails().GetLivenessImage(),
					Dob:            beRes.GetPanReviewDetails().GetDob(),
					PanNumber:      beRes.GetPanReviewDetails().GetPanNumber(),
					FaceMatchScore: beRes.GetPanReviewDetails().GetFaceMatchScore(),
					OcrScore:       beRes.GetPanReviewDetails().GetOcrScore(),
				},
			},
		}, nil
	default:
		cxLogger.Error(ctx, "unhandled review item details")
		return &usstocks.GetReviewItemDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
}

func (s *Service) MarkReviewAsDone(ctx context.Context, req *usstocks.MarkReviewAsDoneRequest) (*usstocks.MarkReviewAsDoneResponse, error) {
	switch req.GetReviewActionDetails().(type) {
	case *usstocks.MarkReviewAsDoneRequest_PanReviewActionDetails:
		status, sErr := convertToBeReviewStatus(req.GetPanReviewActionDetails().GetReviewAction())
		if sErr != nil {
			cxLogger.Error(ctx, "error in converting to be review status", zap.Error(sErr))
			return &usstocks.MarkReviewAsDoneResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		reason, rErr := convertToBeReviewReason(req.GetPanReviewActionDetails().GetReviewReason())
		if rErr != nil {
			cxLogger.Error(ctx, "error in converting to be review reason", zap.Error(rErr))
			return &usstocks.MarkReviewAsDoneResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		beRes, beErr := s.accountManagerClient.MarkReviewAsDone(ctx, &account.MarkReviewAsDoneRequest{
			ReviewId: req.GetReviewId(),
			ReviewActionDetails: &account.MarkReviewAsDoneRequest_PanReviewActionDetails{
				PanReviewActionDetails: &account.PanReviewActionDetails{
					ReviewStatus:  status,
					ReviewReason:  reason,
					ReviewerEmail: req.GetPanReviewActionDetails().GetReviewerEmail(),
				},
			},
		})
		if te := epifigrpc.RPCError(beRes, beErr); te != nil {
			if beRes.GetStatus().IsAlreadyExists() {
				return &usstocks.MarkReviewAsDoneResponse{
					Status: rpcPb.StatusAlreadyExists(),
				}, nil
			}
			cxLogger.Error(ctx, "error while calling BE rpc MarkReviewAsDone", zap.Error(te))
			return &usstocks.MarkReviewAsDoneResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		return &usstocks.MarkReviewAsDoneResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	default:
		cxLogger.Error(ctx, "unhandled review action details type")
		return &usstocks.MarkReviewAsDoneResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
}

func convertToBeReviewStatus(action usstocks.ReviewAction) (account.ReviewStatus, error) {
	switch action {
	case usstocks.ReviewAction_REVIEW_ACTION_APPROVED:
		return account.ReviewStatus_REVIEW_STATUS_APPROVED, nil
	case usstocks.ReviewAction_REVIEW_ACTION_REJECTED:
		return account.ReviewStatus_REVIEW_STATUS_REJECTED, nil
	default:
		return account.ReviewStatus_REVIEW_STATUS_UNSPECIFIED, fmt.Errorf("unhandled review action type: %v", action.String())
	}
}

func convertToBeReviewReason(reason usstocks.ReviewReason) (account.ReviewReason, error) {
	switch reason {
	case usstocks.ReviewReason_REVIEW_REASON_ALL_DETAILS_MATCHING:
		return account.ReviewReason_REVIEW_REASON_ALL_DETAILS_MATCHING, nil
	case usstocks.ReviewReason_REVIEW_REASON_NOT_PAN_IMAGE:
		return account.ReviewReason_REVIEW_REASON_NOT_PAN_IMAGE, nil
	case usstocks.ReviewReason_REVIEW_REASON_IMAGE_NOT_CLEAR:
		return account.ReviewReason_REVIEW_REASON_IMAGE_NOT_CLEAR, nil
	case usstocks.ReviewReason_REVIEW_REASON_DOB_INCORRECT:
		return account.ReviewReason_REVIEW_REASON_DOB_INCORRECT, nil
	case usstocks.ReviewReason_REVIEW_REASON_PAN_NUMBER_INCORRECT:
		return account.ReviewReason_REVIEW_REASON_PAN_NUMBER_INCORRECT, nil
	case usstocks.ReviewReason_REVIEW_REASON_USER_PHOTO_NOT_CLEAR_ON_PAN:
		return account.ReviewReason_REVIEW_REASON_USER_PHOTO_NOT_CLEAR_ON_PAN, nil
	case usstocks.ReviewReason_REVIEW_REASON_USER_PHOTO_ON_PAN_NOT_MATCHING:
		return account.ReviewReason_REVIEW_REASON_USER_PHOTO_ON_PAN_NOT_MATCHING, nil
	default:
		return account.ReviewReason_REVIEW_REASON_UNSPECIFIED, fmt.Errorf("unhandled review reason: %v", reason.String())
	}
}

func getStageStatusString(stageStatus stage.Status) string {
	// Renamed “BLOCKED” to “PROCESSING”. Otherwise, agent might see “BLOCKED” and communicate to user that their account
	// is blocked rather than going into the details.
	if stageStatus == stage.Status_BLOCKED {
		return "PROCESSING"
	}
	return stageStatus.String()
}
