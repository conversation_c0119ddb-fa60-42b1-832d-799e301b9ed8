package usstocks

import (
	"context"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/cx/data_collector/investment/usstocks"
	"github.com/epifi/gamma/api/typesv2/webui"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

const (
	TimestampCoLabel = "Timestamp"
	TimestampColKey  = "timestamp"

	TimelineStepCoLabel = "Timeline Step"
	TimelineStepColKey  = "timeline_step"

	StepStatusCoLabel = "Step Status"
	StepStatusColKey  = "step_status"

	FailureReasonCoLabel = "Failure Reason"
	FailureReasonColKey  = "failure_reason"
)

var (
	getRemittanceDetailTableHeader = []*webui.TableHeader{
		{
			Label:     TimestampCoLabel,
			HeaderKey: TimestampColKey,
			IsVisible: true,
		},
		{
			Label:     TimelineStepCoLabel,
			HeaderKey: TimelineStepColKey,
			IsVisible: true,
		},
		{
			Label:     StepStatusCoLabel,
			HeaderKey: StepStatusColKey,
			IsVisible: true,
		},
		{
			Label:     FailureReasonCoLabel,
			HeaderKey: FailureReasonColKey,
			IsVisible: true,
		},
	}
)

func (s *Service) GetRemittanceOrderDetails(ctx context.Context, req *usstocks.GetRemittanceOrderDetailsRequest) (*usstocks.GetRemittanceOrderDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &usstocks.GetRemittanceOrderDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	getWalletOrderResp, err := s.OrderManagerClient.GetWalletOrderProcessingDetails(ctx, &orderPb.GetWalletOrderProcessingDetailsRequest{
		Id:      &orderPb.GetWalletOrderProcessingDetailsRequest_OrderId{OrderId: req.GetRemittanceId()},
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if err2 := epifigrpc.RPCError(getWalletOrderResp, err); err2 != nil {
		cxLogger.Error(ctx, "error while GetWalletOrderProcessingDetails", zap.Error(err2))
		return &usstocks.GetRemittanceOrderDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &usstocks.GetRemittanceOrderDetailsResponse{
		Status:                 rpc.StatusOk(),
		RemittanceOrderDetails: s.getRemittanceDetails(getWalletOrderResp.GetStageDetails(), getWalletOrderResp.GetOrder()),
	}, nil
}

func (s *Service) getRemittanceDetails(processingState []*orderPb.WalletOrderProcessingStageDetails, order *orderPb.WalletOrder) []*webui.Table {
	return []*webui.Table{
		{
			TableHeaders: getRemittanceDetailTableHeader,
			TableRows:    s.getRemittanceDetailTableRows(processingState, order),
			TableName:    "Remittance Step Details",
		}}
}

func (s *Service) getRemittanceDetailTableRows(processingStates []*orderPb.WalletOrderProcessingStageDetails, order *orderPb.WalletOrder) []*webui.TableRow {
	rows := make([]*webui.TableRow, 0)
	for _, processingState := range processingStates {
		headerKeyCellMap := s.getRemittanceDetailColHeaderKeyToCellMap(processingState, order)
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: headerKeyCellMap,
			Meta:             order.GetId() + processingState.GetStage() + processingState.GetStatus().String(),
		})
	}
	return rows
}

func (s *Service) getRemittanceDetailColHeaderKeyToCellMap(processingState *orderPb.WalletOrderProcessingStageDetails, order *orderPb.WalletOrder) map[string]*webui.TableCell {
	rowCell := make(map[string]*webui.TableCell, 0)
	rowCell[TimestampColKey] = &webui.TableCell{
		Value: processingState.GetLastUpdatedTimestamp().AsTime().Format(RemittanceTimeFormat),
	}
	rowCell[TimelineStepColKey] = &webui.TableCell{
		Value: processingState.GetStage(),
	}
	rowCell[StepStatusColKey] = &webui.TableCell{
		Value: processingState.GetStatus().String(),
	}
	failureReason := s.usStocksOpsConfig.OrderTypeStageStatusMap[strings.ReplaceAll(order.GetOrderType().String(), "WALLET_ORDER_TYPE_", "")+"-"+processingState.GetStage()+"-"+processingState.GetStatus().String()]
	rowCell[FailureReasonColKey] = &webui.TableCell{
		Value: failureReason,
	}
	return rowCell
}
