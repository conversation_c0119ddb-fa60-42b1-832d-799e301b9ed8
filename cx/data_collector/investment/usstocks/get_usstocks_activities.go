package usstocks

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/cx/data_collector/investment/usstocks"
	usstocks2 "github.com/epifi/gamma/api/usstocks"
	order2 "github.com/epifi/gamma/api/usstocks/order"
	ussOrderMgPb "github.com/epifi/gamma/api/usstocks/order"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

func isBuyOrSellOrder(activityType ussOrderMgPb.AccountActivityType) bool {
	return activityType == ussOrderMgPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_SELL || activityType == ussOrderMgPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY ||
		activityType == ussOrderMgPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_REWARD || activityType == ussOrderMgPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_FOR_SIP
}

func (s *Service) GetUsStockActivities(ctx context.Context, req *usstocks.GetUsStockActivitiesRequest) (*usstocks.GetUsStockActivitiesResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &usstocks.GetUsStockActivitiesResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()

	if req.GetOrderId() != "" {
		orderDetails, err := s.getActivityForGivenOrderId(ctx, req.GetOrderId(), req.GetHeader().GetActor().GetId())
		if err != nil {
			cxLogger.Error(ctx, "error while getting activity", zap.Error(err))
			return &usstocks.GetUsStockActivitiesResponse{Status: rpcPb.StatusInternal()}, nil
		}
		return &usstocks.GetUsStockActivitiesResponse{
			Status:       rpcPb.StatusOk(),
			OrderDetails: orderDetails,
		}, nil
	}

	var symbols []string
	if req.GetAssetName() != "" {
		symbols = append(symbols, req.GetAssetName())
	}

	if s.usStocksOpsConfig.UseAccountActivities {
		orderDetails, pageContext, err := s.getActivitiesForCX(ctx, req, symbols)
		if err != nil {
			cxLogger.Error(ctx, "error while getting activities", zap.Error(err))
			return &usstocks.GetUsStockActivitiesResponse{Status: rpcPb.StatusInternal()}, nil
		}
		return &usstocks.GetUsStockActivitiesResponse{
			Status:       rpcPb.StatusOk(),
			OrderDetails: orderDetails,
			PageContext:  pageContext,
		}, nil
	}
	ordersResp, ordersErr := s.OrderManagerClient.GetOrders(ctx, &order2.GetOrdersRequest{
		ActorId:     actorId,
		OrderSides:  getOrderSides(req.GetActivityType()),
		Symbols:     symbols,
		PageContext: req.GetPageContext(),
		StartTime:   req.GetFromDate(),
		EndTime:     req.GetToDate(),
	})
	if te := epifigrpc.RPCError(ordersResp, ordersErr); te != nil {
		cxLogger.Error(ctx, "error while get orders", zap.Error(te), zap.String(logger.ORDER_ID, req.GetOrderId()), zap.String(logger.ACTOR_ID_V2, actorId))
		return &usstocks.GetUsStockActivitiesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &usstocks.GetUsStockActivitiesResponse{
		Status:       rpcPb.StatusOk(),
		OrderDetails: getOrderDetails(ordersResp.GetOrders()),
		PageContext:  ordersResp.GetPageContext(),
	}, nil
}

func (s *Service) getActivitiesForCX(ctx context.Context, req *usstocks.GetUsStockActivitiesRequest, symbols []string) ([]*usstocks.OrderDetails, *rpc.PageContextResponse, error) {
	resp, err := s.OrderManagerClient.GetAccountActivities(ctx, &ussOrderMgPb.GetAccountActivitiesRequest{
		ActorId:       req.GetHeader().GetActor().GetId(),
		ActivityType:  getActivitiesTypeBE(req.GetActivityType()),
		Symbols:       symbols,
		PageContext:   req.GetPageContext(),
		FieldMask:     []ussOrderMgPb.GetAccountActivitiesRequest_FieldMask{ussOrderMgPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_ORDER_MAP},
		CreatedAfter:  req.GetFromDate(),
		CreatedBefore: req.GetToDate(),
	})
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		return nil, nil, errors.Wrap(err2, "error while getting activities")
	}

	var ordersDetails []*usstocks.OrderDetails
	for _, activity := range resp.GetAccountActivities() {
		if isBuyOrSellOrder(activity.GetType()) {
			ordersDetails = append(ordersDetails, getOrderInformation(resp.OrderMappedToActivity[activity.GetOrderId()]))
		} else {
			ordersDetails = append(ordersDetails, s.getDividendInformation(activity))
		}
	}
	return ordersDetails, resp.GetPageContext(), nil
}

func (s *Service) getDividendInformation(activity *order2.AccountActivity) *usstocks.OrderDetails {
	return &usstocks.OrderDetails{
		ActivityId:          activity.GetId(),
		AssetName:           activity.GetSymbol(),
		CreatedAt:           activity.GetCreatedAt(),
		UpdatedAt:           activity.GetUpdatedAt(),
		ValueOfActivity:     activity.GetNetAmount(),
		Units:               activity.GetActivityInfo().GetDividendInfo().GetQty(),
		ActivityType:        "Dividend",
		CurrentStatus:       activity.GetState().String(),
		FxRate:              activity.GetActivityInfo().GetDividendInfo().GetForexRate(),
		CbsNumber:           activity.GetActivityInfo().GetDividendInfo().GetUtr(),
		FiExternalTxnNumber: "",
		GstPaid:             activity.GetActivityInfo().GetDividendInfo().GetGstCharged(),
		TcsPaid:             money.ZeroINR().GetPb(),
		IsSellLock:          false,
	}

}

func (s *Service) getActivityForGivenOrderId(ctx context.Context, id, actorId string) ([]*usstocks.OrderDetails, error) {
	orderResp, orderErr := s.OrderManagerClient.GetOrder(ctx, &order2.GetOrderRequest{
		Id:      &order2.GetOrderRequest_OrderId{OrderId: id},
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(orderResp, orderErr); te != nil {
		cxLogger.Error(ctx, "error while get order", zap.Error(te), zap.String(logger.ORDER_ID, id))
		if s.usStocksOpsConfig.UseAccountActivities {
			activityResp, err := s.OrderManagerClient.GetAccountActivity(ctx, &ussOrderMgPb.GetAccountActivityRequest{ActivityId: id})
			if err2 := epifigrpc.RPCError(activityResp, err); err2 != nil {
				return nil, errors.Wrap(err, "error while GetAccountActivity")
			}
			return []*usstocks.OrderDetails{s.getDividendInformation(activityResp.GetActivity())}, nil
		}
		return nil, errors.Wrap(te, "error while get order")
	}
	return []*usstocks.OrderDetails{getOrderInformation(orderResp.GetOrder())}, nil
}

func getActivitiesTypeBE(activityType usstocks.UsStocksActivityType) []order2.AccountActivityType {
	if activityType == usstocks.UsStocksActivityType_US_STOCKS_ACTIVITY_TYPE_UNSPECIFIED {
		// if activityType is not provided then will show all type of orders
		return []order2.AccountActivityType{order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY, order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_SELL,
			order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_REWARD, order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND,
			order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_FOR_SIP}
	}
	// TODO(satyam) update this when auto investment buy flow would be enable
	switch activityType {
	case usstocks.UsStocksActivityType_US_STOCKS_ACTIVITY_TYPE_BUY:
		return []order2.AccountActivityType{order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY}
	case usstocks.UsStocksActivityType_US_STOCKS_ACTIVITY_TYPE_SELL:
		return []order2.AccountActivityType{order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_SELL}
	case usstocks.UsStocksActivityType_US_STOCKS_ACTIVITY_TYPE_DIVIDEND_OR_REG_FEE:
		return []order2.AccountActivityType{order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND}
	default:
		return []order2.AccountActivityType{order2.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_UNSPECIFIED}
	}

}
func getOrderSides(activityType usstocks.UsStocksActivityType) []usstocks2.OrderSide {
	if activityType == usstocks.UsStocksActivityType_US_STOCKS_ACTIVITY_TYPE_UNSPECIFIED {
		// if activityType is not provided then will show all type of orders
		return nil
	}
	// TODO(satyam) update this when auto investment buy flow would be enable
	switch activityType {
	case usstocks.UsStocksActivityType_US_STOCKS_ACTIVITY_TYPE_BUY:
		return []usstocks2.OrderSide{usstocks2.OrderSide_BUY}
	case usstocks.UsStocksActivityType_US_STOCKS_ACTIVITY_TYPE_SELL:
		return []usstocks2.OrderSide{usstocks2.OrderSide_SELL}
	case usstocks.UsStocksActivityType_US_STOCKS_ACTIVITY_TYPE_DIVIDEND_OR_REG_FEE:
		return []usstocks2.OrderSide{usstocks2.OrderSide_SELL}
	default:
		return []usstocks2.OrderSide{usstocks2.OrderSide_ORDER_SIDE_UNSPECIFIED}
	}
}

func getOrderDetails(ordersReq []*order2.Order) []*usstocks.OrderDetails {
	var ordersResp []*usstocks.OrderDetails
	for _, orderReq := range ordersReq {
		ordersResp = append(ordersResp, getOrderInformation(orderReq))
	}

	return ordersResp
}

func getOrderInformation(orderObj *order2.Order) *usstocks.OrderDetails {
	var isSellLocked bool
	if orderObj.GetSide() == usstocks2.OrderSide_BUY && orderObj.GetQtyConfirmed() != 0 && orderObj.GetState() != usstocks2.OrderState_ORDER_SUCCESS {
		isSellLocked = true
	}
	return &usstocks.OrderDetails{
		ActivityId:          orderObj.GetId(),
		AssetName:           orderObj.GetSymbol(),
		CreatedAt:           orderObj.GetCreatedAt(),
		UpdatedAt:           orderObj.GetUpdatedAt(),
		ValueOfActivity:     orderObj.GetAmountConfirmed(),
		Units:               orderObj.GetQtyConfirmed(),
		ActivityType:        getActivityType(orderObj.GetSide()),
		CurrentStatus:       orderObj.GetState().String(),
		FxRate:              orderObj.GetInvoiceDetails().GetPartnerExchangeRate(),
		CbsNumber:           "",
		FiExternalTxnNumber: orderObj.GetExternalOrderId(),
		GstPaid:             orderObj.GetInvoiceDetails().GetGST(),
		TcsPaid:             orderObj.GetInvoiceDetails().GetTCS(),
		IsSellLock:          isSellLocked,
	}
}

func getActivityType(orderType usstocks2.OrderSide) string {
	// TODO(satyam) update this when auto investment buy flow would be enable
	switch orderType {
	case usstocks2.OrderSide_BUY:
		return "Buy"
	case usstocks2.OrderSide_SELL:
		return "Sell"
	default:
		return "-"
	}
}
