package usstocks

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/gamma/api/cx/data_collector/investment/usstocks"
	usstocks2 "github.com/epifi/gamma/api/usstocks"
	order2 "github.com/epifi/gamma/api/usstocks/order"
	ussOrderMgPb "github.com/epifi/gamma/api/usstocks/order"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

func (s *Service) GetActivityDetails(ctx context.Context, req *usstocks.GetActivityDetailsRequest) (*usstocks.GetActivityDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &usstocks.GetActivityDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	opdResp, opdErr := s.OrderManagerClient.GetOrderProcessingDetails(ctx,
		&order2.GetOrderProcessingDetailsRequest{
			Id:      &order2.GetOrderProcessingDetailsRequest_OrderId{OrderId: req.GetOrderId()},
			ActorId: actorId,
		})
	if te := epifigrpc.RPCError(opdResp, opdErr); te != nil {
		if opdResp.GetStatus().IsRecordNotFound() {
			activityResp, err := s.OrderManagerClient.GetAccountActivity(ctx, &ussOrderMgPb.GetAccountActivityRequest{ActivityId: req.GetOrderId()})
			if err2 := epifigrpc.RPCError(activityResp, err); err2 != nil {
				cxLogger.Error(ctx, "error while GetAccountActivity", zap.Error(err2))
				return &usstocks.GetActivityDetailsResponse{Status: rpcPb.StatusInternal()}, nil
			}
			// considering activity as dividend activity, if order processing details is not present for the order
			return &usstocks.GetActivityDetailsResponse{
				Status:           rpcPb.StatusOk(),
				OrderStepDetails: s.getDividendDetails(activityResp.GetActivity()),
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching order processing details", zap.Error(te), zap.String(logger.ORDER_ID, req.GetOrderId()), zap.String(logger.ACTOR_ID_V2, actorId))
		return &usstocks.GetActivityDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &usstocks.GetActivityDetailsResponse{
		Status:           rpcPb.StatusOk(),
		OrderStepDetails: s.getOrderDetails(ctx, opdResp),
	}, nil
}

func (s *Service) getDividendDetails(activity *ussOrderMgPb.AccountActivity) []*usstocks.OrderStepDetails {
	var stepDetailsList []*usstocks.OrderStepDetails
	eta := activity.GetCreatedAt().AsTime().In(datetime.IST).Add(s.usStocksOpsConfig.ExpectedEtaForDividend)
	stepDetailsStage := ""
	if activity.GetState() == ussOrderMgPb.AccountActivityState_ACCOUNT_ACTIVITY_STATE_COMPLETED {
		stepDetailsStage = "Dividend Received"
	} else {
		stepDetailsStage = "Dividend Declared"
	}
	value := s.usStocksOpsConfig.OrderTypeStageStatusMap[activity.GetType().String()+"_"+activity.GetState().String()]
	stepDetailsList = append(stepDetailsList, &usstocks.OrderStepDetails{
		Timestamp:      activity.GetUpdatedAt(),
		TimelineStep:   stepDetailsStage,
		StepStatus:     fmt.Sprintf("Declared %s amount per share and user has %.6f shares", money.ToDisplayString(activity.GetActivityInfo().GetDividendInfo().GetPerShareAmount()), activity.GetActivityInfo().GetDividendInfo().GetQty()),
		InternalStatus: value,
		FailureReason:  "-",
		Eta:            timestampPb.New(eta),
	})
	return stepDetailsList
}

func (s *Service) getOrderDetails(ctx context.Context, opdResp *ussOrderMgPb.GetOrderProcessingDetailsResponse) []*usstocks.OrderStepDetails {
	orderSide := opdResp.GetOrder().GetSide().String()
	var stepDetailsList []*usstocks.OrderStepDetails
	for _, stepDetails := range opdResp.GetOrderProcessingStages() {
		stepDetailsStage := stepDetails.GetStage().String()
		stepDetailsStatus := getStageStatusString(stepDetails.GetStatus())
		key := orderSide + "-" + stepDetailsStage + "-" + stepDetailsStatus

		// this check is specific for handle order canceled condition, for order canceled need to check order state also.
		if stepDetails.GetStage() == workflow.Stage_TRACK_ORDER && stepDetails.GetStatus() == stage.Status_FAILED {
			if opdResp.GetOrder().GetState() == usstocks2.OrderState_ORDER_CANCELED {
				key = key + "-" + opdResp.GetOrder().GetState().String()
			}
		}

		value := s.usStocksOpsConfig.OrderTypeStageStatusMap[key]
		logger.Info(ctx, fmt.Sprintf("usstocks details for cx ops, key- %v, value-%v", key, value))
		stepDetailsList = append(stepDetailsList, &usstocks.OrderStepDetails{
			Timestamp:      stepDetails.GetLastUpdatedTimestamp(),
			TimelineStep:   stepDetailsStage,
			StepStatus:     stepDetailsStatus,
			InternalStatus: value, // TODO(numan) need to update proto name when web name will update name to troubleshooting advice
			FailureReason:  "-",
			Eta:            stepDetails.GetEta(),
		})
	}
	return stepDetailsList
}
