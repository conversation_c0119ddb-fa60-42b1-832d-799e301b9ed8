package usstocks

import (
	"context"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/cx/data_collector/investment/usstocks"
	"github.com/epifi/gamma/api/typesv2/webui"
	usstocksPb "github.com/epifi/gamma/api/usstocks"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

const (
	RemittanceTimeFormat       = "January 02, 2006 15:04:05"
	RemittanceIDCoLabel        = "Remittance ID"
	RemittanceIDColKey         = "remittance_id"
	CreatedAtCoLabel           = "Created At"
	CreatedAtColKey            = "created_at"
	UpdatedAtCoLabel           = "Updated At"
	UpdatedAtColKey            = "updated_at"
	ValueOfRemittanceCoLabel   = "Value of Remittance"
	ValueOfRemittanceColKey    = "value_of_remittance"
	UnitCoLabel                = "Unit"
	UnitColKey                 = "unit"
	RemittanceTypeCoLabel      = "Remittance Type"
	RemittanceTypeColKey       = "remittance_type"
	CurrentStatusCoLabel       = "Current Status"
	CurrentStatusColKey        = "current_status"
	FxRateCoLabel              = "Fx Rate"
	FxRateColKey               = "fx_rate"
	CBSNumberCoLabel           = "CBS Number"
	CBSNumberColKey            = "cbs_number"
	FiExternalTxnNumberCoLabel = "Fi external Txn number"
	FiExternalTxnNumberColKey  = "fi_external_txn_number"
	GSTPaidCoLabel             = "GST Paid"
	GSTPaidColKey              = "gst_paid"
	TCSPaidCoLabel             = "TCS Paid"
	TCSPaidColKey              = "tcs_paid"
	ActionCoLabel              = "Action"
)

var (
	getRemittancesTableHeader = []*webui.TableHeader{
		{
			Label:     RemittanceIDCoLabel,
			HeaderKey: RemittanceIDColKey,
			IsVisible: true,
		},
		{
			Label:     CreatedAtCoLabel,
			HeaderKey: CreatedAtColKey,
			IsVisible: true,
		},
		{
			Label:     UpdatedAtCoLabel,
			HeaderKey: UpdatedAtColKey,
			IsVisible: true,
		},
		{
			Label:     ValueOfRemittanceCoLabel,
			HeaderKey: ValueOfRemittanceColKey,
			IsVisible: true,
		},
		{
			Label:     UnitCoLabel,
			HeaderKey: UnitColKey,
			IsVisible: true,
		},
		{
			Label:     RemittanceTypeCoLabel,
			HeaderKey: RemittanceTypeColKey,
			IsVisible: true,
		},
		{
			Label:     CurrentStatusCoLabel,
			HeaderKey: CreatedAtColKey,
			IsVisible: true,
		},
		{
			Label:     FxRateCoLabel,
			HeaderKey: FxRateColKey,
			IsVisible: true,
		},
		{
			Label:     CBSNumberCoLabel,
			HeaderKey: CBSNumberColKey,
			IsVisible: true,
		},
		{
			Label:     FiExternalTxnNumberCoLabel,
			HeaderKey: FiExternalTxnNumberColKey,
			IsVisible: true,
		},
		{
			Label:     GSTPaidCoLabel,
			HeaderKey: GSTPaidColKey,
			IsVisible: true,
		},
		{
			Label:     TCSPaidCoLabel,
			HeaderKey: TCSPaidColKey,
			IsVisible: true,
		},
	}
)

func (s *Service) GetRemittances(ctx context.Context, req *usstocks.GetRemittancesRequest) (*usstocks.GetRemittancesResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &usstocks.GetRemittancesResponse{Status: rpc.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	var pageToken *rpc.PageContextResponse
	var orders []*orderPb.WalletOrder
	if len(req.GetRemittanceId()) > 0 {
		getWalletOrderResp, err := s.OrderManagerClient.GetWalletOrder(ctx, &orderPb.GetWalletOrderRequest{
			Id:      &orderPb.GetWalletOrderRequest_OrderId{OrderId: req.GetRemittanceId()},
			ActorId: req.GetHeader().GetActor().GetId(),
		})
		if err2 := epifigrpc.RPCError(getWalletOrderResp, err); err2 != nil && !getWalletOrderResp.GetStatus().IsRecordNotFound() {
			cxLogger.Error(ctx, "error while GetWalletOrder", zap.Error(err2))
			return &usstocks.GetRemittancesResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		orders = append(orders, getWalletOrderResp.GetWalletOrder())
	} else {
		getWalletOrdersResp, err := s.OrderManagerClient.GetWalletOrders(ctx, &orderPb.GetWalletOrdersRequest{
			ActorId:     req.GetHeader().GetActor().GetId(),
			OrderType:   getWalletOrderType(req.GetRemittanceType()),
			PageContext: req.GetPageContext(),
			StartTime:   req.GetFromDate(),
			EndTime:     req.GetToDate(),
		})
		if err2 := epifigrpc.RPCError(getWalletOrdersResp, err); err2 != nil {
			cxLogger.Error(ctx, "error while GetWalletOrders", zap.Error(err2))
			return &usstocks.GetRemittancesResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		pageToken = getWalletOrdersResp.GetPageContext()
		orders = getWalletOrdersResp.GetOrders()
	}
	return &usstocks.GetRemittancesResponse{
		Status:      rpc.StatusOk(),
		Remittances: getWalletOrderTable(orders),
		PageContext: pageToken,
	}, nil
}

func getWalletOrderType(remittanceType usstocks.RemittanceType) []usstocksPb.WalletOrderType {
	walletOrderTypes := make([]usstocksPb.WalletOrderType, 0)
	switch remittanceType {
	case usstocks.RemittanceType_REMITTANCE_TYPE_ADD_FUNDS:
		walletOrderTypes = append(walletOrderTypes, usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS)
	case usstocks.RemittanceType_REMITTANCE_TYPE_WITHDRAWAL_FUNDS:
		walletOrderTypes = append(walletOrderTypes, usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_WITHDRAW_FUNDS)
	}
	return walletOrderTypes
}

func getWalletOrderTable(walletOrders []*orderPb.WalletOrder) []*webui.Table {
	return []*webui.Table{
		{
			TableHeaders: getRemittancesTableHeader,
			TableRows:    getRemittancesTableRows(walletOrders),
			Actions: []*webui.CTA{
				{
					Label: "View all",
				},
			},
		},
	}
}

func getRemittancesTableRows(walletOrders []*orderPb.WalletOrder) []*webui.TableRow {
	rows := make([]*webui.TableRow, 0)
	for _, walletOrder := range walletOrders {
		headerKeyCellMap := getRemittancesOrderRow(walletOrder)
		rows = append(rows, &webui.TableRow{
			HeaderKeyCellMap: headerKeyCellMap,
			Meta:             walletOrder.GetId(),
		})
	}
	return rows
}

// nolint: funlen
func getRemittancesOrderRow(walletOrder *orderPb.WalletOrder) map[string]*webui.TableCell {
	res := make(map[string]*webui.TableCell, 0)
	res[RemittanceIDColKey] = &webui.TableCell{
		Value: walletOrder.GetId(),
	}
	res[CreatedAtColKey] = &webui.TableCell{
		Value: walletOrder.GetCreatedAt().AsTime().Format(RemittanceTimeFormat),
	}
	res[UpdatedAtColKey] = &webui.TableCell{
		Value: walletOrder.GetUpdatedAt().AsTime().Format(RemittanceTimeFormat),
	}
	res[ValueOfRemittanceColKey] = &webui.TableCell{
		Value: moneyPkg.ToDisplayStringWithSuffixAndPrecision(walletOrder.GetAmountRequested(), true, true, 2, moneyPkg.InternationalNumberSystem),
	}
	res[UnitColKey] = &webui.TableCell{
		Value: moneyPkg.ToDisplayStringWithSuffixAndPrecision(walletOrder.GetInvoiceDetails().GetAmountIn_USD(), true, true, 2, moneyPkg.InternationalNumberSystem),
	}
	res[RemittanceTypeColKey] = &webui.TableCell{
		Value: toDisplayWalletOrderType(walletOrder.GetOrderType()),
	}
	res[CurrentStatusColKey] = &webui.TableCell{
		Value: strings.ReplaceAll(walletOrder.GetStatus().String(), "WALLET_ORDER_STATUS_", ""),
	}
	res[CBSNumberColKey] = &webui.TableCell{
		Value: walletOrder.GetPaymentInfo().GetUtrNumber(),
	}
	res[FiExternalTxnNumberColKey] = &webui.TableCell{
		Value: walletOrder.GetExternalOrderId(),
	}
	res[GSTPaidColKey] = &webui.TableCell{
		Value: moneyPkg.ToDisplayStringWithSuffixAndPrecision(walletOrder.GetInvoiceDetails().GetGST(), true, true, 2, moneyPkg.InternationalNumberSystem),
	}
	res[TCSPaidColKey] = &webui.TableCell{
		Value: moneyPkg.ToDisplayStringWithSuffixAndPrecision(walletOrder.GetInvoiceDetails().GetTCS(), true, true, 2, moneyPkg.InternationalNumberSystem),
	}
	res[FxRateColKey] = &webui.TableCell{
		Value: moneyPkg.ToDisplayStringWithSuffixAndPrecision(walletOrder.GetInvoiceDetails().GetPartnerExchangeRate(), true, true, 2, moneyPkg.InternationalNumberSystem),
	}
	res[CBSNumberColKey] = &webui.TableCell{
		Value: walletOrder.GetPaymentInfo().GetUtrNumber(),
	}
	return res
}

func toDisplayWalletOrderType(orderType usstocksPb.WalletOrderType) string {
	if orderType == usstocksPb.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS {
		return "Add Funds"
	}
	return "Withdraw Funds"
}
