package referrals

import (
	"context"

	"github.com/epifi/gamma/cx/config"

	oPb "github.com/epifi/gamma/api/order/cx"

	savingsPb "github.com/epifi/gamma/api/savings"

	onboardingPb "github.com/epifi/gamma/api/user/onboarding"

	helper2 "github.com/epifi/gamma/cx/data_collector/helper"

	"github.com/epifi/gamma/cx/helper"

	rewardsPb "github.com/epifi/gamma/api/rewards"

	"go.uber.org/zap"

	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxReferralPb "github.com/epifi/gamma/api/cx/data_collector/referrals"
	inappReferralPb "github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
)

const (
	QualifyingConditionDescription = "Add Rs. 3000 in 7 days"
)

type Service struct {
	authEngine         auth_engine.IAuthEngine
	refClient          inappReferralPb.InAppReferralClient
	rewardsClient      rewardsPb.RewardsGeneratorClient
	customerIdentifier helper.ICustomerIdentifier
	onboardingClient   onboardingPb.OnboardingClient
	savingsClient      savingsPb.SavingsClient
	orderTxnClient     oPb.CXClient
	referralConfig     *config.ReferralConfig
}

func NewReferralService(authEngine auth_engine.IAuthEngine, refClient inappReferralPb.InAppReferralClient,
	rewardsClient rewardsPb.RewardsGeneratorClient, customerIdentifier helper.ICustomerIdentifier,
	onboardingClient onboardingPb.OnboardingClient, savingsClient savingsPb.SavingsClient,
	orderTxnClient oPb.CXClient, referralConfig *config.ReferralConfig) *Service {
	return &Service{
		rewardsClient:      rewardsClient,
		authEngine:         authEngine,
		refClient:          refClient,
		customerIdentifier: customerIdentifier,
		onboardingClient:   onboardingClient,
		savingsClient:      savingsClient,
		orderTxnClient:     orderTxnClient,
		referralConfig:     referralConfig,
	}
}

var _ cxReferralPb.ReferralsServer = &Service{}

func (s *Service) GetReferralDetailsForActor(ctx context.Context, req *cxReferralPb.GetReferralDetailsForActorRequest) (*cxReferralPb.GetReferralDetailsForActorResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxReferralPb.GetReferralDetailsForActorResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	referralResp, err := s.refClient.GetReferralDetailsForActor(ctx, &inappReferralPb.GetReferralDetailsForActorRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(referralResp, err); te != nil {
		if referralResp.GetStatus().IsRecordNotFound() {
			cxLogger.Info(ctx, "referral details not found for user")
			return &cxReferralPb.GetReferralDetailsForActorResponse{
				Status:               rpcPb.StatusOk(),
				OnboardedViaReferral: false,
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching referral details for actor", zap.Error(te))
		return &cxReferralPb.GetReferralDetailsForActorResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referral details")}, nil
	}
	rewardDetails, err := s.GetRewardDetailsUsingClaimId(ctx, req.GetHeader().GetActor().GetId(), referralResp.GetReferralDetails().GetFiniteCodeClaimId())
	if err != nil {
		cxLogger.Error(ctx, "error while fetching referral reward details using finite code claim id for user", zap.Error(err))
		return &cxReferralPb.GetReferralDetailsForActorResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referral details")}, nil
	}
	referrerUser, err := s.customerIdentifier.GetUserByActorId(ctx, referralResp.GetReferralDetails().GetReferrerActorId())
	if err != nil {
		cxLogger.Error(ctx, "error while fetching referrer actor details", zap.Error(err))
		return &cxReferralPb.GetReferralDetailsForActorResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referral details")}, nil
	}
	return s.buildReferralDetailsResponse(ctx, req.GetHeader(), referralResp.GetReferralDetails(), rewardDetails, referrerUser)
}

func (s *Service) GetReferrerDetails(ctx context.Context, req *cxReferralPb.GetReferrerDetailsRequest) (*cxReferralPb.GetReferrerDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "customer auth is required to access the resource")
		return &cxReferralPb.GetReferrerDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	referrerSummaryResp, err := s.refClient.GetReferralSummaryForReferrer(ctx, &inappReferralPb.GetReferralSummaryForReferrerRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(referrerSummaryResp, err); te != nil {
		if referrerSummaryResp.GetStatus().IsRecordNotFound() {
			cxLogger.Info(ctx, "record not found for referrer")
			return &cxReferralPb.GetReferrerDetailsResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		cxLogger.Error(ctx, "error while fetching referrer details for actor", zap.Error(te))
		return &cxReferralPb.GetReferrerDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referrer details")}, nil
	}
	rewardSummaryResp, err := s.rewardsClient.GetRewardSummary(ctx, &rewardsPb.GetRewardSummaryRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		Filter: &rewardsPb.GetRewardSummaryRequest_Filter{
			RewardOfferType: rewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER,
		},
	})
	if te := epifigrpc.RPCError(rewardSummaryResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching reward summary for actor", zap.Error(te))
		return &cxReferralPb.GetReferrerDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch reward details")}, nil
	}
	return s.buildReferrerDetailsResp(referrerSummaryResp, rewardSummaryResp)
}

func (s *Service) GetRefereesForActor(ctx context.Context, req *cxReferralPb.GetRefereesForActorRequest) (*cxReferralPb.GetRefereesForActorResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "customer auth is required to access the resource")
		return &cxReferralPb.GetRefereesForActorResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	refereesResp, err := s.refClient.GetRefereesForActor(ctx, &inappReferralPb.GetRefereesForActorRequest{
		ActorId:     req.GetHeader().GetActor().GetId(),
		PageSize:    s.referralConfig.RefereePageSize,
		PageContext: helper2.ConvertToRpcPageContext(req.GetPageContext()),
	})
	if te := epifigrpc.RPCError(refereesResp, err); te != nil {
		if refereesResp.GetStatus().IsRecordNotFound() {
			cxLogger.Info(ctx, "record not found for referrer")
			return &cxReferralPb.GetRefereesForActorResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		cxLogger.Error(ctx, "error while fetching referrer details for actor", zap.Error(te))
		return &cxReferralPb.GetRefereesForActorResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referrer details")}, nil
	}
	return buildReferresForActorResponse(refereesResp)
}

func (s *Service) GetRefereeDetails(ctx context.Context, req *cxReferralPb.GetRefereeDetailsRequest) (*cxReferralPb.GetRefereeDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "customer auth is required to access the resource")
		return &cxReferralPb.GetRefereeDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	refereeUser, err := s.customerIdentifier.GetUserByActorId(ctx, req.GetActorId())
	if err != nil {
		cxLogger.Error(ctx, "error while fetching referee user details", zap.Error(err))
		return &cxReferralPb.GetRefereeDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referee user details")}, nil
	}
	referralResp, err := s.refClient.GetReferralDetailsForActor(ctx, &inappReferralPb.GetReferralDetailsForActorRequest{
		ActorId: req.GetActorId(),
	})
	if te := epifigrpc.RPCError(referralResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching referee details for referee", zap.Error(te))
		return &cxReferralPb.GetRefereeDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referee details")}, nil
	}
	rewardDetails, err := s.GetRewardDetailsUsingClaimId(ctx, req.GetActorId(), referralResp.GetReferralDetails().GetFiniteCodeClaimId())
	if err != nil {
		cxLogger.Error(ctx, "error while fetching referee reward details using finite code claim id for referee", zap.Error(err))
		return &cxReferralPb.GetRefereeDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referral details")}, nil
	}
	onboardingDetails, err := s.onboardingClient.GetDetails(ctx, &onboardingPb.GetDetailsRequest{
		ActorId: req.GetActorId(),
	})
	if te := epifigrpc.RPCError(onboardingDetails, err); te != nil {
		cxLogger.Error(ctx, "error while fetching onboarding details for referee", zap.Error(te))
		return &cxReferralPb.GetRefereeDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch referee details")}, nil
	}
	return s.buildRefereeDetailsResp(ctx, req.GetActorId(), refereeUser, rewardDetails, onboardingDetails, referralResp.GetReferralDetails())
}
