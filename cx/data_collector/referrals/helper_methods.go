package referrals

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxPb "github.com/epifi/gamma/api/cx"
	cxReferralPb "github.com/epifi/gamma/api/cx/data_collector/referrals"
	inappReferralPb "github.com/epifi/gamma/api/inappreferral"
	orderPb "github.com/epifi/gamma/api/order"
	oPb "github.com/epifi/gamma/api/order/cx"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/cx/data_collector/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

func (s *Service) GetRewardDetailsUsingClaimId(ctx context.Context, actorId string, claimId string) (*rewardsPb.Reward, error) {
	resp, err := s.rewardsClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId: actorId,
		Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
			ExternalRefList: []string{claimId},
			RewardOfferType: rewardsPb.RewardOfferType_REFERRAL_REFEREE_OFFER,
		},
		PageContext: &rpcPb.PageContextRequest{
			PageSize: 10,
		},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return nil, errors.Wrap(te, "error while fetching reward details for actor")
	}
	// no reward found for user
	if len(resp.GetRewards()) == 0 {
		return nil, nil
	}
	return resp.GetRewards()[0], nil
}

func (s *Service) buildReferralDetailsResponse(ctx context.Context, header *cxPb.Header, referralDetails *inappReferralPb.GetReferralDetailsForActorResponse_ReferralDetails,
	rewardDetails *rewardsPb.Reward, referrerUser *userPb.User) (*cxReferralPb.GetReferralDetailsForActorResponse, error) {
	isQualifyingEventPassed, err := s.isQualifyingConditionPassedForUser(ctx, header.GetActor().GetId(), referrerUser.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error while checking qualifying event status")
	}
	resp := &cxReferralPb.GetReferralDetailsForActorResponse{
		Status:               rpcPb.StatusOk(),
		OnboardedViaReferral: true,
		ReferralDetails: &cxReferralPb.ReferralDetails{
			FiniteCodeChannel:          referralDetails.GetFiniteCodeChannel().String(),
			FiniteCodeId:               referralDetails.GetFiniteCodeId(),
			ReferrerName:               referrerUser.GetProfile().GetKycName(),
			FiniteCodeType:             referralDetails.GetFiniteCodeType().String(),
			FiniteCodeClaimId:          referralDetails.GetFiniteCodeClaimId(),
			QualifyingEventDescription: QualifyingConditionDescription,
			IsQualifyingEventPassed:    isQualifyingEventPassed,
			FiniteCode:                 referralDetails.GetCode(),
		},
		RewardDetails: getRewardDetails(rewardDetails),
	}

	return resp, nil
}

func getRewardDetails(rewardDetails *rewardsPb.Reward) *cxReferralPb.RewardDetails {
	resp := &cxReferralPb.RewardDetails{
		RewardEarned: isRewardEarned(rewardDetails),
		RewardState:  rewardDetails.GetStatus().String(),
		CreatedAt:    rewardDetails.GetCreatedAt(),
		RewardType:   rewardDetails.GetChosenReward().GetRewardType().String(),
	}
	switch rewardDetails.GetChosenReward().GetOption().(type) {
	case *rewardsPb.RewardOption_Cash:
		resp.Option = &cxReferralPb.RewardDetails_Cash{
			Cash: rewardDetails.GetChosenReward().GetCash(),
		}
	case *rewardsPb.RewardOption_FiCoins:
		resp.Option = &cxReferralPb.RewardDetails_FiCoins{
			FiCoins: rewardDetails.GetChosenReward().GetFiCoins(),
		}
	case *rewardsPb.RewardOption_GiftHamper:
		resp.Option = &cxReferralPb.RewardDetails_GiftHamper{
			GiftHamper: rewardDetails.GetChosenReward().GetGiftHamper(),
		}
	case *rewardsPb.RewardOption_LuckyDraw:
		resp.Option = &cxReferralPb.RewardDetails_LuckyDraw{
			LuckyDraw: rewardDetails.GetChosenReward().GetLuckyDraw(),
		}
	case *rewardsPb.RewardOption_SmartDeposit:
		resp.Option = &cxReferralPb.RewardDetails_SmartDeposit{
			SmartDeposit: rewardDetails.GetChosenReward().GetSmartDeposit(),
		}
	}
	return resp
}

func isRewardEarned(details *rewardsPb.Reward) commontypes.BooleanEnum {
	if details == nil {
		return commontypes.BooleanEnum_FALSE
	}
	return commontypes.BooleanEnum_TRUE
}

// this method checks if sum of all credit txns for a user in first n days is above x amount
// will return true if condition is satisfied
// will return false otherwise
// will return error if fetch txn fails
func (s *Service) isQualifyingConditionPassedForUser(ctx context.Context, actorId string, userId string) (commontypes.BooleanEnum, error) {
	savingsResp, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: userId,
		},
	})
	if err != nil || savingsResp.GetAccount() == nil {
		cxLogger.Error(ctx, "unable to get savings account details for user", zap.Error(err))
		return commontypes.BooleanEnum_FALSE, nil
	}
	sevenDaysDuration := time.Hour * 7 * 24
	fromTime := savingsResp.GetAccount().GetCreationInfo().GetFiCreationSucceededAt().AsTime()
	// Add 7 days to from time
	toTime := fromTime.Add(sevenDaysDuration)

	fromDate := datetime.TimeToDate(&fromTime)
	toDate := datetime.TimeToDate(&toTime)
	orderReq := &oPb.GetOrdersForActorRequest{
		ActorId:  actorId,
		FromDate: fromDate,
		ToDate:   toDate,
		PageSize: 10,
	}
	hasMoreTxn := true
	totalCreditAmount := moneyPb.AmountINR(0).GetPb()

	for hasMoreTxn {
		resp, err := s.orderTxnClient.GetOrdersForActor(ctx, orderReq)
		if te := epifigrpc.RPCError(resp, err); te != nil {
			if resp.GetStatus().IsRecordNotFound() {
				hasMoreTxn = false
				continue
			}
			return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.Wrap(te, "unable to get all orders for an actor")
		}
		if len(resp.GetResults()) == 0 {
			hasMoreTxn = false
			continue
		}
		for _, order := range resp.GetResults() {
			// check only successful credit txns
			if order.GetToActorId() == actorId && isOrderSuccess(order.GetStatus()) {
				totalCreditAmount, err = moneyPb.Sum(totalCreditAmount, order.Amount)
				if err != nil {
					return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.Wrap(err, "error while calculating sum of order amounts")
				}
				if totalCreditAmount.GetUnits() >= 3000 {
					return commontypes.BooleanEnum_TRUE, nil
				}
			}
		}
		// set after page token for fetching next page
		orderReq.Token = &oPb.GetOrdersForActorRequest_AfterPageToken{
			AfterPageToken: resp.GetAfterPageToken(),
		}
	}
	return commontypes.BooleanEnum_FALSE, nil
}

func isOrderSuccess(status orderPb.OrderStatus) bool {
	return status == orderPb.OrderStatus_SETTLED || status == orderPb.OrderStatus_PAID
}

func (s *Service) buildReferrerDetailsResp(referrerResp *inappReferralPb.GetReferralSummaryForReferrerResponse, rewardResp *rewardsPb.GetRewardSummaryResponse) (*cxReferralPb.GetReferrerDetailsResponse, error) {
	return &cxReferralPb.GetReferrerDetailsResponse{
		Status:          rpcPb.StatusOk(),
		ReferrerDetails: getReferrerDetails(referrerResp),
		RewardSummary:   getRewardSummary(rewardResp),
	}, nil
}

func getRewardSummary(resp *rewardsPb.GetRewardSummaryResponse) *cxReferralPb.ReferrerRewardSummary {
	return &cxReferralPb.ReferrerRewardSummary{
		TotalCashRewardEarned:             resp.GetTotalCashRewardEarned(),
		TotalSidRewardEarned:              resp.GetTotalSidRewardEarned(),
		TotalFiCoinsEarned:                resp.GetTotalFiCoinsEarned(),
		TotalInProcessingCashRewardAmount: resp.GetTotalInProcessingCashRewardAmount(),
		TotalInProcessingFiCoins:          resp.GetTotalInProcessingFiCoins(),
		TotalInProcessingSidRewardAmount:  resp.GetTotalInProcessingSidRewardAmount(),
		TotalCountOfRewards:               resp.GetTotalCountOfRewards(),
		TotalCountOfCashRewards:           resp.GetTotalCountOfCashRewards(),
		TotalCountOfFiCoinRewards:         resp.GetTotalCountOfFiCoinRewards(),
		TotalCountOfSidRewards:            resp.GetTotalCountOfSidRewards(),
	}
}

func getReferrerDetails(resp *inappReferralPb.GetReferralSummaryForReferrerResponse) *cxReferralPb.ReferrerDetails {
	var finiteCodeStrings []string
	var totalClaimCount int64
	for _, finiteCode := range resp.GetFiniteCodes() {
		finiteCodeStrings = append(finiteCodeStrings, finiteCode.GetCode())
		totalClaimCount += int64(finiteCode.ClaimedCount)
	}
	return &cxReferralPb.ReferrerDetails{
		IsEligibleForReferral: boolToBooleanEnum(resp.GetIsEligibleForReferral()),
		FiniteCodes:           finiteCodeStrings,
		TotalClaimCount:       totalClaimCount,
	}
}

func boolToBooleanEnum(val bool) commontypes.BooleanEnum {
	if val {
		return commontypes.BooleanEnum_TRUE
	}
	return commontypes.BooleanEnum_FALSE
}

func buildReferresForActorResponse(resp *inappReferralPb.GetRefereesForActorResponse) (*cxReferralPb.GetRefereesForActorResponse, error) {
	var referees []*cxReferralPb.Referee
	for _, referee := range resp.GetRecentReferees() {
		referees = append(referees, &cxReferralPb.Referee{
			ActorId:                 referee.GetActorId(),
			RefereeName:             referee.GetName(),
			RefereeOnboardingStatus: referee.GetOnboardingStatus().String(),
			FiniteCodeUsed:          referee.GetFiniteCode(),
		})
	}
	return &cxReferralPb.GetRefereesForActorResponse{
		Status:      rpcPb.StatusOk(),
		Referees:    referees,
		PageContext: helper.ConvertToCxPageContext(resp.GetPageContext()),
	}, nil
}

func (s *Service) buildRefereeDetailsResp(ctx context.Context, actorId string, user *userPb.User,
	rewardDetails *rewardsPb.Reward, onboardingDetails *onbPb.GetDetailsResponse,
	referralDetails *inappReferralPb.GetReferralDetailsForActorResponse_ReferralDetails) (*cxReferralPb.GetRefereeDetailsResponse, error) {
	isQualifyingEventPassed, err := s.isQualifyingConditionPassedForUser(ctx, actorId, user.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "error while checking qualifying event status")
	}
	resp := &cxReferralPb.GetRefereeDetailsResponse{
		Status: rpcPb.StatusOk(),
		RefereeDetails: &cxReferralPb.RefereeDetails{
			RewardDetails:              getRewardDetails(rewardDetails),
			PhoneNumber:                user.GetProfile().GetPhoneNumber(),
			QualifyingEventDescription: QualifyingConditionDescription,
			IsQualifyingEventPassed:    isQualifyingEventPassed,
			CurrentOnboardingStage:     onboardingDetails.GetDetails().GetCurrentOnboardingStage().String(),
			FiniteCodeClaimId:          referralDetails.GetFiniteCodeClaimId(),
		},
	}
	return resp, nil
}
