package wealth_onboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strings"

	"github.com/epifi/be-common/api/rpc"
	cxWonbPb "github.com/epifi/gamma/api/cx/data_collector/wealth_onboarding"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"go.uber.org/zap"
)

var (
	onboardingTypeMap = map[cxWonbPb.OnboardingType]woPb.OnboardingType{
		cxWonbPb.OnboardingType_ONBOARDING_TYPE_UNSPECIFIED:    woPb.OnboardingType_ONBOARDING_TYPE_UNSPECIFIED,
		cxWonbPb.OnboardingType_ONBOARDING_TYPE_WEALTH:         woPb.OnboardingType_ONBOARDING_TYPE_WEALTH,
		cxWonbPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT: woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
	}
	statusStringMap = map[woPb.OnboardingStatus]string{
		woPb.OnboardingStatus_ONBOARDING_STATUS_UNSPECIFIED:                "UNSPECIFIED",
		woPb.OnboardingStatus_ONBOARDING_STATUS_PENDING:                    "PENDING",
		woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS:                "IN_PROGRESS",
		woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED:                  "COMPLETED",
		woPb.OnboardingStatus_ONBOARDING_STATUS_FAILED:                     "FAILED",
		woPb.OnboardingStatus_ONBOARDING_STATUS_MANUAL_INTERVENTION_NEEDED: "MANUAL_INTERVENTION_NEEDED",
		woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE:               "CONSIDER_IN_FUTURE",
	}
)

type Service struct {
	wonbClient   woPb.WealthOnboardingClient
	wonbCxClient woCxPb.WealthCxServiceClient
	authEngine   auth_engine.IAuthEngine
}

func NewService(wonbClient woPb.WealthOnboardingClient, wonbCxClient woCxPb.WealthCxServiceClient, authEngine auth_engine.IAuthEngine) *Service {
	return &Service{
		wonbClient:   wonbClient,
		wonbCxClient: wonbCxClient,
		authEngine:   authEngine,
	}
}

// nolint: funlen
func (s *Service) GetOnboardingDetails(ctx context.Context, req *cxWonbPb.GetOnboardingDetailsRequest) (*cxWonbPb.GetOnboardingDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxWonbPb.GetOnboardingDetailsResponse{Status: rpc.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	onbResp, onbErr := s.wonbClient.GetOnboardingTroubleshootDetails(ctx, &woPb.GetOnboardingTroubleshootDetailsRequest{
		ActorId:        actorId,
		OnboardingType: onboardingTypeMap[req.GetOnboardingType()],
	})
	if rpcErr := epifigrpc.RPCError(onbResp, onbErr); rpcErr != nil {
		cxLogger.Error(ctx, "error in fetching wealth onboarding details", zap.Error(rpcErr))
		// Record not found error
		if onbResp.GetStatus().IsRecordNotFound() {
			cxLogger.Error(ctx, "wealth onboarding record not found for the actor", zap.Error(rpcErr))
			return &cxWonbPb.GetOnboardingDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		// Other error
		return &cxWonbPb.GetOnboardingDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	onbDetails := &cxWonbPb.OnboardingDetails{
		Status: statusStringMap[onbResp.GetOnboardingTroubleshootDetails().GetStatus()],
		CurrentStageTroubleShootingDetails: &cxWonbPb.CurrentStageTroubleShootingDetails{
			Stage:             onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetStage(),
			Status:            onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetStatus(),
			UpdatedAt:         onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetUpdatedAt(),
			L1:                onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetL1(),
			L2:                onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetL2(),
			L3:                onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetL3(),
			Advice:            onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetAdvice(),
			AdditionalDetails: onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetAdditionalDetails(),
		},
		StageDetailsMapping: make(map[string]*cxWonbPb.StageDetails),
	}
	for k, v := range onbResp.GetOnboardingTroubleshootDetails().GetStageDetailsMapping() {
		onbDetails.StageDetailsMapping[k] = &cxWonbPb.StageDetails{
			Status:      v.GetStatus(),
			Order:       v.GetOrder(),
			Description: v.GetDescription(),
			UpdatedAt:   v.GetUpdatedAt(),
			MetaData:    v.GetMetaData(),
		}
	}

	return &cxWonbPb.GetOnboardingDetailsResponse{
		Status:            rpc.StatusOk(),
		OnboardingDetails: onbDetails,
	}, nil
}

func (s *Service) GetIsInvestmentReady(ctx context.Context, req *cxWonbPb.GetIsInvestmentReadyRequest) (*cxWonbPb.GetIsInvestmentReadyResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxWonbPb.GetIsInvestmentReadyResponse{Status: rpc.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	onbResp, onbErr := s.wonbClient.GetOnboardingTroubleshootDetails(ctx, &woPb.GetOnboardingTroubleshootDetailsRequest{
		ActorId:        actorId,
		OnboardingType: woPb.OnboardingType_ONBOARDING_TYPE_PRE_INVESTMENT,
	})
	if rpcErr := epifigrpc.RPCError(onbResp, onbErr); rpcErr != nil {
		cxLogger.Error(ctx, "error in fetching wealth onboarding details", zap.Error(rpcErr))
		// Record not found error
		if onbResp.GetStatus().IsRecordNotFound() {
			cxLogger.Error(ctx, "wealth onboarding record not found for the actor", zap.Error(rpcErr))
			return &cxWonbPb.GetIsInvestmentReadyResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		// Other error
		return &cxWonbPb.GetIsInvestmentReadyResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	isInvReady := commontypes.BooleanEnum_FALSE
	if onbResp.GetOnboardingTroubleshootDetails().GetStatus() == woPb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED ||
		(onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetStage() == woPb.OnboardingStep_ONBOARDING_STEP_DOWNLOAD_KRA_DOC.String() &&
			!strings.Contains(onbResp.GetOnboardingTroubleshootDetails().GetCurrentStageTroubleShootingDetails().GetL1(), woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_EMPTY_DOWNLOAD_API_RESPONSE.String())) {
		isInvReady = commontypes.BooleanEnum_TRUE
	}
	return &cxWonbPb.GetIsInvestmentReadyResponse{
		Status:            rpc.StatusOk(),
		IsInvestmentReady: isInvReady,
	}, nil
}

func (s *Service) GetCustomerProfileDetails(ctx context.Context, req *cxWonbPb.GetCustomerProfileDetailsRequest) (*cxWonbPb.GetCustomerProfileDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxWonbPb.GetCustomerProfileDetailsResponse{Status: rpc.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	onbResp, onbErr := s.wonbCxClient.GetCustomerProfileDetails(ctx, &woCxPb.GetCustomerProfileDetailsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(onbResp, onbErr); rpcErr != nil {
		// Record not found error
		if onbResp.GetStatus().IsRecordNotFound() {
			cxLogger.Info(ctx, "wealth onboarding record not found for the actor", zap.String("actorID", actorId))
			return &cxWonbPb.GetCustomerProfileDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		// Other error
		cxLogger.Error(ctx, "error in fetching customer profile details", zap.Error(rpcErr))
		return &cxWonbPb.GetCustomerProfileDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &cxWonbPb.GetCustomerProfileDetailsResponse{
		Status: rpc.StatusOk(),
		CustomerDetails: &cxWonbPb.CustomerProfileDetails{
			Name:        onbResp.GetCustomerProfileDetails().GetName(),
			Gender:      onbResp.GetCustomerProfileDetails().GetGender(),
			PhoneNumber: onbResp.GetCustomerProfileDetails().GetPhoneNumber(),
			Email:       onbResp.GetCustomerProfileDetails().GetEmail(),
			BankDetails: &cxWonbPb.CustomerProfileDetails_BankDetails{
				AccountNumber: onbResp.GetCustomerProfileDetails().GetBankDetails().GetAccountNumber(),
				IfscCode:      onbResp.GetCustomerProfileDetails().GetBankDetails().GetIfscCode(),
				AccountType:   onbResp.GetCustomerProfileDetails().GetBankDetails().GetAccountType(),
				BankName:      onbResp.GetCustomerProfileDetails().GetBankDetails().GetBankName(),
				BranchName:    onbResp.GetCustomerProfileDetails().GetBankDetails().GetBranchName(),
				BankCity:      onbResp.GetCustomerProfileDetails().GetBankDetails().GetBankCity(),
			},
			AddressWithType: onbResp.GetCustomerProfileDetails().GetAddressWithType(),
		},
		SherlockDeepLink: nil,
	}, nil
}
