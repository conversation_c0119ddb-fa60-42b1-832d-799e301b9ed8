package transaction

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	tPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	recurringPay "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enums"
	"github.com/epifi/gamma/api/typesv2/webui"
	cxLogger "github.com/epifi/gamma/cx/logger"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

const (
	MaxNoOfMonthsForWhichFutureTxnIsShown = 3
)

func (s *Service) GetUpcomingTransactions(ctx context.Context, req *tPb.GetUpcomingTransactionsRequest) (*tPb.GetUpcomingTransactionsResponse, error) {
	var (
		beGetUpcomingTxnsReq  *recurringPay.GetUpcomingTransactionsRequest
		beGetUpcomingTxnsResp *recurringPay.GetUpcomingTransactionsResponse
		res                   = &tPb.GetUpcomingTransactionsResponse{}
		err                   error
	)

	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		res.Status = rpcPb.StatusOk()
		res.SherlockDeepLink = sherlockDeepLink
		return res, nil
	}

	if req.GetHeader().GetActor() == nil {
		res.Status = rpcPb.StatusInvalidArgumentWithDebugMsg("actor info not present in header")
		return res, nil
	}

	beGetUpcomingTxnsReq = &recurringPay.GetUpcomingTransactionsRequest{
		ActorId:         req.GetHeader().GetActor().GetId(),
		ToTime:          timestampPb.New(datetimePkg.TimeFromNow(MaxNoOfMonthsForWhichFutureTxnIsShown * time.Hour)),
		AccountingEntry: paymentPb.AccountingEntryType_DEBIT,
		Source: []enums.UpcomingTransactionSource{
			enums.UpcomingTransactionSource_DS,
			enums.UpcomingTransactionSource_FITTT,
		},
	}

	beGetUpcomingTxnsResp, err = s.recurringPaymentsClient.GetUpcomingTransactions(ctx, beGetUpcomingTxnsReq)
	if err = epifigrpc.RPCError(beGetUpcomingTxnsResp, err); err != nil {
		cxLogger.Error(ctx, "unable to get upcoming transactions for given actor", zap.Error(err))
		res.Status = rpcPb.StatusInternalWithDebugMsg("unable to get upcoming transactions for given actor")
		return res, nil
	}

	res, err = s.createUpcomingTransactionsResponse(beGetUpcomingTxnsResp.GetUpcomingTxns())
	if err != nil {
		cxLogger.Error(ctx, "unable to create table view for upcoming txns response", zap.Error(err))
		res.Status = rpcPb.StatusInternalWithDebugMsg("unable to create table view for upcoming txns response")
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) createUpcomingTransactionsResponse(upcomingTxnsList []*recurringPay.UpcomingTransaction) (
	*tPb.GetUpcomingTransactionsResponse, error) {
	var (
		getUpcomingTransactionsResp = &tPb.GetUpcomingTransactionsResponse{}
		tableRows                   []*webui.TableRow
		err                         error
	)

	getUpcomingTransactionsResp.UpcomingTransactionDetails = createUpcomingTransactionsTable()

	tableRows, err = s.createUpcomingTransactionsRowData(upcomingTxnsList)
	if err != nil {
		return nil, fmt.Errorf("failed to populate data in table rows: %w", err)
	}
	getUpcomingTransactionsResp.UpcomingTransactionDetails.TableRows = tableRows

	return getUpcomingTransactionsResp, nil
}

func createUpcomingTransactionsTable() *webui.Table {
	return &webui.Table{
		TableName: UpcomingTransactionsTableName,
		TableHeaders: []*webui.TableHeader{
			{Label: UpcomingTxnsDebitDateLabel, HeaderKey: UpcomingTxnsDebitDateKey},
			{Label: MerchantNameLabel, HeaderKey: MerchantNameKey},
			{Label: AmountLabel, HeaderKey: AmountKey},
			{Label: ModeLabel, HeaderKey: ModeKey},
			{Label: UpcomingTxnsTypeLabel, HeaderKey: UpcomingTxnsTypeKey},
		},
	}
}

func (s *Service) createUpcomingTransactionsRowData(upcomingTxnsList []*recurringPay.UpcomingTransaction) (
	[]*webui.TableRow, error) {
	var (
		minAmount    string
		maxAmount    string
		minDebitDate string
		maxDebitDate string
		err          error
		tableRows    []*webui.TableRow
	)
	for _, upcomingTxn := range upcomingTxnsList {
		minAmount, err = s.convertMoneyToString(upcomingTxn.GetMinAmount())
		if err != nil {
			return nil, fmt.Errorf("failed to convert minimum amount to string: %w", err)
		}
		maxAmount, err = s.convertMoneyToString(upcomingTxn.GetMaxAmount())
		if err != nil {
			return nil, fmt.Errorf("failed to convert maximum amount to string: %w", err)
		}

		minDebitDate = datetimePkg.DateToString(datetimePkg.TimeToDateInLoc(upcomingTxn.GetMinTime().AsTime(), datetimePkg.IST), "2 Jan", datetimePkg.IST)
		maxDebitDate = datetimePkg.DateToString(datetimePkg.TimeToDateInLoc(upcomingTxn.GetMaxTime().AsTime(), datetimePkg.IST), "2 Jan", datetimePkg.IST)

		tableRow := &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				UpcomingTxnsDebitDateKey: createTableCell(fmt.Sprintf("%s - %s", minDebitDate, maxDebitDate), nil),
				MerchantNameKey:          createTableCell(upcomingTxn.GetEntityName(), nil),
				AmountKey:                createTableCell(fmt.Sprintf("%s - %s", minAmount, maxAmount), nil),
			},
		}
		tableRows = append(tableRows, tableRow)
	}

	return tableRows, nil
}
