package payment_instruments

import (
	"context"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxPb "github.com/epifi/gamma/api/cx"
	"github.com/epifi/gamma/api/cx/customer_auth"
	cxPiPb "github.com/epifi/gamma/api/cx/data_collector/payment_instruments"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	acPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upiPb "github.com/epifi/gamma/api/upi"
	upiMocks "github.com/epifi/gamma/api/upi/mocks"
	"github.com/epifi/gamma/cx/test"
	mAuthEngine "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
// nolint
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, teardown = test.InitTestServer(false)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

var (
	headerWithActor = &cxPb.Header{
		AgentEmail: "test-agent",
		TicketId:   1,
		Identifier: nil,
		Actor: &typesPb.Actor{
			Id: "test-actor-1",
		},
	}
	headerWithoutActor = &cxPb.Header{
		AgentEmail: "test-agent",
		TicketId:   1,
		Identifier: nil,
	}
	accPi1 = &accPiPb.AccountPI{
		PiId: "test-pi-id-1",
	}
	pi1 = &piPb.PaymentInstrument{
		Id:   "test-pi-id-1",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
	}
	pi2 = &piPb.PaymentInstrument{
		Id:   "test-pi-id-1",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{
			Upi: &piPb.Upi{
				Vpa: "hardik0406agrawal@fede",
			},
		},
	}
)

func TestService_GetUPIDetails(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	authEngineMock := mAuthEngine.NewMockIAuthEngine(ctr)
	acPiClientMock := acPiMocks.NewMockAccountPIRelationClient(ctr)
	piClientMock := piMocks.NewMockPiClient(ctr)
	upiClientMock := upiMocks.NewMockUPIClient(ctr)
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *cxPiPb.GetUPIDetailsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *cxPiPb.GetUPIDetailsResponse
		wantErr bool
	}{
		{
			name: "auth engine returned is action required as true",
			args: args{
				mocks: []interface{}{
					authEngineMock.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(true, nil),
				},
				ctx: context.Background(),
				req: &cxPiPb.GetUPIDetailsRequest{},
			},
			want:    &cxPiPb.GetUPIDetailsResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "customer not found in system",
			args: args{
				mocks: []interface{}{
					authEngineMock.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(false, nil),
				},
				ctx: context.Background(),
				req: &cxPiPb.GetUPIDetailsRequest{Header: headerWithoutActor},
			},
			want:    &cxPiPb.GetUPIDetailsResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found in system")},
			wantErr: false,
		},
		{
			name: "error in account pi relations service call",
			args: args{
				mocks: []interface{}{
					authEngineMock.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					acPiClientMock.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).
						Return(&accPiPb.GetByActorIdResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx: context.Background(),
				req: &cxPiPb.GetUPIDetailsRequest{Header: headerWithActor},
			},
			want:    &cxPiPb.GetUPIDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("cannot fetch account pi's for actor")},
			wantErr: false,
		},
		{
			name: "error in payment instruments service call",
			args: args{
				mocks: []interface{}{
					authEngineMock.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					acPiClientMock.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).
						Return(&accPiPb.GetByActorIdResponse{Status: rpcPb.StatusOk(), AccountPis: []*accPiPb.AccountPI{accPi1}}, nil),
					piClientMock.EXPECT().GetPIsByIds(gomock.Any(), gomock.Any()).
						Return(&piPb.GetPIsByIdsResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx: context.Background(),
				req: &cxPiPb.GetUPIDetailsRequest{Header: headerWithActor},
			},
			want:    &cxPiPb.GetUPIDetailsResponse{Status: rpcPb.StatusInternalWithDebugMsg("cannot fetch pi details by pi id's")},
			wantErr: false,
		},
		{
			name: "success in services but UPI details not found",
			args: args{
				mocks: []interface{}{
					authEngineMock.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					acPiClientMock.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).
						Return(&accPiPb.GetByActorIdResponse{Status: rpcPb.StatusOk(), AccountPis: []*accPiPb.AccountPI{accPi1}}, nil),
					piClientMock.EXPECT().GetPIsByIds(gomock.Any(), gomock.Any()).
						Return(&piPb.GetPIsByIdsResponse{Status: rpcPb.StatusOk(), Paymentinstruments: []*piPb.PaymentInstrument{pi1}}, nil),
				},
				ctx: context.Background(),
				req: &cxPiPb.GetUPIDetailsRequest{Header: headerWithActor},
			},
			want:    &cxPiPb.GetUPIDetailsResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no upi details found for customer in system")},
			wantErr: false,
		},
		{
			name: "success in services and UPI details found",
			args: args{
				mocks: []interface{}{
					authEngineMock.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).
						Return(false, nil),
					acPiClientMock.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).
						Return(&accPiPb.GetByActorIdResponse{Status: rpcPb.StatusOk(), AccountPis: []*accPiPb.AccountPI{accPi1}}, nil),
					piClientMock.EXPECT().GetPIsByIds(gomock.Any(), gomock.Any()).
						Return(&piPb.GetPIsByIdsResponse{Status: rpcPb.StatusOk(), Paymentinstruments: []*piPb.PaymentInstrument{pi1, pi2}}, nil),
				},
				ctx: context.Background(),
				req: &cxPiPb.GetUPIDetailsRequest{Header: headerWithActor},
			},
			want: &cxPiPb.GetUPIDetailsResponse{
				Status:    rpcPb.StatusOk(),
				VpaHandle: "XXXXXXXXXXXXXXwal@fede",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				piClient:   piClientMock,
				acPiClient: acPiClientMock,
				authEngine: authEngineMock,
				upiClient:  upiClientMock,
			}
			got, err := s.GetUPIDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUPIDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUPIDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_DisableOrEnableVPA(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	authEngineMock := mAuthEngine.NewMockIAuthEngine(ctr)
	acPiClientMock := acPiMocks.NewMockAccountPIRelationClient(ctr)
	piClientMock := piMocks.NewMockPiClient(ctr)
	upiClientMock := upiMocks.NewMockUPIClient(ctr)

	type mockIsAuthActionRequiredForInformationLevel struct {
		enable   bool
		want     bool
		deeplink *customer_auth.SherlockDeepLink
	}

	type mockDisableOrEnableVpa struct {
		enable bool
		res    *upiPb.DisableOrEnableVPAResponse
		err    error
	}
	type args struct {
		ctx context.Context
		req *cxPiPb.DisableOrEnableVPARequest
	}
	tests := []struct {
		name                                        string
		args                                        args
		mockIsAuthActionRequiredForInformationLevel mockIsAuthActionRequiredForInformationLevel
		mockDisableOrEnableVpa                      mockDisableOrEnableVpa
		want                                        *cxPiPb.DisableOrEnableVPAResponse
		wantErr                                     bool
	}{
		{
			name: "auth engine returned is action required as true",
			args: args{
				ctx: context.Background(),
				req: &cxPiPb.DisableOrEnableVPARequest{},
			},
			mockIsAuthActionRequiredForInformationLevel: mockIsAuthActionRequiredForInformationLevel{
				enable: true,
				want:   true,
			},
			want:    &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "no actual action required return confirm screen",
			args: args{
				ctx: context.Background(),
				req: &cxPiPb.DisableOrEnableVPARequest{IsActionable: false},
			},
			mockIsAuthActionRequiredForInformationLevel: mockIsAuthActionRequiredForInformationLevel{
				enable: true,
				want:   false,
			},

			want: &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: &customer_auth.SherlockDeepLink{
				Screen: customer_auth.Screen_CONFIRM_ACTION_SCREEN}},
			wantErr: false,
		},
		{
			name: "request type missing in request",
			args: args{
				ctx: context.Background(),
				req: &cxPiPb.DisableOrEnableVPARequest{Header: headerWithActor, IsActionable: true},
			},
			mockIsAuthActionRequiredForInformationLevel: mockIsAuthActionRequiredForInformationLevel{
				enable: true,
				want:   false,
			},
			want:    &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("request type or both (pi-id and vpa) cannot be nil")},
			wantErr: false,
		},
		{
			name: "customer not found in system",
			args: args{
				ctx: context.Background(),
				req: &cxPiPb.DisableOrEnableVPARequest{
					Header:       headerWithoutActor,
					RequestType:  cxPiPb.DisableOrEnableVPARequest_DISABLE,
					IsActionable: true,
					PiId:         "pi-id",
				},
			},
			mockIsAuthActionRequiredForInformationLevel: mockIsAuthActionRequiredForInformationLevel{
				enable: true,
				want:   false,
			},
			want:    &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found in system")},
			wantErr: false,
		},
		{
			name: "success in services and UPI details found but enabled and disabled gave error",
			args: args{
				ctx: context.Background(),
				req: &cxPiPb.DisableOrEnableVPARequest{
					Header:       headerWithActor,
					RequestType:  cxPiPb.DisableOrEnableVPARequest_DISABLE,
					IsActionable: true,
					PiId:         "pi-id",
				},
			},
			mockIsAuthActionRequiredForInformationLevel: mockIsAuthActionRequiredForInformationLevel{
				enable: true,
				want:   false,
			},
			mockDisableOrEnableVpa: mockDisableOrEnableVpa{
				enable: true,
				res:    &upiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusInternal()},
			},
			want:    &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusInternalWithDebugMsg("unable to enable/disable VPA for customer")},
			wantErr: false,
		},
		{
			name: "success disable UPI",
			args: args{
				ctx: context.Background(),
				req: &cxPiPb.DisableOrEnableVPARequest{
					Header:       headerWithActor,
					RequestType:  cxPiPb.DisableOrEnableVPARequest_DISABLE,
					IsActionable: true,
					PiId:         "pi-id",
				},
			},
			mockIsAuthActionRequiredForInformationLevel: mockIsAuthActionRequiredForInformationLevel{
				enable: true,
				want:   false,
			},
			mockDisableOrEnableVpa: mockDisableOrEnableVpa{
				enable: true,
				res:    &upiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusOk()},
			},
			want:    &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
		{
			name: "success enable UPI",
			args: args{
				ctx: context.Background(),
				req: &cxPiPb.DisableOrEnableVPARequest{
					Header:       headerWithActor,
					RequestType:  cxPiPb.DisableOrEnableVPARequest_DISABLE,
					IsActionable: true,
					PiId:         "pi-id",
				},
			},
			mockIsAuthActionRequiredForInformationLevel: mockIsAuthActionRequiredForInformationLevel{
				enable: true,
				want:   false,
			},
			mockDisableOrEnableVpa: mockDisableOrEnableVpa{
				enable: true,
				res:    &upiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusOk()},
			},
			want:    &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				piClient:   piClientMock,
				acPiClient: acPiClientMock,
				authEngine: authEngineMock,
				upiClient:  upiClientMock,
			}

			if tt.mockIsAuthActionRequiredForInformationLevel.enable {
				authEngineMock.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(tt.mockIsAuthActionRequiredForInformationLevel.want, tt.mockIsAuthActionRequiredForInformationLevel.deeplink)
			}

			if tt.mockDisableOrEnableVpa.enable {
				upiClientMock.EXPECT().DisableOrEnableVPA(gomock.Any(), gomock.Any()).
					Return(tt.mockDisableOrEnableVpa.res, tt.mockDisableOrEnableVpa.err)
			}

			got, err := s.DisableOrEnableVPA(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisableOrEnableVPA() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DisableOrEnableVPA() got = %v, want %v", got, tt.want)
			}
		})
	}
}
