package payment_instruments

import (
	"context"

	cxLogger "github.com/epifi/gamma/cx/logger"

	"github.com/epifi/gamma/cx/data_collector/helper"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxPb "github.com/epifi/gamma/api/cx"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	cxPiPb "github.com/epifi/gamma/api/cx/data_collector/payment_instruments"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/cx"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type Service struct {
	piClient    piPb.PiClient
	acPiClient  accountPIPb.AccountPIRelationClient
	authEngine  auth_engine.IAuthEngine
	upiClient   upiPb.UPIClient
	upiCXClient cx.UpiCXClient
}

func NewService(piClient piPb.PiClient, acPiClient accountPIPb.AccountPIRelationClient, authEngine auth_engine.IAuthEngine,
	upiClient upiPb.UPIClient, upiCXClient cx.UpiCXClient) *Service {
	return &Service{
		piClient:    piClient,
		acPiClient:  acPiClient,
		authEngine:  authEngine,
		upiClient:   upiClient,
		upiCXClient: upiCXClient,
	}
}

var _ cxPiPb.CustomerPIServer = &Service{}

func (s *Service) GetUPIDetails(ctx context.Context, req *cxPiPb.GetUPIDetailsRequest) (*cxPiPb.GetUPIDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxPiPb.GetUPIDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if actorId == "" {
		cxLogger.Error(ctx, "header does not have actor information")
		return &cxPiPb.GetUPIDetailsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found in system"),
		}, nil
	}
	upiPi, status := getUpiFromPi(ctx, req.GetHeader(), s.piClient, s.acPiClient)
	if !status.IsSuccess() {
		cxLogger.Error(ctx, "cannot get upi details of customer")
		return &cxPiPb.GetUPIDetailsResponse{
			Status: status,
		}, nil
	}
	return buildResponse(upiPi), nil
}

func (s *Service) DisableOrEnableVPA(ctx context.Context, req *cxPiPb.DisableOrEnableVPARequest) (*cxPiPb.DisableOrEnableVPAResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	// Client will take user to confirm page screen if auth passed but action to be taken was set as false
	if !req.GetIsActionable() {
		return &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: &caPb.SherlockDeepLink{
			Screen: caPb.Screen_CONFIRM_ACTION_SCREEN,
		}}, nil
	}

	if req.GetRequestType() == cxPiPb.DisableOrEnableVPARequest_REQUEST_TYPE_UNSPECIFIED || (req.GetPiId() == "" && req.GetVpa() == "") {
		logger.Info(ctx, "request type not specified")
		return &cxPiPb.DisableOrEnableVPAResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("request type or both (pi-id and vpa) cannot be nil")}, nil
	}

	if actorId == "" {
		logger.Info(ctx, "header does not have actor information")
		return &cxPiPb.DisableOrEnableVPAResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found in system"),
		}, nil
	}

	// call UPI service to enable/disable VPA
	var reqType upiPb.DisableOrEnableVPARequest_RequestType
	if req.GetRequestType() == cxPiPb.DisableOrEnableVPARequest_ENABLE {
		reqType = upiPb.DisableOrEnableVPARequest_ENABLE
	} else if req.GetRequestType() == cxPiPb.DisableOrEnableVPARequest_DISABLE {
		reqType = upiPb.DisableOrEnableVPARequest_DISABLE
	}

	// for backward compatibility
	disableOrEnableVpaBeReq := &upiPb.DisableOrEnableVPARequest{
		Identifier: &upiPb.DisableOrEnableVPARequest_UserVpa{
			UserVpa: req.GetVpa(),
		},
		ActorId:     actorId,
		RequestType: reqType,
	}

	if req.GetPiId() != "" {
		disableOrEnableVpaBeReq = &upiPb.DisableOrEnableVPARequest{
			Identifier: &upiPb.DisableOrEnableVPARequest_PiId{
				PiId: req.GetPiId(),
			},
			ActorId:     actorId,
			RequestType: reqType,
		}
	}

	upiResp, err := s.upiClient.DisableOrEnableVPA(ctx, disableOrEnableVpaBeReq)
	if te := epifigrpc.RPCError(upiResp, err); te != nil {
		logger.Error(ctx, "unable to enable/disable VPA for customer", zap.Error(te))
		return &cxPiPb.DisableOrEnableVPAResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to enable/disable VPA for customer"),
		}, nil
	}
	return &cxPiPb.DisableOrEnableVPAResponse{
		Status: upiResp.GetStatus(),
	}, nil
}

func buildResponse(pi *piPb.PaymentInstrument) *cxPiPb.GetUPIDetailsResponse {
	resp := &cxPiPb.GetUPIDetailsResponse{Status: rpcPb.StatusOk()}
	resp.CreationDate = pi.GetCreatedAt()
	resp.VpaHandle = helper.MaskVPA(pi.GetUpi().GetVpa())
	if pi.GetState() != piPb.PaymentInstrumentState_PAYMENT_INSTRUMENT_STATE_UNSPECIFIED {
		resp.UpiStatus = pi.GetState().String()
	}
	return resp
}

func getUpiFromPi(ctx context.Context, header *cxPb.Header, piClient piPb.PiClient, acPiClient accountPIPb.AccountPIRelationClient) (*piPb.PaymentInstrument, *rpcPb.Status) {

	actorId := header.GetActor().GetId()
	piResp, err := acPiClient.GetByActorId(ctx, &accountPIPb.GetByActorIdRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(piResp, err); te != nil {
		logger.Error(ctx, "cannot fetch account pi's for actor", zap.Error(err))
		return nil, rpcPb.StatusInternalWithDebugMsg("cannot fetch account pi's for actor")
	}
	// we have got the list of PI Id's for the actor
	piIdList := getPiIdList(piResp.GetAccountPis())
	resp1, err := piClient.GetPIsByIds(ctx, &piPb.GetPIsByIdsRequest{Ids: piIdList})
	if te := epifigrpc.RPCError(resp1, err); te != nil {
		logger.Error(ctx, "cannot fetch pi details by pi id's", zap.Any("piIdList", piIdList), zap.Error(err))
		return nil, rpcPb.StatusInternalWithDebugMsg("cannot fetch pi details by pi id's")
	}
	upiPi := getUpiPiFromResponse(resp1.GetPaymentinstruments())
	if upiPi == nil {
		return nil, rpcPb.StatusRecordNotFoundWithDebugMsg("no upi details found for customer in system")
	}
	return upiPi, rpcPb.StatusOk()
}

func getUpiPiFromResponse(paymentInstrumentsList []*piPb.PaymentInstrument) *piPb.PaymentInstrument {
	for _, pi := range paymentInstrumentsList {
		if pi.GetType() == piPb.PaymentInstrumentType_UPI && !pi.IsMandateVPA() {
			return pi
		}
	}
	return nil
}

func getPiIdList(pis []*accountPIPb.AccountPI) []string {
	var piIdList []string
	for _, accountPi := range pis {
		piIdList = append(piIdList, accountPi.GetPiId())
	}
	return piIdList
}

func (s *Service) GetUpiInfo(ctx context.Context, req *cxPiPb.GetUpiInfoRequest) (*cxPiPb.GetUpiInfoResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		logger.Info(ctx, "auth action required to show information")
		return &cxPiPb.GetUpiInfoResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if actorId == "" {
		logger.Error(ctx, "header does not have actor information")
		return &cxPiPb.GetUpiInfoResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found in system"),
		}, nil
	}
	resp, err := s.upiCXClient.GetUpiInfo(ctx, &cx.GetUpiInfoRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "cannot call cx upi service to get upi info for actor", zap.Error(te))
		if err != nil {
			return &cxPiPb.GetUpiInfoResponse{Status: rpcPb.StatusInternalWithDebugMsg("unable to get upi info from backend")}, nil
		}
		return &cxPiPb.GetUpiInfoResponse{
			Status: resp.GetStatus(),
		}, nil
	}
	upiInfoList := s.buildUpiInfoListFromResponse(resp)
	return &cxPiPb.GetUpiInfoResponse{
		Status:  rpcPb.StatusOk(),
		UpiInfo: upiInfoList,
	}, nil
}

func (s *Service) buildUpiInfoListFromResponse(resp *cx.GetUpiInfoResponse) []*cxPiPb.UpiInfo {
	var upiInfoList []*cxPiPb.UpiInfo
	for _, upiInfo := range resp.GetUpiInfo() {
		var vpaStateLogList []*cxPiPb.PaymentInstrumentStateLog
		for _, log := range upiInfo.GetVpaStateLog() {
			vpaStateLog := &cxPiPb.PaymentInstrumentStateLog{
				Source:    log.GetSource().String(),
				Reason:    log.GetReason(),
				State:     log.GetState().String(),
				CreatedAt: log.GetCreatedAt(),
				UpdatedAt: log.GetUpdatedAt(),
				DeletedAt: log.GetDeletedAt(),
			}
			vpaStateLogList = append(vpaStateLogList, vpaStateLog)
		}
		var upiPinInfoList []*cxPiPb.AccountUpiPinInfo
		for _, pinInfo := range upiInfo.GetUpiPinInfo() {
			upiPinInfo := &cxPiPb.AccountUpiPinInfo{
				UserAction: pinInfo.GetUserAction().String(),
				CreatedAt:  pinInfo.GetCreatedAt(),
				UpdatedAt:  pinInfo.GetUpdateAt(),
				DeletedAt:  pinInfo.GetDeletedAt(),
				Status:     pinInfo.GetStatus().String(),
			}
			for _, row := range pinInfo.GetDetailedStatusList().GetDetailedStatus() {
				upiPinInfo.DetailedStatus = append(upiPinInfo.DetailedStatus, &cxPiPb.DetailedStatus{
					RawStatusCode:        row.RawStatusCode,
					RawStatusDescription: row.RawStatusDescription,
				})
			}
			upiPinInfoList = append(upiPinInfoList, upiPinInfo)
		}
		cxUpiInfo := &cxPiPb.UpiInfo{
			PiId:        upiInfo.GetPiId(),
			Vpa:         helper.MaskVPA(upiInfo.GetVpa()),
			VpaState:    upiInfo.GetVpaState().String(),
			VpaStateLog: vpaStateLogList,
			UpiPinInfo:  upiPinInfoList,
			BankName:    upiInfo.GetBankName(),
			AccountType: upiInfo.GetAccountType().String(),
		}
		upiInfoList = append(upiInfoList, cxUpiInfo)
	}
	return upiInfoList
}
