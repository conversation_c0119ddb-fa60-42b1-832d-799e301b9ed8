package model

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"strconv"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	pb "github.com/epifi/gamma/api/cx/data_collector/sherlock_actor_activity"
	"github.com/epifi/gamma/api/cx/ticket"
	web "github.com/epifi/gamma/api/typesv2/webui"
)

type ActorActivityRequestParams struct {
	ActorId      string
	Areas        []*pb.SherlockArea
	IsDescending commontypes.BooleanEnum
	StartTime    *timestamppb.Timestamp
	EndTime      *timestamppb.Timestamp
	PageContext  *rpc.PageContextRequest
}

type ActorActivityResponse struct {
	ActivityDetails   *web.Table
	PageContext       *rpc.PageContextResponse
	ActivityMeta      []*ticket.AttachEntityMeta
	ActivityDetailsV2 *web.Table
	PageContextV2     *rpc.PageContextResponse
}

type SherlockActivityAreas struct {
	SherlockActivityArea []pb.SherlockActivityArea
}

func ConvertSherlockActivityRequestProtoToActorActivityParams(req *pb.GetActivitiesRequest) (*ActorActivityRequestParams, error) {
	endTimeStamp := req.GetEndTime()
	var token string
	if req.GetPageContext().GetBeforeToken() != "" {
		token = req.GetPageContext().GetBeforeToken()
	}
	if req.GetPageContext().GetAfterToken() != "" {
		token = req.GetPageContext().GetAfterToken()
	}
	if token != "" {
		tokenTimeStampInt, err := strconv.Atoi(token)
		if err != nil {
			return nil, errors.Wrap(err, "failed to parse token to timestamp")
		}
		endTimeStamp = timestamppb.New(time.Unix(int64(tokenTimeStampInt), 0))
	}

	return &ActorActivityRequestParams{
		ActorId:      req.GetHeader().GetActor().GetId(),
		Areas:        req.GetSherlockAreas(),
		IsDescending: req.GetIsDescending(),
		StartTime:    req.GetStartTime(),
		EndTime:      endTimeStamp,
		PageContext:  req.GetPageContext(),
	}, nil
}
