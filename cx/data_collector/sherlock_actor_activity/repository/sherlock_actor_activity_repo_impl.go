package repository

import (
	"context"

	"github.com/pkg/errors"

	aaPb "github.com/epifi/gamma/api/actor_activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type SherlockActorActivityRepoImpl struct {
	actorActivityClient aaPb.ActorActivityClient
}

func NewSherlockActorActivityRepoImpl(aaClient aaPb.ActorActivityClient) *SherlockActorActivityRepoImpl {
	return &SherlockActorActivityRepoImpl{actorActivityClient: aaClient}
}

var _ SherlockActorActivityRepository = &SherlockActorActivityRepoImpl{}

func (s *SherlockActorActivityRepoImpl) GetActivity(ctx context.Context, activityAreas *aaPb.ActivityFilter) ([]*aaPb.Activity, []*aaPb.ActivityV2, error) {
	aaRequest := &aaPb.GetActivitiesRequest{ActivityFilter: activityAreas}
	resp, err := s.actorActivityClient.GetActivities(ctx, aaRequest)
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, nil, epifierrors.ErrRecordNotFound
		}
		return nil, nil, errors.Wrap(te, "failed to fetch activity from actor activity service")
	}
	return resp.GetActivities(), resp.GetActivitiesV2(), nil
}
