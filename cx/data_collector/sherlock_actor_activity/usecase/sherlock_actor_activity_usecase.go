package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	actor_activity2 "github.com/epifi/gamma/actor_activity"
	"github.com/epifi/gamma/api/actor_activity"
	pb "github.com/epifi/gamma/api/cx/data_collector/sherlock_actor_activity"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	typesActivityPb "github.com/epifi/gamma/api/typesv2/activity"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/data_collector/helper"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/constant"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/model"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/repository"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

type SherlockActorActivityServiceImpl struct {
	sherlockAARepo      repository.SherlockActorActivityRepository
	dataCollectorHelper helper.IDataCollectorHelper
	orderPaymentClient  paymentPb.PaymentClient
}

var sherlockAreaToActivityArea = map[pb.SherlockActivityArea]typesActivityPb.ActivityArea{
	pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_ACCOUNTS:    typesActivityPb.ActivityArea_ACTIVITY_AREA_ACCOUNTS,
	pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_CREDIT_CARD: typesActivityPb.ActivityArea_ACTIVITY_AREA_CREDIT_CARD,
	pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_PAY:         typesActivityPb.ActivityArea_ACTIVITY_AREA_PAY,
	pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_REWARDS:     typesActivityPb.ActivityArea_ACTIVITY_AREA_REWARDS,
}

func NewSherlockActorActivityServiceImpl(repo repository.SherlockActorActivityRepository, dcHelper helper.IDataCollectorHelper,
	paymentClient paymentPb.PaymentClient) *SherlockActorActivityServiceImpl {
	return &SherlockActorActivityServiceImpl{
		sherlockAARepo:      repo,
		dataCollectorHelper: dcHelper,
		orderPaymentClient:  paymentClient,
	}
}

var _ SherlockActorActivity = &SherlockActorActivityServiceImpl{}

func (s *SherlockActorActivityServiceImpl) GetSherlockActorActivityAreas(ctx context.Context) (*model.SherlockActivityAreas, error) {
	sherlockAreas := []pb.SherlockActivityArea{
		pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_ALL,
		pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_PAY,
		pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_CREDIT_CARD,
		pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_ACCOUNTS,
		pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_REWARDS,
	}
	return &model.SherlockActivityAreas{SherlockActivityArea: sherlockAreas}, nil
}

func (s *SherlockActorActivityServiceImpl) GetActivities(ctx context.Context, actorActivityParams *model.ActorActivityRequestParams) (*model.ActorActivityResponse, error) {
	activities, activitiesV2, err := s.sherlockAARepo.GetActivity(ctx, s.getActivityFilterRequest(actorActivityParams))
	// if err is not nil and the error is not record not found, we return the error
	// and if the err is record not found, we return a table with empty rows
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &model.ActorActivityResponse{}, errors.Wrap(err, "failed to fetch activities from actor activity service")
	}
	activityTable := s.createActivityDetails(ctx, activities, actorActivityParams)
	pageContextResponse := s.getPageContextResponseForActivities(activities, actorActivityParams.PageContext)

	var filteredActivities []*actor_activity.ActivityV2
	for _, activity := range activitiesV2 {
		// if activity was performed before the start time being passed in request
		// we would not process such requests
		// this is needed on the client side as the platform does not support passing start and end time
		if activity.GetReportedAt().AsTime().Before(actorActivityParams.StartTime.AsTime()) {
			continue
		}
		filteredActivities = append(filteredActivities, activity)
	}
	activityTableV2 := s.createActivityV2Details(ctx, filteredActivities, actorActivityParams)
	pageContextResponseV2 := s.getPageContextResponseForActivitiesV2(filteredActivities, actorActivityParams.PageContext)
	return &model.ActorActivityResponse{
		ActivityDetails:   activityTable,
		PageContext:       pageContextResponse,
		ActivityDetailsV2: activityTableV2,
		PageContextV2:     pageContextResponseV2,
	}, nil
}
func (s *SherlockActorActivityServiceImpl) getActivityIdentifiersForArea(area typesActivityPb.ActivityArea) *typesActivityPb.ActivityIdentifier {
	switch area {
	case typesActivityPb.ActivityArea_ACTIVITY_AREA_PAY:
		return &typesActivityPb.ActivityIdentifier{
			ActivityArea: typesActivityPb.ActivityArea_ACTIVITY_AREA_PAY,
			ActivityType: &typesActivityPb.ActivityIdentifier_PayActivityType{PayActivityType: typesActivityPb.PayActivityType_PAY_ACTIVITY_TYPE_PAYMENT},
		}
	case typesActivityPb.ActivityArea_ACTIVITY_AREA_CREDIT_CARD:
		return &typesActivityPb.ActivityIdentifier{
			ActivityArea: typesActivityPb.ActivityArea_ACTIVITY_AREA_CREDIT_CARD,
			ActivityType: &typesActivityPb.ActivityIdentifier_CreditCardActivityType{CreditCardActivityType: typesActivityPb.CreditCardActivityType_CREDIT_CARD_ACTIVITY_TYPE_PAYMENT},
		}
	// default case handles all non-financial (event based) activities
	// where only activityArea acts as identifier. Hence, activity type is not required to be populated
	default:
		return &typesActivityPb.ActivityIdentifier{
			ActivityArea: area,
		}
	}
}
func (s *SherlockActorActivityServiceImpl) getActivityIdentifiersForSherlockArea(area []*pb.SherlockArea) []*typesActivityPb.ActivityIdentifier {
	var activityIdentifiers []*typesActivityPb.ActivityIdentifier
	for _, activityArea := range area {
		// in case all is provided return identifier for all area
		if activityArea.GetSherlockActivityArea() == pb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_ALL {
			return s.getActivityIdentifierForAll()
		}
		activityIdentifiers = append(activityIdentifiers,
			s.getActivityIdentifiersForArea(sherlockAreaToActivityArea[activityArea.GetSherlockActivityArea()]))

	}
	return activityIdentifiers
}
func (s *SherlockActorActivityServiceImpl) getActivityIdentifierForAll() []*typesActivityPb.ActivityIdentifier {
	var activityIdentifiers []*typesActivityPb.ActivityIdentifier
	for _, area := range typesActivityPb.ActivityArea_value {
		// ignore unspecified value of activity area
		if area == 0 {
			continue
		}
		activityIdentifiers = append(activityIdentifiers, s.getActivityIdentifiersForArea(typesActivityPb.ActivityArea(area)))
	}
	return activityIdentifiers
}

func (s *SherlockActorActivityServiceImpl) getActivityFilterRequest(actorActivityParams *model.ActorActivityRequestParams) *actor_activity.ActivityFilter {
	activityIdentifiers := s.getActivityIdentifiersForSherlockArea(actorActivityParams.Areas)
	activityFilter := &actor_activity.ActivityFilter{
		ActorId:             actorActivityParams.ActorId,
		ActivityIdentifiers: activityIdentifiers,
		// We need to populate end time here in start time request
		// StartTime for platform means it will return all activities for a
		// User till this time only
		StartTime:    actorActivityParams.EndTime,
		IsDescending: actorActivityParams.IsDescending,
		// fetching one additional activity than requested by client, so that we can paginate the response
		// it helps us determine whether we have more activities or not
		RecordLimit: int64(actorActivityParams.PageContext.GetPageSize() + 1),
	}
	return activityFilter
}

func (s *SherlockActorActivityServiceImpl) getAttachEntityMetaForActivity(activity *actor_activity.Activity) (string, error) {

	var entityMeta ticketPb.AttachEntityMeta
	switch activity.GetActivityIdentifier().GetActivityArea() {
	case typesActivityPb.ActivityArea_ACTIVITY_AREA_PAY:
		txnId := activity.GetPayPaymentActivity().GetInternalTransactionId()
		entityMeta = ticketPb.AttachEntityMeta{
			Meta: &ticketPb.AttachEntityMeta_OrderWithTransactionMeta{OrderWithTransactionMeta: &ticketPb.OrderWithTransactionMeta{TransactionId: txnId}},
		}
	case typesActivityPb.ActivityArea_ACTIVITY_AREA_CREDIT_CARD:
		txnId := activity.GetCreditCardActivity().GetInternalTransactionId()
		entityMeta = ticketPb.AttachEntityMeta{
			Meta: &ticketPb.AttachEntityMeta_OrderWithTransactionMeta{OrderWithTransactionMeta: &ticketPb.OrderWithTransactionMeta{TransactionId: txnId}},
		}
	}

	entityMetaBytes, err := protojson.Marshal(&entityMeta)
	if err != nil {
		return "", errors.Wrap(err, "failed to marshal attach entity meta to string")
	}
	return string(entityMetaBytes), nil
}
func (s *SherlockActorActivityServiceImpl) getPageContextResponseForActivities(activities []*actor_activity.Activity, pageCtxReq *rpc.PageContextRequest) *rpc.PageContextResponse {
	pageCtxResp := &rpc.PageContextResponse{}
	// if token is passed in request it means, this is not the first request, and there will always be a later response
	if pageCtxReq.GetAfterToken() != "" || pageCtxReq.GetBeforeToken() != "" {
		pageCtxResp.HasAfter = true
	}
	// since there were no activities here, no pagination needs to be performed
	if len(activities) == 0 {
		return pageCtxResp
	}

	// before token will be true when current number of activities are equal to or more than pageSize required
	// if there are fewer activities then pageSize, it means we have reach end of activity stream for that user
	if len(activities) >= int(pageCtxReq.GetPageSize()) {
		pageCtxResp.HasBefore = true
	}

	// This is the first response in the actor activity response
	firstActivityOfPage := activities[0]

	// this is the last response in the actor activity response
	lastActivityOfPage := activities[len(activities)-1]

	if pageCtxResp.GetHasAfter() {
		pageCtxResp.AfterToken = strconv.Itoa(int(firstActivityOfPage.GetPerformedAt().GetSeconds()))
	}

	if pageCtxResp.GetHasBefore() {
		pageCtxResp.BeforeToken = strconv.Itoa(int(lastActivityOfPage.GetPerformedAt().GetSeconds()))
	}
	return pageCtxResp
}
func (s *SherlockActorActivityServiceImpl) getPageContextResponseForActivitiesV2(activities []*actor_activity.ActivityV2, pageCtxReq *rpc.PageContextRequest) *rpc.PageContextResponse {
	pageCtxResp := &rpc.PageContextResponse{}
	// since there were no activities here, no pagination needs to be performed
	if len(activities) == 0 {
		return pageCtxResp
	}

	// as activity platform was build-in keeping in mind a stream view it doesn't support paginating the response in both way
	pageCtxResp.HasAfter = false
	// before token will be true when current number of activities are more than pageSize required
	// while fetching activities we try to fetch one additional activity than required
	// to determine whether we want to allow loading next page
	if len(activities) > int(pageCtxReq.GetPageSize()) {
		pageCtxResp.HasBefore = true
		// in-case we received one extra activity removing it
		activities = activities[:pageCtxReq.GetPageSize()]
	}

	// This is the first response in the actor activity response
	firstActivityOfPage := activities[0]

	// this is the last response in the actor activity response
	lastActivityOfPage := activities[len(activities)-1]

	if pageCtxResp.GetHasAfter() {
		pageCtxResp.AfterToken = strconv.Itoa(int(firstActivityOfPage.GetReportedAt().GetSeconds()))
	}

	if pageCtxResp.GetHasBefore() {
		pageCtxResp.BeforeToken = strconv.Itoa(int(lastActivityOfPage.GetReportedAt().GetSeconds()))
	}
	return pageCtxResp
}

// nolint: funlen
func (s *SherlockActorActivityServiceImpl) createActivityDetails(ctx context.Context, activities []*actor_activity.Activity, actorActivityParams *model.ActorActivityRequestParams) *webui.Table {
	var (
		activityDetailsTable = &webui.Table{
			TableName: constant.SherlockActivityTableName,
			TableHeaders: []*webui.TableHeader{
				{Label: constant.TimeStampLabel, HeaderKey: constant.TimeStampKey, IsVisible: true},
				{Label: constant.AreaLabel, HeaderKey: constant.AreaKey, IsVisible: true},
				{Label: constant.ActivityLabel, HeaderKey: constant.ActivityKey, IsVisible: true},
				{Label: constant.FiTxnIdLabel, HeaderKey: constant.FiTxnIdKey, IsVisible: true},
				{Label: constant.UtrLabel, HeaderKey: constant.UtrKey, IsVisible: true},
				{Label: constant.MerchantLabel, HeaderKey: constant.MerchantKey, IsVisible: true},
				{Label: constant.UnitLabel, HeaderKey: constant.UnitKey, IsVisible: true},
				{Label: constant.TypeOfTransactionLabel, HeaderKey: constant.TypeOfTransactionKey, IsVisible: true},
				{Label: constant.InternalTransactionIdLabel, HeaderKey: constant.InternalTransactionIdKey, IsVisible: false},
				{Label: constant.FundsDebitedAtLabel, HeaderKey: constant.FundsDebitedAtKey, IsVisible: true},
			},
			TableRows: []*webui.TableRow{},
		}
	)

	for _, activity := range activities {

		// if activity was performed before the start time being passed in request
		// we would not process such requests
		// this is needed on the client side as the platform does not support passing start and end time
		if activity.GetPerformedAt().AsTime().Before(actorActivityParams.StartTime.AsTime()) {
			continue
		}
		var (
			activityType  string
			merchant      string
			unit          string
			fiTxnId       string
			utrNumber     string
			area          string
			err           error
			amtBadge      string
			internalTxnId string
		)
		attachEntityMetaString, err := s.getAttachEntityMetaForActivity(activity)
		if err != nil {
			// in case we are not able to parse some activities, we skip processing that activity and
			// iterate further. we don't fail if for some activities the meta entity parsing fails
			cxLogger.Error(ctx, "failed to marshal attach entity meta to string", zap.Error(err))
			continue
		}
		switch activity.GetActivityIdentifier().GetActivityType().(type) {
		case *typesActivityPb.ActivityIdentifier_PayActivityType:
			activityType = activity.GetActivityIdentifier().GetPayActivityType().String()
		case *typesActivityPb.ActivityIdentifier_CreditCardActivityType:
			activityType = activity.GetActivityIdentifier().GetCreditCardActivityType().String()
		}
		switch activity.GetDetail().(type) {
		case *actor_activity.Activity_PayPaymentActivity:
			activityType = constant.PayActivityType
			area = constant.TransactionArea
			merchant = activity.GetPayPaymentActivity().GetTitle()
			fiTxnId = s.dataCollectorHelper.MaskStringExceptLast4(activity.GetPayPaymentActivity().GetOrderExternalId())
			utrNumber = s.dataCollectorHelper.MaskStringExceptLast4(activity.GetPayPaymentActivity().GetUtr())
			unit, err = s.convertMoneyToString(activity.GetPayPaymentActivity().GetAmount())
			if err != nil {
				unit = activity.GetPayPaymentActivity().GetAmount().String()
			}
			amtBadge = constant.PayAmountBadgeToTransactionFlow[activity.GetPayPaymentActivity().GetAmountBadge()]
			internalTxnId = activity.GetPayPaymentActivity().GetInternalTransactionId()

		case *actor_activity.Activity_CreditCardActivity:
			activityType = constant.CreditCardActivityType
			area = constant.TransactionArea
			fiTxnId = s.dataCollectorHelper.MaskStringExceptLast4(activity.GetCreditCardActivity().GetExternalTxnId())
			merchant = activity.GetCreditCardActivity().GetTitle()
			unit, err = s.convertMoneyToString(activity.GetCreditCardActivity().GetAmount())
			if err != nil {
				unit = activity.GetCreditCardActivity().GetAmount().String()
			}
			amtBadge = constant.CCAmountBadgeToTransactionFlow[activity.GetCreditCardActivity().GetAmountBadge()]
			internalTxnId = activity.GetCreditCardActivity().GetInternalTransactionId()
		}

		getTxnRes, err := s.orderPaymentClient.GetTransaction(ctx, &paymentPb.GetTransactionRequest{
			Identifier: &paymentPb.GetTransactionRequest_TransactionId{
				TransactionId: internalTxnId,
			},
		})
		if err = epifigrpc.RPCError(getTxnRes, err); err != nil {
			cxLogger.Error(ctx, "error while fetching txn for the given txn-id",
				zap.String(logger.TXN_ID, internalTxnId),
				zap.Error(err))

			continue
		}

		tableRow := &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				constant.FiTxnIdKey: {
					Value: fiTxnId,
				},
				constant.UtrKey: {
					Value: utrNumber,
				},
				constant.TimeStampKey: {
					Value: activity.GetPerformedAt().AsTime().In(datetime.IST).Format(constant.SherlockActivityTimeStampLayout),
					Style: constant.ActivityTableRowStyle,
				},
				constant.AreaKey: {
					Value: area,
					Style: constant.ActivityTableRowStyle,
				},
				constant.ActivityKey: {
					Value: activityType,
					Style: constant.ActivityTableRowStyle,
				},
				constant.MerchantKey: {
					Value: merchant,
					Style: constant.ActivityTableRowStyle,
				},
				constant.UnitKey: {
					Value: unit,
					Style: constant.ActivityTableRowStyle,
				},
				constant.TypeOfTransactionKey: {
					Value: amtBadge,
					Style: constant.ActivityTableRowStyle,
				},
				constant.InternalTransactionIdKey: {
					Value: internalTxnId,
					Style: constant.ActivityTableRowStyle,
				},
				constant.FundsDebitedAtKey: {
					Value: convertTimestampToString(getTxnRes.GetTransaction().GetDebitedAt()),
				},
			},
			Meta: attachEntityMetaString,
		}
		activityDetailsTable.TableRows = append(activityDetailsTable.TableRows, tableRow)
	}

	return activityDetailsTable
}

func convertTimestampToString(tm *timestampPb.Timestamp) string {
	defaultTime := &timestampPb.Timestamp{}

	// Ideally this should not be empty
	if tm.AsTime() == defaultTime.AsTime() {
		return constant.NoDebit
	}

	return tm.AsTime().In(datetime.IST).Format(constant.SherlockActivityTimeStampLayout)
}

func (s *SherlockActorActivityServiceImpl) convertMoneyToString(amount *moneyPb.Money) (string, error) {
	amtStr, err := money.ToString(amount, 2)
	if err != nil {
		return "", fmt.Errorf("failed to convert amount to paisa: %w", err)
	}
	return fmt.Sprintf("%s %s", amount.GetCurrencyCode(), amtStr), nil
}

//nolint:funlen
func (s *SherlockActorActivityServiceImpl) createActivityV2Details(ctx context.Context, activities []*actor_activity.ActivityV2, actorActivityParams *model.ActorActivityRequestParams) *webui.Table {
	var (
		activityDetailsTable = &webui.Table{
			TableName: constant.SherlockActivityTableName,
			TableHeaders: []*webui.TableHeader{
				{Label: constant.TimeStampLabel, HeaderKey: constant.TimeStampKey, IsVisible: true},
				{Label: constant.AreaLabel, HeaderKey: constant.AreaKey, IsVisible: true},
				{Label: constant.ActivityLabel, HeaderKey: constant.ActivityKey, IsVisible: true},
				{Label: constant.PrimaryPropertiesLabel, HeaderKey: constant.PrimaryPropertiesKey, IsVisible: false},
				{Label: constant.InternalTransactionIdLabel, HeaderKey: constant.InternalTransactionIdKey, IsVisible: false},
			},
			TableRows: []*webui.TableRow{},
		}
	)
	// removing the extra record fetched to perform pagination
	if len(activities) > int(actorActivityParams.PageContext.GetPageSize()) {
		activities = activities[:actorActivityParams.PageContext.GetPageSize()]
	}
	for _, activity := range activities {
		var (
			activityType           = activity.GetActivityType()
			area                   = activity.GetArea().String()
			timestamp              = activity.GetReportedAt().AsTime().In(datetime.IST).Format(constant.SherlockActivityTimeStampLayout)
			primaryPropertyJsonStr string
			internalTxnId          string
		)
		if activity.GetPrimaryProperties().GetFields() != nil {
			internalTxnId = activity.GetPrimaryProperties().GetFields()[actor_activity2.InternalTxnId].GetStringValue()
		}

		attachEntityMetaString, err := s.getAttachEntityMetaForActivityV2(activity)
		if err != nil {
			// in case we are not able to parse some activities, we skip processing that activity and
			// iterate further. we don't fail if for some activities the meta entity parsing fails
			cxLogger.Error(ctx, "failed to marshal attach entity meta to string", zap.Error(err))
			continue
		}
		maskedPrimaryProperties := s.maskPrimaryProperties(activity.GetPrimaryProperties())
		bytes, err := json.Marshal(maskedPrimaryProperties)
		if err != nil {
			cxLogger.Error(ctx, "failed to marshal primary properties", zap.Error(err))
			continue
		}
		primaryPropertyJsonStr = string(bytes)
		tableRow := &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				constant.TimeStampKey: {
					DataType: webui.TableCell_DATA_TYPE_STRING,
					ValueV2:  &webui.TableCell_StringValue{StringValue: timestamp},
				},
				constant.AreaKey: {
					DataType: webui.TableCell_DATA_TYPE_STRING,
					ValueV2:  &webui.TableCell_StringValue{StringValue: area},
				},
				constant.ActivityKey: {
					DataType: webui.TableCell_DATA_TYPE_STRING,
					ValueV2:  &webui.TableCell_StringValue{StringValue: activityType},
				},
				constant.InternalTransactionIdKey: {
					Value:    internalTxnId,
					DataType: webui.TableCell_DATA_TYPE_STRING,
					ValueV2:  &webui.TableCell_StringValue{StringValue: internalTxnId},
				},
				constant.PrimaryPropertiesKey: {
					Value:    primaryPropertyJsonStr,
					DataType: webui.TableCell_DATA_TYPE_JSON,
					ValueV2:  &webui.TableCell_StringValue{StringValue: primaryPropertyJsonStr},
				},
			},
			Meta:    attachEntityMetaString,
			Actions: getCTAForActivity(activity),
		}
		activityDetailsTable.TableRows = append(activityDetailsTable.GetTableRows(), tableRow)
	}

	return activityDetailsTable
}

func getCTAForActivity(activity *actor_activity.ActivityV2) []*webui.CTA {
	// view details and mark against dispute is supported in all activities
	cta := []*webui.CTA{
		{
			Label: "View details",
			Deeplink: &webui.Deeplink{
				ScreenName: webui.ScreenName_SCREEN_NAME_RECENT_ACTIVITY_VIEW_DETAILS,
			},
		},
		{
			Label: "Mark against ticket",
			Deeplink: &webui.Deeplink{
				ScreenName: webui.ScreenName_SCREEN_NAME_RECENT_ACTIVITY_MARK_AGAINST_TICKET,
			},
		},
	}

	disputeCta := []*webui.CTA{
		{
			Label: "Raise dispute",
			Deeplink: &webui.Deeplink{
				ScreenName: webui.ScreenName_SCREEN_NAME_RECENT_ACTIVITY_RAISE_DISPUTE,
			},
		},
		{
			Label: "View dispute",
			Deeplink: &webui.Deeplink{
				ScreenName: webui.ScreenName_SCREEN_NAME_RECENT_ACTIVITY_VIEW_DISPUTE_DETAILS,
			},
		},
	}
	// only when are is Pay or Credit Card add support for raising and viewing dispute details
	if activity.GetArea() == typesActivityPb.ActivityArea_ACTIVITY_AREA_PAY || activity.GetArea() == typesActivityPb.ActivityArea_ACTIVITY_AREA_CREDIT_CARD {
		cta = append(cta, disputeCta...)
	}
	return cta
}

func (s *SherlockActorActivityServiceImpl) maskPrimaryProperties(properties *structpb.Struct) map[string]string {
	masked := make(map[string]string)
	for k, v := range properties.GetFields() {
		formattedKey := convertSnakeCaseToTitleCase(k)
		switch {
		case lo.Contains(constant.PropertyToMask, strings.ToLower(k)) || lo.Contains(constant.PropertyToMask, formattedKey):
			masked[formattedKey] = s.dataCollectorHelper.MaskStringExceptLast4(v.GetStringValue())
		case lo.Contains(constant.BlackListedProperties, strings.ToLower(k)) || lo.Contains(constant.BlackListedProperties, formattedKey):
			continue
		default:
			masked[formattedKey] = v.GetStringValue()
		}
	}
	return masked
}

func (s *SherlockActorActivityServiceImpl) getAttachEntityMetaForActivityV2(activity *actor_activity.ActivityV2) (string, error) {
	entityMeta := &ticketPb.AttachEntityMeta{
		Meta: &ticketPb.AttachEntityMeta_SherlockActorActivityV2Meta{
			SherlockActorActivityV2Meta: &ticketPb.SherlockActorActivityV2Meta{
				Activity: activity,
			},
		},
	}
	jsonStr, err := protojson.Marshal(entityMeta)
	if err != nil {
		return "", errors.Wrap(err, "failed to marshal entity meta")
	}
	return string(jsonStr), nil
}

func convertSnakeCaseToTitleCase(k string) string {
	result := ""
	for idx, char := range k {
		switch {
		case idx == 0:
			result += strings.ToUpper(string(char))
		case char == '_':
			result += " "
		default:
			result += string(char)
		}
	}
	return result
}

func (s *SherlockActorActivityServiceImpl) GetActivityDetails(ctx context.Context, activityMetadata string) ([]*webui.Section, error) {
	entityMeta := &ticketPb.AttachEntityMeta{}
	err := protojson.Unmarshal([]byte(activityMetadata), entityMeta)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshal activity metadata")
	}
	activity := entityMeta.GetSherlockActorActivityV2Meta().GetActivity()
	return []*webui.Section{
		{
			Title: constant.BasicInfoSectionName,
			LabelValues: []*webui.LabelValueV2{
				{
					Value:    &webui.LabelValueV2_StringValue{StringValue: activity.GetArea().String()},
					Label:    constant.AreaLabel,
					DataType: webui.LabelValueV2_DATA_TYPE_STRING,
				},
				{
					Value:    &webui.LabelValueV2_StringValue{StringValue: activity.GetActivityType()},
					Label:    constant.ActivityLabel,
					DataType: webui.LabelValueV2_DATA_TYPE_STRING,
				},
				{
					Value:    &webui.LabelValueV2_StringValue{StringValue: activity.GetReportedAt().AsTime().In(datetime.IST).Format(constant.SherlockActivityTimeStampLayout)},
					Label:    constant.TimeStampLabel,
					DataType: webui.LabelValueV2_DATA_TYPE_STRING,
				},
			},
		},
		{
			Title:       constant.PrimaryDetailsSectionName,
			LabelValues: s.convertPropertyToLabelValuePair(activity.GetPrimaryProperties()),
		},
		{
			Title:       constant.SecondaryDetailsSectionName,
			LabelValues: s.convertPropertyToLabelValuePair(activity.GetSecondaryProperties()),
		},
	}, nil
}

func (s *SherlockActorActivityServiceImpl) convertPropertyToLabelValuePair(properties *structpb.Struct) []*webui.LabelValueV2 {
	masked := s.maskPrimaryProperties(properties)
	var list []*webui.LabelValueV2
	for k, v := range masked {
		list = append(list, &webui.LabelValueV2{
			Label:    k,
			Value:    &webui.LabelValueV2_StringValue{StringValue: v},
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		})
	}
	return list
}
