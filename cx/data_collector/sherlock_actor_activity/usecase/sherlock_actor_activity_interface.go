package usecase

import (
	"context"

	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/model"
)

type SherlockActorActivity interface {
	GetSherlockActorActivityAreas(ctx context.Context) (*model.SherlockActivityAreas, error)
	GetActivities(ctx context.Context, actorActivityParams *model.ActorActivityRequestParams) (*model.ActorActivityResponse, error)
	GetActivityDetails(ctx context.Context, activityMetadata string) ([]*webui.Section, error)
}
