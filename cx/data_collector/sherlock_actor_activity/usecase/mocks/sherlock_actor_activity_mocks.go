// Code generated by MockGen. DO NOT EDIT.
// Source: sherlock_actor_activity_interface.go

// Package usecase is a generated GoMock package.
package usecase

import (
	context "context"
	reflect "reflect"

	model "github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/model"
	gomock "github.com/golang/mock/gomock"
)

// MockSherlockActorActivity is a mock of SherlockActorActivity interface.
type MockSherlockActorActivity struct {
	ctrl     *gomock.Controller
	recorder *MockSherlockActorActivityMockRecorder
}

// MockSherlockActorActivityMockRecorder is the mock recorder for MockSherlockActorActivity.
type MockSherlockActorActivityMockRecorder struct {
	mock *MockSherlockActorActivity
}

// NewMockSherlockActorActivity creates a new mock instance.
func NewMockSherlockActorActivity(ctrl *gomock.Controller) *MockSherlockActorActivity {
	mock := &MockSherlockActorActivity{ctrl: ctrl}
	mock.recorder = &MockSherlockActorActivityMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSherlockActorActivity) EXPECT() *MockSherlockActorActivityMockRecorder {
	return m.recorder
}

// GetActivities mocks base method.
func (m *MockSherlockActorActivity) GetActivities(ctx context.Context, actorActivityParams model.ActorActivityRequestParams) (model.ActorActivityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActivities", ctx, actorActivityParams)
	ret0, _ := ret[0].(model.ActorActivityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActivities indicates an expected call of GetActivities.
func (mr *MockSherlockActorActivityMockRecorder) GetActivities(ctx, actorActivityParams interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActivities", reflect.TypeOf((*MockSherlockActorActivity)(nil).GetActivities), ctx, actorActivityParams)
}

// GetSherlockActorActivityAreas mocks base method.
func (m *MockSherlockActorActivity) GetSherlockActorActivityAreas(ctx context.Context) (model.SherlockActivityAreas, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSherlockActorActivityAreas", ctx)
	ret0, _ := ret[0].(model.SherlockActivityAreas)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSherlockActorActivityAreas indicates an expected call of GetSherlockActorActivityAreas.
func (mr *MockSherlockActorActivityMockRecorder) GetSherlockActorActivityAreas(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSherlockActorActivityAreas", reflect.TypeOf((*MockSherlockActorActivity)(nil).GetSherlockActorActivityAreas), ctx)
}
