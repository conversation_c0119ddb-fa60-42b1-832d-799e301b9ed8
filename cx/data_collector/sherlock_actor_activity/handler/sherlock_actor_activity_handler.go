package handler

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	saaPb "github.com/epifi/gamma/api/cx/data_collector/sherlock_actor_activity"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/model"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/usecase"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/logger"
)

type SherlockActorActivityHandler struct {
	authEngine        auth_engine.IAuthEngine
	sherlockAAUsecase usecase.SherlockActorActivity
	saaPb.UnimplementedSherlockActorActivityServer
}

func NewSherlockActorActivityHandler(authEngine auth_engine.IAuthEngine, sherlockAAUsecase usecase.SherlockActorActivity) *SherlockActorActivityHandler {
	return &SherlockActorActivityHandler{
		authEngine:        authEngine,
		sherlockAAUsecase: sherlockAAUsecase,
	}
}

var _ saaPb.SherlockActorActivityServer = &SherlockActorActivityHandler{}

func (s *SherlockActorActivityHandler) GetActivityDetailsForSherlock(ctx context.Context,
	request *saaPb.GetActivityDetailsForSherlockRequest) (*saaPb.GetActivityDetailsForSherlockResponse, error) {
	sections, err := s.sherlockAAUsecase.GetActivityDetails(ctx, request.GetActivityMeta())
	if err != nil {
		cxLogger.Error(ctx, "failed to fetch activity details", zap.Error(err), zap.String("meta", request.GetActivityMeta()))
		return &saaPb.GetActivityDetailsForSherlockResponse{Status: rpc.StatusInternal()}, nil
	}
	return &saaPb.GetActivityDetailsForSherlockResponse{
		Status:   rpc.StatusOk(),
		Sections: sections,
	}, nil
}

func (s *SherlockActorActivityHandler) GetSherlockActivityAreas(ctx context.Context, req *saaPb.GetSherlockActivityAreasRequest) (*saaPb.GetSherlockActivityAreasResponse, error) {
	cxLogger.Debug(ctx, "GetSherlockActivityAreas request", zap.Any(logger.REQUEST, req))
	sherlockActivityAreas, err := s.sherlockAAUsecase.GetSherlockActorActivityAreas(ctx)
	if err != nil {
		err = errors.Wrap(err, "failed to fetch Sherlock Activity Areas")
		cxLogger.Error(ctx, "failed to fetch sherlock activity areas", zap.Error(err))
		return &saaPb.GetSherlockActivityAreasResponse{
			Status: rpc.StatusInternal(),
			Areas:  nil,
		}, nil
	}
	return &saaPb.GetSherlockActivityAreasResponse{
		Status: rpc.StatusOk(),
		Areas:  sherlockActivityAreas.SherlockActivityArea,
	}, nil
}

func (s *SherlockActorActivityHandler) GetActivities(ctx context.Context, sherlockAARequest *saaPb.GetActivitiesRequest) (*saaPb.GetActivitiesResponse, error) {
	cxLogger.Debug(ctx, "GetActivities request", zap.Any(logger.REQUEST, sherlockAARequest))
	if !s.isSherlockAARequestValid(sherlockAARequest) {
		cxLogger.Error(ctx, "failed to validate request")
		return &saaPb.GetActivitiesResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, sherlockAARequest.GetHeader(), sherlockAARequest.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Error(ctx, "missing auth level required to fetch the information")
		return &saaPb.GetActivitiesResponse{
			Status:           rpc.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	platformActorActivityRequest, err := model.ConvertSherlockActivityRequestProtoToActorActivityParams(sherlockAARequest)
	if err != nil {
		cxLogger.Error(ctx, "failed to convert client request to platform request", zap.Error(err))
		return &saaPb.GetActivitiesResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	res, err := s.sherlockAAUsecase.GetActivities(ctx, platformActorActivityRequest)
	if err != nil {
		err = errors.Wrap(err, fmt.Sprintf("failed to fetch activities for %v", sherlockAARequest.GetHeader().GetUser().GetProfile().GetKycName()))
		cxLogger.Error(ctx, "failed to fetch sherlock activity areas", zap.Error(err))
		return &saaPb.GetActivitiesResponse{
			Status: rpc.StatusInternalWithDebugMsg("no activities found for user"),
		}, nil
	}
	return &saaPb.GetActivitiesResponse{
		Status:            rpc.StatusOk(),
		ActivityDetails:   res.ActivityDetails,
		PageContext:       res.PageContext,
		ActivityDetailsV2: res.ActivityDetailsV2,
		PageContextV2:     res.PageContextV2,
	}, nil
}

func (s *SherlockActorActivityHandler) isSherlockAARequestValid(sherlockAARequest *saaPb.GetActivitiesRequest) bool {
	var isValid bool
	for _, areas := range sherlockAARequest.GetSherlockAreas() {
		if areas.GetSherlockActivityArea() != saaPb.SherlockActivityArea_SHERLOCK_ACTIVITY_AREA_UNSPECIFIED {
			isValid = true
		}
	}
	return isValid
}
