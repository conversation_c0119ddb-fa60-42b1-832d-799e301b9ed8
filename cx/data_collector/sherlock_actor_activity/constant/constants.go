package constant

import (
	ffBeAccountsEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	actoractivitypb "github.com/epifi/gamma/api/order/actoractivity"
	"github.com/epifi/gamma/api/typesv2/webui"
)

const (
	TimeStampLabel             = "Performed At"
	AreaLabel                  = "Area"
	ActivityLabel              = "Activity"
	MerchantLabel              = "Merchant"
	UnitLabel                  = "Unit"
	FiTxnIdLabel               = "Fi Txn ID"
	UtrLabel                   = "UTR no/ URN"
	TypeOfTransactionLabel     = "Transaction Type"
	InternalTransactionIdLabel = "Internal Transaction Id"
	PrimaryPropertiesLabel     = "Primary properties"

	// FundsDebitedAtLabel : represents the timestamp when
	// funds were debited from user's account
	// NOTE - It represents only Pay txns timestamps (Credit card txns timestampp won't be populated.)
	FundsDebitedAtLabel = "Funds Debited At"

	TimeStampKey             = "timeStamp"
	AreaKey                  = "area"
	ActivityKey              = "activity"
	MerchantKey              = "merchant"
	UnitKey                  = "unit"
	FiTxnIdKey               = "fiTxnId"
	UtrKey                   = "utr"
	TypeOfTransactionKey     = "transactionType"
	InternalTransactionIdKey = "internalTransactionIdKey"
	FundsDebitedAtKey        = "fundsDebitedAt"
	PrimaryPropertiesKey     = "primaryProperties"

	SherlockActivityTableName = "SherlockActivityTable"
	PayActivityType           = "Made Payment"
	CreditCardActivityType    = "Made Payment"
	TransactionArea           = "Transaction"
	NoDebit                   = "NO DEBIT"

	// Layout for time stamps to be surfaced on sherlock
	SherlockActivityTimeStampLayout = "January 02, 2006 15:04:05"

	// label for incoming transaction
	Credited = "credit"
	Debited  = "debit"

	BasicInfoSectionName        = "Basic Info"
	PrimaryDetailsSectionName   = "Primary Details"
	SecondaryDetailsSectionName = "Secondary Details"
)

var (
	PayAmountBadgeToTransactionFlow = map[string]string{
		actoractivitypb.GetActivitiesResponse_Activity_CREDIT.String(): Credited,
		actoractivitypb.GetActivitiesResponse_Activity_DEBIT.String():  Debited,
	}

	CCAmountBadgeToTransactionFlow = map[string]string{
		ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT.String(): Credited,
		ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT.String():  Debited,
	}

	PropertyToMask = []string{
		"txn_id", "internal_txn_id", "transaction_id", "utr", "InternalTxnId", "UTR",
	}
	BlackListedProperties = []string{
		"StatusIconUrl", "RightIconUrl", "StatusToDisplay", "PaymentStatusToDisplay", "AmountToDisplay", "IconUrl",
		"MarshalledDeeplink",
	}
	ActivityTableRowStyle = &webui.Style{
		Color:           "#000000",
		BackgroundColor: "#FFFFFF",
	}
)
