package card

const (
	forexRefundCreatedString       = "Created"
	forexRefundInProgressString    = "Under Progress"
	ineligibleRefundString         = "Ineligible"
	ineligibleCappedRefundString   = "Ineligible - Capped"
	frozenAccountForexRefundString = "Frozen account"
	forexRefundCompletedString     = "Refunded"
	prsTableName                   = "Card order status"
	amcInfoTableName               = "Amc details"
	prsStageString                 = "Stage"
	prsStatusString                = "Status"
	prsTroubleshootString          = "Troubleshoot"
	dateLayoutDDMM                 = "02/01"
	dateLayoutDDMMYYYY             = "02/01/2006"
	amcAreaString                  = "Area"
	amcDetailsString               = "Details"
	amcChargesAmountString         = "199 + GST"
	spendsExceeding25kString       = "exceeding 25k"
	spendsNotExceeding25kString    = "Not exceeding 25k"
	dcSpendsThresholdForAmc        = float64(25000)
)
