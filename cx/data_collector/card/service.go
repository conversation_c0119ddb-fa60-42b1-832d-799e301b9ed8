// nolint:funlen,ineffassign
package card

import (
	"context"
	"fmt"
	"math"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/shopspring/decimal"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	errorsPkg "github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/errgroup"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"

	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/tiering"
	externalPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/data_collector/helper"

	cxLogger "github.com/epifi/gamma/cx/logger"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cardPb "github.com/epifi/gamma/api/card"
	cardControlPb "github.com/epifi/gamma/api/card/control"
	cardProvisioningPb "github.com/epifi/gamma/api/card/provisioning"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	cxCardPb "github.com/epifi/gamma/api/cx/data_collector/card"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
)

const (
	// August 2, 2024: Federal split the incoming DC international transactions into 2 transactions: original amount + forex markup
	forexTransactionsSplitDate = "01/08/2024"
	forexGstInclusionDate      = "31/05/2023"
)

var (
	prsTableColumnsOrder = []string{
		prsStageString,
		prsStatusString,
		prsTroubleshootString,
	}

	prsStageEnumToStageNameMap = map[cardProvisioningPb.DCRequestStage]string{
		cardProvisioningPb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS:       "Payment",
		cardProvisioningPb.DCRequestStage_DC_REQUEST_STAGE_CREATE_SHIPPING_PREFERENCE: "Shipping address update",
		cardProvisioningPb.DCRequestStage_DC_REQUEST_STAGE_UPDATE_SHIPPING_ADDRESS:    "Shipping address update",
		cardProvisioningPb.DCRequestStage_DC_REQUEST_STAGE_DISPATCH_PHYSICAL_CARD:     "Fulfillment",
		cardProvisioningPb.DCRequestStage_DC_REQUEST_STAGE_UNSPECIFIED:                "",
	}

	prsStatusEnumToNameMap = map[cardProvisioningPb.RequestState]string{
		cardProvisioningPb.RequestState_QUEUED:                    "Pending",
		cardProvisioningPb.RequestState_MANUAL_INTERVENTION:       "manual intervention",
		cardProvisioningPb.RequestState_INITIATED:                 "initiated",
		cardProvisioningPb.RequestState_FAILED:                    "failed",
		cardProvisioningPb.RequestState_SUCCESS:                   "success",
		cardProvisioningPb.RequestState_REQUEST_STATE_UNSPECIFIED: "",
	}

	prsCreatedAtString = "Physical card successfully requested at %s"
	prsSuccessAtString = "Physical card successfully created at %s"
)

type Service struct {
	cpClient      cardProvisioningPb.CardProvisioningClient
	ccClient      cardControlPb.CardControlClient
	authEngine    auth_engine.IAuthEngine
	tieringClient tiering.TieringClient
	payClient     pay.PayClient
}

func NewService(cpClient cardProvisioningPb.CardProvisioningClient, ccClient cardControlPb.CardControlClient, authEngine auth_engine.IAuthEngine,
	tieringClient tiering.TieringClient, payClient pay.PayClient) *Service {
	return &Service{
		cpClient:      cpClient,
		ccClient:      ccClient,
		authEngine:    authEngine,
		tieringClient: tieringClient,
		payClient:     payClient,
	}
}

var _ cxCardPb.CardsServer = &Service{}

func (s *Service) FetchPhysicalCardDispatchStatus(ctx context.Context, req *cxCardPb.FetchPhysicalCardDispatchStatusRequest) (*cxCardPb.FetchPhysicalCardDispatchStatusResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.FetchPhysicalCardDispatchStatusResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	var (
		res             = &cxCardPb.FetchPhysicalCardDispatchStatusResponse{}
		dispatchRequest = &cardProvisioningPb.PhysicalCardDispatchRequest{}
	)
	fetchRequestsResp, err := s.cpClient.FetchPhysicalCardDispatchRequests(ctx, &cardProvisioningPb.FetchPhysicalCardDispatchRequestsRequest{
		CardId: req.GetCardId(),
		CardIdentifier: &cardProvisioningPb.CardIdentifier{
			Identifier: &cardProvisioningPb.CardIdentifier_CardId{
				CardId: req.GetCardId(),
			},
		},
	})
	switch {
	case fetchRequestsResp.GetStatus().IsRecordNotFound():
		logger.Info(ctx, "no dispatch requests found for card id", zap.String(logger.CARD_ID, req.GetCardId()))
		fetchCardsResp, fetchErr := s.cpClient.FetchCardDetails(ctx, &cardProvisioningPb.FetchCardDetailsRequest{
			IssuingBank: commonvgpb.Vendor_FEDERAL_BANK,
			CardIds:     []string{req.GetCardId()},
		})
		if te := epifigrpc.RPCError(fetchCardsResp, fetchErr); te != nil {
			logger.Error(ctx, "error fetching card details")
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		savedCard := fetchCardsResp.GetCards()[req.GetCardId()]
		if savedCard.GetForm() != cardPb.CardForm_PHYSICAL {
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		res.RequestPlacedAtString = fmt.Sprintf(prsSuccessAtString, datetime.TimestampToString(savedCard.GetCreatedAt(), dateLayoutDDMMYYYY, datetime.IST))
		res.Status = rpcPb.StatusOk()
		return res, nil
	case err != nil || !fetchRequestsResp.GetStatus().IsSuccess():
		te := epifigrpc.RPCError(fetchRequestsResp, err)
		logger.Error(ctx, "error fetching dispatch request for card id",
			zap.String(logger.CARD_ID, req.GetCardId()), zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		dispatchRequest = fetchRequestsResp.GetPhysicalCardDispatchRequests()[len(fetchRequestsResp.GetPhysicalCardDispatchRequests())-1]
	}

	prsTable := &webui.Table{
		TableHeaders: getPrsTableHeader(),
		TableName:    prsTableName,
	}
	prsTable.TableRows = getPrsTableRows(dispatchRequest)

	switch dispatchRequest.GetState() {
	case cardProvisioningPb.RequestState_SUCCESS:
		res.RequestPlacedAtString = fmt.Sprintf(prsSuccessAtString, datetime.TimestampToString(dispatchRequest.GetCreatedAt(), dateLayoutDDMMYYYY, datetime.IST))
	default:
		res.RequestPlacedAtString = fmt.Sprintf(prsCreatedAtString, datetime.TimestampToString(dispatchRequest.GetCreatedAt(), dateLayoutDDMMYYYY, datetime.IST))
		res.DispatchStatusDetailsTable = prsTable
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func getPrsTableHeader() []*webui.TableHeader {
	var tableHeaders []*webui.TableHeader
	for _, column := range prsTableColumnsOrder {
		tableHeaders = append(tableHeaders, &webui.TableHeader{
			HeaderKey: column,
			Label:     column,
			IsVisible: true,
		})
	}
	return tableHeaders
}

func getPrsTableRows(dispatchRequest *cardProvisioningPb.PhysicalCardDispatchRequest) []*webui.TableRow {
	headerKeyCellMap := make(map[string]*webui.TableCell, 0)
	for _, prsColumn := range prsTableColumnsOrder {
		switch prsColumn {
		case prsStageString:
			headerKeyCellMap[prsStageString] = &webui.TableCell{
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: prsStageEnumToStageNameMap[dispatchRequest.GetCurrentStage()]},
			}
		case prsStatusString:
			headerKeyCellMap[prsStatusString] = &webui.TableCell{
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: prsStatusEnumToNameMap[dispatchRequest.GetState()]},
			}
		case prsTroubleshootString:
			headerKeyCellMap[prsTroubleshootString] = &webui.TableCell{
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: getPrsTroubleShootString(dispatchRequest.GetState(), dispatchRequest.GetSubStatus())},
			}
		default:
		}
	}
	return []*webui.TableRow{
		{HeaderKeyCellMap: headerKeyCellMap},
	}
}

func getPrsTroubleShootString(state cardProvisioningPb.RequestState, subStatus cardProvisioningPb.RequestSubStatus) string {
	switch state {
	case cardProvisioningPb.RequestState_FAILED, cardProvisioningPb.RequestState_MANUAL_INTERVENTION:
		switch subStatus {
		case cardProvisioningPb.RequestSubStatus_REQUEST_SUB_STATUS_FAILED_ON_VENDOR:
			return "Fed Block"
		case cardProvisioningPb.RequestSubStatus_REQUEST_SUB_STATUS_ACTIVITY_RETRIES_EXHAUSTED,
			cardProvisioningPb.RequestSubStatus_REQUEST_SUB_STATUS_NO_DATA_FOUND,
			cardProvisioningPb.RequestSubStatus_REQUEST_SUB_STATUS_FAILED_TO_CALL_VENDOR:
			return "Fi system error"
		case cardProvisioningPb.RequestSubStatus_REQUEST_SUB_STATUS_PENDING_ON_VENDOR:
			return "pending on fed"
		default:
			return ""
		}
	default:
		return ""
	}
}

func (s *Service) FetchAmcInfo(ctx context.Context, req *cxCardPb.FetchAmcInfoRequest) (*cxCardPb.FetchAmcInfoResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.FetchAmcInfoResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	var (
		res = &cxCardPb.FetchAmcInfoResponse{}
		// this is time when user first time onboarded on physical debit card
		// user can switch between forms of cards but time will always remain same as the first time when user's card form changes to physical
		onboardingTime = &timestamppb.Timestamp{}
	)

	fetchCardsResp, err := s.cpClient.FetchCards(ctx, &cardProvisioningPb.FetchCardsRequest{
		Actor:        req.GetHeader().GetActor(),
		IssuingBanks: []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardTypes:    []cardPb.CardType{cardPb.CardType_DEBIT},
		CardForms:    []cardPb.CardForm{cardPb.CardForm_PHYSICAL}, // fetching only physical cards
		SortedBy:     cardPb.CardFieldMask_CARD_CREATED_AT,
	})
	if te := epifigrpc.RPCError(fetchCardsResp, err); te != nil {
		logger.Error(ctx, "error fetching cards for actor", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetActor().GetId()))
		res.Status = rpcPb.StatusFromErrorWithDefaultInternal(te)
		return res, nil
	}
	firstPhysicalCard := fetchCardsResp.GetCards()[len(fetchCardsResp.GetCards())-1]
	onboardingTime = firstPhysicalCard.GetCreatedAt()

	dispatchRequestResp, fetchErr := s.cpClient.FetchPhysicalCardDispatchRequests(ctx, &cardProvisioningPb.FetchPhysicalCardDispatchRequestsRequest{
		CardId: firstPhysicalCard.GetId(),
	})
	if te := epifigrpc.RPCError(dispatchRequestResp, fetchErr); te != nil && !dispatchRequestResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching dispatch", zap.Error(te), zap.String(logger.CARD_ID, firstPhysicalCard.GetId()))
		res.Status = rpcPb.StatusFromErrorWithDefaultInternal(te)
		return res, nil
	}
	if dispatchRequestResp.GetStatus().IsSuccess() &&
		dispatchRequestResp.GetPhysicalCardDispatchRequests()[0].GetState() == cardProvisioningPb.RequestState_SUCCESS {
		onboardingTime = dispatchRequestResp.GetPhysicalCardDispatchRequests()[0].GetCreatedAt()
	}

	amcInfoTable := &webui.Table{
		TableHeaders: getAmcInfoTableHeader(),
		TableName:    amcInfoTableName,
	}
	amcInfoTable.TableRows, err = s.getAmcInfoTableRows(ctx, req.GetHeader().GetActor().GetId(), onboardingTime)
	if err != nil {
		logger.Error(ctx, "error getting amc info table rows", zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return res, nil
	}
	res.AmcInfoTable = amcInfoTable
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) getAmcInfoTableRows(ctx context.Context, actorId string, cardOnboardingTime *timestamppb.Timestamp) ([]*webui.TableRow, error) {
	var (
		tableRows            = make([]*webui.TableRow, 0)
		tier                 externalPb.Tier
		getTierErr           error
		spendsValueString    string
		amcChargeString      string
		cardSpendsAggregates *payPb.GetTransactionAggregatesResponse
		txnAggrErr           error
	)
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		tier, getTierErr = s.getTierOnTime(grpCtx, actorId, getLastCardAnniversaryDate(cardOnboardingTime))
		if getTierErr != nil {
			return getTierErr
		}
		return nil
	})
	cardAnniversary := getLastCardAnniversaryDate(cardOnboardingTime)

	grp.Go(func() error {
		cardSpendsAggregates, txnAggrErr = s.payClient.GetTransactionAggregates(grpCtx, &payPb.GetTransactionAggregatesRequest{
			ActorId:             actorId,
			AccountingEntryType: paymentPb.AccountingEntryType_DEBIT,
			FromTime:            timestamppb.New(datetime.StartOfDay(cardAnniversary.AsTime()).In(datetime.IST).AddDate(-1, 0, 0)),
			ToTime:              timestamppb.New(datetime.StartOfDay(cardAnniversary.AsTime().In(datetime.IST))),
			PaymentProtocol:     []paymentPb.PaymentProtocol{paymentPb.PaymentProtocol_CARD},
			TransactionsStatus:  []paymentPb.TransactionStatus{paymentPb.TransactionStatus_SUCCESS},
			Provenance:          []orderPb.OrderProvenance{orderPb.OrderProvenance_ECOMM, orderPb.OrderProvenance_POS},
		})
		if te := epifigrpc.RPCError(cardSpendsAggregates, txnAggrErr); te != nil {
			return errorsPkg.Wrap(te, "error fetching txn aggregates")
		}
		return nil
	})

	if err := grp.Wait(); err != nil {
		return nil, errorsPkg.Wrap(err, "error in getTierOnTime/GetTransactionAggregates")
	}

	// construct spends string
	cmp, err := moneyPb.CompareV2(cardSpendsAggregates.GetTransactionAggregates().GetSumAmount(), moneyPb.ParseFloat(dcSpendsThresholdForAmc, moneyPb.RupeeCurrencyCode))
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error comparing money")
	}
	if cmp >= 0 {
		spendsValueString = spendsExceeding25kString
	} else {
		spendsValueString = spendsNotExceeding25kString
	}

	// construct amc charges string
	if tier.IsBaseTier() || (tier == externalPb.Tier_TIER_FI_PLUS && cmp < 0) {
		amcChargeString = amcChargesAmountString
	} else {
		amcChargeString = "NA"
	}

	// onboarding details
	tableRows = append(tableRows, &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			amcAreaString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: "Date Onboarded"},
			},
			amcDetailsString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: datetime.TimestampToString(cardOnboardingTime, dateLayoutDDMMYYYY, datetime.IST)},
			},
		},
	})

	// card anniversary details
	tableRows = append(tableRows, &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			amcAreaString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: "Card anniversary"},
			},
			amcDetailsString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: datetime.TimestampToString(cardAnniversary, dateLayoutDDMM, datetime.IST)},
			},
		},
	})

	// tier info on last card anniversary
	tableRows = append(tableRows, &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			amcAreaString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: "Tier level on card anniversary"},
			},
			amcDetailsString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: tier.String()},
			},
		},
	})

	// amount eligible for AMC
	tableRows = append(tableRows, &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			amcAreaString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: "Amount eligible"},
			},
			amcDetailsString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: amcChargeString},
			},
		},
	})

	// DC spends are greater than 25k or not
	tableRows = append(tableRows, &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			amcAreaString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: "DC spends"},
			},
			amcDetailsString: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: spendsValueString},
			},
		},
	})
	return tableRows, nil
}

func getLastCardAnniversaryDate(cardOnboardingTime *timestamppb.Timestamp) *timestamppb.Timestamp {
	firstAnniversary := datetime.StartOfDay(cardOnboardingTime.AsTime())
	today := datetime.StartOfDay(timestamppb.Now().AsTime())

	lastAnniversary := firstAnniversary.AddDate(int(math.Abs(float64(today.Year()-firstAnniversary.Year()))), 0, 0)
	if today.Before(lastAnniversary) {
		lastAnniversary = lastAnniversary.AddDate(-1, 0, 0)
	}
	return timestamppb.New(lastAnniversary)
}

func (s *Service) getTierOnTime(ctx context.Context, actorId string, time *timestamppb.Timestamp) (externalPb.Tier, error) {
	tierResp, err := s.tieringClient.GetTierAtTime(ctx, &tiering.GetTierAtTimeRequest{
		ActorId:       actorId,
		TierTimestamp: time,
	})
	if te := epifigrpc.RPCError(tierResp, err); te != nil {
		return externalPb.Tier_TIER_UNSPECIFIED, errorsPkg.Wrap(te, "error fetching tier of user")
	}
	return tierResp.GetTierInfo().GetTier(), nil
}

func getAmcInfoTableHeader() []*webui.TableHeader {
	var tableHeaders []*webui.TableHeader
	tableHeaders = append(tableHeaders, &webui.TableHeader{
		HeaderKey: amcAreaString,
		Label:     amcAreaString,
		IsVisible: true,
	}, &webui.TableHeader{
		HeaderKey: amcDetailsString,
		Label:     amcDetailsString,
		IsVisible: true,
	})
	return tableHeaders
}

func (s *Service) FetchForexRefundInfoForDcTxn(ctx context.Context, req *cxCardPb.FetchForexRefundInfoForDcTxnRequest) (*cxCardPb.FetchForexRefundInfoForDcTxnResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		return &cxCardPb.FetchForexRefundInfoForDcTxnResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	res := &cxCardPb.FetchForexRefundInfoForDcTxnResponse{}
	refunds := make([]*cxCardPb.ForexRefundDetails, 0)
	switch {
	case len(req.GetTxnId()) != 0:
		refundDetails, err := s.cpClient.GetForexRefunds(ctx, &cardProvisioningPb.GetForexRefundsRequest{
			Identifier: &cardProvisioningPb.GetForexRefundsRequest_TxnId{
				TxnId: req.GetTxnId(),
			},
		})
		if refundDetails.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "no refunds for the given txn id", zap.String(logger.TXN_ID, req.GetTxnId()))
			res.Status = rpcPb.StatusOk()
			res.Refunds = refunds
			return res, nil
		}
		if te := epifigrpc.RPCError(refundDetails, err); te != nil {
			logger.Error(ctx, "error in fetching forex refunds",
				zap.String(logger.TXN_ID, req.GetTxnId()),
				zap.Error(te))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		refunds = append(refunds, s.getCxForexRefundInfoFromBeForexRefundInfo(ctx, refundDetails.GetRefunds()[0]))
	default:
		isBeforeToken := false
		token := ""
		var err error
		pageToken := &pagination.PageToken{}
		startTime := req.GetStartTime()
		switch {
		case startTime.GetSeconds() == 0 && req.GetPageContextRequest().GetToken() == nil:
			pageToken = &pagination.PageToken{
				Timestamp: timestamppb.Now(),
				Offset:    0,
				IsReverse: false,
			}
			token, err = pageToken.Marshal()
			if err != nil {
				logger.Error(ctx, "error in marshalling page token",
					zap.Error(err),
					zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetActor().GetId()))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			isBeforeToken = true
		case req.GetPageContextRequest().GetToken() != nil:
			token = req.GetPageContextRequest().GetAfterToken()
			if token == "" {
				isBeforeToken = true
				token = req.GetPageContextRequest().GetBeforeToken()
			}
		case startTime.GetSeconds() != 0:
			pageToken = &pagination.PageToken{
				Timestamp: startTime,
				Offset:    0,
				IsReverse: false,
			}
			token, err = pageToken.Marshal()
			if err != nil {
				logger.Error(ctx, "error in marshalling page token",
					zap.Error(err),
					zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetActor().GetId()))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			isBeforeToken = true
		}
		rpcReq := &cardProvisioningPb.GetPaginatedForexRefundsByActorIdRequest{}
		if isBeforeToken {
			rpcReq = &cardProvisioningPb.GetPaginatedForexRefundsByActorIdRequest{
				ActorId: req.GetHeader().GetActor().GetId(),
				PageContextRequest: &rpcPb.PageContextRequest{
					Token:    &rpcPb.PageContextRequest_BeforeToken{BeforeToken: token},
					PageSize: 20,
				},
			}
		} else {
			rpcReq = &cardProvisioningPb.GetPaginatedForexRefundsByActorIdRequest{
				ActorId: req.GetHeader().GetActor().GetId(),
				PageContextRequest: &rpcPb.PageContextRequest{
					Token:    &rpcPb.PageContextRequest_AfterToken{AfterToken: token},
					PageSize: 20,
				},
			}
		}
		refundDetails, err := s.cpClient.GetPaginatedForexRefundsByActorId(ctx, rpcReq)
		if refundDetails.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "no refunds for the given actor id", zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetActor().GetId()))
			res.Status = rpcPb.StatusOk()
			res.Refunds = refunds
			return res, nil
		}
		if te := epifigrpc.RPCError(refundDetails, err); te != nil {
			logger.Error(ctx, "error in fetching forex refunds",
				zap.String(logger.TXN_ID, req.GetTxnId()),
				zap.Error(te))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.PageContextResponse = helper.ConvertToCxPageContext(refundDetails.GetPageContextResponse())
		for _, pb := range refundDetails.GetRefunds() {
			refunds = append(refunds, s.getCxForexRefundInfoFromBeForexRefundInfo(ctx, pb))
		}
	}
	res.Status = rpcPb.StatusOk()
	res.Refunds = refunds
	return res, nil
}

func (s *Service) getCxForexRefundInfoFromBeForexRefundInfo(ctx context.Context, beRefund *cardPb.DcForexTxnRefund) *cxCardPb.ForexRefundDetails {
	if beRefund.GetOriginalTransaction() != "" {

	}
	res := &cxCardPb.ForexRefundDetails{
		TxnAmount:             s.getInternationalTxnAmount(ctx, beRefund),
		TxnTime:               beRefund.GetTxnTime(),
		UserTier:              beRefund.GetTxnTimeUserTier().String(),
		ForexMarkup:           getDebitCardForexMarkupForTxn(beRefund.GetTxnTime(), beRefund.GetTotalTxnAmount()),
		RefundAmount:          beRefund.GetRefundAmount(),
		RefundStatus:          getRefundStatusString(beRefund.GetRefundStatus(), beRefund.GetRefundSubStatus()),
		TxnId:                 beRefund.GetTxnId(),
		OriginalTransactionId: beRefund.GetOriginalTransaction(),
		DedupeIdentifier:      beRefund.GetDedupeIdentifier(),
	}
	if beRefund.GetRefundStatus() == cardEnumsPb.RefundStatus_REFUND_STATUS_COMPLETED {
		res.RefundTime = beRefund.GetUpdatedAt()
	}
	return res
}

func (s *Service) getInternationalTxnAmount(ctx context.Context, forexTxn *cardPb.DcForexTxnRefund) *money.Money {
	if forexTxn.GetOriginalTransaction() != "" {
		// if original transaction is present, then we can fetch the amount of the original txn
		getTransactionRes, getTransactionErr := s.payClient.GetTransaction(ctx, &payPb.GetTransactionRequest{TransactionId: forexTxn.GetOriginalTransaction()})
		if grpcErr := epifigrpc.RPCError(getTransactionRes, getTransactionErr); grpcErr != nil {
			cxLogger.Error(ctx, "error fetching original transaction for forex refund", zap.Error(grpcErr))
			return forexTxn.GetTotalTxnAmount()
		}
		return getTransactionRes.GetTransaction().GetAmount()
	}

	// 3.5% + GST = 4.13% forex markup
	// hence forex txn amount is 4.13% of original txn amount
	transactionsSplitTimeStamp, _ := datetime.ParseStringTimestampProtoInLocation("02/01/2006", forexTransactionsSplitDate, datetime.IST)
	if datetime.IsAfter(forexTxn.GetTxnTime(), transactionsSplitTimeStamp) {
		return moneyPb.Multiply(forexTxn.GetTotalTxnAmount(), decimal.NewFromFloat(100.0/4.13))
	}

	// by default return original txn amount
	return forexTxn.GetTotalTxnAmount()
}

// nolint:gocritic
func getRefundStatusString(refundStatus cardEnumsPb.RefundStatus, refundSubStatus cardEnumsPb.RefundSubStatus) string {
	switch refundSubStatus {
	case cardEnumsPb.RefundSubStatus_REFUND_SUB_STATUS_REFUND_CAPPED:
		return ineligibleCappedRefundString
	}

	switch refundStatus {
	case cardEnumsPb.RefundStatus_REFUND_STATUS_CREATED:
		return forexRefundCreatedString
	case cardEnumsPb.RefundStatus_REFUND_STATUS_APPROVED:
		return forexRefundInProgressString
	case cardEnumsPb.RefundStatus_REFUND_STATUS_REJECTED:
		return ineligibleRefundString
	case cardEnumsPb.RefundStatus_REFUND_STATUS_PROCESSED:
		return forexRefundInProgressString
	case cardEnumsPb.RefundStatus_REFUND_STATUS_EVALUATED:
		if refundSubStatus == cardEnumsPb.RefundSubStatus_REFUND_SUB_STATUS_ACCOUNT_NON_OPERATIONAL {
			return frozenAccountForexRefundString
		}
		return forexRefundInProgressString
	case cardEnumsPb.RefundStatus_REFUND_STATUS_COMPLETED:
		return forexRefundCompletedString
	default:
		return ""
	}
}

func getDebitCardForexMarkupForTxn(txnTime *timestamppb.Timestamp, txnAmount *money.Money) *money.Money {
	txnAmountFloat, _ := moneyPb.ToDecimal(txnAmount).Float64()
	transactionsSplitTimeStamp, _ := datetime.ParseStringTimestampProtoInLocation("02/01/2006", forexTransactionsSplitDate, datetime.IST)
	if datetime.IsAfter(txnTime, transactionsSplitTimeStamp) {
		return txnAmount
	}

	forexMarkupPercentage := 2.95
	cutoffDateForGstInclusion, _ := datetime.ParseStringTimestampProtoInLocation("02/01/2006", forexGstInclusionDate, datetime.IST)
	if datetime.IsBefore(txnTime, cutoffDateForGstInclusion) {
		forexMarkupPercentage = 2.5
	}
	forexCharged := txnAmountFloat - txnAmountFloat/(1+forexMarkupPercentage/100)
	return moneyPb.ParseFloat(forexCharged, "INR")
}

// Deprecated: In favour of GetAllCardsForCustomer
func (s *Service) GetCardsForCustomer(ctx context.Context, req *cxCardPb.GetCardsForCustomerRequest) (*cxCardPb.GetCardsForCustomerResponse, error) {
	return &cxCardPb.GetCardsForCustomerResponse{Status: rpcPb.StatusUnimplemented()}, nil
}

// TODO (hardik) : populate actual 16 digit card number and pin status when available
func convertToCCProto(card *cardPb.Card) *cxCardPb.CustomerCard {
	customerCard := &cxCardPb.CustomerCard{
		Id:                 card.GetId(),
		State:              card.GetState(),
		Type:               card.GetType(),
		NetworkType:        card.GetNetworkType(),
		IssuerBank:         card.GetIssuerBank(),
		CardLastFourDigits: getLastFourDigits(card.GetBasicInfo().GetMaskedCardNumber()),
		CardForm:           card.GetForm().String(),
	}
	return customerCard
}

func getLastFourDigits(cardDigits string) string {
	if len(cardDigits) < 4 {
		return ""
	}
	return cardDigits[len(cardDigits)-4:]
}

//nolint:dupl
func (s *Service) BlockCustomerCard(ctx context.Context, req *cxCardPb.BlockCustomerCardRequest) (*cxCardPb.BlockCustomerCardResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.BlockCustomerCardResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	// Client will take user to confirm page screen if auth passed but action to be taken was set as false
	if !req.GetIsActionable() {
		return &cxCardPb.BlockCustomerCardResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: &caPb.SherlockDeepLink{
			Screen: caPb.Screen_CONFIRM_ACTION_SCREEN,
		}}, nil
	}
	if req.GetHeader().GetActor() == nil {
		return &cxCardPb.BlockCustomerCardResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor not present in header")}, nil
	}
	resp, err := s.ccClient.BlockCard(ctx, &cardControlPb.BlockCardRequest{
		CardIds:             []string{req.GetCardId()},
		BlockCardProvenance: cardPb.Provenance_SHERLOCK,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "unable to call block card for actor", zap.String(logger.ACTOR_ID, req.GetHeader().GetActor().GetId()), zap.Error(err))
		if err != nil {
			return &cxCardPb.BlockCustomerCardResponse{Status: rpcPb.StatusInternal()}, nil
		}
		return &cxCardPb.BlockCustomerCardResponse{Status: resp.GetStatus()}, nil
	}
	return &cxCardPb.BlockCustomerCardResponse{Status: resp.GetStatus()}, nil
}

//nolint:dupl
func (s *Service) SuspendCustomerCard(ctx context.Context, req *cxCardPb.SuspendCustomerCardRequest) (*cxCardPb.SuspendCustomerCardResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.SuspendCustomerCardResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	// Client will take user to confirm page screen if auth passed but action to be taken was set as false
	if !req.GetIsActionable() {
		return &cxCardPb.SuspendCustomerCardResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: &caPb.SherlockDeepLink{
			Screen: caPb.Screen_CONFIRM_ACTION_SCREEN,
		}}, nil
	}
	if req.GetHeader().GetActor() == nil {
		return &cxCardPb.SuspendCustomerCardResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor not present in header")}, nil
	}
	// cred block and request id are not needed for suspend card
	// card control action and bank are needed
	resp, err := s.ccClient.SuspendCard(ctx, &cardControlPb.SuspendCardRequest{
		CardIds: []string{req.GetCardId()},
		Action:  cardPb.CardControlAction_ENABLE,
		Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "unable to call suspend card for actor", zap.String(logger.ACTOR_ID, req.GetHeader().GetActor().GetId()), zap.Error(err))
		if err != nil {
			return &cxCardPb.SuspendCustomerCardResponse{Status: rpcPb.StatusInternal()}, nil
		}
		return &cxCardPb.SuspendCustomerCardResponse{Status: resp.GetStatus()}, nil
	}
	return &cxCardPb.SuspendCustomerCardResponse{Status: resp.GetStatus()}, nil
}

func (s *Service) GetChannelAndLimitSettings(ctx context.Context, req *cxCardPb.GetChannelAndLimitSettingsRequest) (*cxCardPb.GetChannelAndLimitSettingsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.GetChannelAndLimitSettingsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetCardId() == "" || req.GetIssuerBank() == commonvgpb.Vendor_VENDOR_UNSPECIFIED {
		cxLogger.Error(ctx, "mandatory params card id or issuer bank not passed")
		return &cxCardPb.GetChannelAndLimitSettingsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mandatory params card id or issuer bank not passed"),
		}, nil
	}
	cardResp, err := s.cpClient.FetchCardDetails(ctx, &cardProvisioningPb.FetchCardDetailsRequest{
		CardIds:     []string{req.GetCardId()},
		IssuingBank: req.GetIssuerBank(),
	})
	if te := epifigrpc.RPCError(cardResp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch card details", zap.Error(err), zap.String(logger.CARD_ID, req.GetCardId()))
		return &cxCardPb.GetChannelAndLimitSettingsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch card details"),
		}, nil
	}

	errGroup, gctx := errgroup.WithContext(ctx)
	var (
		limitResp                         *cardControlPb.GetCardLimitsResponse
		countryWiseInternationalLimitResp *cardControlPb.GetInternationalAtmLimitsResponse
	)

	// fetch card limits
	errGroup.Go(func() error {
		limitResp, err = s.ccClient.GetCardLimits(gctx, &cardControlPb.GetCardLimitsRequest{
			CardId: req.GetCardId(),
		})
		if te := epifigrpc.RPCError(limitResp, err); te != nil {
			return fmt.Errorf("failed to fetch card limits")
		}
		return nil
	})

	errGroup.Go(func() error {
		// fetch international limits
		countryWiseInternationalLimitResp, err = s.ccClient.GetInternationalAtmLimits(gctx, &cardControlPb.GetInternationalAtmLimitsRequest{
			GetBy: &cardControlPb.GetInternationalAtmLimitsRequest_GetAll{GetAll: true},
		})
		if te := epifigrpc.RPCError(countryWiseInternationalLimitResp, err); te != nil {
			return fmt.Errorf("failed to fetch country-wise international limits")
		}
		return nil
	})

	if errGroupErr := errGroup.Wait(); errGroupErr != nil {
		logger.Error(ctx, "error fetching card limit or international limits", zap.Error(errGroupErr), zap.String(logger.CARD_ID, req.GetCardId()))
		return &cxCardPb.GetChannelAndLimitSettingsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(errGroupErr.Error()),
		}, nil
	}
	return buildChannelAndLimitResponse(cardResp, limitResp, req.GetCardId(), countryWiseInternationalLimitResp), nil
}

func buildChannelAndLimitResponse(cardResp *cardProvisioningPb.FetchCardDetailsResponse, limitResp *cardControlPb.GetCardLimitsResponse, cardId string, countryWiseInternationalLimitResp *cardControlPb.GetInternationalAtmLimitsResponse) *cxCardPb.GetChannelAndLimitSettingsResponse {
	resp := &cxCardPb.GetChannelAndLimitSettingsResponse{
		Status: rpcPb.StatusOk(),
	}
	cardDetails, ok := cardResp.GetCards()[cardId]
	if !ok || cardDetails == nil {
		return &cxCardPb.GetChannelAndLimitSettingsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch card details"),
		}
	}
	var controlDefs []*cxCardPb.ControlDefinition

	for _, val := range cardDetails.GetControls().GetDefs() {
		controlDefs = append(controlDefs, &cxCardPb.ControlDefinition{
			Action:          val.GetAction().String(),
			TransactionType: val.GetTransactionType().String(),
			LocationType:    val.GetLocationType().String(),
			Vendor:          val.GetVendor().String(),
		})
	}
	resp.ControlData = &cxCardPb.ControlData{
		Defs:      controlDefs,
		TxnStates: cardDetails.GetControls().GetTxnStates(),
		LocStates: cardDetails.GetControls().GetLocStates(),
	}
	var cardLimitDetails []*cxCardPb.CardLimitDetail
	for _, val := range limitResp.GetCardLimitData().GetCardLimitDetails() {
		cardLimitDetails = append(cardLimitDetails, &cxCardPb.CardLimitDetail{
			TxnType:              val.GetTxnType().String(),
			LocType:              val.GetLocType().String(),
			CurrentAllowedAmount: val.CurrentAllowedAmount,
			MaxAllowedAmount:     val.MaxAllowedAmount,
		})
	}
	resp.CardLimitData = &cxCardPb.CardLimitData{
		CardLimitDetails: cardLimitDetails,
	}

	resp.CountryWiseInternationalLimits = countryWiseInternationalLimitResp.GetInternationalAtmLimits()
	return resp
}

func (s *Service) GetCardDeliveryTrackingData(ctx context.Context, req *cxCardPb.GetCardDeliveryTrackingDataRequest) (*cxCardPb.GetCardDeliveryTrackingDataResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.GetCardDeliveryTrackingDataResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetCardId() == "" {
		cxLogger.Error(ctx, "mandatory params card id or issuer bank not passed")
		return &cxCardPb.GetCardDeliveryTrackingDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mandatory params card id or issuer bank not passed"),
		}, nil
	}
	resp, err := s.cpClient.CardDeliveryTracking(ctx, &cardProvisioningPb.CardDeliveryTrackingRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		CardId:  req.GetCardId(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching card tracking details", zap.Error(te))
		return &cxCardPb.GetCardDeliveryTrackingDataResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching tracking details"),
		}, nil
	}
	return convertToTrackingResponse(resp)
}

func convertToTrackingResponse(resp *cardProvisioningPb.CardDeliveryTrackingResponse) (*cxCardPb.GetCardDeliveryTrackingDataResponse, error) {
	return &cxCardPb.GetCardDeliveryTrackingDataResponse{
		Status:             rpcPb.StatusOk(),
		CardDispatchDate:   resp.GetCardDispatchDate(),
		CardGenerationDate: resp.GetCardGenerationDate(),
		Awb:                resp.GetAwb(),
		CardReturnedDate:   resp.GetCardReturnedDate(),
		CourierPartner:     resp.GetCourierPartner(),
		Remarks:            resp.GetRemarks(),
		State:              resp.GetState(),
	}, nil
}

func (s *Service) GetAllCardsForCustomer(ctx context.Context, req *cxCardPb.GetAllCardsForCustomerRequest) (*cxCardPb.GetAllCardsForCustomerResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.GetAllCardsForCustomerResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	resp, err := s.cpClient.FetchCards(ctx, &cardProvisioningPb.FetchCardsRequest{
		Actor: req.GetHeader().GetActor(),
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		cxLogger.Error(ctx, "unable to get all cards for actor", zap.String(logger.ACTOR_ID, req.GetHeader().GetActor().GetId()), zap.Error(err))
		return &cxCardPb.GetAllCardsForCustomerResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch cards")}, nil
	}
	var customerCardList []*cxCardPb.CardInfo
	for _, card := range resp.GetCards() {
		customerCardList = append(customerCardList, convertToCardInfoProto(card))
	}
	return &cxCardPb.GetAllCardsForCustomerResponse{Status: resp.GetStatus(), CustomerCardList: customerCardList}, nil
}

func convertToCardInfoProto(card *cardPb.Card) *cxCardPb.CardInfo {
	return &cxCardPb.CardInfo{
		Id:                 card.GetId(),
		State:              card.GetState().String(),
		Type:               card.GetType().String(),
		NetworkType:        card.GetNetworkType().String(),
		IssuerBank:         card.GetIssuerBank().String(),
		CardLastFourDigits: getLastFourDigits(card.GetBasicInfo().GetMaskedCardNumber()),
		CreatedAt:          card.GetCreatedAt(),
		UpdatedAt:          card.GetUpdatedAt(),
		IssuanceFee:        card.GetIssuanceFee(),
		CardForm:           card.GetForm().String(),
	}
}

func (s *Service) UpdateFreeCardReplacement(ctx context.Context, req *cxCardPb.UpdateFreeCardReplacementRequest) (*cxCardPb.UpdateFreeCardReplacementResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.UpdateFreeCardReplacementResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	resp, err := s.cpClient.UpdateFreeCardReplacement(ctx, &cardProvisioningPb.UpdateFreeCardReplacementRequest{
		ActorId:     req.GetHeader().GetActor().GetId(),
		CardSkuType: cardPb.CardSKUType_CLASSIC,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while calling card servcie to update free card replacement", zap.Error(err))
		return &cxCardPb.UpdateFreeCardReplacementResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while updating user free card replacement")}, nil
	}
	return &cxCardPb.UpdateFreeCardReplacementResponse{Status: rpcPb.StatusOk()}, nil
}

func (s *Service) GetCardDeliveryTrackingDataV2(ctx context.Context, req *cxCardPb.GetCardDeliveryTrackingDataV2Request) (*cxCardPb.GetCardDeliveryTrackingDataV2Response, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCardPb.GetCardDeliveryTrackingDataV2Response{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	if req.GetCardId() == "" {
		cxLogger.Error(ctx, "mandatory params card id or issuer bank not passed")
		return &cxCardPb.GetCardDeliveryTrackingDataV2Response{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mandatory params card id or issuer bank not passed"),
		}, nil
	}
	var (
		trackingResp       *cardProvisioningPb.GetCardShipmentTrackingDetailsResponse
		trackingErr        error
		trackingStatusResp *cardProvisioningPb.FetchDeliveryTrackingStatusResponse
		trackingStatusErr  error
	)

	g, gctx := errgroup.WithContext(ctx)
	g.Go(func() error {
		trackingResp, trackingErr = s.cpClient.GetCardShipmentTrackingDetails(gctx, &cardProvisioningPb.GetCardShipmentTrackingDetailsRequest{
			CardId: req.GetCardId(),
		})
		return nil
	})

	g.Go(func() error {
		trackingStatusResp, trackingStatusErr = s.cpClient.FetchDeliveryTrackingStatus(gctx, &cardProvisioningPb.FetchDeliveryTrackingStatusRequest{
			CardId: req.GetCardId(),
		})
		return nil
	})

	if err := g.Wait(); err != nil {
		cxLogger.Error(ctx, "error in goroutines", zap.Error(err))
		return &cxCardPb.GetCardDeliveryTrackingDataV2Response{
			Status: rpcPb.StatusInternalWithDebugMsg("error in goroutines"),
		}, nil
	}

	if te := epifigrpc.RPCError(trackingResp, trackingErr); te != nil {
		if trackingResp.GetStatus().IsRecordNotFound() {
			return &cxCardPb.GetCardDeliveryTrackingDataV2Response{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching card tracking details", zap.Error(te))
		return &cxCardPb.GetCardDeliveryTrackingDataV2Response{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching tracking details"),
		}, nil
	}
	if te := epifigrpc.RPCError(trackingStatusResp, trackingStatusErr); te != nil {
		if trackingResp.GetStatus().IsRecordNotFound() {
			return &cxCardPb.GetCardDeliveryTrackingDataV2Response{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching card tracking status", zap.Error(te))
		return &cxCardPb.GetCardDeliveryTrackingDataV2Response{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching tracking details"),
		}, nil
	}
	// return not found if awb is missing in response
	if trackingResp.GetTrackingDetails().GetAwb() == "" || trackingResp.GetTrackingDetails().GetAwb() == " " {
		return &cxCardPb.GetCardDeliveryTrackingDataV2Response{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	return convertToTrackingV2Response(trackingResp, trackingStatusResp)
}

func convertToTrackingV2Response(resp *cardProvisioningPb.GetCardShipmentTrackingDetailsResponse, statusResp *cardProvisioningPb.FetchDeliveryTrackingStatusResponse) (*cxCardPb.GetCardDeliveryTrackingDataV2Response, error) {
	return &cxCardPb.GetCardDeliveryTrackingDataV2Response{
		Status: rpcPb.StatusOk(),
		TrackingDetails: &cxCardPb.TrackingDetails{
			Awb:              resp.GetTrackingDetails().GetAwb(),
			CourierPartner:   resp.GetTrackingDetails().GetCarrier(),
			GenerationDate:   resp.GetTrackingDetails().GetCreatedAt(),
			IsCardActivated:  GetIsCardActivated(statusResp.GetDeliveryState()),
			DeliveryState:    resp.GetTrackingDetails().GetDeliveryState().String(),
			IsCardDispatched: getIsCardDispatched(resp.GetTrackingDetails().GetScans()),
			Scans:            resp.GetTrackingDetails().GetScans(),
		},
	}, nil
}

func getIsCardDispatched(scans []*cardProvisioningPb.Scan) commontypes.BooleanEnum {
	if len(scans) == 0 {
		return commontypes.BooleanEnum_FALSE
	}
	return commontypes.BooleanEnum_TRUE
}

func GetIsCardActivated(state cardProvisioningPb.CardDeliveryTrackingState) commontypes.BooleanEnum {
	switch state {
	case cardProvisioningPb.CardDeliveryTrackingState_RECEIVED_BY_USER:
		return commontypes.BooleanEnum_TRUE
	default:
		return commontypes.BooleanEnum_FALSE
	}
}
