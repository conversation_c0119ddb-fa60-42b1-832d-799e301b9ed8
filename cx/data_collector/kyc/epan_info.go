package kyc

import (
	"sort"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxKycPb "github.com/epifi/gamma/api/cx/data_collector/kyc"
	"github.com/epifi/gamma/api/pan/epan"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/pkg/vkyc"
)

const (
	epanNotAttempted     = "No EPAN attempt present"
	latestTableName      = "Latest attempt"
	pastAttemptTableName = "Past EPAN attempt"

	tableHeaderStateKey            = "state"
	tableHeaderStateLabel          = "State"
	tableHeaderSubStateKey         = "sub_state"
	tableHeaderSubStateLabel       = "SubState"
	tableHeaderDownloadedOnKey     = "downloaded_on"
	tableHeaderDownloadedOnLabel   = "Downloaded On"
	tableHeaderAttemptCreatedKey   = "attempt_created_on"
	tableHeaderAttemptCreatedLabel = "Attempt created on"
	tableHeaderValidTillKey        = "valid_till"
	tableHeaderValidTillLabel      = "Valid Till"
)

func buildEPanInfoResp(epanAttempts []*epan.EPANAttempt) *cxKycPb.GetCustomerEPANAttemptsResponse {
	var pastEPanAttemptTable *webui.Table
	if len(epanAttempts) == 0 {
		return &cxKycPb.GetCustomerEPANAttemptsResponse{
			Status:            rpcPb.StatusOk(),
			LatestAttemptInfo: epanNotAttempted,
		}
	}
	sort.Slice(epanAttempts, func(i, j int) bool {
		return epanAttempts[i].CreatedAt.AsTime().After(epanAttempts[j].CreatedAt.AsTime())
	})
	if len(epanAttempts) > 1 {
		pastEPanAttemptTable = buildPastEPANInfoTable(epanAttempts[1:])
	}
	return &cxKycPb.GetCustomerEPANAttemptsResponse{
		Status:            rpcPb.StatusOk(),
		LatestAttemptInfo: epanAttempts[0].GetState().String(),
		LatestEPanInfo:    buildLatestEPANInfoTable(epanAttempts[0]),
		PastEPanAttempts:  pastEPanAttemptTable,
	}
}

func buildLatestEPANInfoTable(epanAttempt *epan.EPANAttempt) *webui.Table {
	tableRow := []*webui.TableRow{
		createEPANRow(epanAttempt),
	}
	return &webui.Table{
		TableName:    latestTableName,
		TableHeaders: createEPANHeader(),
		TableRows:    tableRow,
	}
}

func buildPastEPANInfoTable(epanAttempts []*epan.EPANAttempt) *webui.Table {
	var tableRows []*webui.TableRow
	for _, epanAttempt := range epanAttempts {
		tableRows = append(tableRows, createEPANRow(epanAttempt))
	}

	return &webui.Table{
		TableName:    pastAttemptTableName,
		TableHeaders: createEPANHeader(),
		TableRows:    tableRows,
	}
}

func createEPANHeader() []*webui.TableHeader {
	var tableHeaders []*webui.TableHeader

	keyToLabel := map[string]string{
		tableHeaderStateKey:          tableHeaderStateLabel,
		tableHeaderSubStateKey:       tableHeaderSubStateLabel,
		tableHeaderDownloadedOnKey:   tableHeaderDownloadedOnLabel,
		tableHeaderAttemptCreatedKey: tableHeaderAttemptCreatedLabel,
		tableHeaderValidTillKey:      tableHeaderValidTillLabel,
	}

	keysOrder := []string{
		tableHeaderStateKey,
		tableHeaderSubStateKey,
		tableHeaderDownloadedOnKey,
		tableHeaderAttemptCreatedKey,
		tableHeaderValidTillKey,
	}

	for _, keys := range keysOrder {
		tableHeaders = append(tableHeaders, &webui.TableHeader{
			HeaderKey: keys,
			Label:     keyToLabel[keys],
			IsVisible: true,
		})
	}

	return tableHeaders
}

func createEPANRow(epanAttempt *epan.EPANAttempt) *webui.TableRow {
	var epanAttemptValidTill string
	var downloadedOn string
	var createdAt string
	if epanAttempt.GetCompletedAt().IsValid() {
		epanAttemptValidTill = epanAttempt.GetCompletedAt().AsTime().Add(vkyc.EPANExpiredDuration).String()
	}
	if epanAttempt.GetCompletedAt().IsValid() {
		downloadedOn = epanAttempt.GetCompletedAt().AsTime().String()
	}
	if epanAttempt.GetCreatedAt().IsValid() {
		createdAt = epanAttempt.GetCreatedAt().AsTime().String()
	}
	return &webui.TableRow{
		HeaderKeyToCellValueMap: map[string]string{
			tableHeaderStateKey:          epanAttempt.GetState().String(),
			tableHeaderSubStateKey:       epanAttempt.GetSubState().String(),
			tableHeaderDownloadedOnKey:   downloadedOn,
			tableHeaderAttemptCreatedKey: createdAt,
			tableHeaderValidTillKey:      epanAttemptValidTill,
		},
	}
}
