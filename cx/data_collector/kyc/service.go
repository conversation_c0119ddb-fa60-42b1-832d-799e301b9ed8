package kyc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth/liveness"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	cxKycPb "github.com/epifi/gamma/api/cx/data_collector/kyc"
	"github.com/epifi/gamma/api/cx/data_collector/onboarding"
	"github.com/epifi/gamma/api/kyc"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxLogger "github.com/epifi/gamma/cx/logger"
	kyc2 "github.com/epifi/gamma/kyc"
	"github.com/epifi/gamma/pkg/vkyc"
)

type Service struct {
	kycClient      kycPb.KycClient
	livenessClient liveness.LivenessClient
	authEngine     auth_engine.IAuthEngine
	vkycClient     vkycPb.VKYCClient
	kycConf        *config.KYCConfig
	actorClient    actor.ActorClient
	usersClient    user.UsersClient
	onbClient      onbPb.OnboardingClient
	bcClient       bankCustomerPb.BankCustomerServiceClient
	panClient      pan.PanClient
	compClient     compliancePb.ComplianceClient
}

func NewService(kycClient kycPb.KycClient, authEngine auth_engine.IAuthEngine, vkycClient vkycPb.VKYCClient,
	kycConf *config.KYCConfig, livenessClient liveness.LivenessClient,
	actorClient actor.ActorClient, usersClient user.UsersClient,
	onbClient onbPb.OnboardingClient, bcClient bankCustomerPb.BankCustomerServiceClient, panClient pan.PanClient,
	compClient compliancePb.ComplianceClient) *Service {
	return &Service{
		kycClient:      kycClient,
		authEngine:     authEngine,
		vkycClient:     vkycClient,
		kycConf:        kycConf,
		livenessClient: livenessClient,
		actorClient:    actorClient,
		usersClient:    usersClient,
		onbClient:      onbClient,
		bcClient:       bcClient,
		panClient:      panClient,
		compClient:     compClient,
	}
}

var _ cxKycPb.CustomerKYCServer = &Service{}

type RiskOpsUserInfo struct {
	ActorId string

	// Details for manual review of Liveness & FM fails
	LivenessFmStatus kycPb.LivenessStatus
	LivenessStatus   kycPb.StatusLiveness
	LivenessScore    float32
	LivenessParams   *kycPb.LivenessParams
	FMParams         []*kycPb.FaceMatchParams
	IsFMPassed       bool

	// Details for manual review of name check fails
	Name    string
	PanName string
	KycName string

	// This temporary to unblock risk-ops
	OnboardingStageDetails *onbPb.OnboardingDetails

	// Details regarding potential spoof or fraud users
	AccessRevokeState user.AccessRevokeState
	PhoneNumber       *commontypes.PhoneNumber
}

const (
	kycNameMismatch = "There seems to be a name mismatch between the details you provided while creating an account on Fi and your Aadhaar card"
)

func (s *Service) GetCustomerKYCData(ctx context.Context, req *cxKycPb.GetCustomerKYCDataRequest) (*cxKycPb.GetCustomerKYCDataResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxKycPb.GetCustomerKYCDataResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	historyResp, err := s.kycClient.GetKYCHistory(ctx, &kycPb.GetKYCHistoryRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(historyResp, err); te != nil {
		cxLogger.Error(ctx, "unable to get kyc history data for customer", zap.Error(te))

		if historyResp.GetStatus().IsRecordNotFound() {
			return &cxKycPb.GetCustomerKYCDataResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("kyc record not found for customer"),
			}, nil
		}

		return &cxKycPb.GetCustomerKYCDataResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to get kyc data for customer"),
		}, nil
	}
	historyResp = ensureFailReasonForLSO(ctx, historyResp)

	kycStatusResp, kycStatusErr := s.kycClient.CheckKYCStatus(ctx, &kycPb.CheckKYCStatusRequest{
		ActorId: actorId,
	})
	if kycStatusErr = epifigrpc.RPCError(kycStatusResp, kycStatusErr); kycStatusErr != nil {
		cxLogger.Error(ctx, "unable to check kyc status data for customer", zap.Error(kycStatusErr))
		return &cxKycPb.GetCustomerKYCDataResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to get kyc data for customer"),
		}, nil
	}

	bankCustomerInfo, errResp := s.bcClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(bankCustomerInfo, errResp); err != nil && !bankCustomerInfo.GetStatus().IsRecordNotFound() {
		cxLogger.Info(ctx, "failed to get bank customer info for federal", zap.Error(err))
	}
	compResp, compErr := s.compClient.GetPeriodicKYCDetail(ctx, &compliancePb.GetPeriodicKYCDetailRequest{
		ActorId:     actorId,
		FetchLatest: true,
	})
	if rpcErr := epifigrpc.RPCError(compResp, compErr); rpcErr != nil && !compResp.GetStatus().IsRecordNotFound() {
		cxLogger.Error(ctx, "fall back to db as failed to get latest kyc compliance details", zap.Error(rpcErr))
		pKycResp, pKycErr := s.compClient.GetPeriodicKYCDetail(ctx, &compliancePb.GetPeriodicKYCDetailRequest{
			ActorId:     actorId,
			FetchLatest: false,
		})
		if pKycErr = epifigrpc.RPCError(pKycResp, pKycErr); pKycErr != nil {
			cxLogger.Error(ctx, "failed to get kyc compliance details", zap.Error(pKycErr))
		}
	}
	return s.convertToProto(ctx, historyResp, req, kycStatusResp, bankCustomerInfo.GetBankCustomer(), compResp.GetPeriodicKYCDetail()), nil
}

func (s *Service) convertToProto(ctx context.Context, kycHistoryResp *kycPb.GetKYCHistoryResponse, req *cxKycPb.GetCustomerKYCDataRequest, kycStatusResp *kycPb.CheckKYCStatusResponse,
	bankCustResp *bankCustomerPb.BankCustomer, periodicKYCDetail *compliancePb.PeriodicKYCDetail) *cxKycPb.GetCustomerKYCDataResponse {
	var kycAttemptList []*cxKycPb.KycAttemptData
	for _, attempt := range kycHistoryResp.GetKycAttempts() {
		var faceMatchScoreList []*onboarding.FaceMatchScoreResponse
		for _, fmParams := range attempt.FaceMatchParams {
			faceMatchScoreList = append(faceMatchScoreList, &onboarding.FaceMatchScoreResponse{
				AttemptId:             fmParams.GetRequestId(),
				FaceMatchStatus:       onboarding.FaceMatchStatus(fmParams.GetStatus()),
				FaceMatchStatusString: fmParams.GetStatus().String(),
				FaceMatchScore:        fmParams.GetFaceMatchScore(),
				FmScoreThreshold:      fmParams.GetFmThreshold(),
			})
		}
		kycAttemptList = append(kycAttemptList, &cxKycPb.KycAttemptData{
			ActorId:                attempt.GetActorId(),
			KycAttemptId:           attempt.KycAttemptId,
			Source:                 attempt.GetSource(),
			KycType:                attempt.GetKycType().String(),
			State:                  attempt.GetState().String(),
			UpdatedAt:              attempt.GetUpdatedAt(),
			LivenessScore:          attempt.GetLivenessParams().GetLivenessScore(),
			OtpScore:               attempt.GetLivenessParams().GetOtpScore(),
			FaceMatchScores:        faceMatchScoreList,
			FailureReason:          attempt.FailureReason,
			LivenessScoreThreshold: attempt.GetLivenessParams().GetLivenessThreshold(),
			OtpScoreThreshold:      attempt.GetLivenessParams().GetOtpThreshold(),
		})
	}
	ret := &cxKycPb.GetCustomerKYCDataResponse{
		Status:         rpcPb.StatusOk(),
		KycAttemptList: kycAttemptList,
		KycType:        kycStatusResp.GetKycType().String(),
		KycStatus:      getKYCStatus(ctx, kycStatusResp, bankCustResp),
	}
	reKycDetails := getComplianceDetails(periodicKYCDetail)
	ret.ReKycStatus = reKycDetails[0].GetValue()[0]
	ret.LabelValues = reKycDetails
	// get kyc level from user info
	bankCustomerInfo, errResp := s.bcClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_UserId{UserId: req.GetHeader().GetUser().GetId()},
	})
	if err := epifigrpc.RPCError(bankCustomerInfo, errResp); err != nil {
		cxLogger.Info(ctx, "failed to get bank customer info for federal", zap.Error(err))
		ret.KycLevel = kyc.KYCLevel_UNSPECIFIED.String()
	} else {
		ret.KycLevel = bankCustomerInfo.GetBankCustomer().GetDedupeInfo().GetKycLevel().String()
	}
	ret.ShouldAllowRetryAction = s.shouldAllowLivenessRetry(kycStatusResp)
	return ret
}

func getComplianceDetails(periodicKYCDetail *compliancePb.PeriodicKYCDetail) []*webui.LabelValue {
	var reKYCDetails []*webui.LabelValue

	const (
		label = "Re-KYC Status"
		// completeReKYC = "<br><i>If user’s KYC details have not changed</i><br><br>1. Send an SMS “KYC Y” to ********** from your registered mobile number.<br>2. Once the SMS has been sent successfully, it takes 3 hours for your KYC status to become compliant.<br>3. User can also check the federal bank mail with subject line “Periodic Know Your Customer (KYC) Updation” for more information.<br><br><i>If user’s KYC details such as address etc have changed</i><br><br>1. Please check the federal bank mail with subject line “Periodic Know Your Customer (KYC) Updation” for more information.<br>2. Document size should be less than 750kb.<br>3. You will need to upload a self-attested copy of any one of the ‘Officially Valid Documents’ (Passport, Driving License, Voter Id, Aadhaar, NREGA Job Card & Certificate from National Population Register) containing name and address of the account holder(s) on this portal <a href='https://accountopen.federalbank.co.in/CustomerPortal/index' target='_blank'>https://accountopen.federalbank.co.in/CustomerPortal/index</a> <br>4. Once documents are successfully uploaded, it takes 2 business days for your account to become KYC Compliant again.<br><br><i>The user’s valid KYC documents are not present with federal bank</i><br><br>1. You will need to upload a self-attested copy of any one of the ‘Officially Valid Documents’ (Passport, Driving License, Voter Id, Aadhaar, NREGA Job Card & Certificate from National Population Register) containing name and address of the account holder(s) on this portal <a href='https://accountopen.federalbank.co.in/CustomerPortal/index' target='_blank'>https://accountopen.federalbank.co.in/CustomerPortal/index</a> <br>2. Document size should be less than 750kb.<br>3. Once documents are successfully uploaded, it takes 2 business days for your account to become KYC Compliant again.<br>4. User can also check the federal bank mail with subject line “Periodic Know Your Customer (KYC) Updation” for more information."
		completeReKYC = "<br><b>Via SMS:</b><br> If your <b>name, date of birth, or address</b> have not changed, you can complete your Re-KYC by sending <b>“KYC Y”</b> to <b>**********</b> from your registered mobile number. Your KYC status will be updated within <b>48 hours</b>. If it doesn't get updated within that time, you can proceed with the Aadhaar-based KYC method.<br><br><b>Via Aadhaar Authentication:</b><br>If your details have not changed, you can update your Re-KYC via Aadhaar. Simply open the Fi app, tap on the KYC banner, select “KYC Update Via Aadhaar”, enter your Aadhaar number, and verify with the OTP sent to your registered mobile. The process will be completed within 3 business days.<br><br>If there are any changes in your profile details, such as <b>Name, Date of Birth, or Address</b>, you need to visit the nearest <b>Federal Bank branch</b> to complete the Re-KYC process. As per the circular <b>“FINT/CC PL SB/FAQ/2024-25”</b> issued by the Federal Fintech Team on <b>22-08-2024</b>, you are advised to visit the branch with the necessary documents for updating your KYC details. While visiting, please carry your <b>Aadhaar, PAN, and a passport-sized photograph</b>. The bank will assist you in completing the Re-KYC update with the new information during the visit.<br><br><b>Important Notes:</b>If your account is frozen, it will be unfrozen within 7-8 working days once KYC is updated.<b>Ensure you're using your registered mobile number for SMS or Aadhaar authentication."
		kycCompiledAt = "Last Re-KYC Completed Date"
		kycDueAt      = "Re-KYC Due Date"
	)
	switch {
	case periodicKYCDetail == nil:
		reKYCDetails = append(reKYCDetails, &webui.LabelValue{
			Label: label,
			Value: []string{"NA"},
		})
		return reKYCDetails
	case periodicKYCDetail.GetKYCComplianceStatus() == compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE:
		if datetime.IsPastDateFromToday(periodicKYCDetail.GetKYCDueAt().AsTime()) {
			reKYCDetails = append(reKYCDetails,
				&webui.LabelValue{
					Label: label,
					Value: []string{"KYC Overdue"},
				})
		} else {
			daysToDue := math.Ceil(periodicKYCDetail.GetKYCDueAt().AsTime().Sub(time.Now()).Hours() / 24)
			reKYCDetails = append(reKYCDetails,
				&webui.LabelValue{
					Label: label,
					Value: []string{fmt.Sprintf("KYC Due in %v days", daysToDue)},
				})
		}
		reKYCDetails = append(reKYCDetails,
			&webui.LabelValue{
				Label: kycCompiledAt,
				Value: []string{getDateInString(periodicKYCDetail.GetKYCCompliedAt())},
			},
			&webui.LabelValue{
				Label: kycDueAt,
				Value: []string{getDateInString(periodicKYCDetail.GetKYCDueAt())},
			},
			&webui.LabelValue{
				Label: "You can complete your Re-KYC quickly and easily by following the steps below:",
				Value: []string{completeReKYC},
			})
		return reKYCDetails

	case periodicKYCDetail.GetKYCComplianceStatus() == compliancePb.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_COMPLIED:
		reKYCDetails = append(reKYCDetails,
			&webui.LabelValue{
				Label: label,
				Value: []string{"KYC Compliant"},
			},
			&webui.LabelValue{
				Label: kycCompiledAt,
				Value: []string{getDateInString(periodicKYCDetail.GetKYCCompliedAt())},
			},
			&webui.LabelValue{
				Label: kycDueAt,
				Value: []string{getDateInString(periodicKYCDetail.GetKYCDueAt())},
			},
			&webui.LabelValue{
				Label: "Advice to be shared with the user",
				Value: []string{"The user's account is KYC compliant. They need not do anything as of now."},
			})
		return reKYCDetails
	default:
		reKYCDetails = append(reKYCDetails, &webui.LabelValue{
			Label: label,
			Value: []string{"NA"},
		})
		return reKYCDetails
	}
}

func getKYCStatus(ctx context.Context, kycStatusResp *kycPb.CheckKYCStatusResponse, bankCustResp *bankCustomerPb.BankCustomer) string {
	if bankCustResp.GetKycInfo().GetKycLevel() == kycPb.KYCLevel_MIN_KYC && lo.Contains(vkyc.DowngradedUsersList, bankCustResp.GetVendorCustomerId()) {
		cxLogger.Info(ctx, "user is downgraded to min kyc")
		return fmt.Sprintf("%v%v", kycStatusResp.GetKycStatus().String(), " (Downgraded to min kyc since onboarded as ckyc)")
	}
	return kycStatusResp.GetKycStatus().String()
}

func (s *Service) GetCustomerVKYCData(ctx context.Context, req *cxKycPb.GetCustomerVKYCDataRequest) (*cxKycPb.GetCustomerVKYCDataResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxKycPb.GetCustomerVKYCDataResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	resp, err := s.vkycClient.GetVKYCSummary(ctx, &vkycPb.GetVKYCSummaryRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Info(ctx, "error while fetching vkyc summary data for user", zap.Error(te))
		if resp.GetStatus().IsRecordNotFound() {
			return &cxKycPb.GetCustomerVKYCDataResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &cxKycPb.GetCustomerVKYCDataResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching vkyc data"),
		}, nil
	}
	kycResp, err := s.kycClient.GetKYCRecord(ctx, &kycPb.GetKYCRecordRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(kycResp, err); te != nil &&
		!kycResp.GetStatus().IsRecordNotFound() &&
		kycResp.GetStatus().GetCode() == uint32(kyc.GetKYCRecordResponse_NAME_MISMATCH) {
		cxLogger.Info(ctx, "error while fetching kyc record for user", zap.Error(te))
		return &cxKycPb.GetCustomerVKYCDataResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	troubleShootingResp, err := s.vkycClient.GetTroubleshootingDetails(ctx, &vkycPb.GetTroubleshootingDetailsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(troubleShootingResp, err); te != nil {
		cxLogger.Info(ctx, "error while fetching vkyc summary troubleshooting details for user", zap.Error(te))
	}
	if len(troubleShootingResp.GetResp()) == 0 {
		cxLogger.Info(ctx, "vkyc troubleshooting details not found")
	}
	return s.buildVkycSummaryResponse(resp, kycResp, troubleShootingResp)
}

func (s *Service) buildVkycSummaryResponse(vkycResp *vkycPb.GetVKYCSummaryResponse, kycResp *kycPb.GetKYCRecordResponse, tsResp *vkycPb.GetTroubleshootingDetailsResponse) (*cxKycPb.GetCustomerVKYCDataResponse, error) {
	cxResp := &cxKycPb.GetCustomerVKYCDataResponse{
		Status:      rpcPb.StatusOk(),
		VkycSummary: s.parseSummary(vkycResp.GetVkycRecord().GetVkycSummary(), kycResp),
	}

	for idx := 0; idx < len(tsResp.GetResp()); idx++ {
		tsdetail := tsResp.GetResp()[idx]
		cxResp.VkycTroubleshootings = append(cxResp.VkycTroubleshootings, &webui.LabelValue{
			Label: tsdetail.GetKey(),
			Value: []string{tsdetail.GetValue()},
		})
	}

	// populate attempt data in resp
	for _, attempt := range vkycResp.GetVkycRecord().GetVkycAttemptDataList() {
		cxResp.VkycAttemptDataList = append(cxResp.VkycAttemptDataList, &cxKycPb.VKYCAttemptData{
			VkycAttempt:               s.parseAttempt(attempt.GetVkycAttempt()),
			VkycKarzaCallInfoDataList: s.parseCallInfoList(attempt.GetVkycKarzaCallInfoDataList()),
		})
	}
	// populate customer infos in resp
	for _, customerInfo := range vkycResp.GetVkycRecord().GetVkycKarzaCustomerInfos() {
		cxResp.VkycCustomerInfos = append(cxResp.VkycCustomerInfos, &cxKycPb.VKYCCustomerInfo{
			Weblink:   customerInfo.GetTransactionMetadata().GetWeblink(),
			KycDate:   customerInfo.GetTransactionMetadata().GetKycDate(),
			CreatedAt: customerInfo.GetCreatedAt(),
			UpdatedAt: customerInfo.GetUpdatedAt(),
		})
	}
	return cxResp, nil
}

func (s *Service) parseCallInfoList(list []*vkycPb.VKYCRecord_VKYCAttemptData_VKYCKarzaCallInfoData) []*cxKycPb.VKYCKarzaCallInfoData {
	var callInfoList []*cxKycPb.VKYCKarzaCallInfoData
	for _, callInfo := range list {
		callInfoList = append(callInfoList, &cxKycPb.VKYCKarzaCallInfoData{
			VkycKarzaCallInfo: s.parseCallInfo(callInfo),
			VkycCallSchedule:  s.parseCallSchedule(callInfo),
		})
	}
	return callInfoList
}

func (s *Service) parseCallSchedule(info *vkycPb.VKYCRecord_VKYCAttemptData_VKYCKarzaCallInfoData) *cxKycPb.VKYCCallSchedule {
	return &cxKycPb.VKYCCallSchedule{
		Id:        info.GetVkycCallSchedule().GetId(),
		StartTime: info.GetVkycCallSchedule().GetStartTime(),
		EndTime:   info.GetVkycCallSchedule().GetEndTime(),
		AgentId:   info.GetVkycCallSchedule().GetAgentId(),
		Vendor:    info.GetVkycCallSchedule().GetVendor().String(),
		Status:    info.GetVkycCallSchedule().GetStatus().String(),
		RefId:     info.GetVkycCallSchedule().GetRefId(),
		SubStatus: info.GetVkycCallSchedule().GetSubStatus().String(),
		CreatedAt: info.GetVkycCallSchedule().GetCreatedAt(),
		UpdatedAt: info.GetVkycCallSchedule().GetUpdatedAt(),
	}
}

func (s *Service) parseCallInfo(info *vkycPb.VKYCRecord_VKYCAttemptData_VKYCKarzaCallInfoData) *cxKycPb.VKYCKarzaCallInfo {
	return &cxKycPb.VKYCKarzaCallInfo{
		Id:             info.GetVkycKarzaCallInfo().GetId(),
		VkycAttemptId:  info.GetVkycKarzaCallInfo().GetVkycAttemptId(),
		CallType:       info.GetVkycKarzaCallInfo().GetCallType().String(),
		Status:         info.GetVkycKarzaCallInfo().GetStatus().String(),
		SubStatus:      info.GetVkycKarzaCallInfo().GetSubStatus().String(),
		CreatedAt:      info.GetVkycKarzaCallInfo().GetCreatedAt(),
		UpdatedAt:      info.GetVkycKarzaCallInfo().GetUpdatedAt(),
		Interpretation: s.getCallStatusInterpretation(info.GetVkycKarzaCallInfo().GetStatus(), info.GetVkycKarzaCallInfo().GetSubStatus()),
	}
}

func (s *Service) getCallStatusInterpretation(status vkycPb.VKYCKarzaCallInfoStatus, subStatus vkycPb.VKYCKarzaCallInfoSubStatus) string {
	val, ok := s.kycConf.CallInfoInterpretationMap[status.String()][subStatus.String()]
	if ok {
		return val
	}
	cxLogger.InfoNoCtx("interpretation mapping not found for", zap.String("status", status.String()),
		zap.String("sub_status", subStatus.String()))
	return ""
}

func (s *Service) parseAttempt(attempt *vkycPb.VKYCAttempt) *cxKycPb.VKYCAttempt {
	return &cxKycPb.VKYCAttempt{
		Id:            attempt.GetId(),
		AttemptSource: attempt.GetAttemptSource().String(),
		Vendor:        attempt.GetVendor().String(),
		Status:        attempt.GetStatus().String(),
		SubStatus:     attempt.GetSubStatus().String(),
		CreatedAt:     attempt.GetCreatedAt(),
		UpdatedAt:     attempt.GetUpdatedAt(),
		DeletedAt:     attempt.GetDeletedAt(),
	}
}

func (s *Service) parseSummary(summary *vkycPb.VKYCSummary, kycResp *kycPb.GetKYCRecordResponse) *cxKycPb.VKYCSummary {
	return &cxKycPb.VKYCSummary{
		Id:             summary.GetId(),
		Vendor:         summary.GetVendor().String(),
		Status:         summary.GetStatus().String(),
		SubStatus:      summary.GetSubStatus().String(),
		CreatedAt:      summary.GetCreatedAt(),
		UpdatedAt:      summary.GetUpdatedAt(),
		Interpretation: s.getSummaryInterpretation(summary.GetStatus(), summary.GetSubStatus(), kycResp),
	}
}

func (s *Service) getSummaryInterpretation(status vkycPb.VKYCSummaryStatus, subStatus vkycPb.VKYCSummarySubStatus, kycResp *kycPb.GetKYCRecordResponse) string {
	if (status == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNSPECIFIED ||
		status == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_RE_REGISTER ||
		status == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNREGISTERED) &&
		kycResp.GetStatus().GetCode() == uint32(kyc.GetKYCRecordResponse_NAME_MISMATCH) {
		return kycNameMismatch
	}
	val, ok := s.kycConf.SummaryInterpretationMap[status.String()][subStatus.String()]
	if ok {
		return val
	}
	cxLogger.InfoNoCtx("interpretation mapping not found for", zap.String("status", status.String()),
		zap.String("sub_status", subStatus.String()))
	return ""
}

func (s *Service) AllowLivenessRetry(ctx context.Context, req *cxKycPb.AllowLivenessRetryRequest) (*cxKycPb.AllowLivenessRetryResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxKycPb.AllowLivenessRetryResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	// first fetch kyc status data to check if liveness retry should be allowed
	kycStatusResp, kycStatusErr := s.kycClient.CheckKYCStatus(ctx, &kycPb.CheckKYCStatusRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if kycStatusErr = epifigrpc.RPCError(kycStatusResp, kycStatusErr); kycStatusErr != nil {
		cxLogger.Error(ctx, "unable to check kyc status data for customer", zap.Error(kycStatusErr))
		return &cxKycPb.AllowLivenessRetryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to get kyc data for customer"),
		}, nil
	}

	if !s.shouldAllowLivenessRetry(kycStatusResp) {
		cxLogger.Info(ctx, "received allow liveness retry call but liveness retry condition is not satisfied")
		return &cxKycPb.AllowLivenessRetryResponse{
			Status: rpcPb.StatusFailedPreconditionWithDebugMsg("liveness retry condition is not satisfied for user"),
		}, nil
	}

	resp, err := s.kycClient.AllowLivenessRetry(ctx, &kycPb.AllowLivenessRetryRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		cxLogger.Error(ctx, "error while calling kyc service to allow liveness retry for user", zap.Error(err))
		return &cxKycPb.AllowLivenessRetryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while calling liveness service"),
		}, nil
	}
	return &cxKycPb.AllowLivenessRetryResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// this method checks the kyc history and return if allow Retry liveness action should be allowed on sherlock or not
// will return true if liveness state is failed and attempt count is greater than 3
func (s *Service) shouldAllowLivenessRetry(resp *kycPb.CheckKYCStatusResponse) bool {
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=28105
	return false
	// return resp.GetLivenessStatus() == kycPb.LivenessStatus_LIVENESS_STATUS_FAILED
}

// nolint:funlen
func (s *Service) GetLivenessSummary(ctx context.Context, req *cxKycPb.GetLivenessSummaryRequest) (*cxKycPb.GetLivenessSummaryResponse, error) {
	// TODO(shubham): call all APIs concurrently
	userInfo := &RiskOpsUserInfo{}
	livenessAttempts, err := s.livenessClient.GetLivenessAttempts(ctx, &liveness.GetLivenessAttemptsRequest{
		ActorId: req.GetActorId(),
	})
	if err != nil {
		cxLogger.Error(ctx, "error in getting livenessAttempt", zap.Error(err))
		return &cxKycPb.GetLivenessSummaryResponse{Status: rpcPb.StatusInternal()}, nil
	}
	var filteredLivenessAttempts []*liveness.LivenessAttempt
	for _, lAttempt := range livenessAttempts.GetLivenessAttempts() {
		if lAttempt.GetStatus() != liveness.LivenessStatus_LIVENESS_OTP_RECEIVED {
			lAttempt.VendorRequestId = ""
			lAttempt.AttemptId = ""
			lAttempt.VideoLocation = ""
			if lAttempt.GetMetadata().GetDeviceSensorData() != nil {
				lAttempt.Metadata.DeviceSensorData = nil
			}
			filteredLivenessAttempts = append(filteredLivenessAttempts, lAttempt)
		}
	}
	var marshalledLivenessAttemptRes, marshalledKycSummaryRes, marshalledFaceMatchAttemptRes []byte
	marshalledLivenessAttemptRes, err = json.Marshal(filteredLivenessAttempts)
	if err != nil {
		cxLogger.Error(ctx, "cannot marshal filteredLivenessAttempts struct to json", zap.Error(err))
		return &cxKycPb.GetLivenessSummaryResponse{Status: rpcPb.StatusInternal()}, nil
	}

	facematchAttempts, err := s.livenessClient.GetFaceMatchAttempts(ctx, &liveness.GetFaceMatchAttemptsRequest{
		ActorId: req.GetActorId(),
	})
	if err != nil {
		cxLogger.Error(ctx, "error in getting facematchAttempt", zap.Error(err))
		return &cxKycPb.GetLivenessSummaryResponse{Status: rpcPb.StatusInternal()}, nil
	}
	marshalledFaceMatchAttemptRes, err = json.Marshal(facematchAttempts)
	if err != nil {
		cxLogger.Error(ctx, "cannot marshal facematch struct to json", zap.Error(err))
		return &cxKycPb.GetLivenessSummaryResponse{Status: rpcPb.StatusInternal()}, nil
	}

	actorResp, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
		Id: req.GetActorId(),
	})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		cxLogger.Error(ctx, "error getting actor details by actor id", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(te))
		if actorResp.GetStatus() == rpcPb.StatusRecordNotFound() {
			return &cxKycPb.GetLivenessSummaryResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("actor details not found"),
			}, nil
		}
		return &cxKycPb.GetLivenessSummaryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error fetching actor details"),
		}, nil
	}
	userResp, err := s.usersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: actorResp.GetActor().GetEntityId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		cxLogger.Error(ctx, "error getting user details by entity id", zap.String(logger.USER_ID, actorResp.GetActor().GetEntityId()), zap.Error(te))
		if userResp.GetStatus() == rpcPb.StatusRecordNotFound() {
			return &cxKycPb.GetLivenessSummaryResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("user details not found for actor"),
			}, nil
		}
		return &cxKycPb.GetLivenessSummaryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting user details by entity id"),
		}, nil
	}

	kycHistory, err := s.kycClient.GetKYCHistory(ctx, &kycPb.GetKYCHistoryRequest{
		ActorId: req.GetActorId(),
	})
	if err = epifigrpc.RPCError(kycHistory, err); err != nil {
		cxLogger.Error(ctx, "error in getting kyc history", zap.Error(err))
		if kycHistory.GetStatus().IsRecordNotFound() {
			return &cxKycPb.GetLivenessSummaryResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("kyc history not found for actor"),
			}, nil
		}
		return &cxKycPb.GetLivenessSummaryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error fetching kyc history"),
		}, nil
	}
	for _, attempt := range kycHistory.GetKycAttempts() {
		if attempt.GetKycAttemptId() == kycHistory.GetKycSummary().GetKycAttemptId() {
			if attempt.GetLivenessParams() == nil {
				attempt.LivenessParams = &kycPb.LivenessParams{}
			}
			attempt.LivenessParams.VideoLocation = ""
			userInfo = &RiskOpsUserInfo{
				ActorId:          req.GetActorId(),
				LivenessFmStatus: kycHistory.GetKycSummary().GetLivenessStatus(),
				LivenessStatus:   attempt.GetLivenessParams().GetStatus(),
				LivenessScore:    attempt.GetLivenessParams().GetLivenessScore(),
				LivenessParams:   attempt.GetLivenessParams(),
				FMParams:         attempt.GetFaceMatchParams(),
				IsFMPassed:       getSucceededFM(attempt.GetFaceMatchParams()),
				PanName:          userResp.GetUser().GetProfile().GetPanName().ToString(),
				KycName:          userResp.GetUser().GetProfile().GetKycName().ToString(),
			}
		}
	}

	// populate blocked users status
	userInfo.AccessRevokeState = userResp.GetUser().GetAccessRevokeState()
	if userInfo.AccessRevokeState != user.AccessRevokeState_ACCESS_REVOKE_STATE_UNSPECIFIED {
		userInfo.PhoneNumber = userResp.GetUser().GetProfile().GetPhoneNumber()
	}

	// fetch onboarding details for an actor
	resp, err := s.onbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId: req.GetActorId(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch onboarding stage details", zap.Error(te))
		if resp.GetStatus() == rpcPb.StatusRecordNotFound() {
			return &cxKycPb.GetLivenessSummaryResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("details not found for actor"),
			}, nil
		}
		return &cxKycPb.GetLivenessSummaryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch onboarding stage details"),
		}, nil
	}
	userInfo.OnboardingStageDetails = resp.GetDetails()

	marshalledKycSummaryRes, err = json.Marshal(userInfo)
	if err != nil {
		cxLogger.Error(ctx, "cannot marshal userInfo struct to json", zap.Error(err))
		return &cxKycPb.GetLivenessSummaryResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxKycPb.GetLivenessSummaryResponse{
		Status:            rpcPb.StatusOk(),
		KycSummary:        string(marshalledKycSummaryRes),
		LivenessAttempts:  string(marshalledLivenessAttemptRes),
		FacematchAttempts: string(marshalledFaceMatchAttemptRes),
	}, nil

}

func (s *Service) MarkLivenessPassed(ctx context.Context, req *cxKycPb.MarkLivenessPassedRequest) (*cxKycPb.MarkLivenessPassedResponse, error) {
	var (
		actorId, livURL string
		livenessStatus  kyc.StatusLiveness
		fmStatus        kyc.StatusFaceMatch
	)
	switch req.GetVerify() {
	case cxKycPb.Verify_VERIFY_PASS_LIVENESS:
		cxLogger.Info(ctx, "will pass liveness")
		livenessStatus = kyc.StatusLiveness_STATUS_LIVENESS_MANUALLY_PASSED
	case cxKycPb.Verify_VERIFY_PASS_FACEMATCH:
		cxLogger.Info(ctx, "will pass fm")
		fmStatus = kyc.StatusFaceMatch_STATUS_FACE_MATCH_MANUALLY_PASSED
	default:
		cxLogger.Error(ctx, fmt.Sprintf("invalid option to pass liveness or fm: %v", req.GetVerify()))
		return &cxKycPb.MarkLivenessPassedResponse{Status: rpcPb.StatusInternal()}, nil
	}
	actorId = req.GetActorId()
	livURL = req.GetS3Url()
	livResp, err := s.kycClient.UpdateLivenessStatus(ctx, &kyc.UpdateLivenessStatusRequest{
		ActorId:         actorId,
		LivenessStatus:  livenessStatus,
		FaceMatchStatus: fmStatus,
		LivenessS3Url:   livURL,
	})
	if te := epifigrpc.RPCError(livResp, err); te != nil {
		cxLogger.Error(ctx, "error in UpdateLivenessStatus for MarkLiveNessPassed", zap.Error(te))
		return &cxKycPb.MarkLivenessPassedResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxKycPb.MarkLivenessPassedResponse{Status: rpcPb.StatusOk()}, nil
}

func (s *Service) GetCustomerEPANAttempts(ctx context.Context, req *cxKycPb.GetCustomerEPANAttemptsRequest) (*cxKycPb.GetCustomerEPANAttemptsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxKycPb.GetCustomerEPANAttemptsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	epanAttempt, epanErr := s.panClient.GetAllEPANAttempts(ctx, &pan.GetAllEPANAttemptsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		Limit:   50,
	})
	if te := epifigrpc.RPCError(epanAttempt, epanErr); te != nil && !epanAttempt.GetStatus().IsRecordNotFound() {
		cxLogger.Error(ctx, "error while fetching all epan attempts", zap.Error(te))
		return &cxKycPb.GetCustomerEPANAttemptsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(te.Error()),
		}, nil
	}
	if epanAttempt.GetStatus().IsRecordNotFound() {
		return &cxKycPb.GetCustomerEPANAttemptsResponse{
			Status:            rpcPb.StatusOk(),
			LatestAttemptInfo: epanNotAttempted,
		}, nil
	}
	res := buildEPanInfoResp(epanAttempt.GetEPanAttempts())
	return res, nil
}

func getSucceededFM(fmp []*kyc.FaceMatchParams) bool {
	for _, p := range fmp {
		if kyc2.IsFMSuccessState(p.Status) {
			return true
		}
	}
	return false
}

// ensureFailReasonForLSO ensures kyc attempt failure reason is L,S,O flag if the user is L,S,O type.
// This is mainly for the old users who have onboarded before we started storing L,S,O failure reason.
func ensureFailReasonForLSO(ctx context.Context, res *kycPb.GetKYCHistoryResponse) *kycPb.GetKYCHistoryResponse {
	var (
		splCKYCFirstChars = map[string]kycPb.FailureType{
			"L": kycPb.FailureType_CKYC_L_FLAG,
			"S": kycPb.FailureType_CKYC_S_FLAG,
			"O": kycPb.FailureType_CKYC_O_FLAG,
		}
	)

	for _, att := range res.GetKycAttempts() {
		ckycNos := att.GetRequestParams().GetCkycDownload().GetCkycNumbers()
		if len(ckycNos) == 0 {
			continue
		}
		ckycNo := ckycNos[0]
		if len(strings.TrimSpace(ckycNo)) == 0 {
			logger.Info(ctx, "unexpected empty ckyc number in kyc history")
			continue
		}

		firstChar := strings.TrimSpace(ckycNo)[0:1]
		logger.Info(ctx, fmt.Sprintf("first char: %v", firstChar)) // TODO(aditya): remove after feature is stable
		for splChar, failReason := range splCKYCFirstChars {
			if strings.EqualFold(firstChar, splChar) {
				if len(att.GetFailureReason().GetFailures()) > 0 && att.FailureReason.Failures[0].GetType() == failReason {
					logger.Debug(ctx, "right failure reason already present")
					continue
				}
				logger.Info(ctx, fmt.Sprintf("updating failure reason to %v", failReason))
				att.FailureReason = &kycPb.FailureReason{
					Failures: []*kycPb.Failure{
						{
							Type:        failReason,
							Description: fmt.Sprintf("user is %v type", splChar),
						},
					},
				}
			}
		}
	}
	return res
}

func getDateInString(time *timestampPb.Timestamp) string {
	return datetimePkg.DateToString(datetimePkg.TimestampToDateInLoc(time, datetimePkg.IST), "02 Jan 2006", datetimePkg.IST)
}
