package kyc

import (
	"context"
	"testing"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

func Test_ensureLSOReasonPresent(t *testing.T) {
	t.<PERSON>llel()
	logger.Init(cfg.TestEnv)
	type args struct {
		res *kyc.GetKYCHistoryResponse
	}
	tests := []struct {
		name	string
		args	args
		want	*kyc.GetKYCHistoryResponse
	}{
		{
			name:	"lso user; different failure type",
			args: args{
				res: &kyc.GetKYCHistoryResponse{
					KycAttempts: []*kyc.KycAttempt{
						{
							RequestParams: &kyc.KYCAttemptRequestParams{
								CkycDownload: &kyc.CKYCDownloadRequestParams{
									CkycNumbers: []string{"L123"},
								},
							},
							FailureReason: &kyc.FailureReason{
								Failures: []*kyc.Failure{
									{
										Type: kyc.FailureType_DOB_MISMATCH,
									},
								},
							},
						},
					},
				},
			},
			want: &kyc.GetKYCHistoryResponse{
				KycAttempts: []*kyc.KycAttempt{
					{
						RequestParams: &kyc.KYCAttemptRequestParams{
							CkycDownload: &kyc.CKYCDownloadRequestParams{
								CkycNumbers: []string{"L123"},
							},
						},
						FailureReason: &kyc.FailureReason{
							Failures: []*kyc.Failure{
								{
									Type:		kyc.FailureType_CKYC_L_FLAG,
									Description:	"user is L type",
								},
							},
						},
					},
				},
			},
		},
		{
			name:	"non lso user; no change",
			args: args{
				res: &kyc.GetKYCHistoryResponse{
					KycAttempts: []*kyc.KycAttempt{
						{
							RequestParams: &kyc.KYCAttemptRequestParams{
								CkycDownload: &kyc.CKYCDownloadRequestParams{
									CkycNumbers: []string{"123"},
								},
							},
							FailureReason: &kyc.FailureReason{
								Failures: []*kyc.Failure{
									{
										Type: kyc.FailureType_DOB_MISMATCH,
									},
								},
							},
						},
					},
				},
			},
			want: &kyc.GetKYCHistoryResponse{
				KycAttempts: []*kyc.KycAttempt{
					{
						RequestParams: &kyc.KYCAttemptRequestParams{
							CkycDownload: &kyc.CKYCDownloadRequestParams{
								CkycNumbers: []string{"123"},
							},
						},
						FailureReason: &kyc.FailureReason{
							Failures: []*kyc.Failure{
								{
									Type: kyc.FailureType_DOB_MISMATCH,
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ensureFailReasonForLSO(context.Background(), tt.args.res); !proto.Equal(got, tt.want) {
				t.Errorf(" got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}
