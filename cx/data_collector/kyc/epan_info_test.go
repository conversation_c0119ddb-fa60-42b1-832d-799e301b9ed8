package kyc

import (
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	cxKycPb "github.com/epifi/gamma/api/cx/data_collector/kyc"
	"github.com/epifi/gamma/api/pan/epan"
	"github.com/epifi/gamma/api/typesv2/webui"
)

var (
	epanAttemptFix	= &epan.EPANAttempt{
		State:		epan.EPANState_EPAN_STATE_INITIATED,
		SubState:	epan.EPANSubState_EPAN_SUB_STATE_CAPTCHA_INIT_SUCCESS,
		CreatedAt:	timestamp.New(time.Now().Add(time.Hour * 1)),
	}
	epanAttemptFix1	= &epan.EPANAttempt{
		State:		epan.EPANState_EPAN_STATE_FAILED,
		SubState:	epan.EPANSubState_EPAN_SUB_STATE_VALIDATE_FORM_ERROR_PAN_NOT_LINKED_TO_MOBILE_EMAIL,
		CreatedAt:	timestamp.Now(),
	}
	epanAttemptFix2	= &epan.EPANAttempt{
		State:		epan.EPANState_EPAN_STATE_SUCCESS,
		SubState:	epan.EPANSubState_EPAN_SUB_STATE_PAN_DATA_DOWNLOADED_SUCCESS,
		CreatedAt:	timestamp.New(time.Now().Add(time.Hour * -3)),
		CompletedAt:	timestamp.New(time.Now().Add(time.Hour * -2)),
	}
	tableHeader	= []*webui.TableHeader{
		{
			Label:		tableHeaderStateLabel,
			HeaderKey:	tableHeaderStateKey,
			IsVisible:	true,
		},
		{
			Label:		tableHeaderSubStateLabel,
			HeaderKey:	tableHeaderSubStateKey,
			IsVisible:	true,
		},
		{
			Label:		tableHeaderDownloadedOnLabel,
			HeaderKey:	tableHeaderDownloadedOnKey,
			IsVisible:	true,
		},
		{
			Label:		tableHeaderAttemptCreatedLabel,
			HeaderKey:	tableHeaderAttemptCreatedKey,
			IsVisible:	true,
		},
		{
			Label:		tableHeaderValidTillLabel,
			HeaderKey:	tableHeaderValidTillKey,
			IsVisible:	true,
		},
	}
)

func Test_buildEPanInfoResp(t *testing.T) {
	t.Parallel()
	type args struct {
		epanAttempts []*epan.EPANAttempt
	}
	tests := []struct {
		name	string
		args	args
		want	*cxKycPb.GetCustomerEPANAttemptsResponse
	}{
		{
			name:	"Epan attempt length as 0",
			args:	args{},
			want: &cxKycPb.GetCustomerEPANAttemptsResponse{
				Status:			rpc.StatusOk(),
				LatestAttemptInfo:	epanNotAttempted,
			},
		},
		{
			name:	"Only one attempt present",
			args: args{
				epanAttempts: []*epan.EPANAttempt{
					epanAttemptFix2,
				},
			},
			want: &cxKycPb.GetCustomerEPANAttemptsResponse{
				Status:			rpc.StatusOk(),
				LatestAttemptInfo:	"EPAN_STATE_SUCCESS",
				LatestEPanInfo: &webui.Table{
					TableName:	latestTableName,
					TableHeaders:	tableHeader,
					TableRows: []*webui.TableRow{
						{
							HeaderKeyToCellValueMap: map[string]string{
								tableHeaderStateKey:		epan.EPANState_EPAN_STATE_SUCCESS.String(),
								tableHeaderSubStateKey:		epan.EPANSubState_EPAN_SUB_STATE_PAN_DATA_DOWNLOADED_SUCCESS.String(),
								tableHeaderDownloadedOnKey:	epanAttemptFix2.GetCompletedAt().AsTime().String(),
								tableHeaderAttemptCreatedKey:	epanAttemptFix2.GetCreatedAt().AsTime().String(),
								tableHeaderValidTillKey:	epanAttemptFix2.GetCompletedAt().AsTime().Add(3 * 24 * time.Hour).String(),
							},
						},
					},
				},
			},
		},
		{
			name:	"Multiple attempt present",
			args: args{
				epanAttempts: []*epan.EPANAttempt{
					epanAttemptFix2,
					epanAttemptFix,
					epanAttemptFix1,
				},
			},
			want: &cxKycPb.GetCustomerEPANAttemptsResponse{
				Status:			rpc.StatusOk(),
				LatestAttemptInfo:	"EPAN_STATE_INITIATED",
				LatestEPanInfo: &webui.Table{
					TableName:	latestTableName,
					TableHeaders:	tableHeader,
					TableRows: []*webui.TableRow{
						{
							HeaderKeyToCellValueMap: map[string]string{
								tableHeaderStateKey:		epan.EPANState_EPAN_STATE_INITIATED.String(),
								tableHeaderSubStateKey:		epan.EPANSubState_EPAN_SUB_STATE_CAPTCHA_INIT_SUCCESS.String(),
								tableHeaderAttemptCreatedKey:	epanAttemptFix.GetCreatedAt().AsTime().String(),
								tableHeaderDownloadedOnKey:	"",
								tableHeaderValidTillKey:	"",
							},
						},
					},
				},
				PastEPanAttempts: &webui.Table{
					TableName:	pastAttemptTableName,
					TableHeaders:	tableHeader,
					TableRows: []*webui.TableRow{
						{
							HeaderKeyToCellValueMap: map[string]string{
								tableHeaderStateKey:		epan.EPANState_EPAN_STATE_FAILED.String(),
								tableHeaderSubStateKey:		epan.EPANSubState_EPAN_SUB_STATE_VALIDATE_FORM_ERROR_PAN_NOT_LINKED_TO_MOBILE_EMAIL.String(),
								tableHeaderAttemptCreatedKey:	epanAttemptFix1.GetCreatedAt().AsTime().String(),
								tableHeaderDownloadedOnKey:	"",
								tableHeaderValidTillKey:	"",
							},
						},
						{
							HeaderKeyToCellValueMap: map[string]string{
								tableHeaderStateKey:		epan.EPANState_EPAN_STATE_SUCCESS.String(),
								tableHeaderSubStateKey:		epan.EPANSubState_EPAN_SUB_STATE_PAN_DATA_DOWNLOADED_SUCCESS.String(),
								tableHeaderDownloadedOnKey:	epanAttemptFix2.GetCompletedAt().AsTime().String(),
								tableHeaderAttemptCreatedKey:	epanAttemptFix2.GetCreatedAt().AsTime().String(),
								tableHeaderValidTillKey:	epanAttemptFix2.GetCompletedAt().AsTime().Add(3 * 24 * time.Hour).String(),
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := buildEPanInfoResp(tt.args.epanAttempts)
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("buildEPanInfoResp() value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
