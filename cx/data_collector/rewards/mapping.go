package rewards

import (
	rePb "github.com/epifi/gamma/api/cx/data_collector/rewards"
	rewardsPb "github.com/epifi/gamma/api/rewards"
)

var (
	cxRewardOfferTypeToBeRewardOfferType = map[rePb.RewardOfferTypesOptions]rewardsPb.RewardOfferType{
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_UNSPECIFIED:                                      rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE,
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_PLUS_TIER_1_PERCENT_CASHBACK_OFFER:               rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER,
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER:        rewardsPb.RewardOfferType_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER,
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER:   rewardsPb.RewardOfferType_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER,
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER:           rewardsPb.RewardOfferType_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER,
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_SALARY_TIER_2_PERCENT_CASHBACK_OFFER:             rewardsPb.RewardOfferType_SALARY_TIER_2_PERCENT_CASHBACK_OFFER,
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER: rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER,
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER:   rewardsPb.RewardOfferType_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER,
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER:   rewardsPb.RewardOfferType_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER,
	}

	cxRewardOfferTypeToTextDescription = map[rePb.RewardOfferTypesOptions]string{
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_UNSPECIFIED:                                      "All",
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_PLUS_TIER_1_PERCENT_CASHBACK_OFFER:               "1% cashback for Plus tier",
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER:        "1% cashback for Salary Lite tier",
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER:   "1% cashback for Prime tier band 1",
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER:           "2% cashback for Infinite tier",
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_SALARY_TIER_2_PERCENT_CASHBACK_OFFER:             "2% cashback for Salary tier",
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER: "2% cashback for Salary/Infinite tier",
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER:   "2% cashback for Prime tier band 2",
		rePb.RewardOfferTypesOptions_REWARD_OFFER_TYPES_OPTIONS_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER:   "3% cashback for Prime tier band 3",
	}
)
