// nolint:gosec,funlen
package firefly

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxCcPb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/be-common/pkg/epifigrpc"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

const (
	cardProgramHeaderLeft  = "Type"
	cardProgramHeaderRight = "Description"
	cardProgramCardName    = "Card Name"
	cardProgramNetwork     = "Card Network"
	cardProgramIssuer      = "Card Issuer"
	cardProgramJoiningFee  = "Joining Fee"
)

func (s *Service) GetCardProgramDetails(ctx context.Context, req *cxCcPb.GetCardProgramDetailsRequest) (*cxCcPb.GetCardProgramDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardProgramDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetCardProgramDetailsResponse{}

	onbRes, rpcErr := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
		ActorId:             req.GetHeader().GetActor().GetId(),
		CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	if err := epifigrpc.RPCError(onbRes, rpcErr); err != nil {
		logger.Error(ctx, "error fetching credit card onboarding request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	cardProgram := onbRes.GetCardRequest().GetRequestDetails().GetCardProgram()

	var cardIssuer, cardNetwork, joiningFee string

	cardIssuer = ffPkg.GetCardProgramAttributes(cardProgram)[ffPkg.VendorAttributeString]

	// TODO(team) - remove hard-coding when network gets added
	cardNetwork = "Visa"

	joiningFee = moneyPkg.ToDisplayStringInIndianFormat(ffPkg.JoiningFee, 0, true)

	res.Status = rpcPb.StatusOk()
	res.CardProgramDetailsTable = &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				HeaderKey: cardProgramHeaderLeft,
			},
			{
				HeaderKey: cardProgramHeaderRight,
			},
		},
		TableRows: []*webui.TableRow{
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					cardProgramHeaderLeft: {
						Value: cardProgramCardName,
					},
					cardProgramHeaderRight: {
						Value: ffPkg.GetCardNameFromProgram(cardProgram),
					},
				},
			},
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					cardProgramHeaderLeft: {
						Value: cardProgramNetwork,
					},
					cardProgramHeaderRight: {
						Value: cardNetwork,
					},
				},
			},
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					cardProgramHeaderLeft: {
						Value: cardProgramIssuer,
					},
					cardProgramHeaderRight: {
						Value: cardIssuer,
					},
				},
			},
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					cardProgramHeaderLeft: {
						Value: cardProgramJoiningFee,
					},
					cardProgramHeaderRight: {
						Value: joiningFee,
					},
				},
			},
		},
		Actions: nil,
	}

	return res, nil
}
