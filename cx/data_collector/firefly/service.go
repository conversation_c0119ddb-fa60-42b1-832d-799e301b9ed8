//nolint:all
package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/text/currency"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	cxCcPb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	depositPb "github.com/epifi/gamma/api/deposit"
	ffPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBeAccountsEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffBeBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ccCxPb "github.com/epifi/gamma/api/firefly/cx"
	ccCxEnumsPb "github.com/epifi/gamma/api/firefly/cx/enum"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ffLmsPb "github.com/epifi/gamma/api/firefly/lms"
	"github.com/epifi/gamma/api/firefly/pinot"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/api/segment"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

var (
	txnStatusVsStatusStr = map[ffBeAccountsEnumsPb.TransactionStatus]string{
		ffBeAccountsEnumsPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED: "UNKNOWN",
		ffBeAccountsEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE:     "FAILURE",
		ffBeAccountsEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS:     "SUCCESS",
	}
	txnTypeVsTxnTypeStr = map[ffBeAccountsEnumsPb.TransactionType]string{
		ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED: "UNKNOWN",
		ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT:      "CREDIT",
		ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT:       "DEBIT",
	}
	beDisputeStateVsDisputeStateString = map[ffBeAccountsEnumsPb.DisputeState]string{
		ffBeAccountsEnumsPb.DisputeState_DISPUTE_STATE_UNSPECIFIED: "UNKNOWN",
		ffBeAccountsEnumsPb.DisputeState_DISPUTE_STATE_INITIATED:   "INITIATED",
		ffBeAccountsEnumsPb.DisputeState_DISPUTE_STATE_ACCEPTED:    "ACCEPTED",
		ffBeAccountsEnumsPb.DisputeState_DISPUTE_STATE_REJECTED:    "REJECTED",
	}
	beCardStatusVsCardStatusString = map[ffEnumsPb.CardState]string{
		ffEnumsPb.CardState_CARD_STATE_UNSPECIFIED:         "UNKNOWN",
		ffEnumsPb.CardState_CARD_STATE_CREATED:             "CREATED",
		ffEnumsPb.CardState_CARD_STATE_ACTIVATED:           "ACTIVATED",
		ffEnumsPb.CardState_CARD_STATE_SUSPENDED:           "SUSPENDED",
		ffEnumsPb.CardState_CARD_STATE_BLOCKED:             "BLOCKED",
		ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED: "DIGITALLY ACTIVATED",
		ffEnumsPb.CardState_CARD_STATE_CLOSED:              "CLOSED",
	}
	bePaymentStatusVsPaymentStatusString = map[ffEnumsPb.PaymentStatus]string{
		ffEnumsPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED: "UNSPECIFIED",
		ffEnumsPb.PaymentStatus_PAYMENT_STATUS_IN_PROGRESS: "IN PROGRESS",
		ffEnumsPb.PaymentStatus_PAYMENT_STATUS_SUCCESS:     "SUCCESS",
		ffEnumsPb.PaymentStatus_PAYMENT_STATUS_FAILED:      "FAILURE",
	}
	beCardFormVsCardFormString = map[ffEnumsPb.CardForm]string{
		ffEnumsPb.CardForm_CARD_FORM_UNSPECIFIED: "UNKNOWN",
		ffEnumsPb.CardForm_CARD_FORM_PHYSICAL:    "PHYSICAL",
		ffEnumsPb.CardForm_CARD_FORM_VIRTUAL:     "DIGITAL",
	}
	mapBeStageNameToCxStageName = map[ffEnumsPb.CardRequestStageName]string{
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_VKYC:                 "VKYC, if applicable, done",
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_BILLING_DAYS_CAPTURE: "Bill generation date selected",
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CARD_CREATION:        "Card created",
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_ADDRESS_CAPTURE:      "Shipping address selected",
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_AUTH:                 "Liveness and Facematch check done",
		ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_FD_CREATION:          "Fd creation/selection",
	}
	mapBeStageStatusToCxStatus = map[ffEnumsPb.CardRequestStageStatus]string{
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED:             "Pending",
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS:             "Success",
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_IN_PROGRESS:         "Pending",
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED:              "Failed",
		ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_MANUAL_INTERVENTION: "Manual intervention",
	}
	mapCreditCardStateToString = map[ffEnumsPb.CardState]string{
		ffEnumsPb.CardState_CARD_STATE_UNSPECIFIED:         "",
		ffEnumsPb.CardState_CARD_STATE_CREATED:             "Created",
		ffEnumsPb.CardState_CARD_STATE_ACTIVATED:           "Activated",
		ffEnumsPb.CardState_CARD_STATE_SUSPENDED:           "Suspended",
		ffEnumsPb.CardState_CARD_STATE_BLOCKED:             "Blocked",
		ffEnumsPb.CardState_CARD_STATE_DIGITALLY_ACTIVATED: "Digitally activated",
	}
	mapBeCardProgramToFeCardProgram = map[types.CardProgramType]cxCcPb.CardProgramType{
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:     cxCcPb.CardProgramType_CARD_PROGRAM_TYPE_SECURED,
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED: cxCcPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:   cxCcPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
	}
)

const (
	dpdSegmentId = "65ea3cc4-87b1-4c45-9d7e-0901c5d4c530"
	// GetDisputes table headers keys
	disputeIdHeaderKey             = "disputeId"
	disputeTransactionIdHeaderKey  = "disputeTransactionId"
	disputeAccountIdHeaderKey      = "disputeAccountId"
	disputeExtDisputeRefHeaderKey  = "disputeExtDisputeRef"
	disputeDisputeStateHeaderKey   = "disputeDisputeState"
	disputeDisputedAtHeaderKey     = "disputeDisputedAt"
	disputeUpdatedAtHeaderKey      = "disputeUpdatedAt"
	disputeDisputedAmountHeaderKey = "disputeDisputedAmount"
	disputeIsActiveHeaderKey       = "disputeIsActive"
)

var (
	disputesTableHeaders = []*webui.TableHeader{
		{
			Label:      "Dispute Id",
			HeaderKey:  disputeIdHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
		{
			Label:      "Transaction Id",
			HeaderKey:  disputeTransactionIdHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
		{
			Label:      "Account Id",
			HeaderKey:  disputeAccountIdHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
		{
			Label:      "Ext Dispute Ref ID",
			HeaderKey:  disputeExtDisputeRefHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
		{
			Label:      "Dispute State",
			HeaderKey:  disputeDisputeStateHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
		{
			Label:      "Disputed At",
			HeaderKey:  disputeDisputedAtHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
		{
			Label:      "Updated At",
			HeaderKey:  disputeUpdatedAtHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
		{
			Label:      "Amount",
			HeaderKey:  disputeDisputedAmountHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
		{
			Label:      "Is Active",
			HeaderKey:  disputeIsActiveHeaderKey,
			IsVisible:  true,
			IsSortable: true,
		},
	}

	milestoneRewardTableHeaders = []*webui.TableHeader{
		{
			Label:      "Spend Criteria",
			HeaderKey:  "SpendCriteria",
			IsVisible:  true,
			IsSortable: false,
		},
		{
			Label:      "Milestone Rewards",
			HeaderKey:  "MilestoneRewards",
			IsVisible:  true,
			IsSortable: false,
		},
		{
			Label:      "Milestone reward status",
			HeaderKey:  "MilestoneRewardStatus",
			IsVisible:  true,
			IsSortable: false,
		},
		{
			Label:      "Milestone rewards will be triggered on",
			HeaderKey:  "MilestoneTriggeredOn",
			IsVisible:  true,
			IsSortable: false,
		},
	}
	renewalFeeDetailsTableHeaders = []*webui.TableHeader{
		{
			Label:      "User's Spend Details",
			HeaderKey:  "UserSpendDetails",
			IsVisible:  true,
			IsSortable: false,
		},
		{
			Label:      "Renewal Fees",
			HeaderKey:  "RenewalFees",
			IsVisible:  true,
			IsSortable: false,
		},
		{
			Label:      "Renewal fees payment status",
			HeaderKey:  "RenewalFeesPaymentStatus",
			IsVisible:  true,
			IsSortable: false,
		},
		{
			Label:      "Renewal Fees will be added to bill on",
			HeaderKey:  "AddedToBill",
			IsVisible:  true,
			IsSortable: false,
		},
		{
			Label:      "Renewal fees payment Due Date",
			HeaderKey:  "DueDate",
			IsVisible:  true,
			IsSortable: false,
		},
	}
)

type Service struct {
	authEngine         auth_engine.IAuthEngine
	ccCxClient         ccCxPb.CxClient
	ffClient           ffPb.FireflyClient
	ffBillingClient    ffBeBillingPb.BillingClient
	ffAccountingClient ffAccountsPb.AccountingClient
	limitEstimator     limitEstimatorPb.CreditLimitEstimatorClient
	ffLmsClient        ffLmsPb.LoanManagementSystemClient
	actorClient        actorPb.ActorClient
	rewardsClient      rewardsPb.RewardsGeneratorClient
	bcClient           bankcust.BankCustomerServiceClient
	depositClient      depositPb.DepositClient
	projectorClient    projectorPb.ProjectorServiceClient
	txnAggClient       pinot.TxnAggregatesClient
	segmentationClient segment.SegmentationServiceClient
}

func NewService(authEngine auth_engine.IAuthEngine, ccCxClient ccCxPb.CxClient, ffClient ffPb.FireflyClient,
	ffBillingClient ffBeBillingPb.BillingClient,
	ffAccountingClient ffAccountsPb.AccountingClient,
	limitEstimator limitEstimatorPb.CreditLimitEstimatorClient,
	ffLmsClient ffLmsPb.LoanManagementSystemClient,
	actorClient actorPb.ActorClient,
	rewardsClient rewardsPb.RewardsGeneratorClient, bcClient bankcust.BankCustomerServiceClient,
	depositClient depositPb.DepositClient, projectorClient projectorPb.ProjectorServiceClient,
	txnAggClient pinot.TxnAggregatesClient, segmentationClient segment.SegmentationServiceClient) *Service {
	return &Service{
		authEngine:         authEngine,
		ccCxClient:         ccCxClient,
		ffClient:           ffClient,
		ffBillingClient:    ffBillingClient,
		ffAccountingClient: ffAccountingClient,
		limitEstimator:     limitEstimator,
		ffLmsClient:        ffLmsClient,
		actorClient:        actorClient,
		rewardsClient:      rewardsClient,
		bcClient:           bcClient,
		depositClient:      depositClient,
		projectorClient:    projectorClient,
		txnAggClient:       txnAggClient,
		segmentationClient: segmentationClient,
	}
}

var _ cxCcPb.FireflyServer = &Service{}

func (s *Service) GetMilestoneRewardsDetails(ctx context.Context, request *cxCcPb.GetMilestoneRewardDetailsRequest) (*cxCcPb.GetMilestoneRewardDetailsResponse, error) {
	actorId := request.GetHeader().GetActor().GetId()
	accResp, err := s.ffAccountingClient.GetAccounts(ctx, &ffAccountsPb.GetAccountsRequest{
		GetBy: &ffAccountsPb.GetAccountsRequest_ActorId{
			ActorId: actorId,
		},
	})
	if grpcErr := epifigrpc.RPCError(accResp, err); grpcErr != nil {
		logger.Error(ctx, "error in GetAccounts", zap.Error(grpcErr))
		return &cxCcPb.GetMilestoneRewardDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(grpcErr.Error()),
		}, nil
	}

	if len(accResp.GetAccounts()) == 0 {
		logger.Error(ctx, "no credit account for actor")
		return &cxCcPb.GetMilestoneRewardDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("no credit account for actor"),
		}, nil
	}

	creditAccount := accResp.GetAccounts()[0]

	switch accResp.GetAccounts()[0].GetCardProgram().GetCardProgramType() {
	case types.CardProgramType_CARD_PROGRAM_TYPE_SECURED, types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		return &cxCcPb.GetMilestoneRewardDetailsResponse{
			Status: rpcPb.StatusFailedPrecondition(),
		}, nil
	}

	milestoneSpends, waiverSpendsFi, waiverSpendsM2p, err := s.getUserSpendsForMilestoneRewardsAndRenewalFeesWaiver(ctx, actorId, accResp.GetAccounts()[0])
	if err != nil {
		logger.Error(ctx, "error in getUserSpendsForMilestoneRewardsAndRenewalFeesWaiver", zap.Error(err))
		return &cxCcPb.GetMilestoneRewardDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	renewalFeesTable, err := s.getRenewalFeesDetailsTable(ctx, waiverSpendsM2p, waiverSpendsFi, accResp.GetAccounts()[0])
	if err != nil {
		logger.Error(ctx, "error in getRenewalFeesDetailsTable", zap.Error(err))
		return &cxCcPb.GetMilestoneRewardDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	milestoneRewardsTable, milestoneRewardInfo, err := s.getMilestoneRewardsTableAndInfo(ctx, actorId, getMilestoneSpendsCriteria(milestoneSpends), creditAccount.GetCreatedAt())
	if err != nil {
		logger.Error(ctx, "error in getMilestoneRewardsTable", zap.Error(err))
		return &cxCcPb.GetMilestoneRewardDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	dpdStatusString, dpdErr := s.getDpdStatusString(ctx, actorId)
	if dpdErr != nil {
		logger.Error(ctx, "error in getDpdStatusString", zap.Error(dpdErr))
		return &cxCcPb.GetMilestoneRewardDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(dpdErr.Error()),
		}, nil
	}

	retainUserString, retainErr := getRetainUserString(ctx, waiverSpendsM2p)
	if retainErr != nil {
		logger.Error(ctx, "error in getRetainUserString", zap.Error(retainErr))
		return &cxCcPb.GetMilestoneRewardDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(retainErr.Error()),
		}, nil
	}

	return &cxCcPb.GetMilestoneRewardDetailsResponse{
		Status: rpcPb.StatusOk(),
		AnnualMilestoneRewardDetails: []*cxCcPb.AnnualMilestoneRewardData{
			{
				Title:                 getMilestoneRewardDataTitle(creditAccount.GetCreatedAt()),
				RenewalFeesTable:      renewalFeesTable,
				MilestoneRewardsTable: milestoneRewardsTable,
				DpdStatus:             dpdStatusString,
				IsUserActive:          "NA",
				RetainUser:            retainUserString,
				Recommendation:        "Before the next billing cycle, we will be giving this segment of users a renewal",
				MilestoneRewardInfo:   milestoneRewardInfo,
			},
		},
	}, nil
}

func getRetainUserString(ctx context.Context, spends *moneyPb.Money) (string, error) {
	totalSpendsGreaterThan100K, err := moneyPb.CompareV2(spends.GetPb(), moneyPb.AmountINR(100000).GetPb())
	if err != nil {
		return "", err
	}

	if totalSpendsGreaterThan100K >= 0 {
		return "Yes", nil
	}

	return "No", nil
}
func (s *Service) getDpdStatusString(ctx context.Context, actorId string) (string, error) {
	segmentRes, err := s.segmentationClient.IsMember(ctx, &segment.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: []string{dpdSegmentId},
	})
	if grpcErr := epifigrpc.RPCError(segmentRes, err); grpcErr != nil {
		return "", grpcErr
	}

	if segmentRes.GetSegmentMembershipMap()[dpdSegmentId].GetIsActorMember() {
		return "Yes", nil
	}

	return "No", nil
}

func getMilestoneRewardDataTitle(accCreationDate *timestamppb.Timestamp) string {
	return accCreationDate.AsTime().Format(time.RFC850) + " - " +
		accCreationDate.AsTime().AddDate(1, 0, 0).Format(time.RFC850)
}

func getMilestoneSpendsCriteria(spends *moneyPb.Money) string {
	switch {
	case spends.GetPb().GetUnits() >= 400000:
		return "More than 4L"
	case spends.GetPb().GetUnits() >= 250000:
		return "More than 2.5L"
	default:
		return "Less than 1L"
	}
}

func (s *Service) getMilestoneRewardsTableAndInfo(ctx context.Context, actorId string, spendsCriteria string, accCreationTime *timestamppb.Timestamp) (*webui.Table, string, error) {
	var (
		tableRows []*webui.TableRow
	)
	rewardsM1, err := s.getMilestoneRewardsByActorIdAndRewardType(ctx, actorId, rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_MILESTONE_1_OFFER, accCreationTime)
	if err != nil {
		logger.Error(ctx, "error in getMilestoneRewardsByActorIdAndRewardType", zap.Error(err))
		return nil, "", err
	}

	rewardsM2, err := s.getMilestoneRewardsByActorIdAndRewardType(ctx, actorId, rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_MILESTONE_2_OFFER, accCreationTime)
	if err != nil {
		logger.Error(ctx, "error in getMilestoneRewardsByActorIdAndRewardType", zap.Error(err))
		return nil, "", err
	}

	rewardArr := append(rewardsM1, rewardsM2...)

	if len(rewardArr) == 0 {
		return nil, "Since the user has not spend more than 2.5L, they are not eligible for any milestone rewards", nil
	}

	for _, reward := range rewardArr {
		tableRows = append(tableRows, getMilestoneRewardTableRow(spendsCriteria, reward.GetRewardOptions().GetActionDetails(), "Triggered", reward.GetUpdatedAt().AsTime().Format(datetime.DATE_LAYOUT_YYYYMMDD)))
	}

	return &webui.Table{
			TableHeaders: milestoneRewardTableHeaders,
			TableRows:    tableRows,
			TableName:    "Milestone Reward Details",
		}, "User should have spent 2.5L+ excluding rental, wallet and jewelery spends to get milestone rewards." +
			" Fees, charges and reversals are excluded from the spend", nil
}

func (s *Service) getRenewalFeesDetailsTable(ctx context.Context, m2pSpends, fiSpends *moneyPb.Money, account *accounting.CreditAccount) (*webui.Table, error) {
	m2PGreaterThan4L, err := moneyPb.CompareV2(m2pSpends.GetPb(), moneyPb.AmountINR(400000).GetPb())
	if err != nil {
		logger.Error(ctx, "error in CompareV2", zap.Error(err))
		return nil, err
	}

	m2PGreaterThan250k, err := moneyPb.CompareV2(m2pSpends.GetPb(), moneyPb.AmountINR(250000).GetPb())
	if err != nil {
		logger.Error(ctx, "error in CompareV2", zap.Error(err))
		return nil, err
	}
	fiSpendGreaterThan1L, err := moneyPb.CompareV2(fiSpends.GetPb(), moneyPb.AmountINR(100000).GetPb())
	if err != nil {
		logger.Error(ctx, "error in CompareV2", zap.Error(err))
		return nil, err
	}

	totalLimit := account.GetTotalLimit()

	renewalFeeBillDate, renewalFeeBillDueDate, err := s.getRenewalBillAndDueDate(ctx, account.GetActorId(), account.GetCreatedAt(), 1)
	switch {
	case m2PGreaterThan4L >= 0:
		return &webui.Table{
			TableHeaders: renewalFeeDetailsTableHeaders,
			TableRows:    getRenewalFeesTableRowsForFeesNotWaived("More than 4L", "NA", renewalFeeBillDate.Format(datetime.DATE_LAYOUT_DDMMYYYY), renewalFeeBillDueDate.Format(datetime.DATE_LAYOUT_DDMMYYYY)),
			TableName:    "Renewal Fees Details",
		}, nil
	case m2PGreaterThan250k >= 0:
		return &webui.Table{
			TableHeaders: renewalFeeDetailsTableHeaders,
			TableRows:    getRenewalFeesTableRowsForFeesNotWaived("More than 2.5L", "NA", renewalFeeBillDate.Format(datetime.DATE_LAYOUT_DDMMYYYY), renewalFeeBillDueDate.Format(datetime.DATE_LAYOUT_DDMMYYYY)),
			TableName:    "Renewal Fees Details",
		}, nil
	case fiSpendGreaterThan1L >= 0 && totalLimit < 50000:
		return &webui.Table{
			TableHeaders: renewalFeeDetailsTableHeaders,
			TableRows:    getRenewalFeesTableRowsForFeesNotWaived("More than 1L", "NA", renewalFeeBillDate.Format(datetime.DATE_LAYOUT_DDMMYYYY), renewalFeeBillDueDate.Format(datetime.DATE_LAYOUT_DDMMYYYY)),
			TableName:    "Renewal Fees Details",
		}, nil
	default:
		return &webui.Table{
			TableHeaders: renewalFeeDetailsTableHeaders,
			TableRows:    getRenewalFeesTableRowsForFeesNotWaived("Less than 2.5L", "N/A", renewalFeeBillDate.Format(datetime.DATE_LAYOUT_DDMMYYYY), renewalFeeBillDueDate.Format(datetime.DATE_LAYOUT_DDMMYYYY)),
			TableName:    "Renewal Fees Details",
		}, nil
	}
}

func getMilestoneRewardTableRow(spendsCriteria, rewardName, triggerStatus, triggerDate string) *webui.TableRow {
	return &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			"SpendCriteria": &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: spendsCriteria,
				},
			},
			"MilestoneRewards": &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: rewardName,
				},
			},
			"MilestoneRewardStatus": &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: triggerStatus,
				},
			},
			"MilestoneTriggeredOn": &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: triggerDate,
				},
			},
		},
	}
}

func (s *Service) GetCardOnboardingDetails(ctx context.Context, request *cxCcPb.GetCardOnboardingDetailsRequest) (*cxCcPb.GetCardOnboardingDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, request.GetHeader(), request.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardOnboardingDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetCardOnboardingDetailsResponse{Eligibility: false}

	eligibilityResp, err := s.ffClient.FetchCreditCardEligibility(ctx, &ffPb.FetchCreditCardEligibilityRequest{
		ActorId: request.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(eligibilityResp, err); te != nil {
		switch {
		case !eligibilityResp.GetIsUserCcEligible():
			// record not found means no credit card offer
			res.Eligibility = false
			res.Status = rpcPb.StatusOk()
			return res, nil

		case eligibilityResp.GetStatus().IsAlreadyExists():
			// already exist means credit card already exist
			res.IsCreditCardExist = true
			res.Status = rpcPb.StatusOk()
			res.CardProgramType = mapBeCardProgramToFeCardProgram[eligibilityResp.GetCardProgramType()]
			return res, nil

		default:
			logger.Error(ctx, "error in fetching credit card limit from FetchCreditCardEligibility RPC", zap.Error(te))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	if !eligibilityResp.GetIsUserCcEligible() {
		res.Eligibility = false
		res.Status = rpcPb.StatusOk()
		return res, nil
	}

	res.Eligibility = true
	res.CreditLimit = eligibilityResp.GetAvailableLimit()
	res.CardProgramType = mapBeCardProgramToFeCardProgram[eligibilityResp.GetCardProgramType()]

	offerResp, err := s.ffClient.GetCreditCardOffers(ctx, &ffPb.GetCreditCardOffersRequest{
		ActorId: request.GetHeader().GetActor().GetId(),
		Vendor:  ffEnumsPb.Vendor_FEDERAL,
	})
	if te := epifigrpc.RPCError(offerResp, err); te != nil {
		logger.Error(ctx, "error in fetching card offers from firefly client", zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(offerResp.GetOffers()) > 0 {
		res.OfferValidityTimestamp = offerResp.GetOffers()[0].GetValidTill()
	}

	onboardingStagesResp, err := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
		ActorId:             request.GetHeader().GetActor().GetId(),
		CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	if te := epifigrpc.RPCError(onboardingStagesResp, err); te != nil {
		switch {
		case onboardingStagesResp.GetStatus().IsRecordNotFound():
			// record not found means user has not started onboarding,
			// set APPLICATION_INITIATION status to unspecified and return
			res.Status = rpcPb.StatusOk()
			return res, nil

		default:
			logger.Error(ctx, "error in fetching card request/stages details from firefly client", zap.Error(te))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	res.StageDetails = append(res.StageDetails, &cxCcPb.GetCardOnboardingDetailsResponse_StageDetail{
		OnboardingStageName: "Application initiated",
		StartTimestamp:      onboardingStagesResp.GetCardRequest().GetCreatedAt(),
		Status:              "Success",
	})

	sort.Slice(onboardingStagesResp.GetCardRequestStages(), func(i, j int) bool {
		return onboardingStagesResp.GetCardRequestStages()[i].GetCreatedAt().GetSeconds() < onboardingStagesResp.GetCardRequestStages()[j].GetCreatedAt().GetSeconds()
	})

	for _, stage := range onboardingStagesResp.GetCardRequestStages() {
		stageName, ok := mapBeStageNameToCxStageName[stage.GetStage()]
		if !ok {
			continue
		}
		stageStatus, ok := mapBeStageStatusToCxStatus[stage.GetStatus()]
		if !ok {
			continue
		}
		res.StageDetails = append(res.StageDetails, &cxCcPb.GetCardOnboardingDetailsResponse_StageDetail{
			OnboardingStageName: stageName,
			StartTimestamp:      stage.GetCreatedAt(),
			Status:              stageStatus,
		})
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) UpdateFreeCardReplacement(ctx context.Context, req *cxCcPb.UpdateFreeCardReplacementRequest) (*cxCcPb.UpdateFreeCardReplacementResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.UpdateFreeCardReplacementResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.UpdateFreeCardReplacementResponse{}
	ffCardReplacementResp, err := s.ccCxClient.UpdateFreeCardReplacement(ctx, &ccCxPb.UpdateFreeCardReplacementRequest{ActorId: req.GetHeader().GetActor().GetId(), CardSkuType: ccEnumsPb.CardSKUType_CLASSIC})
	if te := epifigrpc.RPCError(ffCardReplacementResp, err); te != nil {
		logger.Error(ctx, "error in firefly API for UpdateFreeCardReplacement", zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) BlockCard(ctx context.Context, request *cxCcPb.BlockCardRequest) (*cxCcPb.BlockCardResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, request.GetHeader(), request.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.BlockCardResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	// Client will take user to confirm page screen if auth passed but action to be taken was set as false
	if !request.GetIsActionable() {
		return &cxCcPb.BlockCardResponse{
			Status: rpcPb.StatusOk(),
			SherlockDeepLink: &caPb.SherlockDeepLink{
				Screen: caPb.Screen_CONFIRM_ACTION_SCREEN,
			},
		}, nil
	}

	resp := &cxCcPb.BlockCardResponse{}
	if request.GetReason() == "" {
		request.Reason = "REASON NOT AVAILABLE"
	}

	ffBlockCardResp, err := s.ccCxClient.BlockCard(ctx, &ccCxPb.BlockCardRequest{
		ActorId: request.GetHeader().GetActor().GetId(),
		Reason:  request.GetReason(),
	})
	switch {
	case ffBlockCardResp.GetStatus().IsAlreadyExists():
		resp.Status = rpcPb.StatusAlreadyExists()
		return resp, nil
	case !ffBlockCardResp.GetStatus().IsSuccess():
		resp.Status = rpcPb.StatusInternal()
		return resp, nil
	case err != nil:
		resp.Status = rpcPb.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	resp.Status = rpcPb.StatusOk()
	return resp, nil
}

// SuspendCard to temporarily freeze/suspend a card
// This action is reversible and customer can undo this via app
func (s *Service) SuspendCard(ctx context.Context, request *cxCcPb.SuspendCardRequest) (*cxCcPb.SuspendCardResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, request.GetHeader(), request.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.SuspendCardResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	// Client will take user to confirm page screen if auth passed but action to be taken was set as false
	if !request.GetIsActionable() {
		return &cxCcPb.SuspendCardResponse{
			Status: rpcPb.StatusOk(),
			SherlockDeepLink: &caPb.SherlockDeepLink{
				Screen: caPb.Screen_CONFIRM_ACTION_SCREEN,
			},
		}, nil
	}

	resp := &cxCcPb.SuspendCardResponse{}
	if request.GetReason() == "" {
		request.Reason = "REASON NOT AVAILABLE"
	}

	ccBeResp, err := s.ccCxClient.InitiateCardAction(ctx, &ccCxPb.InitiateCardActionRequest{
		ActorId:        request.GetHeader().GetActor().GetId(),
		CardActionType: ccCxEnumsPb.CardActionType_CARD_ACTION_TYPE_FREEZE_UNFREEZE_CARD,
		RequestPayload: &ccCxPb.InitiateCardActionRequest_FreezeUnfreezeCardActionPayload{
			FreezeUnfreezeCardActionPayload: &ccCxPb.FreezeUnfreezeCardActionPayload{
				RequestType: ccEnumsPb.CardRequestType_REQUEST_TYPE_FREEZE_CARD,
				Reason:      request.GetReason(),
			},
		},
	})
	if te := epifigrpc.RPCError(ccBeResp, err); te != nil {
		cxLogger.Error(ctx, "error while initializing freeze card action at firefly server", zap.Error(te))
		resp.Status = rpcPb.StatusInternalWithDebugMsg("failed to initialize card action for SuspendCard")
		return resp, nil
	}

	resp.Status = rpcPb.StatusOk()
	return resp, nil
}

func (s *Service) ChangeControlDetails(ctx context.Context, req *cxCcPb.ChangeControlDetailsRequest) (*cxCcPb.ChangeControlDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.ChangeControlDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.ChangeControlDetailsResponse{}
	ffPrefReq := &ccCxPb.ChangeCardControlDetailsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	}

	for _, change := range req.GetChangeTypes() {
		if !change.GetDisableControl() {
			continue
		}
		switch change.GetControlType() {
		case cxCcPb.CardControlType_CARD_CONTROL_TYPE_ATM:
			ffPrefReq.ChangeData = &ffPb.ControlsChangeData{Atm: commontypes.BooleanEnum_FALSE}
		case cxCcPb.CardControlType_CARD_CONTROL_TYPE_POS:
			ffPrefReq.ChangeData = &ffPb.ControlsChangeData{Pos: commontypes.BooleanEnum_FALSE}
		case cxCcPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS:
			ffPrefReq.ChangeData = &ffPb.ControlsChangeData{Contactless: commontypes.BooleanEnum_FALSE}
		case cxCcPb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL:
			ffPrefReq.ChangeData = &ffPb.ControlsChangeData{International: commontypes.BooleanEnum_FALSE}
		case cxCcPb.CardControlType_CARD_CONTROL_TYPE_ECOM:
			ffPrefReq.ChangeData = &ffPb.ControlsChangeData{Ecom: commontypes.BooleanEnum_FALSE}
		}
	}

	ffPrefChangeResp, err := s.ccCxClient.ChangeCardControlDetails(ctx, ffPrefReq)
	if te := epifigrpc.RPCError(ffPrefChangeResp, err); te != nil {
		logger.Error(ctx, "error in changing control details", zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetCardUsageDetails(ctx context.Context, req *cxCcPb.GetCardUsageDetailsRequest) (*cxCcPb.GetCardUsageDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardUsageDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetCardUsageDetailsResponse{}
	ffCardRes, err := s.ffClient.GetCreditCard(ctx, &ffPb.GetCreditCardRequest{GetBy: &ffPb.GetCreditCardRequest_ActorId{
		ActorId: req.GetHeader().GetActor().GetId(),
	}})
	if te := epifigrpc.RPCError(ffCardRes, err); te != nil {
		logger.Error(ctx, "error in fetching card for the actor", zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	controlDetails := ffCardRes.GetCreditCard().GetControlDetails()
	return &cxCcPb.GetCardUsageDetailsResponse{
		Status: rpcPb.StatusOk(),
		UsageDetails: []*cxCcPb.CardUsageDetails{
			{
				ControlType: cxCcPb.CardControlType_CARD_CONTROL_TYPE_ATM,
				Enabled:     controlDetails.GetAtm(),
			},
			{
				ControlType: cxCcPb.CardControlType_CARD_CONTROL_TYPE_POS,
				Enabled:     controlDetails.GetPos(),
			},
			{
				ControlType: cxCcPb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL,
				Enabled:     controlDetails.GetInternational(),
			},
			{
				ControlType: cxCcPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS,
				Enabled:     controlDetails.GetContactless(),
			},
			{
				ControlType: cxCcPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
				Enabled:     controlDetails.GetEcom(),
			},
		},
	}, nil
}

func (s *Service) GetLimitUsageDetails(ctx context.Context, req *cxCcPb.GetLimitUsageDetailsRequest) (*cxCcPb.GetLimitUsageDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetLimitUsageDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetLimitUsageDetailsResponse{}

	bcResp, bcErr := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: req.GetHeader().GetActor().GetId(),
		},
	})
	if grpcErr := epifigrpc.RPCError(bcResp, bcErr); grpcErr != nil {
		logger.Error(ctx, "error in fetching bank customer", zap.Error(grpcErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	accRes, err := s.ffAccountingClient.GetAccount(ctx, &ffAccountsPb.GetAccountRequest{
		GetBy: &ffAccountsPb.GetAccountRequest_ByActorIdAndRefId{
			ByActorIdAndRefId: &ffAccountsPb.GetAccountRequest_ActorIdAndRefId{
				ActorId:     req.GetHeader().GetActor().GetId(),
				ReferenceId: bcResp.GetBankCustomer().GetVendorCustomerId(),
			}}})
	if te := epifigrpc.RPCError(accRes, err); te != nil {
		logger.Error(ctx, "error in fetching account details", zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	limitAvailable := moneyPb.ParseFloat32(accRes.GetAccount().GetTotalLimit()-accRes.GetAccount().GetTotalOutstanding(), currency.INR.String())
	totalLimit := moneyPb.ParseFloat32(accRes.GetAccount().GetTotalLimit(), currency.INR.String())
	usedLimit := moneyPb.ParseFloat32(accRes.GetAccount().GetTotalOutstanding(), currency.INR.String())
	billedAmount := moneyPb.ParseFloat32(accRes.GetAccount().GetBilledAmount(), currency.INR.String())
	unbilledAmount := moneyPb.ParseFloat32(accRes.GetAccount().GetUnbilledAmount(), currency.INR.String())

	// conversion from float to money gives precision of 10 units
	// ignoring such long precision values
	limitAvailable.Nanos = 0
	totalLimit.Nanos = 0
	usedLimit.Nanos = 0
	billedAmount.Nanos = 0
	unbilledAmount.Nanos = 0

	return &cxCcPb.GetLimitUsageDetailsResponse{
		Status:         rpcPb.StatusOk(),
		TotalLimit:     totalLimit,
		AvailableLimit: limitAvailable,
		UsedLimit:      usedLimit,
		BilledAmount:   billedAmount,
		UnbilledAmount: unbilledAmount,
	}, nil
}

func (s *Service) GetDisputes(ctx context.Context, req *cxCcPb.GetDisputesRequest) (*cxCcPb.GetDisputesResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetDisputesResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetDisputesResponse{}
	disputeRes, err := s.ffAccountingClient.GetAllDisputes(ctx, &ffAccountsPb.GetAllDisputesRequest{ActorId: req.GetHeader().GetActor().GetId()})
	te := epifigrpc.RPCError(disputeRes, err)
	switch {
	case disputeRes.GetStatus().IsRecordNotFound():
		// No record found means no active dispute available
		res.IsDisputeActive = false
		res.Status = rpcPb.StatusOk()
		return res, nil

	case te != nil:
		logger.Error(ctx, "error in fetching disputes raised by the user", zap.Error(te))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}

	switch {
	case req.GetFetchAllTransactions():
		var tableRows []*webui.TableRow
		for _, txn := range disputeRes.GetDisputedTransactions() {
			row, err := getDisputesTableRow(txn)
			if err != nil {
				logger.Error(ctx, "error in creating disputes table row", zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			tableRows = append(tableRows, row)
		}
		res.DisputedTransactions = &webui.Table{
			TableHeaders: disputesTableHeaders,
			TableRows:    tableRows,
			TableName:    "Disputes",
		}
	default:
		res.DisputedTransactionInfo = convertBeDisputedTxnToCxDisputedTxn(req.GetTransactionId(), disputeRes.GetDisputedTransactions())
		if res.DisputedTransactionInfo != nil {
			res.IsDisputeActive = true
		}
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func getDisputesTableRow(txn *ffAccountsPb.DisputedTransaction) (*webui.TableRow, error) {
	if txn == nil {
		return nil, nil
	}

	amount, err := moneyPb.ToString(txn.GetDisputeDetails().GetDisputedAmount(), 2)
	if err != nil {
		return nil, errors.Wrap(err, "error in converting disputed amount to string")

	}

	return &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			disputeIdHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: txn.GetId(),
				},
			},
			disputeTransactionIdHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: txn.GetTransactionId(),
				},
			},
			disputeAccountIdHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: txn.GetAccountId(),
				},
			},
			disputeExtDisputeRefHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: txn.GetExtDisputeRef(),
				},
			},
			disputeDisputeStateHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: beDisputeStateVsDisputeStateString[txn.GetDisputeState()],
				},
			},
			disputeDisputedAtHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: txn.GetDisputedAt().AsTime().Format(time.DateTime),
				},
			},
			disputeUpdatedAtHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: txn.GetUpdatedAt().AsTime().Format(time.DateTime),
				},
			},
			disputeDisputedAmountHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: amount,
				},
			},
			disputeIsActiveHeaderKey: &webui.TableCell{
				ValueV2: &webui.TableCell_StringValue{
					StringValue: "True",
				},
			},
		},
	}, nil
}

func convertBeDisputedTxnToCxDisputedTxn(requestedTxnId string, txns []*ffAccountsPb.DisputedTransaction) *cxCcPb.DisputedTransaction {
	for _, txn := range txns {
		if requestedTxnId != txn.GetTransactionId() {
			continue
		}

		return &cxCcPb.DisputedTransaction{
			Id:             txn.GetId(),
			TransactionId:  txn.GetTransactionId(),
			AccountId:      txn.GetAccountId(),
			ExtDisputeRef:  txn.GetExtDisputeRef(),
			DisputeState:   beDisputeStateVsDisputeStateString[txn.GetDisputeState()],
			DisputedAt:     txn.GetDisputedAt(),
			UpdatedAt:      txn.GetUpdatedAt(),
			DisputedAmount: txn.GetDisputeDetails().GetDisputedAmount(),
		}
	}

	return nil
}

func (s *Service) GetTransactions(ctx context.Context, request *cxCcPb.GetTransactionsRequest) (*cxCcPb.GetTransactionsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, request.GetHeader(), request.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetTransactionsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetTransactionsResponse{}

	accRes, rpcErr := s.ffAccountingClient.GetAccounts(ctx, &ffAccountsPb.GetAccountsRequest{GetBy: &ffAccountsPb.GetAccountsRequest_ActorId{ActorId: request.GetHeader().GetActor().GetId()}})
	if err := epifigrpc.RPCError(accRes, rpcErr); err != nil {
		logger.Error(ctx, "error getting accounts", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	account := accRes.GetAccounts()[0]

	txnRes, err := s.ffAccountingClient.GetTransactionsForATimeInterval(ctx, &ffAccountsPb.GetTransactionsForATimeIntervalRequest{
		ActorId:               request.GetHeader().GetActor().GetId(),
		FromTime:              request.GetFromTime(),
		ToTime:                request.GetToTime(),
		IncludeFrmDeclineTxns: true,
	})
	if te := epifigrpc.RPCError(txnRes, err); te != nil {
		logger.Error(ctx, "error in fetching cc txns within a time interval", zap.Error(te))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}

	res.CardTransactions = s.convertBeTxnToCxTxn(ctx, txnRes.GetTransactions(), request)
	s.populateRewardsInfoForTransaction(ctx, &res.CardTransactions, account)

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) convertBeTxnToCxTxn(ctx context.Context, txns []*ffAccountsPb.CardTransaction, request *cxCcPb.GetTransactionsRequest) []*cxCcPb.CardTransaction {
	resp := make([]*cxCcPb.CardTransaction, 0)
	for _, txn := range txns {
		switch request.SearchByOption.(type) {
		case *cxCcPb.GetTransactionsRequest_Amount:
			if !(request.GetAmount().GetUnits()-10 < txn.GetAmount().GetUnits() && txn.GetAmount().GetUnits() < request.GetAmount().GetUnits()+10) {
				continue
			}
		case *cxCcPb.GetTransactionsRequest_ExternalTransactionId:
			if txn.GetExternalTxnId() != request.GetExternalTransactionId() {
				continue
			}
		}
		txnResp, err := s.ffAccountingClient.GetTransactionWithAdditionalInfo(ctx, &ffAccountsPb.GetTransactionWithAdditionalInfoRequest{
			GetBy: &ffAccountsPb.GetTransactionWithAdditionalInfoRequest_TransactionId{
				TransactionId: txn.GetId()}})
		if te := epifigrpc.RPCError(txnResp, err); te != nil {
			logger.Error(ctx, "error in fetching cc transaction with additional info", zap.Error(te), zap.String(logger.TXN_ID, txn.GetId()))
			// skipping error silently
		}

		resp = append(resp, populateCxTransaction(txn, txnResp.GetCardTransactionWithAdditionalInfo().GetAdditionalInfo()))
	}
	return resp
}

func (s *Service) populateRewardsInfoForTransaction(ctx context.Context, cardTransactions *[]*cxCcPb.CardTransaction, account *ffAccountsPb.CreditAccount) {
	var (
		refIdToProjectionValue = make(map[string]float32)
		refIdToProjectionType  = make(map[string]string)
		offerTypeReadableMap   = map[rewardsPb.RewardOfferType]string{
			rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER:            "1x",
			rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER: "5x",
			rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER:     "Weekday",
			rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER:      "Weekend",
		}
	)
	externalTxnIds := make([]string, 0)
	for _, txn := range *cardTransactions {
		externalTxnIds = append(externalTxnIds, txn.GetExternalTxnId())
	}

	if len(externalTxnIds) < 1 {
		return
	}

	// fetching rewards txns
	getRewardsRes, err := s.projectorClient.GetRewardsProjections(ctx, &projectorPb.GetRewardsProjectionsRequest{
		ActorId: account.GetActorId(),
		Filters: &projectorPb.GetRewardsProjectionsRequest_Filters{
			OfferType: []rewardsPb.RewardOfferType{
				rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
				rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER,
				rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER,
			},
			RefIds: externalTxnIds,
		},
		FetchAggregates: false,
		PageCtxRequest: &rpcPb.PageContextRequest{
			PageSize: uint32(len(externalTxnIds)),
		},
	})
	if te := epifigrpc.RPCError(getRewardsRes, err); te != nil {
		logger.Error(ctx, "failed to fetch reward details", zap.Error(te))
		return
	}

	for _, proj := range getRewardsRes.GetIndividualProjections().GetProjections() {
		if len(proj.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			for _, reward := range proj.GetRewardContributions().GetRewardUnitsWithTypes() {
				if reward.GetRewardUnits() > 0 {
					refIdToProjectionValue[proj.GetRefId()] += reward.GetRewardUnits()
					refIdToProjectionType[proj.GetRefId()] = offerTypeReadableMap[proj.GetOfferType()]
					break
				}
			}
			continue
		}
		if len(proj.GetProjectedOptions().GetRewardUnitsWithTypes()) > 0 {
			for _, reward := range proj.GetProjectedOptions().GetRewardUnitsWithTypes() {
				if reward.GetRewardUnits() > 0 {
					refIdToProjectionValue[proj.GetRefId()] += reward.GetRewardUnits()
					refIdToProjectionType[proj.GetRefId()] = offerTypeReadableMap[proj.GetOfferType()]
					break
				}
			}
			continue
		}
	}

	for _, txn := range *cardTransactions {
		reward, ok := refIdToProjectionValue[txn.GetExternalTxnId()]
		if !ok {
			txn.EligibleForFiCoinRewards = false
			continue
		}
		txn.EligibleForFiCoinRewards = true
		// TODO(akk) - update with accurate values
		txn.TypeOfCoins = refIdToProjectionType[txn.GetExternalTxnId()]
		txn.TotalCoinsEarned = reward
	}
}

func populateCxTransaction(txn *ffAccountsPb.CardTransaction, txnAdditionalInfo *ffAccountsPb.TransactionAdditionalInfo) *cxCcPb.CardTransaction {
	var (
		failureReason    = ""
		settlementStatus = ""
	)

	if txn.GetFailureInfo().GetFailureType() != ffBeAccountsEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_UNSPECIFIED {
		failureReason = txn.GetFailureInfo().GetFailureType().String()
	}

	if txn.GetTransactionAuthorizationStatus() != ffBeAccountsEnumsPb.TransactionAuthorizationStatus_TRANSACTION_AUTHORIZATION_STATUS_UNSPECIFIED {
		settlementStatus = txn.GetTransactionAuthorizationStatus().String()
	}

	return &cxCcPb.CardTransaction{
		Id:                       txn.GetId(),
		AccountId:                txn.GetAccountId(),
		CardId:                   txn.GetCardId(),
		Amount:                   txn.GetAmount(),
		TxnTime:                  txn.GetTxnTime(),
		TxnStatus:                txnStatusVsStatusStr[txn.GetTxnStatus()],
		TxnCategory:              strings.Join(strings.Split(txn.GetTxnCategory().String(), "_")[2:], " "),
		TxnType:                  txnTypeVsTxnTypeStr[txn.GetTxnType()],
		Description:              txn.GetDescription(),
		ExternalTxnId:            txn.GetExternalTxnId(),
		PartnerBank:              ffEnumsPb.Vendor_FEDERAL.String(),
		ToInstrumentNo:           txnAdditionalInfo.GetPiTo(),
		FromInstrumentNo:         txnAdditionalInfo.GetPiFrom(),
		MerchantNameByFi:         txnAdditionalInfo.GetEnrichedBeneficiaryInfo().GetResolvedBeneficiaryName(),
		MerchantNameFromNetwork:  txn.GetBeneficiaryInfo().GetBeneficiaryName(),
		SettlementStatus:         settlementStatus,
		FailureReason:            failureReason,
		VendorExternalTxnId:      txn.GetVendorExtTxnId(),
		RetrievalReferenceNumber: txn.GetRetrievalReferenceNo(),
	}
}

func (s *Service) GetCardBillingInfo(ctx context.Context, request *cxCcPb.GetCardBillingInfoRequest) (*cxCcPb.GetCardBillingInfoResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, request.GetHeader(), request.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardBillingInfoResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetCardBillingInfoResponse{}
	var err error
	billingRes := &ffBeBillingPb.FetchAllBillAndBillPaymentsResponse{}
	accRes := &ffAccountsPb.GetAccountResponse{}
	grp, grpCtx := errgroup.WithContext(ctx)

	grp.Go(func() error {
		billingRes, err = s.ffBillingClient.FetchAllBillAndBillPayments(grpCtx, &ffBeBillingPb.FetchAllBillAndBillPaymentsRequest{ActorId: request.GetHeader().GetActor().GetId()})
		if te := epifigrpc.RPCError(billingRes, err); te != nil {
			return errors.Wrap(te, "error in fetching firefly billing info from firefly")
		}
		return nil
	})
	grp.Go(func() error {
		bcResp, bcErr := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankcust.GetBankCustomerRequest_ActorId{
				ActorId: request.GetHeader().GetActor().GetId(),
			},
		})
		if te := epifigrpc.RPCError(bcResp, bcErr); te != nil {
			return errors.Wrap(te, "error in fetching firefly accounting info from firefly")
		}

		accRes, err = s.ffAccountingClient.GetAccount(grpCtx, &ffAccountsPb.GetAccountRequest{
			GetBy: &ffAccountsPb.GetAccountRequest_ByActorIdAndRefId{
				ByActorIdAndRefId: &ffAccountsPb.GetAccountRequest_ActorIdAndRefId{
					ActorId:     request.GetHeader().GetActor().GetId(),
					ReferenceId: bcResp.GetBankCustomer().GetVendorCustomerId(),
				}}})
		if te := epifigrpc.RPCError(accRes, err); te != nil {
			return errors.Wrap(te, "error in fetching firefly accounting info from firefly")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in CX", zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return res, nil
	}

	billedAmount := moneyPb.ParseFloat32(accRes.GetAccount().GetBilledAmount(), currency.INR.String())
	unBilledAmount := moneyPb.ParseFloat32(accRes.GetAccount().GetUnbilledAmount(), currency.INR.String())

	// conversion from float to money gives precision of 10 units
	// ignoring such long precision values
	billedAmount.Nanos = 0
	unBilledAmount.Nanos = 0

	return &cxCcPb.GetCardBillingInfoResponse{
		Status:          rpcPb.StatusOk(),
		BilledAmount:    billedAmount,
		UnbilledAmount:  unBilledAmount,
		CreditCardBills: convertBeBillToCxBill(billingRes.GetCardBills()),
		Payments:        convertBeBillPaymentToCxBillPayment(billingRes.GetPaymentInfos()),
	}, nil
}

func convertBeBillPaymentToCxBillPayment(payments []*ffBeBillingPb.CreditCardPaymentInfo) []*cxCcPb.BillPaymentInfo {
	resp := make([]*cxCcPb.BillPaymentInfo, 0)
	for _, payment := range payments {
		resp = append(resp, &cxCcPb.BillPaymentInfo{
			Id:            payment.GetId(),
			BillId:        payment.GetBillInfoId(),
			ExternalTxnId: payment.GetExternalTxnId(),
			PaymentDate:   payment.GetPaymentDate(),
			Amount:        payment.GetAmount(),
			PaymentStatus: payment.GetStatus().String(),
		})
	}
	return resp
}

func convertBeBillToCxBill(bills []*ffBeBillingPb.CreditCardBill) []*cxCcPb.CreditCardBill {
	resp := make([]*cxCcPb.CreditCardBill, 0)
	for _, bill := range bills {
		resp = append(resp, &cxCcPb.CreditCardBill{
			Id:                   bill.GetId(),
			AccountId:            bill.GetAccountId(),
			AmountToBePaid:       bill.GetCurrentStatementAmount(),
			BillGenDate:          bill.GetStatementDate(),
			DueDate:              bill.GetSoftDueDate(),
			AvailableLimit:       bill.GetAvailableLimit(),
			MinDue:               bill.GetMinDue(),
			Total_2XRewardsCoins: bill.GetRewardsInfo().GetExtraRewardsConstructInfo().GetProjected_2XRewardsCoins(),
			Total_5XRewardsCoins: bill.GetRewardsInfo().GetExtraRewardsConstructInfo().GetProjected_5XRewardsCoins(),
			Total_1XRewardsCoins: bill.GetRewardsInfo().GetExtraRewardsConstructInfo().GetTotal_1XRewardsCoins(),
			TotalRewardCoins:     bill.GetRewardsInfo().GetExtraRewardsConstructInfo().GetTotalRewardCoins(),
		})
	}
	return resp
}

func (s *Service) GetCurrentCard(ctx context.Context, request *cxCcPb.GetCurrentCardRequest) (*cxCcPb.GetCurrentCardResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, request.GetHeader(), request.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCurrentCardResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetCurrentCardResponse{}
	latestCard, err := s.ffClient.GetCreditCard(ctx, &ffPb.GetCreditCardRequest{
		GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: request.GetHeader().GetActor().GetId()},
	})
	if te := epifigrpc.RPCError(latestCard, err); te != nil {
		logger.Error(ctx, "Error in fetching credit card data from CX", zap.Error(te))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}
	return &cxCcPb.GetCurrentCardResponse{
		Status:      rpcPb.StatusOk(),
		CreditCard:  latestCard.GetCreditCard(),
		CardDetails: convertBeCardDetailsToCxCardDetails(latestCard.GetCreditCard()),
	}, nil
}

func (s *Service) GetAllCards(ctx context.Context, request *cxCcPb.GetAllCardsRequest) (*cxCcPb.GetAllCardsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, request.GetHeader(), request.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetAllCardsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetAllCardsResponse{}
	cardRes, err := s.ffClient.GetAllCards(ctx, &ffPb.GetAllCardsRequest{ActorId: request.GetHeader().GetActor().GetId()})
	if te := epifigrpc.RPCError(cardRes, err); te != nil {
		logger.Error(ctx, "error in fetching card list from CX", zap.Error(te))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}
	cardList := make([]*cxCcPb.CardDetails, 0)
	for _, card := range cardRes.GetCreditCards() {
		cardList = append(cardList, convertBeCardDetailsToCxCardDetails(card))
	}
	return &cxCcPb.GetAllCardsResponse{
		Status:     rpcPb.StatusOk(),
		CreditCard: cardRes.GetCreditCards(),
		CardList:   cardList,
	}, nil
}

func convertBeCardDetailsToCxCardDetails(card *ffPb.CreditCard) *cxCcPb.CardDetails {
	timeNow := datetime.TimestampToDateInLoc(timestamppb.Now(), datetime.IST)
	billCycle := timeNow
	if timeNow.Day < card.GetBasicInfo().GetBillGenDate() {
		// in case that the bill gen date has passed, then the billing cycle will be of
		// previous month
		billCycle.Month = billCycle.Month - 1
		if billCycle.Month == 0 {
			billCycle.Month = 12
		}
	}

	billCycle.Day = card.GetBasicInfo().GetBillGenDate()
	return &cxCcPb.CardDetails{
		CardId:           card.GetId(),
		MaskedCardNumber: card.GetBasicInfo().GetMaskedCardNumber(),
		CardStatus:       beCardStatusVsCardStatusString[card.GetCardState()],
		CreatedAt:        card.GetCreatedAt(),
		UpdatedAt:        card.GetUpdatedAt(),
		PaymentCycle:     datetime.DateToString(billCycle, "January 02", datetime.IST),
		// TODO(aprakash) refactor card category when the kit number assignment issue
		CardCategory:  "VISA Signature",
		CardPinStatus: mapCreditCardStateToString[card.GetCardState()],
		CardForm:      beCardFormVsCardFormString[card.GetCardForm()],
		KitNumber:     card.GetVendorIdentifier(),
	}
}

func (s *Service) GetCardControlDetails(ctx context.Context, req *cxCcPb.GetCardControlDetailsRequest) (*cxCcPb.GetCardControlDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardControlDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetCardControlDetailsResponse{}
	cardResp, err := s.ffClient.GetCreditCard(ctx, &ffPb.GetCreditCardRequest{
		GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: req.GetHeader().GetActor().GetId()},
	})
	if te := epifigrpc.RPCError(cardResp, err); te != nil {
		logger.Error(ctx, "error in fetching card details for the user", zap.Error(te))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}
	controlEnableMap := make(map[ffEnumsPb.CardControlType]bool)
	controlEnableMap[ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM] = cardResp.GetCreditCard().GetControlDetails().GetEcom()
	controlEnableMap[ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM] = cardResp.GetCreditCard().GetControlDetails().GetAtm()
	controlEnableMap[ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS] = cardResp.GetCreditCard().GetControlDetails().GetPos()
	controlEnableMap[ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS] = cardResp.GetCreditCard().GetControlDetails().GetContactless()
	controlEnableMap[ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL] = cardResp.GetCreditCard().GetControlDetails().GetInternational()
	internationalUsageList := make([]*cxCcPb.CardControlDetails, 0)
	domesticUsageList := make([]*cxCcPb.CardControlDetails, 0)
	for _, controlInfo := range cardResp.GetCreditCard().GetCardLimits().GetLimits() {
		controlDetails := &cxCcPb.CardControlDetails{
			CardControlType:   controlInfo.GetControlType(),
			CurrentUsageLimit: controlInfo.GetDailyLimitValue(),
			MaxUsageLimit:     controlInfo.GetMaxDailyLimitValue(),
			IsEnabled:         controlEnableMap[controlInfo.GetControlType()],
		}
		switch controlInfo.GetLocationType() {
		case ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL:
			internationalUsageList = append(internationalUsageList, controlDetails)
		case ffEnumsPb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC:
			domesticUsageList = append(domesticUsageList, controlDetails)
		}
	}
	res.InternationalCardUsageLimits = internationalUsageList
	res.DomesticCardUsageLimits = domesticUsageList
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetCardTrackingInfo(ctx context.Context, req *cxCcPb.GetCardTrackingInfoRequest) (*cxCcPb.GetCardTrackingInfoResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardTrackingInfoResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetCardTrackingInfoResponse{}
	cardReq, err := s.ffClient.GetCardRequestByActorIdAndWorkflow(ctx, &ffPb.GetCardRequestByActorIdAndWorkflowRequest{
		ActorId:             req.GetHeader().GetActor().GetId(),
		CardRequestWorkFlow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_ISSUE_PHYSICAL_CARD,
	})
	if te := epifigrpc.RPCError(cardReq, err); te != nil {
		logger.Error(ctx, "error in fetching card request using card actor id and workflow", zap.Error(te))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}

	cardDeliveryDetails, cardDeliveryDetailsErr := s.ffClient.GetCardDeliveryDetails(ctx, &ffPb.GetCardDeliveryDetailsRequest{
		CardId: cardReq.GetCardRequest().GetCardId(),
	})
	if te := epifigrpc.RPCError(cardDeliveryDetails, cardDeliveryDetailsErr); te != nil {
		logger.Error(ctx, "error in GetCardDeliveryDetails", zap.Error(te), zap.String(logger.CARD_ID, cardReq.GetCardRequest().GetCardId()))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}

	deliveryStatusStr := ""
	isCardDispatched := false
	isCardDelivered := false
	if cardDeliveryDetails.GetShipmentDetails() != nil {
		isCardDispatched = true
		deliveryStatusStr = strings.Join(strings.Split(cardDeliveryDetails.GetPhysicalCardStatus().String(), "_")[3:], " ")
	}
	if cardDeliveryDetails.GetPhysicalCardStatus() == ccEnumsPb.PhysicalCardStatus_PHYSICAL_CARD_STATUS_DELIVERED {
		isCardDelivered = true
	}

	cardReqDetails := cardReq.GetCardRequest().GetRequestDetails().GetCardTrackingRequestDetails()
	return &cxCcPb.GetCardTrackingInfoResponse{
		Status:             rpcPb.StatusOk(),
		Awb:                cardReqDetails.GetAwb(),
		CardCreationDate:   cardReq.GetCardRequest().GetCreatedAt(),
		CourierPartner:     cardReqDetails.GetCourierPartner(),
		CardActivated:      cardReqDetails.GetCardActivated(),
		CardDispatched:     isCardDispatched,
		DeliveryStatus:     deliveryStatusStr,
		CourierPartnerName: cardReqDetails.GetCourierPartnerName(),
		CardDelivered:      isCardDelivered,
	}, nil
}

func (s *Service) GetCreditLimit(ctx context.Context, req *cxCcPb.GetCreditLimitRequest) (*cxCcPb.GetCreditLimitResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCreditLimitResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	ccBeResp, err := s.ccCxClient.GetCreditLimit(ctx, &ccCxPb.GetCreditLimitRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(ccBeResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching credit limits from firefly server", zap.Error(te))
		return &cxCcPb.GetCreditLimitResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch credit limits"),
		}, nil
	}

	res := &cxCcPb.GetCreditLimitResponse{
		Status:         rpcPb.StatusOk(),
		AvailableLimit: ccBeResp.GetAvailableLimit(),
		LimitUtilized:  ccBeResp.GetLimitUtilized(),
		TotalLimit:     ccBeResp.GetAvailableLimit() + ccBeResp.GetLimitUtilized(),
	}
	return res, nil
}

func (s *Service) GetOutstandingDues(ctx context.Context, req *cxCcPb.GetOutstandingDuesRequest) (*cxCcPb.GetOutstandingDuesResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetOutstandingDuesResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	ccBeResp, err := s.ccCxClient.GetOutstandingDues(ctx, &ccCxPb.GetOutstandingDuesRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(ccBeResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching dues from firefly server", zap.Error(te))
		return &cxCcPb.GetOutstandingDuesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch dues"),
		}, nil
	}

	res := &cxCcPb.GetOutstandingDuesResponse{
		Status:               rpcPb.StatusOk(),
		TotalOutstandingDues: ccBeResp.GetTotalOutstandingDues(),
		DueDate:              ccBeResp.GetDueDate(),
	}
	return res, nil
}

func (s *Service) InitiateCardAction(ctx context.Context, req *cxCcPb.InitiateCardActionRequest) (*cxCcPb.InitiateCardActionResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.InitiateCardActionResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.InitiateCardActionResponse{}
	switch req.GetCardActionType() {
	case cxCcPb.CardActionType_CARD_ACTION_TYPE_FREEZE_UNFREEZE_CARD:
		cxToBeRequestTypeMap := map[cxCcPb.CardRequestType]ccEnumsPb.CardRequestType{
			cxCcPb.CardRequestType_CARD_REQUEST_TYPE_FREEZE_CARD:   ccEnumsPb.CardRequestType_REQUEST_TYPE_FREEZE_CARD,
			cxCcPb.CardRequestType_CARD_REQUEST_TYPE_UNFREEZE_CARD: ccEnumsPb.CardRequestType_REQUEST_TYPE_UNFREEZE_CARD,
		}
		requestType, ok := cxToBeRequestTypeMap[req.GetFreezeUnfreezeCardActionPayload().GetCardRequestType()]
		if !ok {
			logger.Error(ctx, "invalid request type for freeze unfreeze card", zap.String(logger.REQUEST_TYPE,
				req.GetFreezeUnfreezeCardActionPayload().GetCardRequestType().String()))
			return &cxCcPb.InitiateCardActionResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to initialize card action"),
			}, nil
		}

		ccBeResp, err := s.ccCxClient.InitiateCardAction(ctx, &ccCxPb.InitiateCardActionRequest{
			ActorId:        req.GetHeader().GetActor().GetId(),
			CardActionType: ccCxEnumsPb.CardActionType_CARD_ACTION_TYPE_FREEZE_UNFREEZE_CARD,
			RequestPayload: &ccCxPb.InitiateCardActionRequest_FreezeUnfreezeCardActionPayload{
				FreezeUnfreezeCardActionPayload: &ccCxPb.FreezeUnfreezeCardActionPayload{
					RequestType: requestType,
					Reason:      req.GetFreezeUnfreezeCardActionPayload().GetReason(),
				},
			},
		})
		if te := epifigrpc.RPCError(ccBeResp, err); te != nil {
			cxLogger.Error(ctx, "error while initializing freeze/unfreeze card action at firefly server", zap.Error(te))
			return &cxCcPb.InitiateCardActionResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to initialize card action"),
			}, nil
		}

		res.CardRequestId = ccBeResp.GetCardRequestId()

	case cxCcPb.CardActionType_CARD_ACTION_TYPE_REISSUE_CARD:
		ccBeResp, err := s.ccCxClient.InitiateCardAction(ctx, &ccCxPb.InitiateCardActionRequest{
			ActorId:        req.GetHeader().GetActor().GetId(),
			CardActionType: ccCxEnumsPb.CardActionType_CARD_ACTION_TYPE_REISSUE_CARD,
			RequestPayload: &ccCxPb.InitiateCardActionRequest_ReissueCardActionPayload{
				ReissueCardActionPayload: &ccCxPb.ReissueCardActionPayload{
					Reason: req.GetReissueCardActionPayload().GetReason(),
				},
			},
		})
		if te := epifigrpc.RPCError(ccBeResp, err); te != nil {
			cxLogger.Error(ctx, "error while initializing reissue card action at firefly server", zap.Error(te))
			return &cxCcPb.InitiateCardActionResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to initialize card action"),
			}, nil
		}

		res.CardRequestId = ccBeResp.GetCardRequestId()

	case cxCcPb.CardActionType_CARD_ACTION_TYPE_PROCESS_DISPUTE:
		beDisputePayload := getBEDisputeCardActionPayload(req.GetDisputeCardActionPayload())
		ccBeResp, err := s.ccCxClient.InitiateCardAction(ctx, &ccCxPb.InitiateCardActionRequest{
			ActorId:        req.GetHeader().GetActor().GetId(),
			CardActionType: ccCxEnumsPb.CardActionType_CARD_ACTION_TYPE_PROCESS_DISPUTE,
			RequestPayload: &ccCxPb.InitiateCardActionRequest_DisputeCardActionPayload{
				DisputeCardActionPayload: beDisputePayload,
			},
		})
		if te := epifigrpc.RPCError(ccBeResp, err); te != nil {
			cxLogger.Error(ctx, "error while initializing process dispute action at firefly server", zap.Error(te))
			return &cxCcPb.InitiateCardActionResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to initialize card action"),
			}, nil
		}

		res.DisputeMessage = "Dispute Raised Successfully!"
		res.CardRequestId = ccBeResp.GetCardRequestId()
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func getBEDisputeCardActionPayload(disputePayload *cxCcPb.DisputeCardActionPayload) *ccCxPb.DisputeCardActionPayload {
	cxToFfDisputeTypeMap := map[cxCcPb.DisputeType]ccEnumsPb.DisputeType{
		cxCcPb.DisputeType_DISPUTE_TYPE_TRANSACTION: ccEnumsPb.DisputeType_DISPUTE_TYPE_TRANSACTION,
	}
	disputeType := cxToFfDisputeTypeMap[disputePayload.GetDisputeType()]
	disputeReason := ""
	disputeDescription := ""

	for _, answer := range disputePayload.GetAnswers() {
		switch {
		case answer.GetQuestionCode() == "CC-05":
			disputeDescription = answer.GetAnswer()
		case answer.GetQuestionCode() == "CC-03":
			disputeReason = fmt.Sprintf("Yes, %s", answer.GetAnswer())
		case answer.GetQuestionCode() == "CC-04":
			disputeReason = fmt.Sprintf("No, %s", answer.GetAnswer())
		}
	}

	return &ccCxPb.DisputeCardActionPayload{
		TxnId:       disputePayload.GetTxnId(),
		Amount:      disputePayload.GetAmount(),
		Reason:      disputeReason,
		Description: disputeDescription,
		DisputeType: disputeType,
	}
}

func (s *Service) GetNextDisputeQuestion(ctx context.Context, request *cxCcPb.GetNextDisputeQuestionRequest) (*cxCcPb.GetNextDisputeQuestionResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, request.GetHeader(), request.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetNextDisputeQuestionResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetNextDisputeQuestionResponse{}
	questionCode := request.GetQuestionCode()

	// Get first/root question
	if strings.Compare(questionCode, "") == 0 {
		questionCode = "CC-01"
	}
	ccBeResp, err := s.ffAccountingClient.GetNextDisputeQuestion(ctx, &ffAccountsPb.GetNextDisputeQuestionRequest{
		QuestionCode: questionCode,
		Answer:       request.GetAnswer(),
	})
	if te := epifigrpc.RPCError(ccBeResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching next question from firefly server", zap.Error(te))
		res.Status = rpcPb.StatusInternalWithDebugMsg("failed to get next question")
		return res, nil
	}

	res.QuestionMeta = ccBeResp.GetQuestionMeta()
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetPaginatedTransactionDetails(ctx context.Context, req *cxCcPb.GetPaginatedTransactionDetailsRequest) (*cxCcPb.GetPaginatedTransactionDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetPaginatedTransactionDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	var (
		res       = &cxCcPb.GetPaginatedTransactionDetailsResponse{}
		ccTxnResp = &ffAccountsPb.GetPaginatedTxnsResponse{}
		err       error
	)

	switch req.GetRequestType() {
	case cxCcPb.TransactionRequestType_TRANSACTION_REQUEST_TYPE_LAST_TEN:
		ccTxnResp, err = s.ffAccountingClient.GetPaginatedTxns(ctx, &ffAccountsPb.GetPaginatedTxnsRequest{
			Identifier: &ffAccountsPb.GetPaginatedTxnsRequest_ActorId{
				ActorId: req.GetHeader().GetActor().GetId(),
			},
			StartTimestamp:        timestamppb.Now(),
			PageSize:              10,
			Descending:            true,
			IncludeFrmDeclineTxns: true,
		})

	case cxCcPb.TransactionRequestType_TRANSACTION_REQUEST_TYPE_FIRST_TEN:
		ccTxnResp, err = s.ffAccountingClient.GetPaginatedTxns(ctx, &ffAccountsPb.GetPaginatedTxnsRequest{
			Identifier: &ffAccountsPb.GetPaginatedTxnsRequest_ActorId{
				ActorId: req.GetHeader().GetActor().GetId(),
			},
			StartTimestamp:        timestamppb.New(time.Now().AddDate(0, -1, 0)),
			PageSize:              10,
			IncludeFrmDeclineTxns: true,
		})
	}
	if te := epifigrpc.RPCError(ccTxnResp, err); te != nil {
		switch {
		case ccTxnResp.GetStatus().IsRecordNotFound():
			res.Status = rpcPb.StatusOk()
			return res, nil

		default:
			cxLogger.Error(ctx, "error while fetching transactions from firefly accounting client", zap.Error(te))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	res.CardTransactions = make([]*cxCcPb.CardTransaction, 0)
	for _, txn := range ccTxnResp.GetTransactionWithAdditionalInfoList() {
		res.CardTransactions = append(res.CardTransactions, populateCxTransaction(txn.GetTransaction(), txn.GetAdditionalInfo()))
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetCardActionStatus(ctx context.Context, req *cxCcPb.GetCardActionStatusRequest) (*cxCcPb.GetCardActionStatusResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardActionStatusResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	ccBeResp, err := s.ccCxClient.GetCardActionStatus(ctx, &ccCxPb.GetCardActionStatusRequest{
		CardRequestId: req.GetCardRequestId(),
	})
	if te := epifigrpc.RPCError(ccBeResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching card action status from firefly server", zap.Error(te))
		return &cxCcPb.GetCardActionStatusResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch card action status"),
		}, nil
	}

	res := &cxCcPb.GetCardActionStatusResponse{
		Status:            rpcPb.StatusOk(),
		CardRequestStatus: ccBeResp.GetCardRequestStatus().String(),
	}
	return res, nil
}
