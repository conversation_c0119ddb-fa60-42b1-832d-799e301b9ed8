// nolint:gosec,funlen
package firefly

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxCcPb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

const (
	elgTableName          = "Credit Card Eligibility"
	elgLeftKey            = "Eligible for CC"
	elgCardProgramKey     = "elgCardProgramKey"
	elgCreditLimitKey     = "elgCreditLimitKey"
	elgOfferValidityKey   = "elgOfferValidityKey"
	elgIssuerBankKey      = "elgIssuerBankKey"
	elgCardProgramValue   = "Card Program"
	elgCreditLimitValue   = "Credit Limit"
	elgOfferValidityValue = "Offer Validity"
	elgIssuerBankValue    = "Issuer Bank"
)

var (
	keyToLabelMap = map[string]string{
		elgCardProgramKey:   elgCardProgramValue,
		elgCreditLimitKey:   elgCreditLimitValue,
		elgOfferValidityKey: elgOfferValidityValue,
		elgIssuerBankKey:    elgIssuerBankValue,
	}
	elgKeysOrder = []string{
		elgCardProgramKey,
		elgOfferValidityKey,
		elgIssuerBankKey,
	}
)

func (s *Service) GetCardEligibilityDetails(ctx context.Context, req *cxCcPb.GetCardEligibilityDetailsRequest) (*cxCcPb.GetCardEligibilityDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardEligibilityDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetCardEligibilityDetailsResponse{}

	accRes, rpcErr := s.ffAccountingClient.GetAccounts(ctx, &ffAccPb.GetAccountsRequest{GetBy: &ffAccPb.GetAccountsRequest_ActorId{ActorId: req.GetHeader().GetActor().GetId()}})
	if err := epifigrpc.RPCError(accRes, rpcErr); err != nil && !accRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error getting accounts", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if len(accRes.GetAccounts()) > 0 {
		account := accRes.GetAccounts()[0]
		res.IsCreditCardExists = true
		res.CardProgramType = mapBeCardProgramToFeCardProgram[account.GetCardProgram().GetCardProgramType()]
		res.CardProgramName = ffPkg.GetCardNameFromProgram(account.GetCardProgram())
		return res, nil
	}

	offerRes, rpcErr := s.ffClient.GetCreditCardOffers(ctx, &ffPb.GetCreditCardOffersRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		Vendor:  ffEnumsPb.Vendor_FEDERAL,
	})
	if err := epifigrpc.RPCError(offerRes, rpcErr); err != nil && !offerRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching credit card offers", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	elgRes, elgErr := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
		ActorId:             req.GetHeader().GetActor().GetId(),
		CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
	})
	if err := epifigrpc.RPCError(elgRes, elgErr); err != nil && !elgRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching credit card eligibility request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	var elgValue string
	switch {
	case len(offerRes.GetOffers()) > 0:
		elgValue = "YES"
	case elgRes.GetCardRequest().GetStatus() == ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS:
		elgValue = "IN PROGRESS"
	default:
		elgValue = "NO"
	}

	offersTable := &webui.Table{
		TableHeaders: getOfferTableHeaders(),
		TableName:    "Offers",
	}
	if len(offerRes.GetOffers()) > 0 {
		offersTable.TableRows = getOfferTableRows(offerRes.GetOffers()[0])
	}
	res.Status = rpcPb.StatusOk()
	res.OffersTable = offersTable
	res.EligibilityTable = &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				HeaderKey: eligibilityKey,
			},
			{
				HeaderKey: eligibilityValue,
			},
		},
		TableRows: []*webui.TableRow{
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					eligibilityKey: {
						Value: elgLeftKey,
					},
					eligibilityValue: {
						Value: elgValue,
					},
				},
			},
		},
		Actions:   nil,
		TableName: elgTableName,
	}
	return res, nil
}

func getOfferTableHeaders() []*webui.TableHeader {
	var tableHeaders []*webui.TableHeader
	for _, key := range elgKeysOrder {
		tableHeaders = append(tableHeaders, &webui.TableHeader{
			HeaderKey: key,
			Label:     keyToLabelMap[key],
			IsVisible: true,
		})
	}
	return tableHeaders
}

func getOfferTableRows(ccOffer *ffPb.CreditCardOffer) []*webui.TableRow {
	headerKeyCellMap := make(map[string]*webui.TableCell, 0)
	for _, key := range elgKeysOrder {
		switch key {
		case elgCardProgramKey:
			headerKeyCellMap[elgCardProgramKey] = &webui.TableCell{
				Value: cardProgramTypeToProgramName[ccOffer.GetCardProgram().GetCardProgramType()],
			}
		case elgCreditLimitKey:
			headerKeyCellMap[elgCreditLimitKey] = &webui.TableCell{
				Value: moneyPkg.ToDisplayStringInIndianFormat(ccOffer.GetOfferConstraints().GetLimit(), 0, true),
			}
		case elgOfferValidityKey:
			headerKeyCellMap[elgOfferValidityKey] = &webui.TableCell{
				Value: datetime.TimestampToString(ccOffer.GetValidTill(), datetime.DATE_LAYOUT_YYYYMMDD, datetime.IST),
			}
		case elgIssuerBankKey:
			headerKeyCellMap[elgIssuerBankKey] = &webui.TableCell{
				Value: ccOffer.GetVendor().String(),
			}
		default:
		}
	}

	return []*webui.TableRow{
		{
			HeaderKeyCellMap: headerKeyCellMap,
		},
	}
}
