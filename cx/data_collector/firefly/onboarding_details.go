//nolint:all
package firefly

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"strconv"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	bcPb "github.com/epifi/gamma/api/bankcust"
	cxCcPb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	onbHeaderLeft       = "onbHeaderLeft"
	onbHeaderRight      = "onbHeaderRight"
	onbUserType         = "Direct To CC"
	onbKycLevel         = "Kyc level"
	onbSelectedCard     = "Selected Card"
	onbCurrentStage     = "Current Stage"
	onbNextStage        = "Next Stage"
	eligibilityKey      = "eligibilityKey"
	eligibilityValue    = "eligibilityValue"
	onbDetailsTableName = "Onboarding Details"
)

var (
	cardProgramTypeToProgramName = map[types.CardProgramType]string{
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED: "AMPLIFI",
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:   "SIMPLIFI",
	}
	onbKeysOrder = []string{
		onbHeaderLeft,
		onbHeaderRight,
	}
	onbRowsOrder = []string{
		onbUserType,
		onbKycLevel,
		onbSelectedCard,
		onbCurrentStage,
		//onbNextStage,
	}
)

func (s *Service) GetCardOnboardingDetailsV2(ctx context.Context, req *cxCcPb.GetCardOnboardingDetailsV2Request) (*cxCcPb.GetCardOnboardingDetailsV2Response, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetCardOnboardingDetailsV2Response{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	var (
		res             = &cxCcPb.GetCardOnboardingDetailsV2Response{}
		cardRequest     *ffPb.CardRequest
		cardReqStages   []*ffPb.CardRequestStage
		cardReqResponse *ffPb.GetCardRequestAndCardRequestStageResponse
	)

	onbRes, rpcErr := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
		ActorId:             req.GetHeader().GetActor().GetId(),
		CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	if err := epifigrpc.RPCError(onbRes, rpcErr); err != nil && !onbRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching credit card onboarding request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if onbRes.GetStatus().IsRecordNotFound() {
		elgRes, rpcErr := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
			ActorId:             req.GetHeader().GetActor().GetId(),
			CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
		})
		if err := epifigrpc.RPCError(elgRes, rpcErr); err != nil {
			logger.Error(ctx, "error fetching credit card eligibility request", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		cardReqResponse = elgRes
	} else {
		cardReqResponse = onbRes
	}

	cardRequest = cardReqResponse.GetCardRequest()
	cardReqStages = cardReqResponse.GetCardRequestStages()

	bcRes, rpcErr := s.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: req.GetHeader().GetActor().GetId()},
	})
	if err := epifigrpc.RPCError(bcRes, rpcErr); err != nil && !bcRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error getting bank customer", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	rows, ctas := getOnbDetailsTableRows(bcRes.GetBankCustomer(), cardRequest, cardReqStages)

	onbDetailsTable := &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				HeaderKey: onbHeaderLeft,
			},
			{
				HeaderKey: onbHeaderRight,
			},
		},
		TableRows: rows,
		Actions:   ctas,
		TableName: onbDetailsTableName,
	}

	res.Status = rpcPb.StatusOk()
	res.OnboardingDetailsTable = onbDetailsTable
	return res, nil
}

func getOnbDetailsTableRows(bc *bcPb.BankCustomer, cardRequest *ffPb.CardRequest, cardRequestStages []*ffPb.CardRequestStage) ([]*webui.TableRow, []*webui.CTA) {
	directToCc := false
	if cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING && cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramOrigin() == types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE {
		directToCc = true
	}
	if cardRequest.GetWorkflow() == ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK {
		directToCc = !cardRequest.GetRequestDetails().GetRealtimeEligibilityDetails().GetIsFiSavingsAccountHolder()
	}
	var (
		rows []*webui.TableRow
		ctas []*webui.CTA
	)

	for _, key := range onbRowsOrder {
		headerKeyCellMap := make(map[string]*webui.TableCell, 0)
		switch key {
		case onbUserType:
			headerKeyCellMap[onbHeaderLeft] = &webui.TableCell{
				Value: onbUserType,
			}
			headerKeyCellMap[onbHeaderRight] = &webui.TableCell{
				Value: strconv.FormatBool(directToCc),
			}
			rows = append(rows, &webui.TableRow{HeaderKeyCellMap: headerKeyCellMap})
			ctas = append(ctas, &webui.CTA{})
		case onbKycLevel:
			headerKeyCellMap[onbHeaderLeft] = &webui.TableCell{
				Value: onbKycLevel,
			}
			headerKeyCellMap[onbHeaderRight] = &webui.TableCell{
				Value: bc.GetKycInfo().GetKycLevel().String(),
			}
			rows = append(rows, &webui.TableRow{HeaderKeyCellMap: headerKeyCellMap})
			ctas = append(ctas, &webui.CTA{
				Label: "View details",
				Deeplink: &webui.Deeplink{
					ScreenName: webui.ScreenName_SCREEN_NAME_CREDIT_CARD_KYC_DETAILS,
					ScreenData: nil,
				},
			})
		case onbCurrentStage:
			headerKeyCellMap[onbHeaderLeft] = &webui.TableCell{
				Value: onbCurrentStage,
			}
			headerKeyCellMap[onbHeaderRight] = &webui.TableCell{
				Value: cardRequestStages[0].GetStage().String(),
			}
			rows = append(rows, &webui.TableRow{HeaderKeyCellMap: headerKeyCellMap})
			ctas = append(ctas, &webui.CTA{
				Label: "View details",
				Deeplink: &webui.Deeplink{
					ScreenName: webui.ScreenName_SCREEN_NAME_CREDIT_CARD_CURRENT_STAGE_DETAILS,
					ScreenData: nil,
				},
			})
		case onbNextStage:
			headerKeyCellMap[onbHeaderLeft] = &webui.TableCell{
				Value: onbNextStage,
			}
			headerKeyCellMap[onbHeaderRight] = &webui.TableCell{
				Value: cardRequestStages[0].GetStage().String(),
			}
			rows = append(rows, &webui.TableRow{HeaderKeyCellMap: headerKeyCellMap})
			ctas = append(ctas, &webui.CTA{
				Label: "View details",
				Deeplink: &webui.Deeplink{
					ScreenName: webui.ScreenName_SCREEN_NAME_CREDIT_CARD_NEXT_STAGE_DETAILS,
					ScreenData: nil,
				},
			})
		}
	}
	return rows, ctas
}
