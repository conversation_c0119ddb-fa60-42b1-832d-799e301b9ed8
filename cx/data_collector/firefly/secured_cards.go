package firefly

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxCcPb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	depositPb "github.com/epifi/gamma/api/deposit"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/typesv2/webui"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

const (
	fdStatusHeaderKey         = "fdStatusHeaderKey"
	fdAmountHeaderKey         = "fdAmountHeaderKey"
	fdInterestRateHeaderKey   = "fdInterestRateHeaderKey"
	fdNumberHeaderKey         = "fdNumberHeaderKey"
	fdCreationDateHeaderKey   = "fdCreationDateHeaderKey"
	fdRenewalHeaderDateKey    = "fdRenewalHeaderDate"
	fdStatusHeaderValue       = "FD Status"
	fdAmountHeaderValue       = "FD amount"
	fdInterestRateHeaderValue = "FD interest rate"
	fdNumberHeaderValue       = "FD number"
	fdCreationDateHeaderValue = "FD creation date"
	fdRenewalHeaderDateValue  = "FD renewal date"
	fdDetailsTableName        = "FD Details"
)

var (
	keyToLabel = map[string]string{
		fdStatusHeaderKey:       fdStatusHeaderValue,
		fdAmountHeaderKey:       fdAmountHeaderValue,
		fdInterestRateHeaderKey: fdInterestRateHeaderValue,
		fdNumberHeaderKey:       fdNumberHeaderValue,
		fdCreationDateHeaderKey: fdCreationDateHeaderValue,
		fdRenewalHeaderDateKey:  fdRenewalHeaderDateValue,
	}

	depositAccountStateToCxState = map[depositPb.DepositState]string{
		depositPb.DepositState_STATE_UNSPECIFIED: "Unknown",
		depositPb.DepositState_IN_PROGRESS:       "In Progress",
		depositPb.DepositState_CREATED:           "Created",
		depositPb.DepositState_PRECLOSE_PENDING:  "Pending",
		depositPb.DepositState_PRECLOSED:         "Pre closed",
		depositPb.DepositState_CLOSED:            "Closed",
		depositPb.DepositState_FAILED:            "Failed",
		depositPb.DepositState_PRECLOSE_FAILED:   "Pre closure Failed",
	}

	keysOrder = []string{
		fdStatusHeaderKey,
		fdAmountHeaderKey,
		fdInterestRateHeaderKey,
		fdNumberHeaderKey,
		fdCreationDateHeaderKey,
		fdRenewalHeaderDateKey,
	}
)

func (s *Service) GetFdDetails(ctx context.Context, req *cxCcPb.GetFdDetailsRequest) (*cxCcPb.GetFdDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetFdDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	var (
		res = &cxCcPb.GetFdDetailsResponse{}
	)

	accResp, err := s.ffAccountingClient.GetAccounts(ctx, &ffAccountsPb.GetAccountsRequest{GetBy: &ffAccountsPb.GetAccountsRequest_ActorId{ActorId: req.GetHeader().GetActor().GetId()}})
	if te := epifigrpc.RPCError(accResp, err); te != nil {
		cxLogger.Error(ctx, "error in fetching account details", zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	account := accResp.GetAccounts()[0]

	if account.GetCollateralDetails().GetCollateralId() == "" {
		cxLogger.Error(ctx, "no collateral id found in account details", zap.String(logger.ACCOUNT_ID, account.GetId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	depositResp, err := s.depositClient.GetById(ctx, &depositPb.GetByIdRequest{Id: account.GetCollateralDetails().GetCollateralId()})
	if te := epifigrpc.RPCError(depositResp, err); te != nil {
		cxLogger.Error(ctx, "error in fetching deposit account details", zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	fdDetailsTable := &webui.Table{
		TableHeaders: GetFdTableHeaders(),
		TableRows:    GetFdTableRows(depositResp.GetAccount()),
		TableName:    fdDetailsTableName,
	}

	res.Status = rpcPb.StatusOk()
	res.FdDetails = fdDetailsTable
	return res, nil
}

func GetFdTableHeaders() []*webui.TableHeader {
	var tableHeaders []*webui.TableHeader
	for _, key := range keysOrder {
		tableHeaders = append(tableHeaders, &webui.TableHeader{
			HeaderKey: key,
			Label:     keyToLabel[key],
			IsVisible: true,
		})
	}
	return tableHeaders
}

func GetFdTableRows(depositAccount *depositPb.DepositAccount) []*webui.TableRow {
	headerKeyCellMap := make(map[string]*webui.TableCell, 0)
	for _, key := range keysOrder {
		switch key {
		case fdStatusHeaderKey:
			headerKeyCellMap[fdStatusHeaderKey] = &webui.TableCell{
				Value: depositAccountStateToCxState[depositAccount.GetState()],
			}
		case fdAmountHeaderKey:
			headerKeyCellMap[fdAmountHeaderKey] = &webui.TableCell{
				Value: moneyPkg.ToDisplayStringInIndianFormat(depositAccount.GetPrincipalAmount(), 0, true),
			}
		case fdInterestRateHeaderKey:
			headerKeyCellMap[fdInterestRateHeaderKey] = &webui.TableCell{
				Value: depositAccount.GetInterestRate(),
			}
		case fdNumberHeaderKey:
			headerKeyCellMap[fdNumberHeaderKey] = &webui.TableCell{
				Value: depositAccount.GetAccountNumber(),
			}
		case fdCreationDateHeaderKey:
			headerKeyCellMap[fdCreationDateHeaderKey] = &webui.TableCell{
				Value: datetime.DateToDDMMYYYY(datetime.TimestampToDateInLoc(depositAccount.GetCreatedAt(), datetime.IST)),
			}
		case fdRenewalHeaderDateKey:
			headerKeyCellMap[fdRenewalHeaderDateKey] = &webui.TableCell{
				Value: datetime.DateToDDMMYYYY(datetime.TimestampToDateInLoc(depositAccount.GetMaturityDate(), datetime.IST)),
			}
		default:
		}
	}

	return []*webui.TableRow{
		{
			HeaderKeyCellMap: headerKeyCellMap,
		},
	}
}
