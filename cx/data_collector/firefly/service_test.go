package firefly

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/enums"
	mockffPb "github.com/epifi/gamma/api/firefly/mocks"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
)

func TestService_GetRenewalBillAndDueDate(t *testing.T) {
	t.Parallel()
	var (
		renewalDate1	= timestamp.New(time.Date(2024, 6, 18, 0, 0, 0, 0, datetimePkg.IST)).AsTime().In(datetimePkg.IST)
		renewalDueDate1	= renewalDate1.AddDate(0, 0, 18)

		renewalDate2	= timestamp.New(time.Date(2024, 5, 18, 0, 0, 0, 0, datetimePkg.IST)).AsTime().In(datetimePkg.IST)
		renewalDueDate2	= renewalDate2.AddDate(0, 0, 18)
	)
	logger.Init("test")
	type req struct {
		actorId			string
		accountCreationDate	*timestamp.Timestamp
		year			int
	}

	type response struct {
		renewalDate		*time.Time
		renewalBillDueDate	*time.Time
	}
	tests := []struct {
		name		string
		req		*req
		response	*response
		wantErr		bool
		mockFunc	func(ffClient *mockffPb.MockFireflyClient)
	}{
		{
			name:	"internal response from get credit card rpc",
			req: &req{
				actorId: "test-actor",
			},
			response:	nil,
			wantErr:	true,
			mockFunc: func(ffClient *mockffPb.MockFireflyClient) {
				ffClient.EXPECT().GetCreditCard(gomock.Any(), &firefly.GetCreditCardRequest{
					GetBy: &firefly.GetCreditCardRequest_ActorId{
						ActorId: "test-actor",
					},
					SelectFieldMasks: []enums.CreditCardFieldMask{
						enums.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
					},
				}).Return(&firefly.GetCreditCardResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
		},
		{
			name:	"success response for account creation date after bill gen date",
			req: &req{
				actorId:		"test-actor",
				accountCreationDate:	timestamp.New(time.Date(2023, 5, 19, 0, 0, 0, 0, datetimePkg.IST)),
				year:			1,
			},
			response: &response{
				renewalDate:		&renewalDate1,
				renewalBillDueDate:	&renewalDueDate1,
			},
			wantErr:	false,
			mockFunc: func(ffClient *mockffPb.MockFireflyClient) {
				ffClient.EXPECT().GetCreditCard(gomock.Any(), &firefly.GetCreditCardRequest{
					GetBy: &firefly.GetCreditCardRequest_ActorId{
						ActorId: "test-actor",
					},
					SelectFieldMasks: []enums.CreditCardFieldMask{
						enums.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
					},
				}).Return(&firefly.GetCreditCardResponse{
					Status:	rpc.StatusOk(),
					CreditCard: &firefly.CreditCard{
						BasicInfo: &firefly.BasicInfo{
							BillGenDate: 18,
						},
					},
				}, nil)
			},
		},
		{
			name:	"success response for account creation date before bill gen date",
			req: &req{
				actorId:		"test-actor",
				accountCreationDate:	timestamp.New(time.Date(2023, 5, 15, 0, 0, 0, 0, datetimePkg.IST)),
				year:			1,
			},
			response: &response{
				renewalDate:		&renewalDate2,
				renewalBillDueDate:	&renewalDueDate2,
			},
			wantErr:	false,
			mockFunc: func(ffClient *mockffPb.MockFireflyClient) {
				ffClient.EXPECT().GetCreditCard(gomock.Any(), &firefly.GetCreditCardRequest{
					GetBy: &firefly.GetCreditCardRequest_ActorId{
						ActorId: "test-actor",
					},
					SelectFieldMasks: []enums.CreditCardFieldMask{
						enums.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
					},
				}).Return(&firefly.GetCreditCardResponse{
					Status:	rpc.StatusOk(),
					CreditCard: &firefly.CreditCard{
						BasicInfo: &firefly.BasicInfo{
							BillGenDate: 18,
						},
					},
				}, nil)
			},
		},
	}

	for _, tt := range tests {
		ctr := gomock.NewController(t)
		ffClient := mockffPb.NewMockFireflyClient(ctr)
		s := &Service{
			ffClient: ffClient,
		}

		if tt.mockFunc != nil {
			tt.mockFunc(ffClient)
		}

		renewalDate, renewalBillDueDate, err := s.getRenewalBillAndDueDate(context.Background(), tt.req.actorId, tt.req.accountCreationDate, tt.req.year)
		if tt.wantErr {
			require.Error(t, err)
		}

		if tt.response == nil {
			assert.Nil(t, renewalBillDueDate)
			assert.Nil(t, renewalDate)
		} else {
			assert.Equal(t, tt.response.renewalBillDueDate, renewalBillDueDate)
			assert.Equal(t, tt.response.renewalDate, renewalDate)
		}
	}
}
