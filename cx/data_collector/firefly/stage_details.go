//nolint:all
package firefly

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxCcPb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	stageHeaderLeft   = "stageLeft"
	stageHeaderRight  = "stageRight"
	currentStageKey   = "Current Stage"
	stageStatus       = "Status"
	stageSubStatus    = "RCA"
	stageTroubleshoot = "Troubleshooting advice"
	stageLeftLabel    = "Next stage"
	stageRightLabel   = "Instructions"
	nextStageKey      = "Next Stage"
)

func (s *Service) GetStageDetails(ctx context.Context, req *cxCcPb.GetStageDetailsRequest) (*cxCcPb.GetStageDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetStageDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetStageDetailsResponse{}

	var cardReqResponse *ffPb.GetCardRequestAndCardRequestStageResponse

	onbRes, rpcErr := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
		ActorId:             req.GetHeader().GetActor().GetId(),
		CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	if err := epifigrpc.RPCError(onbRes, rpcErr); err != nil && !onbRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching credit card onboarding request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if onbRes.GetStatus().IsRecordNotFound() {
		elgRes, rpcErr := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
			ActorId:             req.GetHeader().GetActor().GetId(),
			CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
		})
		if err := epifigrpc.RPCError(elgRes, rpcErr); err != nil {
			logger.Error(ctx, "error fetching credit card eligibility request", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		cardReqResponse = elgRes
	} else {
		cardReqResponse = onbRes
	}

	var status, substatus, troubleshootDetails, stage string
	currentStage := cardReqResponse.GetCardRequestStages()[0]

	status = currentStage.GetStatus().String()
	substatus = currentStage.GetSubStatus().String()
	troubleshootDetails = ""
	stage = currentStage.GetStage().String()

	res.Status = rpcPb.StatusOk()
	res.StageDetailsTable = &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				HeaderKey: stageHeaderLeft,
			},
			{
				HeaderKey: stageHeaderRight,
			},
		},
		TableRows: []*webui.TableRow{
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					stageHeaderLeft: {
						Value: currentStageKey,
					},
					stageHeaderRight: {
						Value: stage,
					},
				},
			},
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					stageHeaderLeft: {
						Value: stageStatus,
					},
					stageHeaderRight: {
						Value: status,
					},
				},
			},
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					stageHeaderLeft: {
						Value: stageSubStatus,
					},
					stageHeaderRight: {
						Value: substatus,
					},
				},
			},
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					stageHeaderLeft: {
						Value: stageTroubleshoot,
					},
					stageHeaderRight: {
						Value: troubleshootDetails,
					},
				},
			},
		},
		Actions: nil,
	}

	return res, nil
}

func (s *Service) GetNextStageDetails(ctx context.Context, req *cxCcPb.GetNextStageDetailsRequest) (*cxCcPb.GetNextStageDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetNextStageDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	res := &cxCcPb.GetNextStageDetailsResponse{}

	var cardReqResponse *ffPb.GetCardRequestAndCardRequestStageResponse

	onbRes, rpcErr := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
		ActorId:             req.GetHeader().GetActor().GetId(),
		CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	if err := epifigrpc.RPCError(onbRes, rpcErr); err != nil && !onbRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching credit card onboarding request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if onbRes.GetStatus().IsRecordNotFound() {
		elgRes, rpcErr := s.ffClient.GetCardRequestAndCardRequestStage(ctx, &ffPb.GetCardRequestAndCardRequestStageRequest{
			ActorId:             req.GetHeader().GetActor().GetId(),
			CardRequestWorkflow: ffEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK,
		})
		if err := epifigrpc.RPCError(elgRes, rpcErr); err != nil {
			logger.Error(ctx, "error fetching credit card eligibility request", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		cardReqResponse = elgRes
	} else {
		cardReqResponse = onbRes
	}

	nextStage := cardReqResponse.GetCardRequestStages()[0]

	res.Status = rpcPb.StatusOk()
	res.NextStageDetailsTable = &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				HeaderKey: stageHeaderLeft,
				Label:     stageLeftLabel,
				IsVisible: true,
			},
			{
				HeaderKey: stageHeaderRight,
				Label:     stageRightLabel,
				IsVisible: true,
			},
		},
		TableRows: []*webui.TableRow{
			{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					stageHeaderLeft: {
						Value: nextStageKey,
					},
					stageHeaderRight: {
						Value: nextStage.GetStage().String(),
					},
				},
			},
		},
		Actions: nil,
	}

	return res, nil
}
