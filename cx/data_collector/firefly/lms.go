//nolint:all
package firefly

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	rpcPb "github.com/epifi/be-common/api/rpc"
	actorPb "github.com/epifi/gamma/api/actor"
	cxCcPb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffLmsPb "github.com/epifi/gamma/api/firefly/lms"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

func (s *Service) GetAllActiveLoanAccounts(ctx context.Context, req *cxCcPb.GetAllActiveLoanAccountsRequest) (*cxCcPb.GetAllActiveLoanAccountsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetAllActiveLoanAccountsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetAllActiveLoanAccountsResponse{}

	loanAccounts, err := s.getLoanAccountsByRequestType(ctx, req.GetHeader().GetActor().GetId(), ffLmsPb.LoanAccountRequestType_LOAN_ACCOUNT_REQUEST_TYPE_ACTIVE_ACCOUNTS)
	if err != nil {
		logger.Error(ctx, "error in getting loan accounts", zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return res, err
	}

	sumOfEmiAmounts := moneyPb.AmountINR(0).GetPb()
	for _, emi := range loanAccounts {
		sumOfEmiAmounts, _ = moneyPb.Sum(sumOfEmiAmounts, emi.GetEmiAmount())
	}

	res.UpcomingEmiInfo = &cxCcPb.GetAllActiveLoanAccountsResponse_UpcomingEMIInfo{
		TotalActiveEmis:       int64(len(loanAccounts)),
		EmiDueInNextStatement: sumOfEmiAmounts,
	}
	res.ActiveEmis = &cxCcPb.GetAllActiveLoanAccountsResponse_ActiveEMIs{LoanAccounts: loanAccounts}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetAllClosedLoanAccounts(ctx context.Context, req *cxCcPb.GetAllClosedLoanAccountsRequest) (*cxCcPb.GetAllClosedLoanAccountsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetAllClosedLoanAccountsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetAllClosedLoanAccountsResponse{}

	loanAccounts, err := s.getLoanAccountsByRequestType(ctx, req.GetHeader().GetActor().GetId(), ffLmsPb.LoanAccountRequestType_LOAN_ACCOUNT_REQUEST_TYPE_CLOSED_ACCOUNTS)
	if err != nil {
		logger.Error(ctx, "error in getting loan accounts", zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return res, err
	}

	res.LoanAccounts = loanAccounts
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetAllEligibleTransactions(ctx context.Context, req *cxCcPb.GetAllEligibleTransactionsRequest) (*cxCcPb.GetAllEligibleTransactionsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetAllEligibleTransactionsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetAllEligibleTransactionsResponse{}

	eligibleTxnResp, err := s.ffLmsClient.GetEligibleTransactions(ctx, &ffLmsPb.GetEligibleTransactionsRequest{
		ActorId:     req.GetHeader().GetActor().GetId(),
		RequestType: ffLmsPb.EligibleTransactionRequestType_ELIGIBLE_TRANSACTION_REQUEST_TYPE_ALL_TRANSACTIONS,
	})
	if te := epifigrpc.RPCError(eligibleTxnResp, err); te != nil {
		logger.Error(ctx, "error in fetching all eligible transactions", zap.Error(te))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}

	// populate the response
	eligibleTransactions, err := s.convertBeEligibleTxnForLoanOffersToCxEligibleTxnForLoanOffers(ctx, eligibleTxnResp.GetEligibleTransactions())
	if err != nil {
		logger.Error(ctx, "error in converting Be eligible transaction for loan offers to cx eligible transaction for loan offers", zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return res, nil
	}

	res.EligibleTransactions = eligibleTransactions
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetLoanAccountDetails(ctx context.Context, req *cxCcPb.GetLoanAccountDetailsRequest) (*cxCcPb.GetLoanAccountDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetLoanAccountDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	var (
		res             = &cxCcPb.GetLoanAccountDetailsResponse{}
		err             error
		loanDetailsResp = &ffLmsPb.GetLoanAccountDetailsByIdResponse{}
		la              = &ffLmsPb.GetLoanAccountWithIdResponse{}
	)

	g, gctx := errgroup.WithContext(ctx)
	g.Go(func() error {
		loanDetailsResp, err = s.ffLmsClient.GetLoanAccountDetailsById(gctx, &ffLmsPb.GetLoanAccountDetailsByIdRequest{
			ActorId:       req.GetHeader().GetActor().GetId(),
			LoanAccountId: req.GetLoanAccountId(),
		})
		if te := epifigrpc.RPCError(loanDetailsResp, err); te != nil {
			return errors.Wrap(te, "error in fetching loan account details")
		}
		return nil
	})

	g.Go(func() error {
		la, err = s.ffLmsClient.GetLoanAccountWithId(gctx, &ffLmsPb.GetLoanAccountWithIdRequest{
			ActorId:       req.GetHeader().GetActor().GetId(),
			LoanAccountId: req.GetLoanAccountId(),
		})
		if te := epifigrpc.RPCError(la, err); te != nil {
			return errors.Wrap(te, "error in fetching loan account by id")
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		logger.Error(ctx, "failed to get loan account details", zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return res, err
	}

	merchantName, err := s.getMerchantName(ctx, la.GetOtherActorId())
	if err != nil {
		logger.Error(ctx, "Error is getting merchant name", zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return res, err
	}

	// populate the response
	res.Merchant = merchantName
	res.InterestRate = loanDetailsResp.GetLoanAccount().GetInterestInfo().GetInterestRate()
	res.Fees = loanDetailsResp.GetLoanAccount().GetFeeInfo().GetTotalFee()
	res.TotalPayableAmount = loanDetailsResp.GetLoanAccount().GetRepaymentInfo().GetTotalExpectedRepayment()
	res.EmiConversionDate = loanDetailsResp.GetLoanAccount().GetDisbursedDate()
	res.EmiClosureDate = loanDetailsResp.GetLoanAccount().GetLoanEndDate()
	res.VendorLoanId = loanDetailsResp.GetLoanAccount().GetVendorLoanId()
	res.NextBillDate = loanDetailsResp.GetNextBillGenDate()
	res.Tenure = loanDetailsResp.GetLoanAccount().GetTenureInMonths()
	res.Status = rpcPb.StatusOk()
	res.InstallmentAmount = loanDetailsResp.GetLoanAccount().GetAmountInfo().GetEmiAmount()
	res.LoanAccountId = req.GetLoanAccountId()
	return res, nil
}

func (s *Service) GetTransactionLoanOffers(ctx context.Context, req *cxCcPb.GetTransactionLoanOffersRequest) (*cxCcPb.GetTransactionLoanOffersResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxCcPb.GetTransactionLoanOffersResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	res := &cxCcPb.GetTransactionLoanOffersResponse{}

	loanOffersResp, err := s.ffLmsClient.GetLoanOffersForTransaction(ctx, &ffLmsPb.GetLoanOffersForTransactionRequest{
		ActorId:               req.GetHeader().GetActor().GetId(),
		ExternalTransactionId: req.GetExternalTransactionId(),
	})
	if te := epifigrpc.RPCError(loanOffersResp, err); te != nil {
		logger.Error(ctx, "error in fetching all eligible transactions", zap.Error(te))
		res.Status = rpcPb.StatusFromError(te)
		return res, nil
	}

	// populate the response
	cxLoanOffersResp, err := s.convertBeTransactionLoanOffersToCxTransactionLoanOffers(ctx, loanOffersResp.GetLoanOffers(), req.GetExternalTransactionId())
	if err != nil {
		logger.Error(ctx, "error in converting be Loan Offers to Cx loan offers", zap.Error(err))
		res.Status = rpcPb.StatusFromError(err)
		return res, nil
	}

	res.LoanOffers = cxLoanOffersResp
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) getLoanAccountsByRequestType(ctx context.Context, actorId string, requestType ffLmsPb.LoanAccountRequestType) ([]*cxCcPb.LoanAccount, error) {
	laResp, err := s.ffLmsClient.GetLoanAccount(ctx, &ffLmsPb.GetLoanAccountRequest{
		ActorId:     actorId,
		RequestType: requestType,
	})
	if te := epifigrpc.RPCError(laResp, err); te != nil {
		return nil, errors.Wrap(te, "error in fetching all active loan accounts")
	}

	loanAccounts, err := s.convertBeLoanAccountToCxLoanAccount(ctx, laResp.GetLoanAccounts(), actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error in converting be Loan account to Cx loan account")
	}

	return loanAccounts, nil
}

func (s *Service) convertBeLoanAccountToCxLoanAccount(ctx context.Context, loanAccounts []*ffLmsPb.GetLoanAccountResponse_LoanAccount, actorId string) ([]*cxCcPb.LoanAccount, error) {
	resp := make([]*cxCcPb.LoanAccount, 0)
	for _, loanAccount := range loanAccounts {

		var (
			err                       error
			additionalLoanAccountInfo = &ffLmsPb.GetLoanAccountDetailsByIdResponse{}
			merchantName              string
		)

		g, gctx := errgroup.WithContext(ctx)
		g.Go(func() error {
			additionalLoanAccountInfo, err = s.ffLmsClient.GetLoanAccountDetailsById(gctx, &ffLmsPb.GetLoanAccountDetailsByIdRequest{
				LoanAccountId: loanAccount.GetLoanAccountId(),
				ActorId:       actorId,
			})
			if te := epifigrpc.RPCError(additionalLoanAccountInfo, err); te != nil {
				return errors.Wrap(err, "error in GetLoanAccountDetailsById BE RPC")
			}
			return nil
		})

		g.Go(func() error {
			merchantName, err = s.getMerchantName(gctx, loanAccount.GetOtherActorId())
			if err != nil {
				return errors.Wrap(err, "Error is getting merchant name")
			}
			return nil
		})

		if err = g.Wait(); err != nil {
			return nil, errors.Wrap(err, "Unable to get merchant name and additional loan info")
		}

		resp = append(resp, &cxCcPb.LoanAccount{
			TransactionAmount:    additionalLoanAccountInfo.GetLoanAccount().GetAmountInfo().GetLoanAmount(),
			EmiConversionDate:    additionalLoanAccountInfo.GetLoanAccount().GetDisbursedDate(),
			EmiClosureDate:       additionalLoanAccountInfo.GetLoanAccount().GetLoanEndDate(),
			Merchant:             merchantName,
			VendorLoanId:         additionalLoanAccountInfo.GetLoanAccount().GetVendorLoanId(),
			LoanAccountStatus:    additionalLoanAccountInfo.GetLoanAccount().GetStatus(),
			Tenure:               loanAccount.GetTenureInMonths(),
			EmiAmount:            loanAccount.GetEmiAmount(),
			LoanAccountId:        loanAccount.GetLoanAccountId(),
			LastEmiStatementDate: getLastEmiStatementDate(additionalLoanAccountInfo.GetLoanAccount().GetLoanSchedule().GetSchedules()),
		})
	}
	return resp, nil
}

func (s *Service) getMerchantName(ctx context.Context, merchantActorId string) (string, error) {
	getEntityDetailsRes, err := s.actorClient.GetEntityDetailsByActorId(ctx,
		&actorPb.GetEntityDetailsByActorIdRequest{ActorId: merchantActorId})
	if te := epifigrpc.RPCError(getEntityDetailsRes, err); te != nil {
		return "", errors.Wrap(te, "error in fetching entity details from actor id")
	}

	return getEntityDetailsRes.GetName().ToSentenceCaseString(), nil
}

func (s *Service) convertBeEligibleTxnForLoanOffersToCxEligibleTxnForLoanOffers(ctx context.Context, eligibleTransactions []*ffLmsPb.GetEligibleTransactionsResponse_EligibleTransaction) ([]*cxCcPb.EligibleTransaction, error) {
	resp := make([]*cxCcPb.EligibleTransaction, 0)

	for _, eligibleTransaction := range eligibleTransactions {
		merchantName, err := s.getMerchantName(ctx, eligibleTransaction.GetOtherActorId())
		if err != nil {
			return nil, errors.Wrap(err, "Error is getting merchant name")
		}

		resp = append(resp, &cxCcPb.EligibleTransaction{
			Merchant:              merchantName,
			TransactionDate:       eligibleTransaction.GetTransactionDate(),
			TransactionAmount:     eligibleTransaction.GetAmount(),
			ExternalTransactionId: eligibleTransaction.GetExternalTransactionId(),
		})
	}
	return resp, nil
}

func (s *Service) convertBeTransactionLoanOffersToCxTransactionLoanOffers(ctx context.Context, loanOffers []*ffLmsPb.GetLoanOffersForTransactionResponse_LoanOffer, extTxnId string) ([]*cxCcPb.LoanOffer, error) {
	cxLoanOffers := make([]*cxCcPb.LoanOffer, 0)

	ccTxnResp, err := s.ffAccountingClient.GetTransactions(ctx, &ffAccountsPb.GetTransactionsRequest{
		GetBy: &ffAccountsPb.GetTransactionsRequest_BatchExternalTxnIds{
			BatchExternalTxnIds: &ffAccountsPb.BatchExternalTxnIds{ExternalTxnIds: []string{extTxnId}},
		},
	})
	if te := epifigrpc.RPCError(ccTxnResp, err); te != nil {
		return nil, errors.Wrap(te, "error in fetching transaction from ext_txn_id from accounting client")
	}

	for _, loanOffer := range loanOffers {
		cxLoanOffers = append(cxLoanOffers, &cxCcPb.LoanOffer{
			Amount:          loanOffer.GetLoanAmount(),
			InterestRate:    loanOffer.GetInterestRate(),
			Tenure:          loanOffer.GetTenureInMonths(),
			EmiAmount:       loanOffer.GetEmiAmount(),
			Principle:       loanOffer.GetEmiAmount(),
			Interest:        loanOffer.GetTotalInterest(),
			ProcessingFee:   loanOffer.GetTotalFee(),
			Taxes:           loanOffer.GetTotalTax(),
			TotalAmount:     loanOffer.GetLoanAmount(),
			TransactionDate: datetime.TimestampToDateInLoc(ccTxnResp.GetTransactions()[0].GetTxnTime(), nil),
		})
	}
	return cxLoanOffers, nil
}

func getLastEmiStatementDate(schedules []*ffLmsPb.LoanSchedule_Schedule) *date.Date {
	if len(schedules) < 2 {
		return nil
	}
	return schedules[len(schedules)-2].GetDueDate()
}
