package profile

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/csv"
	"fmt"
	"io/ioutil"
	"os"
	"strconv"
	"time"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"

	bcPb "github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	profilePb "github.com/epifi/gamma/api/cx/data_collector/profile"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendormapping"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/pkg/obfuscator"

	"github.com/aws/aws-sdk-go-v2/aws"
	s3Sdk "github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type UserDetailsMap map[profilePb.BulkUserInfoField]string

var (
	boolToYesNo = map[bool]string{true: "YES", false: "NO"}
)

const (
	dateTimeFormatForCsvFile = "02-Jan-2006 15:04:05"
)

// nolint:funlen
func (s *Service) GetUserDetailsForTickets(ctx context.Context, ticketIds []int64, agentEmail string) ([]UserDetailsMap, []*profilePb.FailureID) {
	var (
		userDetailList    []UserDetailsMap
		ticketFailureList []*profilePb.FailureID
	)
	totalIdsRequested := len(ticketIds)
	for i, ticketId := range ticketIds {
		// log for every 10 tickets processed for visibility in kibana
		if i%10 == 0 {
			logger.Info(ctx, "Bulk user details: processing ticket Ids...", zap.Int(logKeyIdsProcessedSoFar, i), zap.Int(logKeyTotalIdsRequested, totalIdsRequested), zap.String(logger.AGENT_EMAIL, agentEmail))
		}
		// get ticket details from cx ticket service
		resp, err := s.ticketServiceClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{TicketId: ticketId})
		if err = epifigrpc.RPCError(resp, err); err != nil {
			if resp.GetStatus().IsRecordNotFound() {
				logger.Info(ctx, "ticket details not found", zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err))
				ticketFailureList = append(ticketFailureList, &profilePb.FailureID{
					Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_TicketId{TicketId: ticketId}},
					FailureReason: TicketFailureReasonNotFound,
				})
				continue
			}
			logger.Error(ctx, "error while fetching ticket details", zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err))
			ticketFailureList = append(ticketFailureList, &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_TicketId{TicketId: ticketId}},
				FailureReason: TicketFailureReasonErrorWhileFetchingTicketDetails,
			})
			continue
		}
		if len(resp.GetTickets()) != 1 {
			logger.Error(ctx, "error while fetching ticket details, ticket count in response is not 1",
				zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err), zap.Int("ticket_count", len(resp.GetTickets())))
			ticketFailureList = append(ticketFailureList, &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_TicketId{TicketId: ticketId}},
				FailureReason: TicketFailureReasonErrorWhileFetchingTicketDetails,
			})
			continue
		}
		ticket := resp.GetTickets()[0]
		// fetch user details
		user, err := s.customerIdentifier.GetUserByCxTicket(ctx, ticket)
		if err != nil {
			logger.Error(ctx, "error while fetching user by ticket", zap.Error(err), zap.Int64(logger.TICKET_ID, ticketId))
			ticketFailureList = append(ticketFailureList, &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_TicketId{TicketId: ticketId}},
				FailureReason: FailureReasonErrorWhileFetchingUserDetails,
			})
			continue
		}
		customerInfo, errResp := s.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bcPb.GetBankCustomerRequest_UserId{UserId: user.GetId()},
		})
		if er := epifigrpc.RPCError(customerInfo, errResp); er != nil {
			logger.Error(ctx, "error while getting customer info from user details", zap.Error(er), zap.Int64(logger.TICKET_ID, ticketId))
			ticketFailureList = append(ticketFailureList, &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_TicketId{TicketId: ticketId}},
				FailureReason: FailureReasonErrorWhileFetchingUserDetails,
			})
			continue
		}
		// fetch account details for user
		accountResp, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: user.GetId(),
		}})
		if err != nil || accountResp.GetAccount() == nil {
			logger.Error(ctx, "error while fetching account details", zap.Error(err), zap.Int64(logger.TICKET_ID, ticketId))
			ticketFailureList = append(ticketFailureList, &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_TicketId{TicketId: ticketId}},
				FailureReason: FailureReasonErrorWhileFetchingAccountDetails,
			})
			continue
		}
		actor, err := s.customerIdentifier.GetActorByUserId(ctx, user.GetId())
		if err != nil {
			logger.Error(ctx, "error while fetching actor from user", zap.Error(err), zap.Int64(logger.TICKET_ID, ticketId))
			ticketFailureList = append(ticketFailureList, &profilePb.FailureID{
				Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_TicketId{TicketId: ticketId}},
				FailureReason: FailureReasonErrorWhileFetchingActorDetails,
			})
			continue
		}
		// add user details in array if no errors
		userDetailList = append(userDetailList, UserDetailsMap{
			profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_FRESHDESK_TICKET_ID: strconv.FormatInt(ticketId, 10),
			profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_NUMBER:      accountResp.GetAccount().GetAccountNo(),
			profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_CUSTOMER_ID:         customerInfo.GetBankCustomer().GetVendorCustomerId(),
			profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACTOR_ID:            actor.GetId(),
		})
	}
	logger.Info(ctx, "Bulk user details: done processing ticket Ids", zap.Int(logKeyIdsProcessedSoFar, totalIdsRequested), zap.Int(logKeyTotalIdsRequested, totalIdsRequested), zap.String(logger.AGENT_EMAIL, agentEmail))
	return userDetailList, ticketFailureList
}

func (s *Service) GetUserDetailsForIDs(ctx context.Context, idList []*profilePb.BulkUserIdentifier, requiredColumnsMap map[profilePb.BulkUserInfoField]bool, agentEmail string) ([]UserDetailsMap, []*profilePb.FailureID) {
	var (
		userDetailsList []UserDetailsMap
		failedIdList    []*profilePb.FailureID
	)
	totalIdsRequested := len(idList)
	for i, id := range idList {
		var (
			userDetails UserDetailsMap
			failure     *profilePb.FailureID
		)
		// log for every 10 tickets processed for visibility in kibana
		if i%10 == 0 {
			logger.Info(ctx, "Bulk user details: processing Ids...", zap.Int(logKeyIdsProcessedSoFar, i), zap.Int(logKeyTotalIdsRequested, totalIdsRequested), zap.String(logger.AGENT_EMAIL, agentEmail))
		}
		switch id.GetId().(type) {
		case *profilePb.BulkUserIdentifier_ActorId:
			userDetails, failure = s.GetUserDetailsForActorID(ctx, id.GetActorId(), requiredColumnsMap)
		// NOTE: here AccountId is interpreted as Account Number given by Bank.
		// TODO: For adding support for AccountID(internal ID) in future, need proto changes
		case *profilePb.BulkUserIdentifier_AccountId:
			userDetails, failure = s.GetUserDetailsForAccountNumber(ctx, id.GetAccountId(), requiredColumnsMap)
		case *profilePb.BulkUserIdentifier_CustomerId:
			userDetails, failure = s.GetUserDetailsForCustomerID(ctx, id.GetCustomerId(), requiredColumnsMap)
		case *profilePb.BulkUserIdentifier_PhoneNumber:
			userDetails, failure = s.GetUserDetailsForPhoneNumber(ctx, id.GetPhoneNumber(), requiredColumnsMap)
		default:
			failure = &profilePb.FailureID{
				Id:            id,
				FailureReason: FailureReasonInvalidInputID,
			}
		}
		if failure != nil {
			failedIdList = append(failedIdList, failure)
			continue
		}
		userDetailsList = append(userDetailsList, userDetails)
	}
	logger.Info(ctx, "Bulk user details: done processing Ids", zap.Int(logKeyIdsProcessedSoFar, totalIdsRequested), zap.Int(logKeyTotalIdsRequested, totalIdsRequested), zap.String(logger.AGENT_EMAIL, agentEmail))
	return userDetailsList, failedIdList
}

func (s *Service) GetUserDetailsForActorID(ctx context.Context, actorId string, requiredColumnsMap map[profilePb.BulkUserInfoField]bool) (UserDetailsMap, *profilePb.FailureID) {
	user, err := s.customerIdentifier.GetUserByActorId(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while fetching user by actor", zap.Error(err), zap.String(logger.ACTOR_ID, actorId))
		return nil, &profilePb.FailureID{
			Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_ActorId{ActorId: actorId}},
			FailureReason: FailureReasonErrorWhileFetchingUserDetails,
		}
	}
	userDetails, failureId := s.BuildUserDetails(ctx, user, actorId, nil, requiredColumnsMap)
	if failureId != nil {
		logger.Error(ctx, "error while building user details", zap.Error(err), zap.String(logger.ACTOR_ID, actorId))
		failureId.Id = &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_ActorId{ActorId: actorId}}
		return nil, failureId
	}
	return userDetails, nil
}

func (s *Service) GetUserDetailsForAccountNumber(ctx context.Context, accountNumber string, requiredColumnsMap map[profilePb.BulkUserInfoField]bool) (UserDetailsMap, *profilePb.FailureID) {
	accountResp, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
			AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
				AccountNumber: accountNumber,
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if err != nil || accountResp.GetAccount() == nil {
		logger.Error(ctx, "error while fetching account details", zap.Error(err), zap.String(logger.ACCOUNT_NUMBER, mask.GetMaskedAccountNumber(accountNumber, "")))
		return nil, &profilePb.FailureID{
			Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_AccountId{AccountId: accountNumber}},
			FailureReason: FailureReasonErrorWhileFetchingAccountDetails,
		}
	}
	user, err := s.customerIdentifier.GetUser(ctx, accountResp.GetAccount().GetEmailId(), accountResp.GetAccount().GetPhoneNumber())
	if err != nil {
		logger.Error(ctx, "error while fetching user by email ID / phone number", zap.Error(err), zap.String(logger.ACCOUNT_NUMBER, mask.GetMaskedAccountNumber(accountNumber, "")))
		return nil, &profilePb.FailureID{
			Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_AccountId{AccountId: accountNumber}},
			FailureReason: FailureReasonErrorWhileFetchingUserDetails,
		}
	}
	userDetails, failureId := s.BuildUserDetails(ctx, user, "", accountResp.GetAccount(), requiredColumnsMap)
	if failureId != nil {
		logger.Error(ctx, "error while building user details", zap.Error(err), zap.String(logger.ACCOUNT_NUMBER, mask.GetMaskedAccountNumber(accountNumber, "")))
		failureId.Id = &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_AccountId{AccountId: accountNumber}}
		return nil, failureId
	}
	return userDetails, nil
}

func (s *Service) GetUserDetailsForCustomerID(ctx context.Context, customerId string, requiredColumnsMap map[profilePb.BulkUserInfoField]bool) (UserDetailsMap, *profilePb.FailureID) {
	//TODO: will be supported once an api for getting user by customer ID is exposed from user service
	logger.Error(ctx, "Request for customer ID", zap.String(logger.CUSTOMER_ID, customerId))
	failure := &profilePb.FailureID{
		Id: &profilePb.BulkUserIdentifier{
			Id: &profilePb.BulkUserIdentifier_CustomerId{CustomerId: customerId},
		},
		FailureReason: "customer ID not supported yet",
	}
	return nil, failure
}

func (s *Service) GetUserDetailsForPhoneNumber(ctx context.Context, phoneNumber string, requiredColumnsMap map[profilePb.BulkUserInfoField]bool) (UserDetailsMap, *profilePb.FailureID) {
	phone, err := commontypes.ParsePhoneNumber(phoneNumber)
	if err != nil {
		cxLogger.Error(ctx, "failed to parse phone number string in request", zap.Error(err))
		return nil, &profilePb.FailureID{
			Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: phoneNumber}},
			FailureReason: FailureReasonErrorWhileParsingPhoneNumber,
		}
	}
	// if no country code is given set default value to 91 (India)
	if phone.GetCountryCode() == 0 {
		phone.CountryCode = 91
	}
	if phone.GetCountryCode() != 91 {
		cxLogger.Error(ctx, "non-Indian phone number passed in the request", zap.Error(err), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(phone)))
		return nil, &profilePb.FailureID{
			Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: phoneNumber}},
			FailureReason: FailureReasonNonIndianPhoneNumberPassed,
		}
	}
	user, err := s.customerIdentifier.GetUser(ctx, "", phone)
	if err != nil {
		logger.Error(ctx, "error while fetching user from phone number", zap.Error(err), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(phone)))
		return nil, &profilePb.FailureID{
			Id:            &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: phoneNumber}},
			FailureReason: FailureReasonErrorWhileFetchingUserDetails,
		}
	}
	userDetails, failureId := s.BuildUserDetails(ctx, user, "", nil, requiredColumnsMap)
	if failureId != nil {
		logger.Error(ctx, "error while building user details", zap.Error(err), zap.String(logger.PHONE_NUMBER_HASH, obfuscator.HashedPhoneNum(phone)))
		failureId.Id = &profilePb.BulkUserIdentifier{Id: &profilePb.BulkUserIdentifier_PhoneNumber{PhoneNumber: phoneNumber}}
		return nil, failureId
	}
	return userDetails, nil
}

//nolint:funlen
func (s *Service) BuildUserDetails(ctx context.Context, user *userPb.User, actorId string, account *savingsPb.Account, requiredFieldsMap map[profilePb.BulkUserInfoField]bool) (UserDetailsMap, *profilePb.FailureID) {
	userDetails := make(UserDetailsMap)
	if actorId == "" && isActorIdRequired(requiredFieldsMap) {
		actor, err := s.customerIdentifier.GetActorByUserId(ctx, user.GetId())
		if err != nil {
			logger.Error(ctx, "error while fetching actor by user ID", zap.Error(err), zap.String(logger.USER_ID, user.GetId()))
			return nil, &profilePb.FailureID{FailureReason: FailureReasonErrorWhileFetchingActorDetails}
		}
		actorId = actor.GetId()
	}
	if account == nil && isAccountRequired(requiredFieldsMap) {
		accountResp, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: user.GetId()}})
		if err != nil || accountResp.GetAccount() == nil {
			logger.Error(ctx, "error while fetching account details", zap.Error(err), zap.String(logger.USER_ID, user.GetId()))
			return nil, &profilePb.FailureID{FailureReason: FailureReasonErrorWhileFetchingAccountDetails}
		}
		account = accountResp.GetAccount()
	}
	var customerInfo *bcPb.GetBankCustomerResponse
	if isCustomerInfoRequired(requiredFieldsMap) {
		var err error
		customerInfo, err = s.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
		})
		if er := epifigrpc.RPCError(customerInfo, err); er != nil {
			logger.Error(ctx, "error while getting customer info from actorId", zap.Error(er), zap.String(logger.USER_ID, user.GetId()))
			return nil, &profilePb.FailureID{FailureReason: FailureReasonErrorWhileFetchingCustomerDetails}
		}
	}
	// populate the required user details
	for field := range requiredFieldsMap {
		switch field {
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACTOR_ID:
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACTOR_ID] = actorId
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_NUMBER:
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_NUMBER] = account.GetAccountNo()
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME:
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME] = account.GetCreationInfo().GetVendorCreationSucceededAt().AsTime().In(datetime.IST).Format(dateTimeFormatForCsvFile)
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_CUSTOMER_ID:
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_CUSTOMER_ID] = customerInfo.GetBankCustomer().GetVendorCustomerId()
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_KYC_LEVEL:
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_KYC_LEVEL] = customerInfo.GetBankCustomer().GetDedupeInfo().GetKycLevel().String()
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_FCM_ID:
			vmResp, err := s.vendorMappingClient.GetBEMappingById(ctx, &vendormapping.GetBEMappingByIdRequest{Id: actorId})
			responseString := "Error while fetching FCM ID"
			if te := epifigrpc.RPCError(vmResp, err); te != nil {
				// we are fetching this field on best effort basis so if it fails not returning an error
				logger.Error(ctx, "error while fetching FCM ID from vendorMappingClient", zap.Error(te), zap.String(logger.USER_ID, user.GetId()))
			} else {
				responseString = vmResp.GetFcmId()
			}
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_FCM_ID] = responseString
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED:
			isSavingsAccountClosureAllowed, _, err := s.dataCollectorHelper.IsSavingsAccountClosureAllowed(ctx, user.GetId(), actorId)
			responseString := "Error while checking if account closure allowed"
			if err != nil {
				// we are fetching this field on best effort basis so if it fails not returning an error
				logger.Error(ctx, "error while verifying account closure eligibility", zap.Error(err), zap.String(logger.USER_ID, user.GetId()))
			} else {
				responseString = boolToYesNo[isSavingsAccountClosureAllowed]
			}
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED] = responseString
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_EMAIL_ID:
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_EMAIL_ID] = user.GetProfile().GetEmail()
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_PHONE_NUMBER:
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_PHONE_NUMBER] = user.GetProfile().GetPhoneNumber().ToSignedString()
		case profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_NAME:
			userDetails[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_NAME] = user.GetProfile().GetKycName().ToStringWithHonorific()
		default:
		}
	}
	return userDetails, nil
}

func isCustomerInfoRequired(requiredFieldsMap map[profilePb.BulkUserInfoField]bool) bool {
	return requiredFieldsMap[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_CUSTOMER_ID] ||
		requiredFieldsMap[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_KYC_LEVEL]
}

func isActorIdRequired(requiredFieldsMap map[profilePb.BulkUserInfoField]bool) bool {
	return requiredFieldsMap[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACTOR_ID] ||
		requiredFieldsMap[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_FCM_ID] ||
		requiredFieldsMap[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED]
}

func isAccountRequired(requiredFieldsMap map[profilePb.BulkUserInfoField]bool) bool {
	return requiredFieldsMap[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_NUMBER] ||
		requiredFieldsMap[profilePb.BulkUserInfoField_BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME]
}

func (s *Service) CreateCsvFileForUserDetails(ctx context.Context, agentEmail string, csvColumnsList []profilePb.BulkUserInfoField,
	fieldToCsvColNameMap UserDetailsMap, userDetailsList []UserDetailsMap) (string, error) {
	timestampString := time.Now().Format("**************")

	fileName := fmt.Sprintf("%s_bulk_user_details_%s.*.csv", agentEmail, timestampString)
	csvFile, err := ioutil.TempFile("", fileName)
	if err != nil {
		return "", errors.Wrap(err, "cannot create csv file")
	}

	csvWriter := csv.NewWriter(csvFile)
	defer func() {
		csvWriter.Flush()
		err := csvFile.Close()
		if err != nil {
			logger.Error(ctx, "error while closing the csv file", zap.Error(err))
		}
	}()

	csvErr := csvWriter.Write(getCsvRowFromUserDetailsMap(csvColumnsList, fieldToCsvColNameMap))
	if csvErr != nil {
		return "", errors.Wrap(csvErr, "cannot write headers data to csv file")
	}
	for _, userDetails := range userDetailsList {
		if csvErr := csvWriter.Write(getCsvRowFromUserDetailsMap(csvColumnsList, userDetails)); csvErr != nil {
			return "", errors.Wrap(csvErr, "cannot write data to csv file")
		}
	}
	logger.Info(ctx, "user bulk info csv file generated successfully", zap.Int("total_entries", len(userDetailsList)))
	return csvFile.Name(), nil
}

func getCsvRowFromUserDetailsMap(columnsList []profilePb.BulkUserInfoField, userDetailsMap UserDetailsMap) []string {
	var csvRow []string
	for _, col := range columnsList {
		csvRow = append(csvRow, userDetailsMap[col])
	}
	return csvRow
}

func (s *Service) WriteFileToS3AndGetSignedUrl(ctx context.Context, srcFilePath string, agentEmail string) (string, error) {
	timestampString := time.Now().Format("**************")
	s3FileName := fmt.Sprintf("%s_bulk_user_details_%s.csv", agentEmail, timestampString)
	if srcFilePath == "" {
		return "", errors.New("filepath passed is empty to be uploaded to S3")
	}
	// upload file to s3
	s3FilePath := fmt.Sprintf("%s/%s", s.cxConf.CxS3Config().BulkUserDetailsFolderName, s3FileName)
	logger.Info(ctx, "uploading csv file to s3 bucket", zap.Any("s3path", s3FilePath))
	if err := s.cxS3Client.SCP(ctx, srcFilePath, s3FilePath); err != nil {
		return "", errors.Wrap(err, "error writing file to s3")
	}
	// pre-sign the s3 url
	signedUrl, err := s.cxS3Client.GetPreSignedURLFromS3Input(ctx, &s3Sdk.GetObjectInput{
		Bucket: aws.String(s.cxConf.CxS3Config().BucketName),
		Key:    aws.String(s3FilePath),
	}, 10*time.Minute)
	if err != nil {
		cxLogger.Error(ctx, "error while creating pre-signed url", zap.Error(err))
		return "", errors.New("error while creating pre-signed url for resource")
	}
	return signedUrl, nil
}

func (s *Service) CreateCsvUploadAndGetSignedUrl(ctx context.Context, agentEmail string, csvColumnsList []profilePb.BulkUserInfoField,
	fieldToCsvColNameMap UserDetailsMap, userDetailsList []UserDetailsMap) (string, error) {
	csvFilePath, err := s.CreateCsvFileForUserDetails(ctx, agentEmail, csvColumnsList, fieldToCsvColNameMap, userDetailsList)
	if err != nil {
		logger.Info(ctx, "error while creating csv file with user details", zap.Error(err))
		return "", err
	}
	defer func() {
		_ = os.Remove(csvFilePath)
	}()
	// upload file and get signed url
	signedUrl, err := s.WriteFileToS3AndGetSignedUrl(ctx, csvFilePath, agentEmail)
	if err != nil {
		logger.Info(ctx, "error while uploading file to s3 and getting signed url", zap.Error(err))
		return "", err
	}
	return signedUrl, nil
}

func (s *Service) CreateCsvFileForFailedIdList(ctx context.Context, failedIdList []*profilePb.FailureID, agentEmail string) (string, error) {
	timestampString := time.Now().Format("**************")

	fileName := fmt.Sprintf("%s_failed_id_list_%s.*.csv", agentEmail, timestampString)
	csvFile, err := ioutil.TempFile("", fileName)
	if err != nil {
		return "", errors.Wrap(err, "cannot create csv file")
	}

	csvWriter := csv.NewWriter(csvFile)
	defer func() {
		csvWriter.Flush()
		err := csvFile.Close()
		if err != nil {
			logger.Error(ctx, "error while closing the csv file", zap.Error(err))
		}
	}()
	csvErr := csvWriter.Write([]string{"Failed ID", "Failure Reason"})

	if csvErr != nil {
		return "", errors.Wrap(csvErr, "cannot write headers data to csv file")
	}

	for _, failedItem := range failedIdList {
		switch failedItem.GetId().GetId().(type) {
		case *profilePb.BulkUserIdentifier_ActorId:
			if csvErr := csvWriter.Write([]string{failedItem.GetId().GetActorId(), failedItem.GetFailureReason()}); csvErr != nil {
				return "", errors.Wrap(csvErr, "cannot write failed actor Id to csv file")
			}
		case *profilePb.BulkUserIdentifier_AccountId:
			if csvErr := csvWriter.Write([]string{failedItem.GetId().GetAccountId(), failedItem.GetFailureReason()}); csvErr != nil {
				return "", errors.Wrap(csvErr, "cannot write failed account Id to csv file")
			}
		case *profilePb.BulkUserIdentifier_CustomerId:
			if csvErr := csvWriter.Write([]string{failedItem.GetId().GetCustomerId(), failedItem.GetFailureReason()}); csvErr != nil {
				return "", errors.Wrap(csvErr, "cannot write failed customer Id to csv file")
			}
		case *profilePb.BulkUserIdentifier_PhoneNumber:
			if csvErr := csvWriter.Write([]string{failedItem.GetId().GetPhoneNumber(), failedItem.GetFailureReason()}); csvErr != nil {
				return "", errors.Wrap(csvErr, "cannot write failed phone number to csv file")
			}
		case *profilePb.BulkUserIdentifier_TicketId:
			if csvErr := csvWriter.Write([]string{strconv.FormatInt(failedItem.GetId().GetTicketId(), 10), failedItem.GetFailureReason()}); csvErr != nil {
				return "", errors.Wrap(csvErr, "cannot write failed ticket Id to csv file")
			}
		default:
			if csvErr := csvWriter.Write([]string{failedItem.GetId().String(), failedItem.GetFailureReason()}); csvErr != nil {
				return "", errors.Wrap(csvErr, "cannot write failed id to csv file")
			}
		}
	}

	logger.Info(ctx, "failed ID list csv file generated successfully", zap.Int("total_entries", len(failedIdList)))
	return csvFile.Name(), nil
}

//nolint:funlen,gosec
func (s *Service) SendBulkUserDetailsMail(ctx context.Context, toEmailId string, csvColumnsList []profilePb.BulkUserInfoField, fieldToCsvColNameMap UserDetailsMap,
	userDetailsList []UserDetailsMap, failedIdList []*profilePb.FailureID) {

	logger.Info(ctx, "Bulk user details: creating CSV file(s)", zap.String(logger.AGENT_EMAIL, toEmailId))
	currTime := time.Now().Format("02-01-2006 15:04:05")
	userDetailsFileName := toEmailId + "_bulk_user_details_" + currTime
	userDetailsFile, err := s.CreateCsvFileForUserDetails(ctx, toEmailId, csvColumnsList, fieldToCsvColNameMap, userDetailsList)
	if err != nil {
		logger.Error(ctx, "error while creating csv file with user details", zap.Error(err))
		return
	}
	userDetailsBytes, err := ioutil.ReadFile(userDetailsFile)
	if err != nil {
		logger.Error(ctx, "error while converting user details file to bytes", zap.Error(err))
		return
	}
	failedIdsFileName := toEmailId + "_failed_id_list_" + currTime
	failedIdsFile, err := s.CreateCsvFileForFailedIdList(ctx, failedIdList, toEmailId)
	if err != nil {
		logger.Error(ctx, "error while creating csv file with failed ID list", zap.Error(err))
		return
	}
	failedIdBytes, err := ioutil.ReadFile(failedIdsFile)
	if err != nil {
		logger.Error(ctx, "error while converting failed Ids file to bytes", zap.Error(err))
		return
	}
	failedIdCount := len(failedIdList)
	totalProcessedCount := len(userDetailsList) + failedIdCount
	additionalMsg := "PFA Bulk User details file."
	attachments := []*commsPb.EmailMessage_Attachment{
		{
			FileContent:    userDetailsBytes,
			FileName:       userDetailsFileName,
			Disposition:    commsPb.Disposition_ATTACHMENT,
			AttachmentType: "text/comma-separated-values",
		},
	}
	if failedIdCount != 0 {
		additionalMsg = "PFA Bulk User details file and Failed IDs file (with failure reasons)."
		attachments = append(attachments, &commsPb.EmailMessage_Attachment{
			FileContent:    failedIdBytes,
			FileName:       failedIdsFileName,
			Disposition:    commsPb.Disposition_ATTACHMENT,
			AttachmentType: "text/comma-separated-values",
		})
	}

	logger.Info(ctx, "Bulk user details: sending email", zap.String(logger.AGENT_EMAIL, toEmailId))
	resp, err := s.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_GUARANTEED,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: toEmailId,
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   s.cxConf.BulkUserInfoViaEmailConfig().FromEmailId(),
				FromEmailName: s.cxConf.BulkUserInfoViaEmailConfig().FromEmailName(),
				ToEmailName:   toEmailId,
				EmailOption: &commsPb.EmailOption{
					Option: &commsPb.EmailOption_CxBulkUserDetailsEmailOption{
						CxBulkUserDetailsEmailOption: &commsPb.CxBulkUserDetailsEmailOption{
							EmailType: commsPb.EmailType_CX_BULK_USER_DETAILS_EMAIL,
							Option: &commsPb.CxBulkUserDetailsEmailOption_CxBulkUserDetailsEmailOptionV1{
								CxBulkUserDetailsEmailOptionV1: &commsPb.CxBulkUserDetailsEmailOptionV1{
									ProcessedIdsCount: int64(totalProcessedCount),
									FailedIdsCount:    int64(failedIdCount),
									TemplateVersion:   commsPb.TemplateVersion_VERSION_V1,
									AdditionalMsg:     additionalMsg,
								},
							},
						},
					},
				},
				Attachment: attachments,
			},
		},
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error while emailing bulk user details CSV", zap.Error(err), zap.String("emailId", toEmailId))
	}
}
