package fittt

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"
	cxPb "github.com/epifi/gamma/api/cx"
	cxFitttPb "github.com/epifi/gamma/api/cx/data_collector/fittt"
	fitttPb "github.com/epifi/gamma/api/fittt"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	eventPb "github.com/epifi/gamma/api/rms/orchestrator/event"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/data_collector/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/pkg/fittt"
)

type Service struct {
	fitttClient fitttPb.FitttClient
	authEngine  auth_engine.IAuthEngine
	rmsClient   rmsPb.RuleManagerClient
	usStocks    fittt.IUSStocks
}

func NewService(
	fitttClient fitttPb.FitttClient,
	authEngine auth_engine.IAuthEngine,
	rmsClient rmsPb.RuleManagerClient,
	usStocks fittt.IUSStocks,
) *Service {
	return &Service{
		fitttClient: fitttClient,
		authEngine:  authEngine,
		rmsClient:   rmsClient,
		usStocks:    usStocks,
	}
}

var _ cxFitttPb.FitttServer = &Service{}

func (s *Service) GetActiveRules(ctx context.Context, req *cxFitttPb.GetActiveRulesRequest) (*cxFitttPb.GetActiveRulesResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxFitttPb.GetActiveRulesResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	resp, err := s.rmsClient.GetRulesForClient(ctx, &rmsPb.GetRulesForClientRequest{
		Client:      eventPb.RMSClient_FITTT,
		PageContext: helper.ConvertToRpcPageContext(req.GetPageContext()),
		States:      []rmsPb.RuleState{rmsPb.RuleState_RULE_STATE_ACTIVE},
	})

	// returns record not found error, if received that in response
	// returns internal error for any other error
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return &cxFitttPb.GetActiveRulesResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching active rules", zap.Error(te))
		return &cxFitttPb.GetActiveRulesResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching active rules"),
		}, nil
	}

	return s.buildActiveRulesResponse(resp)
}

func (s *Service) buildActiveRulesResponse(resp *rmsPb.GetRulesForClientResponse) (*cxFitttPb.GetActiveRulesResponse, error) {
	cxResp := &cxFitttPb.GetActiveRulesResponse{
		Status:      resp.GetStatus(),
		PageContext: helper.ConvertToCxPageContext(resp.GetPageContext()),
	}

	// populating Rules field in cxResp
	for _, rule := range resp.GetRules() {
		cxResp.Rules = append(cxResp.GetRules(), &cxFitttPb.FitRule{
			Id:                            rule.GetId(),
			Name:                          rule.GetName(),
			Description:                   rule.GetDescription().GetDisplayStr(),
			Category:                      rule.GetCategory().String(),
			MaxSubscriptionsPerActor:      rule.GetMaxSubscriptionsPerActor(),
			DefaultSubscriptionExpiryData: parseSubscriptionExpiryData(rule.GetDefaultSubscriptionExpiryData()),
			State:                         rule.GetState().String(),
		})
	}

	return cxResp, nil
}

func (s *Service) GetSubscriptionsForActor(ctx context.Context, req *cxFitttPb.GetSubscriptionsForActorRequest) (*cxFitttPb.GetSubscriptionsForActorResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxFitttPb.GetSubscriptionsForActorResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	resp, err := s.rmsClient.GetSubscriptionsByActorId(ctx, &rmsPb.GetSubscriptionsByActorIdRequest{
		ActorId:       req.GetHeader().GetActor().GetId(),
		Client:        eventPb.RMSClient_FITTT,
		PageContext:   helper.ConvertToRpcPageContext(req.GetPageContext()),
		States:        parseRuleSubscriptionStates(req.GetFilters().GetStates()),
		CreatedAfter:  req.GetFilters().GetFromCreatedDate(),
		CreatedBefore: req.GetFilters().GetToCreatedDate(),
		UpdatedAfter:  req.GetFilters().GetFromUpdatedDate(),
		UpdatedBefore: req.GetFilters().GetToUpdatedDate(),
	})

	// returns record not found error, if received that in response
	// returns internal error for any other error
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return &cxFitttPb.GetSubscriptionsForActorResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching subscriptions for actor", zap.Error(te))
		return &cxFitttPb.GetSubscriptionsForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching subscriptions for actor"),
		}, nil
	}

	return s.buildActorSubscriptionsResponse(resp, ctx)
}

// function to change type of RuleSubscriptionState variables from cx to rms
func parseRuleSubscriptionStates(states []cxFitttPb.RuleSubscriptionState) []rmsPb.RuleSubscriptionState {
	var ruleSubscriptionList []rmsPb.RuleSubscriptionState
	for _, ruleState := range states {
		switch ruleState {
		case cxFitttPb.RuleSubscriptionState_SUBSCRIPTION_STATE_UNDEFINED:
			ruleSubscriptionList = append(ruleSubscriptionList, rmsPb.RuleSubscriptionState_RULE_SUBSCRIPTION_STATE_UNSPECIFIED)
		case cxFitttPb.RuleSubscriptionState_ACTIVE_SUBSCRIPTION:
			ruleSubscriptionList = append(ruleSubscriptionList, rmsPb.RuleSubscriptionState_ACTIVE)
		case cxFitttPb.RuleSubscriptionState_INACTIVE_SUBSCRIPTION:
			ruleSubscriptionList = append(ruleSubscriptionList, rmsPb.RuleSubscriptionState_INACTIVE)
		case cxFitttPb.RuleSubscriptionState_CLOSED_SUBSCRIPTION:
			ruleSubscriptionList = append(ruleSubscriptionList, rmsPb.RuleSubscriptionState_CLOSED)
		}
	}
	return ruleSubscriptionList
}

func (s *Service) buildActorSubscriptionsResponse(resp *rmsPb.GetSubscriptionsByActorIdResponse, ctx context.Context) (*cxFitttPb.GetSubscriptionsForActorResponse, error) {
	cxResp := &cxFitttPb.GetSubscriptionsForActorResponse{
		Status:      resp.GetStatus(),
		PageContext: helper.ConvertToCxPageContext(resp.GetPageContext()),
	}

	// populating subscriptions field of cxResp
	for _, subscriptionData := range resp.GetSubscriptionData() {
		// TODO(team): update this
		// getting rule details through GetRuleId function for now
		// need to change this in future
		// GetSubscriptionsByActorIdResponse proto should be updated to contain rule field or subfield
		ruleResp, err := s.rmsClient.GetRuleById(ctx, &rmsPb.GetRuleByIdRequest{
			RuleId: subscriptionData.GetRuleId(),
		})

		// logging the error if rule can't be fetched
		if te := epifigrpc.RPCError(ruleResp, err); te != nil && !ruleResp.GetStatus().IsRecordNotFound() {
			cxLogger.Error(ctx, "error while fetching rules for subscriptions for actor", zap.Error(te))
		}
		sub, err := s.buildFitRuleSubscription(ctx, subscriptionData, ruleResp)
		if err != nil {
			return nil, errors.Wrap(err, "error building fit rule subscription")
		}
		cxResp.Subscriptions = append(cxResp.GetSubscriptions(), sub)
	}

	return cxResp, nil
}

func (s *Service) buildFitRuleSubscription(ctx context.Context, subscriptionData *rmsPb.RuleSubscription, ruleResp *rmsPb.GetRuleByIdResponse) (*cxFitttPb.FitRuleSubscription, error) {
	maskedSdNumber := ""
	// creating FitRuleSubscription type variable to return
	ruleSubscription := &cxFitttPb.FitRuleSubscription{
		RuleId:                 subscriptionData.GetRuleId(),
		ValidFrom:              subscriptionData.GetValidFrom(),
		ValidTill:              subscriptionData.GetValidTill(),
		State:                  subscriptionData.GetState().String(),
		Id:                     subscriptionData.GetId(),
		SubscriptionExpiryData: parseSubscriptionExpiryData(subscriptionData.GetSubscriptionExpiryData()),
		CutoffParamUpdatedAt:   subscriptionData.GetCutoffParamUpdatedAt(),
	}

	// ruleResp can be nil, In that case Rule, DescriptionWithParamValues, MaskedSdNumber fields
	// of ruleSubscription will not be filled.
	if ruleResp != nil {
		// creating DescriptionWithParamValues from template display string
		displayString := ruleResp.GetRule().GetDescription().GetDisplayStr()
		for key, value := range subscriptionData.GetRuleParamValues().GetRuleParamValues() {
			switch value.GetValue().(type) {
			case *rmsPb.Value_SdValue:
				displayString = strings.Replace(displayString, key, value.GetSdValue().GetName(), 1)
				maskedSdNumber = value.GetSdValue().GetMaskedAccountNumber()
			case *rmsPb.Value_MutualFundVal:
				displayString = strings.Replace(displayString, key, value.GetMutualFundVal().GetName(), 1)
			case *rmsPb.Value_UsStockValue:
				stock, err := s.usStocks.GetStock(ctx, value.GetUsStockValue().GetStockId())
				if err != nil {
					return nil, errors.Wrap(err, "error getting stock name")
				}
				displayString = strings.Replace(displayString, key, stock.Name, 1)
			case *rmsPb.Value_MerchantVal:
				displayString = strings.Replace(displayString, key, value.GetMerchantVal().GetName(), 1)
			case *rmsPb.Value_PlayerVal:
				displayString = strings.Replace(displayString, key, value.GetPlayerVal().GetName(), 1)
			case *rmsPb.Value_TeamVal:
				displayString = strings.Replace(displayString, key, value.GetTeamVal().GetName(), 1)
			case *rmsPb.Value_MoneyVal:
				m := value.GetMoneyVal().GetBeMoney()
				displayString = strings.Replace(displayString, key, money.ToDisplayString(m), 1)
			default:
				displayString = strings.Replace(displayString, key, value.String(), 1)
			}
		}

		ruleSubscription.DescriptionWithParamValues = displayString
		ruleSubscription.MaskedSdNumber = maskedSdNumber
		ruleSubscription.Rule = &cxFitttPb.FitRule{
			Id:                            ruleResp.GetRule().GetId(),
			Name:                          ruleResp.GetRule().GetName(),
			Description:                   ruleResp.GetRule().GetDescription().GetDisplayStr(),
			Category:                      ruleResp.GetRule().GetCategory().String(),
			MaxSubscriptionsPerActor:      ruleResp.GetRule().GetMaxSubscriptionsPerActor(),
			DefaultSubscriptionExpiryData: parseSubscriptionExpiryData(ruleResp.GetRule().GetDefaultSubscriptionExpiryData()),
			State:                         ruleResp.GetRule().GetState().String(),
		}
	}

	return ruleSubscription, nil
}

// function to change variables of type subscriptionExpiryData from rms type to cx fittt type
func parseSubscriptionExpiryData(subscriptionExpiryData *rmsPb.SubscriptionExpiryData) *cxFitttPb.SubscriptionExpiryData {
	targetSubscriptionExpiryData := &cxFitttPb.SubscriptionExpiryData{}
	targetSubscriptionExpiryData.MaxValidityInDays = subscriptionExpiryData.GetMaxValidityInDays()
	switch subscriptionExpiryData.GetMaxLimit().(type) {
	case *rmsPb.SubscriptionExpiryData_MaxAutoSaveAmount:
		targetSubscriptionExpiryData.MaxLimit = &cxFitttPb.SubscriptionExpiryData_MaxAutoSaveAmount{
			MaxAutoSaveAmount: subscriptionExpiryData.GetMaxAutoSaveAmount(),
		}
	case *rmsPb.SubscriptionExpiryData_MaxNoOfAutoPay:
		targetSubscriptionExpiryData.MaxLimit = &cxFitttPb.SubscriptionExpiryData_MaxNoOfAutoPay{
			MaxNoOfAutoPay: subscriptionExpiryData.GetMaxNoOfAutoPay(),
		}
	}
	return targetSubscriptionExpiryData
}

func (s *Service) GetSubscriptionExecutionInfo(ctx context.Context, req *cxFitttPb.GetSubscriptionExecutionInfoRequest) (*cxFitttPb.GetSubscriptionExecutionInfoResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxFitttPb.GetSubscriptionExecutionInfoResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	resp, err := s.fitttClient.GetSubscriptionExecutionInfo(ctx, &fitttPb.GetSubscriptionExecutionInfoRequest{
		SubscriptionId: req.GetSubscriptionId(),
		ActorId:        req.GetHeader().GetActor().GetId(),
		PageContextV2:  convertToRPCPageContext(req.GetPageContext()),
	})

	// returns record not found error, if received that in response
	// returns internal error for any other error
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return &cxFitttPb.GetSubscriptionExecutionInfoResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching subscription execution info", zap.Error(te))
		return &cxFitttPb.GetSubscriptionExecutionInfoResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching subscription execution info"),
		}, nil
	}

	return s.buildSubscriptionExecutionInfoResponse(resp)
}

// function to convert page context from cx to fittt
func convertToRPCPageContext(pageContext *cxPb.PageContextRequest) *rpc.PageContextRequest {
	if pageContext == nil {
		return nil
	}
	targetPageContext := &rpc.PageContextRequest{}
	targetPageContext.PageSize = pageContext.GetPageSize()
	if pageContext.GetPageSize() == 0 {
		targetPageContext.PageSize = 10
	}
	switch pageContext.GetToken().(type) {
	case *cxPb.PageContextRequest_AfterToken:
		targetPageContext.Token = &rpc.PageContextRequest_AfterToken{
			AfterToken: pageContext.GetAfterToken(),
		}
	case *cxPb.PageContextRequest_BeforeToken:
		targetPageContext.Token = &rpc.PageContextRequest_BeforeToken{
			BeforeToken: pageContext.GetBeforeToken(),
		}
	}
	return targetPageContext
}

func (s *Service) buildSubscriptionExecutionInfoResponse(resp *fitttPb.GetSubscriptionExecutionInfoResponse) (*cxFitttPb.GetSubscriptionExecutionInfoResponse, error) {
	cxResp := &cxFitttPb.GetSubscriptionExecutionInfoResponse{
		Status: resp.GetStatus(),
		PageContext: &cxPb.PageContextResponse{
			BeforeToken: resp.GetPageContextV2().GetBeforeToken(),
			HasBefore:   resp.GetPageContextV2().GetHasBefore(),
			AfterToken:  resp.GetPageContextV2().GetAfterToken(),
			HasAfter:    resp.GetPageContextV2().GetHasAfter(),
		},
	}

	// populating RuleExecutions field of cxResp
	for _, ruleExecution := range resp.GetRuleExecution() {
		cxResp.RuleExecutions = append(cxResp.GetRuleExecutions(), &cxFitttPb.FitRuleExecution{
			HistoryType: ruleExecution.GetHistoryType().String(),
			Amount:      ruleExecution.GetAmount(),
			Timestamp:   ruleExecution.GetTimestamp(),
			Status:      ruleExecution.GetStatus().String(),
		})
	}

	return cxResp, nil
}
