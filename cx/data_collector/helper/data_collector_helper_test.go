package helper

import (
	"context"
	"flag"
	"os"
	"reflect"
	"sort"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"

	rpcPb "github.com/epifi/be-common/api/rpc"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountBalanceMock "github.com/epifi/gamma/api/accounts/balance/mocks"
	depositPb "github.com/epifi/gamma/api/deposit"
	mocks2 "github.com/epifi/gamma/api/deposit/mocks"
	investmentCatalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mocks3 "github.com/epifi/gamma/api/investment/mutualfund/catalog/mocks"
	"github.com/epifi/gamma/api/p2pinvestment"
	mocks4 "github.com/epifi/gamma/api/p2pinvestment/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/usstocks/order"
	orderManagerMockPb "github.com/epifi/gamma/api/usstocks/order/mocks"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	mocks5 "github.com/epifi/gamma/api/usstocks/portfolio/mocks"
	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, teardown = test.InitTestServer(false)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestDataCollectorHelper_IsSavingsAccountClosureAllowed(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()

	mockSavingsClient := mocks.NewMockSavingsClient(ctr)
	mockDepositClient := mocks2.NewMockDepositClient(ctr)
	mockInvestmentCatalogClient := mocks3.NewMockCatalogManagerClient(ctr)
	mockP2PClient := mocks4.NewMockP2PInvestmentClient(ctr)
	mockUssStocksPortfolioManagerClient := mocks5.NewMockPortfolioManagerClient(ctr)
	mockUsstocksOrderManagerClient := orderManagerMockPb.NewMockOrderManagerClient(ctr)
	mockAccountBalanceClinet := accountBalanceMock.NewMockBalanceClient(ctr)
	type args struct {
		mocks   []interface{}
		ctx     context.Context
		userId  string
		actorId string
	}
	tests := []struct {
		name        string
		args        args
		wantBool    bool
		wantStrList []string
		wantErr     bool
	}{
		{
			name: "got error in savings account",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
						Account: &savingsPb.Account{Id: "AC1"},
					}, nil),
					mockAccountBalanceClinet.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
					mockDepositClient.EXPECT().ListDepositAccounts(gomock.Any(), gomock.Any()).Return(&depositPb.ListDepositAccountsResponse{
						Status:   rpcPb.StatusOk(),
						Accounts: nil,
					}, nil),
					mockInvestmentCatalogClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&investmentCatalogPb.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &investmentCatalogPb.InvestmentSummaryInfo{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        0,
							},
						},
					}, nil),
					mockP2PClient.EXPECT().GetInvestmentDashboard(gomock.Any(), gomock.Any()).Return(&p2pinvestment.GetInvestmentDashboardResponse{
						Status: rpcPb.StatusOk(),
						InvestmentData: &p2pinvestment.GetInvestmentDashboardResponse_InvestmentData{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        0,
							},
						},
					}, nil),
					mockUssStocksPortfolioManagerClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&portfolio.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &portfolio.InvestmentSummaryInfo{
							HasInvested: false,
						},
					}, nil),
					mockUsstocksOrderManagerClient.EXPECT().GetOrders(gomock.Any(), gomock.Any()).Return(&order.GetOrdersResponse{Status: rpcPb.StatusOk()}, nil),
				},
				ctx:     context.Background(),
				userId:  "u1",
				actorId: "a1",
			},
			wantBool:    false,
			wantStrList: nil,
			wantErr:     true,
		},
		{
			name: "mf, usstocks and p2p inv found",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
						Account: &savingsPb.Account{Id: "AC1"},
					}, nil),
					mockAccountBalanceClinet.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
						Status: rpcPb.StatusOk(),
						AvailableBalance: &money.Money{
							CurrencyCode: "INR",
							Units:        0,
						},
					}, nil),
					mockDepositClient.EXPECT().ListDepositAccounts(gomock.Any(), gomock.Any()).Return(&depositPb.ListDepositAccountsResponse{
						Status:   rpcPb.StatusOk(),
						Accounts: nil,
					}, nil),
					mockInvestmentCatalogClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&investmentCatalogPb.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &investmentCatalogPb.InvestmentSummaryInfo{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
						},
					}, nil),
					mockP2PClient.EXPECT().GetInvestmentDashboard(gomock.Any(), gomock.Any()).Return(&p2pinvestment.GetInvestmentDashboardResponse{
						Status: rpcPb.StatusOk(),
						InvestmentData: &p2pinvestment.GetInvestmentDashboardResponse_InvestmentData{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
						},
					}, nil),
					mockUssStocksPortfolioManagerClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&portfolio.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &portfolio.InvestmentSummaryInfo{
							HasInvested: true,
						},
					}, nil),
				},
				ctx:     context.Background(),
				userId:  "u1",
				actorId: "a1",
			},
			wantBool:    false,
			wantStrList: []string{greaterThanOneCurrentValueInP2PInvestmentFailureReason, nonZeroCurrentValueInvestmentFailureReason, nonZeroCurrentValueInUSStocksInvestmentFailureReason},
			wantErr:     false,
		},
		{
			name: "p2p inv found greater than 1 rupee",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
						Account: &savingsPb.Account{Id: "AC1"},
					}, nil),
					mockAccountBalanceClinet.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
						Status: rpcPb.StatusOk(),
						AvailableBalance: &money.Money{
							CurrencyCode: "INR",
							Units:        0,
						},
					}, nil),
					mockDepositClient.EXPECT().ListDepositAccounts(gomock.Any(), gomock.Any()).Return(&depositPb.ListDepositAccountsResponse{
						Status:   rpcPb.StatusOk(),
						Accounts: nil,
					}, nil),
					mockInvestmentCatalogClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&investmentCatalogPb.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &investmentCatalogPb.InvestmentSummaryInfo{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        0,
							},
						},
					}, nil),
					mockP2PClient.EXPECT().GetInvestmentDashboard(gomock.Any(), gomock.Any()).Return(&p2pinvestment.GetInvestmentDashboardResponse{
						Status: rpcPb.StatusOk(),
						InvestmentData: &p2pinvestment.GetInvestmentDashboardResponse_InvestmentData{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
						},
					}, nil),
					mockUssStocksPortfolioManagerClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&portfolio.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &portfolio.InvestmentSummaryInfo{
							HasInvested: false,
						},
					}, nil),
					mockUsstocksOrderManagerClient.EXPECT().GetOrders(gomock.Any(), gomock.Any()).Return(&order.GetOrdersResponse{Status: rpcPb.StatusOk()}, nil),
				},
				ctx:     context.Background(),
				userId:  "u1",
				actorId: "a1",
			},
			wantBool:    false,
			wantStrList: []string{greaterThanOneCurrentValueInP2PInvestmentFailureReason},
			wantErr:     false,
		},
		{
			name: "p2p inv found but less than 1 rupee",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
						Account: &savingsPb.Account{Id: "AC1"},
					}, nil),
					mockAccountBalanceClinet.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
						Status: rpcPb.StatusOk(),
						AvailableBalance: &money.Money{
							CurrencyCode: "INR",
							Units:        0,
						},
					}, nil),
					mockDepositClient.EXPECT().ListDepositAccounts(gomock.Any(), gomock.Any()).Return(&depositPb.ListDepositAccountsResponse{
						Status:   rpcPb.StatusOk(),
						Accounts: nil,
					}, nil),
					mockInvestmentCatalogClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&investmentCatalogPb.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &investmentCatalogPb.InvestmentSummaryInfo{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        0,
							},
						},
					}, nil),
					mockP2PClient.EXPECT().GetInvestmentDashboard(gomock.Any(), gomock.Any()).Return(&p2pinvestment.GetInvestmentDashboardResponse{
						Status: rpcPb.StatusOk(),
						InvestmentData: &p2pinvestment.GetInvestmentDashboardResponse_InvestmentData{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        0,
								Nanos:        *********,
							},
						},
					}, nil),
					mockUssStocksPortfolioManagerClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&portfolio.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &portfolio.InvestmentSummaryInfo{
							HasInvested: false,
						},
					}, nil),
					mockUsstocksOrderManagerClient.EXPECT().GetOrders(gomock.Any(), gomock.Any()).Return(&order.GetOrdersResponse{Status: rpcPb.StatusOk()}, nil),
				},
				ctx:     context.Background(),
				userId:  "u1",
				actorId: "a1",
			},
			wantBool:    true,
			wantStrList: nil,
			wantErr:     false,
		},
		{
			name: "eligible for account closure",
			args: args{
				mocks: []interface{}{
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{
						Account: &savingsPb.Account{Id: "AC1"},
					}, nil),
					mockAccountBalanceClinet.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
						Status: rpcPb.StatusOk(),
						AvailableBalance: &money.Money{
							CurrencyCode: "INR",
							Units:        0,
						},
					}, nil),
					mockDepositClient.EXPECT().ListDepositAccounts(gomock.Any(), gomock.Any()).Return(&depositPb.ListDepositAccountsResponse{
						Status:   rpcPb.StatusOk(),
						Accounts: nil,
					}, nil),
					mockInvestmentCatalogClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&investmentCatalogPb.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &investmentCatalogPb.InvestmentSummaryInfo{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        0,
							},
						},
					}, nil),
					mockP2PClient.EXPECT().GetInvestmentDashboard(gomock.Any(), gomock.Any()).Return(&p2pinvestment.GetInvestmentDashboardResponse{
						Status: rpcPb.StatusOk(),
						InvestmentData: &p2pinvestment.GetInvestmentDashboardResponse_InvestmentData{
							CurrentValue: &money.Money{
								CurrencyCode: "INR",
								Units:        0,
							},
						},
					}, nil),
					mockUssStocksPortfolioManagerClient.EXPECT().GetInvestmentSummaryInfo(gomock.Any(), gomock.Any()).Return(&portfolio.GetInvestmentSummaryInfoResponse{
						Status: rpcPb.StatusOk(),
						InvestmentSummary: &portfolio.InvestmentSummaryInfo{
							HasInvested: false,
						},
					}, nil),
					mockUsstocksOrderManagerClient.EXPECT().GetOrders(gomock.Any(), gomock.Any()).Return(&order.GetOrdersResponse{Status: rpcPb.StatusOk()}, nil),
				},
				ctx:     context.Background(),
				userId:  "u1",
				actorId: "a1",
			},
			wantBool:    true,
			wantStrList: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewDataCollectorHelper(nil, nil, nil, nil, nil, mockSavingsClient, mockDepositClient, mockInvestmentCatalogClient, mockP2PClient, mockUssStocksPortfolioManagerClient, mockUsstocksOrderManagerClient, mockAccountBalanceClinet, nil)
			gotBool, gotStrList, err := s.IsSavingsAccountClosureAllowed(tt.args.ctx, tt.args.userId, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsSavingsAccountClosureAllowed() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotBool != tt.wantBool {
				t.Errorf("IsSavingsAccountClosureAllowed() gotBool = %v, wantBool %v", gotBool, tt.wantBool)
				return
			}
			// sorting both string list here
			// since concurrency does not guarantee which channel will exit first and return failure reason
			// {A, B} is same as {B, A} in our case, so sorting both of them to pass the comparison
			sort.Strings(gotStrList)
			sort.Strings(tt.wantStrList)
			if !reflect.DeepEqual(gotStrList, tt.wantStrList) {
				t.Errorf("IsSavingsAccountClosureAllowed() gotStrList = %v, wantStrList = %v", gotStrList, tt.wantStrList)
			}
		})
	}
}
