package helper

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/epifi/gamma/api/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/pkg/errors"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	enums "github.com/epifi/gamma/api/accounts/balance/enums"
	cxAccountPb "github.com/epifi/gamma/api/cx/data_collector/account"
	"github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	"github.com/epifi/gamma/usstocks/utils"

	accountsPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	investmentCatalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/be-common/pkg/money"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxPb "github.com/epifi/gamma/api/cx"

	cxLogger "github.com/epifi/gamma/cx/logger"

	paymentPb "github.com/epifi/gamma/api/order/payment"

	orderPb "github.com/epifi/gamma/api/order"

	"go.uber.org/zap"

	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	piPb "github.com/epifi/gamma/api/paymentinstrument"

	tPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
)

type IDataCollectorHelper interface {
	GetPiDetails(ctx context.Context, piId, actorId string, isMaskingRequired bool) (*tPb.PIDetails, error)
	FetchOrderForTxnId(ctx context.Context, transactionId string) (*orderPb.GetOrderResponse, error)
	MaskStringExceptLast4(val string) string
	IsSavingsAccountClosureAllowed(ctx context.Context, userId string, actorId string) (bool, []string, error)
	GetAccountFreezeAndBalanceInfo(ctx context.Context, userId string, actorId string) (*cxAccountPb.AccountFreezeInfo, error)
}

type DataCollectorHelper struct {
	actorClient                    actorPb.ActorClient
	piClient                       piPb.PiClient
	usersClient                    userPb.UsersClient
	pClient                        paymentPb.PaymentClient
	orderClient                    orderPb.OrderServiceClient
	savingsClient                  savingsPb.SavingsClient
	depositClient                  depositPb.DepositClient
	investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient
	p2pInvestmentClient            p2pinvestment.P2PInvestmentClient
	portfolioManagerClient         portfolio.PortfolioManagerClient
	usstocksOrderManagerClient     order.OrderManagerClient
	paySavingsBalanceClient        accountBalancePb.BalanceClient
	ffClient                       firefly.FireflyClient
}

var _ IDataCollectorHelper = &DataCollectorHelper{}

func NewDataCollectorHelper(actorClient actorPb.ActorClient, piClient piPb.PiClient, usersClient userPb.UsersClient,
	pClient paymentPb.PaymentClient, orderClient orderPb.OrderServiceClient, savingsClient savingsPb.SavingsClient,
	depositClient depositPb.DepositClient, investmentCatalogManagerClient investmentCatalogPb.CatalogManagerClient,
	p2pInvestmentClient p2pinvestment.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient,
	usstocksOrderManagerClient order.OrderManagerClient, paySavingsBalanceClient accountBalancePb.BalanceClient, ffClient firefly.FireflyClient) *DataCollectorHelper {
	return &DataCollectorHelper{
		actorClient:                    actorClient,
		piClient:                       piClient,
		usersClient:                    usersClient,
		pClient:                        pClient,
		orderClient:                    orderClient,
		savingsClient:                  savingsClient,
		depositClient:                  depositClient,
		investmentCatalogManagerClient: investmentCatalogManagerClient,
		p2pInvestmentClient:            p2pInvestmentClient,
		portfolioManagerClient:         portfolioManagerClient,
		usstocksOrderManagerClient:     usstocksOrderManagerClient,
		paySavingsBalanceClient:        paySavingsBalanceClient,
		ffClient:                       ffClient,
	}
}

const (
	savingsNonZeroBalanceFailureReason                     = "non zero savings account balance"
	activeDepositsFailureReason                            = "active deposits present"
	nonZeroCurrentValueInvestmentFailureReason             = "Current value in investment is not 0. User can redeem externally, please take written consent before raising for account closure request"
	greaterThanOneCurrentValueInP2PInvestmentFailureReason = "current value in P2P investment is greater than 1 rupee"
	nonZeroCurrentValueInUSStocksInvestmentFailureReason   = "current value in USStocks investment is not 0"
	nonClosedCreditCardFailureReason                       = "The user has a credit card which hasn't been closed, account closure not allowed" // #nosec G101
)

func (d *DataCollectorHelper) GetPiDetails(ctx context.Context, piId, actorId string, isMaskingRequired bool) (*tPb.PIDetails, error) {
	piDetails := &tPb.PIDetails{}
	piResp, err := d.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{Id: piId})
	if te := epifigrpc.RPCError(piResp, err); te != nil {
		cxLogger.Error(ctx, "error getting pi details by pi id", zap.String(logger.PI_ID, piId), zap.Error(te))
		return nil, fmt.Errorf("error getting pi details by pi id")
	}
	piDetails.PiState = piResp.GetPaymentInstrument().GetState().String()
	// Pi details like account number, card number and vpa should always be masked
	switch piResp.GetPaymentInstrument().GetIdentifier().(type) {
	case *piPb.PaymentInstrument_Upi:
		piDetails.PiValue = MaskVPA(piResp.GetPaymentInstrument().GetUpi().GetVpa())
	case *piPb.PaymentInstrument_Account:
		piDetails.PiValue = maskString(piResp.GetPaymentInstrument().GetAccount().GetActualAccountNumber())
	case *piPb.PaymentInstrument_Card:
		piDetails.PiValue = maskString(piResp.GetPaymentInstrument().GetCard().GetActualCardNumber())
	default:
		cxLogger.Error(ctx, "payment instrument not in UPI, card, account", zap.String(logger.PI_ID, piId))
	}
	piDetails.PiType = piResp.GetPaymentInstrument().GetType().String()
	actorResp, err := d.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		cxLogger.Error(ctx, "error getting actor details by actor id", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return nil, fmt.Errorf("error getting actor details by actor id")
	}
	if actorResp.GetType() == typesPb.ActorType_USER {
		userId := actorResp.GetEntityId()
		userResp, err := d.usersClient.GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_Id{Id: userId}})
		if te := epifigrpc.RPCError(userResp, err); te != nil {
			cxLogger.Error(ctx, "error getting user details by entity id", zap.String(logger.USER_ID, userId), zap.Error(te))
			return nil, fmt.Errorf("error getting user details by entity id")
		}
		piDetails.CustomerName = userResp.GetUser().GetProfile().GetKycName().ToFirstNameLastNameString()
	} else {
		piDetails.CustomerName = actorResp.GetName().ToFirstNameLastNameString()
	}
	if isMaskingRequired {
		piDetails.CustomerName = maskString(piDetails.GetCustomerName())
	}
	return piDetails, nil
}

func maskString(val string) string {
	rs := []rune(val)
	for i := 2; i < len(rs)-2; i++ {
		rs[i] = 'X'
	}
	return string(rs)
}

func MaskVPA(vpa string) string {
	parts := strings.Split(vpa, "@")
	rs := []rune(parts[0])
	for i := 0; i < len(rs)-3; i++ {
		rs[i] = 'X'
	}
	maskedHandle := string(rs)
	return maskedHandle + "@" + parts[1]
}

func (d *DataCollectorHelper) FetchOrderForTxnId(ctx context.Context, transactionId string) (*orderPb.GetOrderResponse, error) {
	resp, err := d.pClient.GetOrderId(ctx, &paymentPb.GetOrderIdRequest{TransactionId: transactionId})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch order id", zap.Error(te), zap.String(logger.TXN_ID, transactionId))
		return nil, te
	}
	if resp.GetOrderId() == "" {
		cxLogger.Error(ctx, "order not found", zap.String(logger.TXN_ID, transactionId))
		return nil, fmt.Errorf("order not found")
	}
	orderResp, err := d.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: resp.GetOrderId()}})
	if te := epifigrpc.RPCError(orderResp, err); te != nil {
		logger.Error(ctx, "failed to fetch order", zap.Error(te), zap.String(logger.ORDER_ID, resp.GetOrderId()))
		return nil, te
	}
	return orderResp, nil
}

func (d *DataCollectorHelper) MaskStringExceptLast4(val string) string {
	rs := []rune(val)
	for i := 0; i < len(rs)-4; i++ {
		rs[i] = 'X'
	}
	return string(rs)
}

// function to change page context type from cx to rpc
func ConvertToRpcPageContext(pageContext *cxPb.PageContextRequest) *rpcPb.PageContextRequest {
	if pageContext == nil {
		return nil
	}
	targetPageContext := &rpcPb.PageContextRequest{}
	targetPageContext.PageSize = pageContext.GetPageSize()
	if pageContext.GetPageSize() == 0 {
		targetPageContext.PageSize = 10
	}
	switch pageContext.GetToken().(type) {
	case *cxPb.PageContextRequest_AfterToken:
		targetPageContext.Token = &rpcPb.PageContextRequest_AfterToken{
			AfterToken: pageContext.GetAfterToken(),
		}
	case *cxPb.PageContextRequest_BeforeToken:
		targetPageContext.Token = &rpcPb.PageContextRequest_BeforeToken{
			BeforeToken: pageContext.GetBeforeToken(),
		}
	}
	return targetPageContext
}

// function to change page context type from rpc to cx
func ConvertToCxPageContext(pageContext *rpcPb.PageContextResponse) *cxPb.PageContextResponse {
	return &cxPb.PageContextResponse{
		BeforeToken: pageContext.GetBeforeToken(),
		HasBefore:   pageContext.GetHasBefore(),
		AfterToken:  pageContext.GetAfterToken(),
		HasAfter:    pageContext.GetHasAfter(),
	}
}

// IsSavingsAccountClosureAllowed -- helper function to check if closing of the savings account is allowed
// The following checks are put in before deciding the savings account closure eligibility
// 1. Savings account should have a zero balance
// 2. No active/in-progress deposit accounts associated with the savings account should exist
// 3. If a user has a credit card that is in any state other than CLOSED, then the account cannot be closed
// 4. Other checks that have been added to the code but not been commented here
// If both the above checks pass, savings account closure is allowed.
func (d *DataCollectorHelper) IsSavingsAccountClosureAllowed(ctx context.Context, userId string, actorId string) (bool, []string, error) {

	const numberOfGoroutines = 6
	// Define the channels to have buffer size of as many number of Goroutines we are going to define
	// The channel buffer size should be at least numberOfGoroutines to avoid Goroutines blocking on writing to channel
	failureReasonsChannel := make(chan string, numberOfGoroutines)
	failureErrorsChannel := make(chan error, numberOfGoroutines)
	isAccountClosureAllowedForAllConditionsListChannel := make(chan bool, numberOfGoroutines)

	// add number of go routines which are going to be spawned
	var wg sync.WaitGroup
	wg.Add(numberOfGoroutines)

	// Check 1: Savings account should have a zero balance
	goroutine.RunWithCtx(ctx, func(gtCtx context.Context) {
		defer wg.Done()
		isAccountClosureAllowed, closureNotAllowedReason, err := d.ValidateSavingsAccountBalanceForAccountClosure(gtCtx, userId, actorId)
		if err != nil {
			failureErrorsChannel <- err
		}
		if closureNotAllowedReason != "" {
			failureReasonsChannel <- closureNotAllowedReason
		}
		if !isAccountClosureAllowed {
			isAccountClosureAllowedForAllConditionsListChannel <- isAccountClosureAllowed
		}
	})

	// Check 2: No active/in-progress deposit accounts should exist
	goroutine.RunWithCtx(ctx, func(gtCtx context.Context) {
		defer wg.Done()
		isAccountClosureAllowed, closureNotAllowedReason, err := d.ValidateDepositsAccountsForAccountClosure(gtCtx, actorId)
		if err != nil {
			failureErrorsChannel <- err
		}
		if closureNotAllowedReason != "" {
			failureReasonsChannel <- closureNotAllowedReason
		}
		if !isAccountClosureAllowed {
			isAccountClosureAllowedForAllConditionsListChannel <- isAccountClosureAllowed
		}
	})

	// Check 3:
	// check if user has any current value in investments, we should show that account is not ready to be closed.
	// user would need to be asked to liquidate for now before they can close the account
	goroutine.RunWithCtx(ctx, func(gtCtx context.Context) {
		defer wg.Done()
		isAccountClosureAllowed, closureNotAllowedReason, err := d.ValidateInvestmentAmountForAccountClosure(gtCtx, actorId)
		if err != nil {
			failureErrorsChannel <- err
		}
		if closureNotAllowedReason != "" {
			failureReasonsChannel <- closureNotAllowedReason
		}
		if !isAccountClosureAllowed {
			isAccountClosureAllowedForAllConditionsListChannel <- isAccountClosureAllowed
		}
	})

	// Check 4:
	// check if user has any current value in p2p investments, if current value is not 0, we should show that account is not ready to be closed.
	// user would need to be asked to liquidate for now before they can close the account
	goroutine.RunWithCtx(ctx, func(gtCtx context.Context) {
		defer wg.Done()
		isAccountClosureAllowed, closureNotAllowedReason, err := d.ValidateP2PInvestmentAmountForAccountClosure(gtCtx, actorId)
		if err != nil {
			failureErrorsChannel <- err
		}
		if closureNotAllowedReason != "" {
			failureReasonsChannel <- closureNotAllowedReason
		}
		if !isAccountClosureAllowed {
			isAccountClosureAllowedForAllConditionsListChannel <- isAccountClosureAllowed
		}
	})

	// Check 5:
	// check if user has any current value in USStocks investments, we should show that account is not ready to be closed.
	// user would need to be asked to liquidate for now before they can close the account
	goroutine.RunWithCtx(ctx, func(gtCtx context.Context) {
		defer wg.Done()
		isAccountClosureAllowed, closureNotAllowedReason, err := d.ValidateUSStocksInvestmentAmountForAccountClosure(gtCtx, actorId)
		if err != nil {
			failureErrorsChannel <- err
		}
		if closureNotAllowedReason != "" {
			failureReasonsChannel <- closureNotAllowedReason
		}
		if !isAccountClosureAllowed {
			isAccountClosureAllowedForAllConditionsListChannel <- isAccountClosureAllowed
		}
	})

	// Check 6:
	// If a user has a credit card that is in any state other than CLOSED, then the account cannot be closed
	goroutine.RunWithCtx(ctx, func(gtCtx context.Context) {
		defer wg.Done()
		isAccountClosureAllowed, closureNotAllowedReason, err := d.validateCreditCardStateForAccountClosure(gtCtx, actorId)
		if err != nil {
			failureErrorsChannel <- err
		}
		if closureNotAllowedReason != "" {
			failureReasonsChannel <- closureNotAllowedReason
		}
		if !isAccountClosureAllowed {
			isAccountClosureAllowedForAllConditionsListChannel <- isAccountClosureAllowed
		}
	})

	wg.Wait()
	close(isAccountClosureAllowedForAllConditionsListChannel)
	close(failureReasonsChannel)
	close(failureErrorsChannel)

	return IsAccountClosureAllowed(isAccountClosureAllowedForAllConditionsListChannel),
		convertFailureReasonChannelToSlice(failureReasonsChannel),
		buildErrorForAccountClosure(failureErrorsChannel)
}

func convertFailureReasonChannelToSlice(failureReasons <-chan string) []string {
	var result []string
	for reason := range failureReasons {
		result = append(result, reason)
	}
	return result
}

func buildErrorForAccountClosure(accErrList <-chan error) error {
	if len(accErrList) == 0 {
		return nil
	}

	finalErr := errors.New("")
	for accErr := range accErrList {
		finalErr = errors.Wrap(finalErr, accErr.Error())
	}
	return finalErr
}

func IsAccountClosureAllowed(closureFlags <-chan bool) bool {
	for flag := range closureFlags {
		if !flag {
			return false
		}
	}
	return true
}

func (d *DataCollectorHelper) ValidateSavingsAccountBalanceForAccountClosure(ctx context.Context, userId string, actorId string) (bool, string, error) {
	savingsAccountRes, err := d.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: userId,
		}},
	)
	if err != nil || savingsAccountRes.GetAccount() == nil {
		cxLogger.Error(ctx, "error while fetching savings account for user", zap.Error(err))
		return false, "", errors.New("error while fetching savings account")
	}
	savingsAccountBalRes, err := d.paySavingsBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{
			Id: savingsAccountRes.GetAccount().GetId(),
		},
		DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
		ActorId:       actorId,
	})
	if te := epifigrpc.RPCError(savingsAccountBalRes, err); te != nil {
		cxLogger.Error(ctx, "error while fetching savings account balance", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		return false, "", errors.Wrap(te, "error while fetching savings account balance")
	}

	if !money.IsZero(savingsAccountBalRes.GetAvailableBalance()) {
		cxLogger.Info(ctx, "savings account balance is not zero, closure not allowed")
		return false, savingsNonZeroBalanceFailureReason, nil
	}

	return true, "", nil
}

func (d *DataCollectorHelper) ValidateDepositsAccountsForAccountClosure(ctx context.Context, actorId string) (bool, string, error) {
	listDepositRes, err := d.depositClient.ListDepositAccounts(ctx, &depositPb.ListDepositAccountsRequest{
		ActorId:     actorId,
		States:      []depositPb.DepositState{depositPb.DepositState_CREATED, depositPb.DepositState_IN_PROGRESS, depositPb.DepositState_PRECLOSE_PENDING},
		Types:       []accountsPb.Type{accountsPb.Type_SMART_DEPOSIT, accountsPb.Type_FIXED_DEPOSIT},
		Provenances: []depositPb.DepositAccountProvenance{depositPb.DepositAccountProvenance_REWARDS_APP, depositPb.DepositAccountProvenance_USER_APP, depositPb.DepositAccountProvenance_CREDIT_CARD},
	})
	if te := epifigrpc.RPCError(listDepositRes, err); te != nil {
		cxLogger.Error(ctx, "error while fetching active/in-progress deposit accounts", zap.Error(err))
		return false, "", errors.New("error while fetching deposit accounts")
	}
	if len(listDepositRes.GetAccounts()) != 0 {
		cxLogger.Info(ctx, "active/in-progress deposit accounts present, closure not allowed")
		return false, activeDepositsFailureReason, nil
	}
	return true, "", nil
}

func (d *DataCollectorHelper) ValidateInvestmentAmountForAccountClosure(ctx context.Context, actorId string) (bool, string, error) {
	invResp, invErr := d.investmentCatalogManagerClient.GetInvestmentSummaryInfo(ctx, &investmentCatalogPb.GetInvestmentSummaryInfoRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(invResp, invErr); te != nil {
		cxLogger.Error(ctx, "error while fetching investment summary info for actor", zap.Error(te), zap.Any(logger.ACTOR_ID, actorId))
		return false, "", errors.New("error while fetching investment summary info for actor")
	}

	// check if current value in investments is zero or not
	// if not than don't allow account closure
	if !money.IsZero(invResp.GetInvestmentSummary().GetCurrentValue()) {
		cxLogger.Info(ctx, "current value in investments for actor is not 0, need written consent from user for closure", zap.Any(logger.ACTOR_ID, actorId))
		return true, nonZeroCurrentValueInvestmentFailureReason, nil
	}

	return true, "", nil
}

func (d *DataCollectorHelper) ValidateP2PInvestmentAmountForAccountClosure(ctx context.Context, actorId string) (bool, string, error) {
	resp, err := d.p2pInvestmentClient.GetInvestmentDashboard(ctx, &p2pinvestment.GetInvestmentDashboardRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if resp.GetStatus().IsRecordNotFound() ||
			resp.GetStatus().GetCode() == uint32(p2pinvestment.GetInvestmentDashboardResponse_INVESTOR_NOT_APPLICABLE_FOR_DASHBOARD) {
			return true, "", nil
		}
		cxLogger.Error(ctx, "error while fetching p2p investment info for actor", zap.Error(te), zap.Any(logger.ACTOR_ID, actorId))
		return false, "", errors.Wrap(te, "error while fetching p2p investment info for actor")
	}

	// check if current value in investments is greater than 1 rupee or not
	// if not than don't allow account closure
	if money.Compare(resp.GetInvestmentData().GetCurrentValue(), money.FromPaisa(100)) > 0 {
		cxLogger.Info(ctx, "current value in P2P investments for actor is greater than 1 rupee, account closure not allowed", zap.Any(logger.ACTOR_ID, actorId))
		return false, greaterThanOneCurrentValueInP2PInvestmentFailureReason, nil
	}
	return true, "", nil
}

func (d *DataCollectorHelper) ValidateUSStocksInvestmentAmountForAccountClosure(ctx context.Context, actorId string) (bool, string, error) {
	resp, err := d.portfolioManagerClient.GetInvestmentSummaryInfo(ctx, &portfolio.GetInvestmentSummaryInfoRequest{
		ActorId: actorId,
		FieldMasks: []portfolio.GetInvestmentSummaryInfoRequest_FieldMask{
			portfolio.GetInvestmentSummaryInfoRequest_FIELD_MASK_INVESTMENT_SUMMARY,
			portfolio.GetInvestmentSummaryInfoRequest_FIELD_MASK_WALLET_SUMMARY,
		},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching USStocks investment summary info for actor", zap.Error(te), zap.Any(logger.ACTOR_ID_V2, actorId))
		return false, "", errors.Wrap(te, "error while fetching USStocks investment summary info for actor")
	}
	if resp.GetInvestmentSummary().GetHasInvested() || resp.GetInvestmentSummary().GetPendingOrdersCount() > 0 ||
		resp.GetWalletSummaryInfo().GetHasFundsInWallet() || resp.GetWalletSummaryInfo().GetPendingOrdersCount() > 0 {
		cxLogger.Info(ctx, "current value in US Stocks investments for actor is greater than 1 rupee, account closure not allowed",
			zap.Any(logger.ACTOR_ID, actorId), zap.Bool("has_active_investment", resp.GetInvestmentSummary().GetHasInvested()),
			zap.Bool("has_funds_in_wallet", resp.GetWalletSummaryInfo().GetHasFundsInWallet()),
			zap.Int64("trade_orders_count", resp.GetInvestmentSummary().GetPendingOrdersCount()),
			zap.Int64("wallet_orders_count", resp.GetWalletSummaryInfo().GetPendingOrdersCount()))
		return false, nonZeroCurrentValueInUSStocksInvestmentFailureReason, nil
	}

	ordersResp, ordersErr := d.usstocksOrderManagerClient.GetOrders(ctx, &order.GetOrdersRequest{
		ActorId:     actorId,
		OrderSides:  []usstocks.OrderSide{usstocks.OrderSide_SELL},
		OrderStates: utils.NonTerminalOrderState,
	})
	if te := epifigrpc.RPCError(ordersResp, ordersErr); te != nil {
		return false, "", errors.Wrap(te, "error while fetching GetOrders for usstocks")
	}

	// check user has invested money in USStocks or not
	// if yes than don't allow account closure
	if len(ordersResp.GetOrders()) > 0 {
		cxLogger.Info(ctx, "user's sell order is in progress", zap.Any(logger.ACTOR_ID_V2, actorId))
		return false, nonZeroCurrentValueInUSStocksInvestmentFailureReason, nil
	}
	return true, "", nil
}

func (d *DataCollectorHelper) GetAccountFreezeAndBalanceInfo(ctx context.Context, userId string, actorId string) (*cxAccountPb.AccountFreezeInfo, error) {
	account, err := d.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: userId,
		}},
	)
	if err != nil || account.GetAccount() == nil {
		cxLogger.Error(ctx, "error while fetching account for user", zap.Error(err))
		return nil, errors.New("error while fetching account")
	}

	getAccountBalanceRes, err := d.paySavingsBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{
			Id: account.GetAccount().GetId(),
		},
		DataFreshness: enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
		ActorId:       actorId,
	})
	if te := epifigrpc.RPCError(getAccountBalanceRes, err); te != nil {
		cxLogger.Error(ctx, "error while fetching account balanceV1", zap.Error(err))
		return nil, errors.New("error while fetching account balanceV1")
	}

	return &cxAccountPb.AccountFreezeInfo{
		LienBalance:      getAccountBalanceRes.GetLienBalance(),
		FreezeStatus:     getAccountBalanceRes.GetFreezeRawCode(),
		FreezeReason:     getAccountBalanceRes.GetFreezeReason(),
		AvailableBalance: getAccountBalanceRes.GetAvailableBalance(),
	}, nil
}

func (d *DataCollectorHelper) validateCreditCardStateForAccountClosure(ctx context.Context, actorId string) (bool, string, error) {
	// fetch credit card info
	resp, err := d.ffClient.GetCreditCard(ctx, &firefly.GetCreditCardRequest{
		GetBy:            &firefly.GetCreditCardRequest_ActorId{ActorId: actorId},
		SelectFieldMasks: []ffEnumsPb.CreditCardFieldMask{ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE},
	})
	switch {
	case err != nil:
		cxLogger.Error(ctx, "error while fetching credit card info for actor", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		return false, "", errors.Wrap(err, "error while fetching credit card info for actor")
	//	If a record is not found, i.e. a user does not have a CC, they can close the account
	case resp.GetStatus().IsRecordNotFound():
		return true, "", nil
	case epifigrpc.RPCError(resp, err) != nil:
		te := epifigrpc.RPCError(resp, err)
		cxLogger.Error(ctx, "error while fetching credit card info for actor", zap.Error(te), zap.Any(logger.ACTOR_ID_V2, actorId))
		return false, "", errors.Wrap(te, "error while fetching credit card info for actor")
	}

	// check if the user has a non-closed CC
	if resp.GetCreditCard().GetCardState() != ffEnumsPb.CardState_CARD_STATE_CLOSED {
		cxLogger.Info(ctx, nonClosedCreditCardFailureReason, zap.Any(logger.ACTOR_ID_V2, actorId))
		return false, nonClosedCreditCardFailureReason, nil
	}
	return true, "", nil
}
