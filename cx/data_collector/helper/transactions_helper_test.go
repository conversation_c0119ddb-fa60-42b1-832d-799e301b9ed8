package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/typesv2/webui"

	"github.com/epifi/gamma/api/savings"

	"github.com/epifi/gamma/api/cx/chat/bot/workflow"
	"github.com/epifi/gamma/api/pay/cx"
	mocks3 "github.com/epifi/gamma/api/pay/cx/mocks"
	mocks4 "github.com/epifi/gamma/api/pay/mocks"
	mocks2 "github.com/epifi/gamma/api/savings/mocks"

	mockHelper "github.com/epifi/gamma/cx/test/mocks/data_collector/helper"

	"github.com/epifi/gamma/api/order/payment/mocks"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	actorPb "github.com/epifi/gamma/api/actor"
	mockActor "github.com/epifi/gamma/api/actor/mocks"
	tPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	orderPb "github.com/epifi/gamma/api/order"
	oPb "github.com/epifi/gamma/api/order/cx"
	mockOrder "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	mockPi "github.com/epifi/gamma/api/paymentinstrument/mocks"
	mockUser "github.com/epifi/gamma/api/user/mocks"
)

var (
	txnCount	= 5
	order1		= &orderPb.Order{
		FromActorId:	"test-from",
		ToActorId:	"test-to",
		Tags:		[]orderPb.OrderTag{orderPb.OrderTag_MERCHANT},
		Id:		"order-id-1",
	}
	transaction1	= &paymentPb.Transaction{
		Id: "INT-TX-1",
	}
	currentActorId1		= "test-from"
	currentActorIdA		= "test-from"
	merchantDetails1	= &tPb.MerchantDetails{
		MerchantId:	"test-to",
		MerchantName: &commontypes.Name{
			FirstName:	"first",
			MiddleName:	"middle",
			LastName:	"last",
		},
	}
	orderWithTransaction1	= &tPb.OrderWithTransaction{
		FromActorId:		"test-from",
		ToActorId:		"test-to",
		OrderTags:		[]string{"MERCHANT"},
		MerchantDetails:	merchantDetails1,
	}
	orderWithTransaction2	= &tPb.OrderWithTransaction{
		FromActorId:	"test-from",
		ToActorId:	"test-to",
		OrderTags:	[]string{"MERCHANT"},
	}
	orderWithTransactionA	= &tPb.OrderWithTransaction{
		OrderId:		"order-id-1",
		FromActorId:		"test-from",
		ToActorId:		"test-to",
		OrderTags:		[]string{"MERCHANT"},
		MerchantDetails:	merchantDetails1,
		TxnStatus:		"TRANSACTION_STATUS_UNSPECIFIED",
		PartnerBank:		"VENDOR_UNSPECIFIED",
		OrderProvenance:	"ORDER_PROVENANCE_UNSPECIFIED",
		OrderWorkflow:		"ORDER_WORKFLOW_UNSPECIFIED",
		PaymentProtocol:	"PAYMENT_PROTOCOL_UNSPECIFIED",
		ProtocolStatus:		"PROTOCOL_STATUS_UNSPECIFIED",
		DetailedStatus:		&tPb.TransactionDetailedStatus{},
		InternalTxnId:		"INT-TX-1",
		AttachEntityMeta: &ticketPb.AttachEntityMeta{
			Meta: &ticketPb.AttachEntityMeta_OrderWithTransactionMeta{
				OrderWithTransactionMeta: &ticketPb.OrderWithTransactionMeta{
					TransactionId: "INT-TX-1",
				},
			},
		},
		TxnMiscData: []*webui.LabelValueV2{
			{
				Label:		"OffApp",
				DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
				Value:		&webui.LabelValueV2_StringValue{StringValue: "Unknown"},
			},
			{
				Label:		"Order Workflow",
				DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
				Value:		&webui.LabelValueV2_StringValue{StringValue: "ORDER_WORKFLOW_UNSPECIFIED"},
			},
		},
	}
	orderWithTransactionB	= &tPb.OrderWithTransaction{
		OrderId:		"order-id-1",
		FromActorId:		"test-from",
		ToActorId:		"test-to",
		OrderTags:		[]string{"MERCHANT"},
		MerchantDetails:	merchantDetails1,
		TxnStatus:		"TRANSACTION_STATUS_UNSPECIFIED",
		PartnerBank:		"VENDOR_UNSPECIFIED",
		OrderProvenance:	"ORDER_PROVENANCE_UNSPECIFIED",
		OrderWorkflow:		"ORDER_WORKFLOW_UNSPECIFIED",
		PaymentProtocol:	"PAYMENT_PROTOCOL_UNSPECIFIED",
		ProtocolStatus:		"PROTOCOL_STATUS_UNSPECIFIED",
		DetailedStatus:		&tPb.TransactionDetailedStatus{},
		InternalTxnId:		"INT-TX-1",
		TransactionType:	tPb.TransactionType_DEBIT,
		AttachEntityMeta: &ticketPb.AttachEntityMeta{
			Meta: &ticketPb.AttachEntityMeta_OrderWithTransactionMeta{
				OrderWithTransactionMeta: &ticketPb.OrderWithTransactionMeta{
					TransactionId: "INT-TX-1",
				},
			},
		},
		TxnMiscData: []*webui.LabelValueV2{
			{
				Label:		"OffApp",
				DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
				Value:		&webui.LabelValueV2_StringValue{StringValue: "Unknown"},
			},
			{
				Label:		"Order Workflow",
				DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
				Value:		&webui.LabelValueV2_StringValue{StringValue: "ORDER_WORKFLOW_UNSPECIFIED"},
			},
		},
	}

	GetEntityDetailsByActorIdRequest1	= &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: "test-to",
	}
	GetEntityDetailsByActorIdResponse1	= &actorPb.GetEntityDetailsByActorIdResponse{
		Status:	rpcPb.StatusOk(),
		Name: &commontypes.Name{
			FirstName:	"first",
			MiddleName:	"middle",
			LastName:	"last",
		},
	}
	GetEntityDetailsByActorIdResponseA	= &actorPb.GetEntityDetailsByActorIdResponse{
		Status:	rpcPb.StatusOk(),
		Name: &commontypes.Name{
			FirstName:	"first",
			MiddleName:	"middle",
			LastName:	"last",
		},
	}
	ordersWithTransactionsA	= &orderPb.GetOrdersWithTransactionsResponse{
		Status:	rpcPb.StatusOk(),
		OrderWithTransactions: []*orderPb.OrderWithTransactions{
			{
				Order:		order1,
				Transactions:	[]*paymentPb.Transaction{transaction1},
			},
		},
	}
	account1	= &savings.Account{Id: "1234"}
)

// TODO: As of now tests only for Merchant details logic
func TestService_BuildTxnDetailsProto(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mActorClient := mockActor.NewMockActorClient(ctr)
	mPiClient := mockPi.NewMockPiClient(ctr)
	mUserClient := mockUser.NewMockUsersClient(ctr)
	dataCollectorHelper := NewDataCollectorHelper(mActorClient, mPiClient, mUserClient, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks			[]interface{}
		ctx			context.Context
		order			*orderPb.Order
		txn			*paymentPb.Transaction
		currentActorId		string
		isMaskingRequired	bool
	}
	tests := []struct {
		name	string
		args	args
		want	*tPb.OrderWithTransaction
	}{
		{
			name:	"failed to fetch merchant details from actor service",
			args: args{
				ctx:	context.Background(),
				mocks: []interface{}{
					mActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(),
						GetEntityDetailsByActorIdRequest1, gomock.Any()).Return(nil, fmt.Errorf("failed")),
					mPiClient.EXPECT().GetPiById(context.Background(), gomock.Any()).Return(nil, nil),
				},
				order:			order1,
				txn:			transaction1,
				currentActorId:		currentActorId1,
				isMaskingRequired:	true,
			},
			want:	orderWithTransaction2,
		},
		{
			name:	"success",
			args: args{
				ctx:	context.Background(),
				mocks: []interface{}{
					mActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(),
						GetEntityDetailsByActorIdRequest1, gomock.Any()).Return(GetEntityDetailsByActorIdResponse1, nil),
					mPiClient.EXPECT().GetPiById(context.Background(), gomock.Any()).Return(nil, nil),
				},
				order:			order1,
				txn:			transaction1,
				currentActorId:		currentActorId1,
				isMaskingRequired:	true,
			},
			want:	orderWithTransaction1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewTransactionsDataCollectorHelper(nil, nil, mActorClient, nil, dataCollectorHelper, nil, nil, nil, nil, nil)
			got := s.BuildTxnDetailsProto(tt.args.ctx, tt.args.order, tt.args.txn, tt.args.currentActorId, tt.args.isMaskingRequired)
			if !reflect.DeepEqual(got.MerchantDetails, tt.want.MerchantDetails) {
				t.Errorf("BuildTxnDetailsProto() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_FetchOrderWithTxnList(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mOrderClient := mockOrder.NewMockOrderServiceClient(ctr)
	mActorClient := mockActor.NewMockActorClient(ctr)
	mPiClient := mockPi.NewMockPiClient(ctr)
	mUserClient := mockUser.NewMockUsersClient(ctr)
	dataCollectorHelper := NewDataCollectorHelper(mActorClient, mPiClient, mUserClient, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil)

	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		req	*oPb.GetOrdersForActorResponse
		actorId	string
	}
	tests := []struct {
		name	string
		args	args
		want	[]*tPb.OrderWithTransaction
		wantErr	bool
	}{
		{
			name:	"return orderWithTransaction successfully",
			args: args{
				mocks: []interface{}{
					mActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(),
						&actorPb.GetEntityDetailsByActorIdRequest{ActorId: "test-to"}).Return(GetEntityDetailsByActorIdResponseA, nil),
					mPiClient.EXPECT().GetPiById(context.Background(), gomock.Any()).Return(nil, nil),
					mOrderClient.EXPECT().GetOrdersWithTransactions(context.Background(), gomock.Any()).Return(ordersWithTransactionsA, nil),
				},
				ctx:	context.Background(),
				req: &oPb.GetOrdersForActorResponse{
					Results: []*oPb.GetOrdersForActorResponse_Result{
						{
							OrderId: "id-A",
						},
					},
				},
				actorId:	currentActorIdA,
			},
			want: []*tPb.OrderWithTransaction{
				orderWithTransactionA,
			},
		},
		{
			name:	"return record not found",
			args: args{
				mocks: []interface{}{
					mOrderClient.EXPECT().GetOrdersWithTransactions(context.Background(), gomock.Any()).
						Return(&orderPb.GetOrdersWithTransactionsResponse{}, nil),
				},
				ctx:	context.Background(),
				req: &oPb.GetOrdersForActorResponse{
					Results: []*oPb.GetOrdersForActorResponse_Result{
						{
							OrderId: "id-A",
						},
					},
				},
				actorId:	currentActorIdA,
			},
			want:		nil,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewTransactionsDataCollectorHelper(nil, mOrderClient, mActorClient, nil, dataCollectorHelper, nil, nil, nil, nil, nil)
			orderWithTxnReq := &orderPb.GetOrdersWithTransactionsRequest{}
			for _, order := range tt.args.req.GetResults() {
				orderWithTxnReq.OrderIdentifiers = append(orderWithTxnReq.OrderIdentifiers, &orderPb.OrderIdentifier{
					Identifier: &orderPb.OrderIdentifier_OrderId{OrderId: order.GetOrderId()},
				})
			}
			got, err := s.FetchOrderWithTxnList(tt.args.ctx, orderWithTxnReq, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchOrderWithTxnList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FetchOrderWithTxnList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_FetchOrderForTxnId(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mOrderClient := mockOrder.NewMockOrderServiceClient(ctr)
	mPClient := mocks.NewMockPaymentClient(ctr)
	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		txnId	string
	}
	tests := []struct {
		name	string
		args	args
		want	*orderPb.Order
		wantErr	bool
	}{
		{
			name:	"error while fetching order id",
			args: args{
				mocks: []interface{}{
					mPClient.EXPECT().GetOrderId(context.Background(), &paymentPb.GetOrderIdRequest{TransactionId: transaction1.GetId()}).
						Return(&paymentPb.GetOrderIdResponse{
							Status: rpcPb.StatusInternal(),
						}, nil),
				},
				ctx:	context.Background(),
				txnId:	transaction1.GetId(),
			},
			wantErr:	true,
		},
		{
			name:	"error while fetching order",
			args: args{
				mocks: []interface{}{
					mPClient.EXPECT().GetOrderId(context.Background(), &paymentPb.GetOrderIdRequest{TransactionId: transaction1.GetId()}).
						Return(&paymentPb.GetOrderIdResponse{Status: rpcPb.StatusOk(), OrderId: order1.GetId()}, nil),
					mOrderClient.EXPECT().GetOrder(context.Background(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: order1.GetId()}}).
						Return(&orderPb.GetOrderResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx:	context.Background(),
				txnId:	transaction1.GetId(),
			},
			wantErr:	true,
		},
		{
			name:	"success",
			args: args{
				mocks: []interface{}{
					mPClient.EXPECT().GetOrderId(context.Background(), &paymentPb.GetOrderIdRequest{TransactionId: transaction1.GetId()}).
						Return(&paymentPb.GetOrderIdResponse{
							Status:		rpcPb.StatusOk(),
							OrderId:	order1.GetId(),
						}, nil),
					mOrderClient.EXPECT().GetOrder(context.Background(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: order1.GetId()}}).
						Return(&orderPb.GetOrderResponse{Status: rpcPb.StatusOk(), Order: order1}, nil),
				},
				ctx:	context.Background(),
				txnId:	transaction1.GetId(),
			},
			wantErr:	false,
			want:		order1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewTransactionsDataCollectorHelper(nil, mOrderClient, nil, nil, nil, nil, nil, mPClient, nil, nil)
			got, err := s.FetchOrderForTxnId(tt.args.ctx, tt.args.txnId)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchOrderForTxnId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FetchOrderForTxnId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_FetchTransactionDetails(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mPClient := mocks.NewMockPaymentClient(ctr)
	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		txnId	string
	}
	tests := []struct {
		name	string
		args	args
		want	*paymentPb.Transaction
		wantErr	bool
	}{
		{
			name:	"error while fetching transaction",
			args: args{
				mocks: []interface{}{
					mPClient.EXPECT().GetTransaction(context.Background(), &paymentPb.GetTransactionRequest{
						Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: transaction1.GetId()}}).
						Return(&paymentPb.GetTransactionResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx:	context.Background(),
				txnId:	transaction1.GetId(),
			},
			wantErr:	true,
		},
		{
			name:	"success",
			args: args{
				mocks: []interface{}{
					mPClient.EXPECT().GetTransaction(context.Background(), &paymentPb.GetTransactionRequest{
						Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: transaction1.GetId()}}).
						Return(&paymentPb.GetTransactionResponse{Status: rpcPb.StatusOk(), Transaction: transaction1}, nil),
				},
				ctx:	context.Background(),
				txnId:	transaction1.GetId(),
			},
			wantErr:	false,
			want:		transaction1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewTransactionsDataCollectorHelper(nil, nil, nil, nil, nil, nil, nil, mPClient, nil, nil)
			got, err := s.FetchTransactionDetails(tt.args.ctx, tt.args.txnId)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchTransactionDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FetchTransactionDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetOrderWithTxnByTxnId(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mOrderClient := mockOrder.NewMockOrderServiceClient(ctr)
	mPClient := mocks.NewMockPaymentClient(ctr)
	mActorClient := mockActor.NewMockActorClient(ctr)
	mDataCollectorHelper := mockHelper.NewMockIDataCollectorHelper(ctr)
	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		txnId	string
		actorId	string
	}
	tests := []struct {
		name	string
		args	args
		want	*tPb.OrderWithTransaction
		wantErr	bool
	}{
		{
			name:	"error while fetching transaction details",
			args: args{
				mocks: []interface{}{
					mPClient.EXPECT().GetTransaction(context.Background(), &paymentPb.GetTransactionRequest{
						Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: transaction1.GetId()}}).
						Return(&paymentPb.GetTransactionResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx:		context.Background(),
				txnId:		transaction1.GetId(),
				actorId:	currentActorIdA,
			},
			wantErr:	true,
		},
		{
			name:	"error while fetching order details",
			args: args{
				mocks: []interface{}{
					mPClient.EXPECT().GetTransaction(context.Background(), &paymentPb.GetTransactionRequest{
						Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: transaction1.GetId()}}).
						Return(&paymentPb.GetTransactionResponse{
							Status:		rpcPb.StatusOk(),
							Transaction:	transaction1,
						}, nil),
					mPClient.EXPECT().GetOrderId(context.Background(), &paymentPb.GetOrderIdRequest{TransactionId: transaction1.GetId()}).
						Return(&paymentPb.GetOrderIdResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx:		context.Background(),
				txnId:		transaction1.GetId(),
				actorId:	currentActorIdA,
			},
			wantErr:	true,
		},
		{
			name:	"success",
			args: args{
				mocks: []interface{}{
					mPClient.EXPECT().GetTransaction(context.Background(), &paymentPb.GetTransactionRequest{
						Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: transaction1.GetId()}}).
						Return(&paymentPb.GetTransactionResponse{
							Status:		rpcPb.StatusOk(),
							Transaction:	transaction1,
						}, nil),
					mPClient.EXPECT().GetOrderId(context.Background(), &paymentPb.GetOrderIdRequest{TransactionId: transaction1.GetId()}).
						Return(&paymentPb.GetOrderIdResponse{
							Status:		rpcPb.StatusOk(),
							OrderId:	order1.GetId(),
						}, nil),
					mOrderClient.EXPECT().
						GetOrder(context.Background(), &orderPb.GetOrderRequest{
							Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: order1.GetId()}}).Return(&orderPb.GetOrderResponse{
						Status:	rpcPb.StatusOk(), Order: order1,
					}, nil),
					mActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), GetEntityDetailsByActorIdRequest1).
						Return(GetEntityDetailsByActorIdResponse1, nil),
					mDataCollectorHelper.EXPECT().GetPiDetails(context.Background(), transaction1.GetPiFrom(),
						order1.GetFromActorId(), false).Return(nil, nil),
					mDataCollectorHelper.EXPECT().GetPiDetails(context.Background(),
						transaction1.GetPiTo(), order1.GetToActorId(), false).Return(nil, nil),
				},
				ctx:		context.Background(),
				txnId:		transaction1.GetId(),
				actorId:	currentActorIdA,
			},
			wantErr:	false,
			want:		orderWithTransactionB,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewTransactionsDataCollectorHelper(nil, mOrderClient, mActorClient, nil, mDataCollectorHelper, nil, nil, mPClient, nil, nil)
			got, err := s.GetOrderWithTxnByTxnId(tt.args.ctx, tt.args.txnId, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrderWithTxnByTxnId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOrderWithTxnByTxnId() \n got = %v \n want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetRecentTransactions(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mSavingsClient := mocks2.NewMockSavingsClient(ctr)
	mPayCxClient := mocks3.NewMockCXClient(ctr)
	mPayClient := mocks4.NewMockPayClient(ctr)
	mActorClient := mockActor.NewMockActorClient(ctr)
	mDcHelper := mockHelper.NewMockIDataCollectorHelper(ctr)
	defer func() {
		ctr.Finish()
	}()
	type args struct {
		mocks		[]interface{}
		ctx		context.Context
		txnCount	int
		actorId		string
		txnType		workflow.TransactionType
	}
	tests := []struct {
		name	string
		args	args
		want	[]*tPb.OrderWithTransaction
		wantErr	bool
	}{
		{
			name:	"error while fetching atm transactions",
			args: args{
				mocks: []interface{}{
					mPayCxClient.EXPECT().GetATMOrderTransactionsForActor(gomock.Any(), &cx.GetATMOrderTransactionsForActorRequest{
						ActorId:		currentActorIdA,
						TransactionType:	paymentPb.AccountingEntryType_DEBIT,
						PageContext:		&rpcPb.PageContextRequest{PageSize: uint32(txnCount)},
					}).Return(&cx.GetATMOrderTransactionsForActorResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx:		context.Background(),
				txnCount:	txnCount,
				actorId:	currentActorIdA,
				txnType:	workflow.TransactionType_TRANSACTION_TYPE_ATM_WITHDRAWALS,
			},
			wantErr:	true,
		},
		{
			name:	"error while fetching atm transactions: record not found",
			args: args{
				mocks: []interface{}{
					mPayCxClient.EXPECT().GetATMOrderTransactionsForActor(gomock.Any(), &cx.GetATMOrderTransactionsForActorRequest{
						ActorId:		currentActorIdA,
						TransactionType:	paymentPb.AccountingEntryType_DEBIT,
						PageContext:		&rpcPb.PageContextRequest{PageSize: uint32(txnCount)},
					}).Return(&cx.GetATMOrderTransactionsForActorResponse{Status: rpcPb.StatusRecordNotFound()}, nil),
				},
				ctx:		context.Background(),
				txnCount:	txnCount,
				actorId:	currentActorIdA,
				txnType:	workflow.TransactionType_TRANSACTION_TYPE_ATM_WITHDRAWALS,
			},
			wantErr:	true,
		},
		{
			name:	"error while fetching enach-charge transactions",
			args: args{
				mocks: []interface{}{
					mPayCxClient.EXPECT().GetChargeRelatedOrderTransactionForActor(gomock.Any(),
						&cx.GetChargeRelatedOrderTransactionForActorRequest{
							ActorId:	currentActorIdA,
							PageContext:	&rpcPb.PageContextRequest{PageSize: uint32(txnCount)}}).
						Return(&cx.GetChargeRelatedOrderTransactionForActorResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx:		context.Background(),
				txnCount:	txnCount,
				actorId:	currentActorIdA,
				txnType:	workflow.TransactionType_TRANSACTION_TYPE_ENACH_CHARGES,
			},
			wantErr:	true,
		},
		{
			name:	"error while fetching enach-charge transactions: record not found",
			args: args{
				mocks: []interface{}{
					mPayCxClient.EXPECT().GetChargeRelatedOrderTransactionForActor(gomock.Any(),
						&cx.GetChargeRelatedOrderTransactionForActorRequest{
							ActorId:	currentActorIdA,
							PageContext:	&rpcPb.PageContextRequest{PageSize: uint32(txnCount)}}).
						Return(&cx.GetChargeRelatedOrderTransactionForActorResponse{Status: rpcPb.StatusRecordNotFound()}, nil),
				},
				ctx:		context.Background(),
				txnCount:	txnCount,
				actorId:	currentActorIdA,
				txnType:	workflow.TransactionType_TRANSACTION_TYPE_ENACH_CHARGES,
			},
			wantErr:	true,
		},
		{
			name:	"error while fetching account details",
			args: args{
				mocks: []interface{}{
					mSavingsClient.EXPECT().GetAccount(context.Background(), &savings.GetAccountRequest{
						Identifier: &savings.GetAccountRequest_ActorId{ActorId: currentActorIdA}}).
						Return(&savings.GetAccountResponse{}, errors.New("error")),
				},
				ctx:		context.Background(),
				txnCount:	txnCount,
				actorId:	currentActorIdA,
			},
			wantErr:	true,
		},
		{
			name:	"error while fetching all transaction for user",
			args: args{
				mocks: []interface{}{
					mSavingsClient.EXPECT().GetAccount(context.Background(), &savings.GetAccountRequest{
						Identifier: &savings.GetAccountRequest_ActorId{ActorId: currentActorIdA}}).
						Return(&savings.GetAccountResponse{Account: account1}, nil),
					mPayClient.EXPECT().GetOrdersWithTransactionsForActor(context.Background(), gomock.Any()).
						Return(&pay.GetOrdersWithTransactionsForActorResponse{Status: rpcPb.StatusInternal()}, nil),
				},
				ctx:		context.Background(),
				txnCount:	txnCount,
				actorId:	currentActorIdA,
			},
			wantErr:	true,
		},
		{
			name:	"success",
			args: args{
				mocks: []interface{}{
					mSavingsClient.EXPECT().GetAccount(context.Background(), &savings.GetAccountRequest{
						Identifier: &savings.GetAccountRequest_ActorId{ActorId: currentActorIdA}}).
						Return(&savings.GetAccountResponse{Account: account1}, nil),
					mPayClient.EXPECT().GetOrdersWithTransactionsForActor(context.Background(), gomock.Any()).
						Return(
							&pay.GetOrdersWithTransactionsForActorResponse{
								Status:	rpcPb.StatusOk(),
								OrderWithTransaction: []*orderPb.OrderWithTransactions{
									{
										Order:	order1,
										Transactions: []*paymentPb.Transaction{
											transaction1,
										},
									},
								},
							}, nil),
					mActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), GetEntityDetailsByActorIdRequest1).
						Return(GetEntityDetailsByActorIdResponse1, nil),
					mDcHelper.EXPECT().GetPiDetails(context.Background(), transaction1.GetPiFrom(),
						order1.GetFromActorId(), false).Return(nil, nil),
					mDcHelper.EXPECT().GetPiDetails(context.Background(), transaction1.GetPiTo(),
						order1.GetToActorId(), false).Return(nil, nil),
				},
				ctx:		context.Background(),
				txnCount:	txnCount,
				actorId:	currentActorIdA,
			},
			wantErr:	false,
			want: []*tPb.OrderWithTransaction{
				orderWithTransactionB,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewTransactionsDataCollectorHelper(nil, nil, mActorClient, mSavingsClient, mDcHelper, mPayCxClient, mPayClient, nil, nil, nil)
			got, err := s.GetRecentTransactions(tt.args.ctx, tt.args.actorId, tt.args.txnCount, tt.args.txnType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecentTransactions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRecentTransactions() \n got = %v \n want %v", got, tt.want)
			}
		})
	}
}

func TestService_getTxnMiscData(t *testing.T) {
	t.Parallel()
	type args struct {
		order *orderPb.Order
	}
	tests := []struct {
		name	string
		args	args
		want	[]*webui.LabelValueV2
	}{
		{
			name:	"should return OffApp label as TRUE in case of off-app transaction",
			args: args{
				order: &orderPb.Order{
					Id:		"order-id-1",
					Workflow:	orderPb.OrderWorkflow_NO_OP,
				},
			},
			want: []*webui.LabelValueV2{
				{
					Label:		"OffApp",
					DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
					Value:		&webui.LabelValueV2_StringValue{StringValue: commontypes.BooleanEnum_TRUE.String()},
				},
				{
					Label:		"Order Workflow",
					DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
					Value:		&webui.LabelValueV2_StringValue{StringValue: "NO_OP"},
				},
			},
		},
		{
			name:	"should return OffApp label as TRUE in case of off-app transaction",
			args: args{
				order: &orderPb.Order{
					Id:		"order-id-2",
					Workflow:	orderPb.OrderWorkflow_OFF_APP_UPI,
				},
			},
			want: []*webui.LabelValueV2{
				{
					Label:		"OffApp",
					DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
					Value:		&webui.LabelValueV2_StringValue{StringValue: commontypes.BooleanEnum_TRUE.String()},
				},
				{
					Label:		"Order Workflow",
					DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
					Value:		&webui.LabelValueV2_StringValue{StringValue: "OFF_APP_UPI"},
				},
			},
		},
		{
			name:	"should return OffApp label as FALSE in case of on-app transaction",
			args: args{
				order: &orderPb.Order{
					Id:		"order-id-2",
					Workflow:	orderPb.OrderWorkflow_MODIFY_RECURRING_PAYMENT,
				},
			},
			want: []*webui.LabelValueV2{
				{
					Label:		"OffApp",
					DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
					Value:		&webui.LabelValueV2_StringValue{StringValue: commontypes.BooleanEnum_FALSE.String()},
				},
				{
					Label:		"Order Workflow",
					DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
					Value:		&webui.LabelValueV2_StringValue{StringValue: "MODIFY_RECURRING_PAYMENT"},
				},
			},
		},
		{
			name:	"should return OffApp label as Unknown in case its UNSPECIFIED",
			args: args{
				order: &orderPb.Order{
					Id:		"order-id-2",
					Workflow:	orderPb.OrderWorkflow_ORDER_WORKFLOW_UNSPECIFIED,
				},
			},
			want: []*webui.LabelValueV2{
				{
					Label:		"OffApp",
					DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
					Value:		&webui.LabelValueV2_StringValue{StringValue: "Unknown"},
				},
				{
					Label:		"Order Workflow",
					DataType:	webui.LabelValueV2_DATA_TYPE_STRING,
					Value:		&webui.LabelValueV2_StringValue{StringValue: "ORDER_WORKFLOW_UNSPECIFIED"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getTxnMiscData(tt.args.order)
			want := tt.want
			compareTxnMiscData(got, want, t)
		})
	}
}

func compareTxnMiscData(got []*webui.LabelValueV2, want []*webui.LabelValueV2, t *testing.T) {
	for i, gotItem := range got {
		wantItem := want[i]
		if !proto.Equal(gotItem, wantItem) {
			t.Errorf("got:%v, want:%v", gotItem, wantItem)
		}
	}
}
