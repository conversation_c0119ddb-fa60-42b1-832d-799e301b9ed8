//go:generate mockgen -source=./txn_category_helper.go -destination=./mocks/mock_txn_category_helper.go -package=mocks
package helper

import (
	"context"
	"fmt"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/categorizer"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
)

const (
	// Currently there is a limit of 5 orders which the GetLastNTxns rpc can fetch. That is why we have kept the limit as 5 for now for fetching txn categories.
	maxGoroutines = 5
)

var WireTxnCategoryHelperSet = wire.NewSet(NewTxnCategoriesHelperImpl, wire.Bind(new(TxnCategoryDataCollectorHelper), new(*TxnCategoryDataCollectorHelperImpl)))

type TxnCategoryDataCollectorHelper interface {
	// GetCategoriesForOrders returns a map of orderId to list of txn categories for the given list of orderIds.
	GetCategoriesForOrders(ctx context.Context, actorId string, orderIds []string) (map[string][]string, error)
}

type TxnCategoryDataCollectorHelperImpl struct {
	categorizerClient categorizer.TxnCategorizerClient
}

func NewTxnCategoriesHelperImpl(categorizerClient categorizer.TxnCategorizerClient) *TxnCategoryDataCollectorHelperImpl {
	return &TxnCategoryDataCollectorHelperImpl{
		categorizerClient: categorizerClient,
	}
}

type OrderCategoryDetails struct {
	OrderId            string
	TxnCategoryDetails *categorizer.GetTxnCategoryDetailsResponse
}

// GetCategoriesForOrders returns a map of orderId to list of txn categories for the given list of orderIds.
func (t *TxnCategoryDataCollectorHelperImpl) GetCategoriesForOrders(ctx context.Context, actorId string, orderIds []string) (map[string][]string, error) {
	g, gctx := errgroup.WithContext(ctx)
	categoriesChann := make(chan *OrderCategoryDetails, len(orderIds))
	g.SetLimit(maxGoroutines)
	for _, orderId := range orderIds {
		orderIdTmp := orderId
		g.Go(func() error {
			res, err := t.categorizerClient.GetTxnCategoryDetails(gctx, &categorizer.GetTxnCategoryDetailsRequest{
				ActorId:    actorId,
				Provenance: categorizer.Provenance_DS,
				ActivityId: &categorizer.ActivityId{
					Id: &categorizer.ActivityId_OrderId{
						OrderId: orderIdTmp,
					},
				},
			})
			if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !res.GetStatus().IsRecordNotFound() {
				return fmt.Errorf("failed to get category details for order: %s, err: %w", orderIdTmp, rpcErr)
			}
			categoriesChann <- &OrderCategoryDetails{
				OrderId:            orderIdTmp,
				TxnCategoryDetails: res,
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, fmt.Errorf("failed to fetch txn categories: %w", err)
	}
	close(categoriesChann)

	// Get category display name
	var displayCategoryToDisplayNameMap = make(map[categorizer.DisplayCategory]string)
	categoriesInfoRes, err := t.categorizerClient.GetCategoriesInfo(ctx, &categorizer.GetCategoriesInfoRequest{})
	if rpcErr := epifigrpc.RPCError(categoriesInfoRes, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get categories info: %w", rpcErr)
	}
	for _, categoryInfo := range categoriesInfoRes.GetCategoriesInfo() {
		displayCategoryToDisplayNameMap[categoryInfo.GetDisplayCategory()] = categoryInfo.GetDisplayName()
	}

	var txnIdToCategoriesMap = make(map[string][]string)
	for orderCategoryDetail := range categoriesChann {
		txnIdToCategoriesMap[orderCategoryDetail.OrderId] = []string{}
		for _, ontology := range orderCategoryDetail.TxnCategoryDetails.GetTxnCategories().GetOntologies() {
			displayCategoryDisplayName, displayCategoryDisplayNameFound := displayCategoryToDisplayNameMap[ontology.GetDisplayCategory()]
			if !displayCategoryDisplayNameFound {
				displayCategoryDisplayName = ontology.GetDisplayCategory().String()
				cxLogger.Info(ctx, fmt.Sprintf("display category name does not exist for category: %s", ontology.GetDisplayCategory()))
			}
			txnIdToCategoriesMap[orderCategoryDetail.OrderId] = append(txnIdToCategoriesMap[orderCategoryDetail.OrderId], displayCategoryDisplayName)
		}
	}

	return txnIdToCategoriesMap, nil
}
