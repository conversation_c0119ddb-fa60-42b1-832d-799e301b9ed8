package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"sort"
	"time"

	"github.com/gogo/status"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	datePb "google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc/codes"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	maskPkg "github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/pagination"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/cx/chat/bot/workflow"
	txnDcPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	orderPb "github.com/epifi/gamma/api/order"
	oPb "github.com/epifi/gamma/api/order/cx"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	payCxPb "github.com/epifi/gamma/api/pay/cx"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2/webui"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	cxLogger "github.com/epifi/gamma/cx/logger"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

//go:generate mockgen --source=cx/data_collector/helper/transactions_helper.go --destination=cx/test/mocks/data_collector/helper/mock_transactions_helper.go
type ITransactionsDataCollectorHelper interface {
	GetFirstOrLastNTransactions(ctx context.Context, actorId string, firstOrLast txnDcPb.GetFirstOrLastNTransactionsRequest_Type, txnCount int) ([]*txnDcPb.OrderWithTransaction, error)
	FetchOrderWithTxnList(ctx context.Context, orderWithTxnReq *orderPb.GetOrdersWithTransactionsRequest, actorId string) ([]*txnDcPb.OrderWithTransaction, error)
	GetRecentTransactions(ctx context.Context, actorId string, txnCount int, txnType workflow.TransactionType) ([]*txnDcPb.OrderWithTransaction, error)
	GetOrderWithTxnByTxnId(ctx context.Context, txnId string, actorId string) (*txnDcPb.OrderWithTransaction, error)
	FetchTransactionDetails(ctx context.Context, transactionId string) (*paymentPb.Transaction, error)
	FetchOrderForTxnId(ctx context.Context, transactionId string) (*orderPb.Order, error)
}

type TransactionsDataCollectorHelper struct {
	orderTxnClient      oPb.CXClient
	orderClient         orderPb.OrderServiceClient
	actorClient         actorPb.ActorClient
	savingsClient       savingsPb.SavingsClient
	dataCollectorHelper IDataCollectorHelper
	payCxClient         payCxPb.CXClient
	payClient           payPb.PayClient
	pClient             paymentPb.PaymentClient
	accountPiClient     accountPiPb.AccountPIRelationClient
	upiOnbClient        upiOnboardingPb.UpiOnboardingClient
}

var txnTypeToTxnStatus = map[workflow.TransactionType]paymentPb.TransactionStatus{
	workflow.TransactionType_TRANSACTION_TYPE_SUCCESSFUL:        paymentPb.TransactionStatus_SUCCESS,
	workflow.TransactionType_TRANSACTION_TYPE_FAILURE:           paymentPb.TransactionStatus_FAILED,
	workflow.TransactionType_TRANSACTION_TYPE_UNSPECIFIED:       paymentPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED,
	workflow.TransactionType_TRANSACTION_TYPE_DEBIT_SUCCESSFUL:  paymentPb.TransactionStatus_SUCCESS,
	workflow.TransactionType_TRANSACTION_TYPE_CREDIT_SUCCESSFUL: paymentPb.TransactionStatus_SUCCESS,
}

var txnTypeToAccountingEntry = map[workflow.TransactionType]paymentPb.AccountingEntryType{
	workflow.TransactionType_TRANSACTION_TYPE_SUCCESSFUL:        paymentPb.AccountingEntryType_DEBIT,
	workflow.TransactionType_TRANSACTION_TYPE_ALL:               paymentPb.AccountingEntryType_DEBIT,
	workflow.TransactionType_TRANSACTION_TYPE_UNSPECIFIED:       paymentPb.AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED,
	workflow.TransactionType_TRANSACTION_TYPE_DEBIT_SUCCESSFUL:  paymentPb.AccountingEntryType_CREDIT,
	workflow.TransactionType_TRANSACTION_TYPE_CREDIT_SUCCESSFUL: paymentPb.AccountingEntryType_CREDIT,
}

var _ ITransactionsDataCollectorHelper = &TransactionsDataCollectorHelper{}

func NewTransactionsDataCollectorHelper(orderTxnClient oPb.CXClient, orderClient orderPb.OrderServiceClient, actorClient actorPb.ActorClient,
	savingsClient savingsPb.SavingsClient, dataCollectorHelper IDataCollectorHelper, payCxClient payCxPb.CXClient,
	payClient payPb.PayClient, pClient paymentPb.PaymentClient, accountPiClient accountPiPb.AccountPIRelationClient, upiOnbClient upiOnboardingPb.UpiOnboardingClient,
) *TransactionsDataCollectorHelper {
	return &TransactionsDataCollectorHelper{
		orderTxnClient:      orderTxnClient,
		orderClient:         orderClient,
		actorClient:         actorClient,
		savingsClient:       savingsClient,
		dataCollectorHelper: dataCollectorHelper,
		payCxClient:         payCxClient,
		payClient:           payClient,
		pClient:             pClient,
		accountPiClient:     accountPiClient,
		upiOnbClient:        upiOnbClient,
	}
}

func (t *TransactionsDataCollectorHelper) FetchTransactionDetails(ctx context.Context, transactionId string) (*paymentPb.Transaction, error) {
	resp, err := t.pClient.GetTransaction(ctx, &paymentPb.GetTransactionRequest{
		Identifier: &paymentPb.GetTransactionRequest_TransactionId{TransactionId: transactionId},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return nil, errors.Wrap(te, "error while fetching transactions")
	}
	return resp.GetTransaction(), nil
}

func (t *TransactionsDataCollectorHelper) FetchOrderForTxnId(ctx context.Context, transactionId string) (*orderPb.Order, error) {
	resp, err := t.pClient.GetOrderId(ctx, &paymentPb.GetOrderIdRequest{TransactionId: transactionId})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return nil, errors.Wrap(te, "failed to fetch order id by txn id")
	}
	orderResp, err := t.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: resp.GetOrderId()}})
	if te := epifigrpc.RPCError(orderResp, err); te != nil {
		return nil, errors.Wrap(te, "failed to fetch order by id")
	}
	return orderResp.GetOrder(), nil
}

func (t *TransactionsDataCollectorHelper) GetOrderWithTxnByTxnId(ctx context.Context, txnId string, actorId string) (*txnDcPb.OrderWithTransaction, error) {
	txn, getTxnErr := t.FetchTransactionDetails(ctx, txnId)
	if getTxnErr != nil {
		return nil, errors.Wrap(getTxnErr, "error while fetching txn by id")
	}
	orderResp, fetchOrderErr := t.FetchOrderForTxnId(ctx, txnId)
	if fetchOrderErr != nil {
		return nil, errors.Wrap(fetchOrderErr, "error while fetching order by txn id")
	}
	txnWithOrder := t.BuildTxnDetailsProto(ctx, orderResp, txn, actorId, false)
	return txnWithOrder, nil
}

func (t *TransactionsDataCollectorHelper) GetRecentTransactions(ctx context.Context, actorId string, txnCount int, txnType workflow.TransactionType) ([]*txnDcPb.OrderWithTransaction, error) {
	var orderWithTxnList []*orderPb.OrderWithTransactions

	switch txnType {
	case workflow.TransactionType_TRANSACTION_TYPE_ATM_WITHDRAWALS:
		// for now, we are only considering debit transaction in ATM as per product requirement
		resp, err := t.payCxClient.GetATMOrderTransactionsForActor(ctx, &payCxPb.GetATMOrderTransactionsForActorRequest{
			ActorId: actorId,
			PageContext: &rpcPb.PageContextRequest{
				PageSize: uint32(txnCount),
			},
			TransactionType: paymentPb.AccountingEntryType_DEBIT,
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			if resp.GetStatus().IsRecordNotFound() {
				return nil, errors.Wrap(epifierrors.ErrRecordNotFound, te.Error())
			}
			return nil, errors.Wrap(te, "error while fetching atm Transactions orders for actor")
		}
		orderWithTxnList = resp.GetOrdersWithTxns()
	// TODO: (@smit_mistry) Need to fetch only Enach related charges, Below implementation is fetching all the charges for an actor
	case workflow.TransactionType_TRANSACTION_TYPE_ENACH_CHARGES:
		resp, err := t.payCxClient.GetChargeRelatedOrderTransactionForActor(ctx, &payCxPb.GetChargeRelatedOrderTransactionForActorRequest{
			ActorId: actorId,
			PageContext: &rpcPb.PageContextRequest{
				PageSize: uint32(txnCount),
			},
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			if resp.GetStatus().IsRecordNotFound() {
				return nil, errors.Wrap(epifierrors.ErrRecordNotFound, te.Error())
			}
			return nil, errors.Wrap(te, "error while fetching charges Transactions orders for actor")
		}
		orderWithTxnList = resp.GetOrdersWithTxns()
	case workflow.TransactionType_TRANSACTION_TYPE_CHARGES:
		resp, err := t.payCxClient.GetChargeRelatedOrderTransactionForActor(ctx, &payCxPb.GetChargeRelatedOrderTransactionForActorRequest{
			ActorId: actorId,
			PageContext: &rpcPb.PageContextRequest{
				PageSize: uint32(txnCount),
			},
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			if resp.GetStatus().IsRecordNotFound() {
				return nil, errors.Wrap(epifierrors.ErrRecordNotFound, te.Error())
			}
			return nil, errors.Wrap(te, "error while fetching charges Transactions orders for actor")
		}
		orderWithTxnList = resp.GetOrdersWithTxns()
	default:
		accountDetails, getAccErr := t.savingsClient.GetAccount(ctx,
			&savingsPb.GetAccountRequest{
				Identifier: &savingsPb.GetAccountRequest_ActorId{
					ActorId: actorId,
				},
			})
		// we are not handling gRPC error as per standard because GetAccount is an old RPC and doesn't have status in response
		if getAccErr != nil {
			cxLogger.Error(ctx, "error while fetching account details for user", zap.Error(getAccErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, errors.Wrap(getAccErr, "error while fetching account details for user")
		}
		pageContextReq, err := getPageContextForLatestRecord(uint32(txnCount))
		if err != nil {
			cxLogger.Error(ctx, "error while creating page context request for fetching latest transactions", zap.Error(err))
			return nil, errors.Wrap(err, "error while creating page context request")
		}
		resp, err := t.payClient.GetOrdersWithTransactionsForActor(ctx, &payPb.GetOrdersWithTransactionsForActorRequest{
			ActorId:            actorId,
			Status:             txnTypeToTxnStatus[txnType],
			PageContextRequest: pageContextReq,
			AccountFilter: []*payPb.AccountFilter{
				{
					AccountId:   accountDetails.GetAccount().GetId(),
					AccountType: accounts.Type_SAVINGS,
				},
			},
			TransactionType: txnTypeToAccountingEntry[txnType],
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			cxLogger.Error(ctx, "error while fetching txns for actor", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, errors.Wrap(te, "error while fetching txns for actor")
		}
		orderWithTxnList = resp.GetOrderWithTransaction()
	}

	var txnDetailsList []*txnDcPb.OrderWithTransaction
	for _, orderWithTxn := range orderWithTxnList {
		for _, txn := range orderWithTxn.GetTransactions() {
			txnDetailsList = append(txnDetailsList, t.BuildTxnDetailsProto(ctx, orderWithTxn.GetOrder(), txn, actorId, false))
		}
	}

	return txnDetailsList, nil
}

func getPageContextForLatestRecord(pageSize uint32) (*rpcPb.PageContextRequest, error) {
	pageToken := &pagination.PageToken{
		Timestamp: timestampPb.Now(),
		Offset:    0,
		IsReverse: true,
	}
	beforeToken, err := pageToken.Marshal()
	if err != nil {
		return nil, errors.Wrap(err, "error while marshalling page-token")
	}
	return &rpcPb.PageContextRequest{
		Token: &rpcPb.PageContextRequest_BeforeToken{
			BeforeToken: beforeToken,
		},
		PageSize: pageSize,
	}, nil
}

func (t *TransactionsDataCollectorHelper) GetFirstOrLastNTransactions(ctx context.Context, actorId string, firstOrLast txnDcPb.GetFirstOrLastNTransactionsRequest_Type,
	txnCount int) ([]*txnDcPb.OrderWithTransaction, error) {
	savingsResp, err := t.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorId{
			ActorId: actorId,
		},
	})
	// savings GetAccount response does not have rpc.Status field
	// because of which epifi RPCError check cannot be used here
	if err != nil || savingsResp.GetAccount() == nil {
		if err == nil {
			err = errors.New("savings account object is nil")
		}
		// have checked savings server side implementation for error comparison
		if errors.Is(err, status.Error(codes.NotFound, "record not found")) {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, err.Error())
		}
		return nil, errors.Wrap(err, "error while fetching savings account details")
	}
	fromDate, toDate, err := GetFromAndToDate(firstOrLast, savingsResp.GetAccount())

	if err != nil {
		return nil, errors.Wrap(err, "error while getting from and to date")
	}
	orderReq := &oPb.GetOrdersForActorRequest{
		ActorId:  actorId,
		FromDate: fromDate,
		ToDate:   toDate,
		PageSize: int32(txnCount),
	}

	var ordersForActorResp *oPb.GetOrdersForActorResponse
	// for last N transactions we will need to make multiple RPC call with a fixed duration of 30 days till we get required number of
	// transactions.
	// for first 5 transactions we don't need such data manipulation so we are putting the old logic as before inside else block
	if firstOrLast == txnDcPb.GetFirstOrLastNTransactionsRequest_LAST {
		orderReq.Token = &oPb.GetOrdersForActorRequest_BeforePageToken{
			BeforePageToken: &oPb.PageToken{
				OrderOffset: 0,
			},
		}
		var lastNOrdersForActorSlice []*oPb.GetOrdersForActorResponse_Result
		var getOrdersForActorResp *oPb.GetOrdersForActorResponse
		fromDateInTimeFormat := datetime.DateToTimeV2(fromDate, nil)
		toDateInTimeFormat := datetime.DateToTimeV2(toDate, nil)
		// We have called GetOrdersForActor rpc for short duration of 1 month till we get last N transaction for the user.
		// from date starts from 200 days before current date and this whole duration is broken into 1 month duration each so that
		// load on db will be less as it will fetch less data in 1 go.
		// Though, number of calls to the RPC will increase it will benefit latency on db altogether.
		// For every db call we will fetch result and append it to slice till we get the same number of orders as the request txnCount.
		for toDateInTimeFormat.After(fromDateInTimeFormat) {
			oneMonthBeforeFromDate := toDateInTimeFormat.Add(-30 * 24 * time.Hour)
			if oneMonthBeforeFromDate.Before(fromDateInTimeFormat) {
				oneMonthBeforeFromDate = fromDateInTimeFormat
			}

			orderReq.FromDate = datetime.TimeToDateInLoc(oneMonthBeforeFromDate, nil)
			orderReq.ToDate = datetime.TimeToDateInLoc(toDateInTimeFormat, nil)
			getOrdersForActorResp, err = t.orderTxnClient.GetOrdersForActor(ctx, orderReq)
			if te := epifigrpc.RPCError(getOrdersForActorResp, err); te != nil {
				if getOrdersForActorResp.GetStatus().IsRecordNotFound() {
					continue
				}
				return nil, errors.Wrap(te, "error while fetching orders for actor")
			}

			if getOrdersForActorResp.GetResults() != nil {
				lastNOrdersForActorSlice = append(lastNOrdersForActorSlice, getOrdersForActorResp.GetResults()...)
			}

			if len(lastNOrdersForActorSlice) >= txnCount {
				lastNOrdersForActorSlice = lastNOrdersForActorSlice[:txnCount]
				break
			}

			toDateInTimeFormat = oneMonthBeforeFromDate
		}
		ordersForActorResp = &oPb.GetOrdersForActorResponse{
			Status:          rpcPb.StatusOk(),
			Results:         lastNOrdersForActorSlice,
			BeforePageToken: getOrdersForActorResp.GetBeforePageToken(),
			AfterPageToken:  getOrdersForActorResp.GetAfterPageToken(),
		}
	} else {
		ordersForActorResp, err = t.orderTxnClient.GetOrdersForActor(ctx, orderReq)
		if te := epifigrpc.RPCError(ordersForActorResp, err); te != nil {
			if ordersForActorResp.GetStatus().IsRecordNotFound() {
				return nil, errors.Wrap(epifierrors.ErrRecordNotFound, te.Error())
			}
			return nil, errors.Wrap(te, "error while fetching orders for actor")
		}
	}
	orderWithTxnReq := &orderPb.GetOrdersWithTransactionsRequest{}
	for _, order := range ordersForActorResp.GetResults() {
		orderWithTxnReq.OrderIdentifiers = append(orderWithTxnReq.OrderIdentifiers, &orderPb.OrderIdentifier{
			Identifier: &orderPb.OrderIdentifier_OrderId{OrderId: order.GetOrderId()},
		})
	}
	orderWithTxnList, err := t.FetchOrderWithTxnList(ctx, orderWithTxnReq, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching order with txn list")

	}
	if err = t.populateBankDetailsForTxns(ctx, actorId, orderWithTxnList, savingsResp.GetAccount()); err != nil {
		return nil, fmt.Errorf("error while populating bank details for actor in the orderWithTxnList, err = %w", err)
	}

	// sort the transactions based on the transaction's created_at
	sort.Slice(orderWithTxnList, func(i, j int) bool {
		return orderWithTxnList[i].GetTxnCreatedAt().AsTime().After(orderWithTxnList[j].GetTxnCreatedAt().AsTime())
	})
	return orderWithTxnList, nil
}

type bankDetails struct {
	bankName            string
	accountType         accounts.Type
	maskedAccountNumber string
}

// populateBankDetailsForTxns: populate bank level details like BankName, MaskedAccountNumber, AccountType for the actor in the corresponding txn
// nolint
func (t *TransactionsDataCollectorHelper) populateBankDetailsForTxns(ctx context.Context, actorId string, orderWithTxnList []*txnDcPb.OrderWithTransaction, savingsAccount *savingsPb.Account) error {

	// Instead of fetching account details corresponding to PI for
	// each transaction, we'll fetch all the Pis for the actor and
	// store bank detail for it in a map, because number of calls for
	// fetching account details for each txn would be much more than
	// no of Pis for a user, because generally
	// TransactionsCount >>> number of Pis of user.
	piToBankDetailsMap, err := t.generatePiToBankDetailsMapForActor(ctx, actorId, savingsAccount)
	if err != nil {
		return fmt.Errorf("error while generating pi to bank details map for actor = %s, err = %w", actorId, err)
	}

	for _, orderWithTxn := range orderWithTxnList {

		// populate Remitter bank details and Beneficiary bank details (on best effort basis)
		switch {
		case piMappingExists(orderWithTxn.GetPiFrom(), piToBankDetailsMap):
			// fetch bank detail for the pi-id and populate in the OrderWithTxn entry
			bankDetail, _ := piToBankDetailsMap[orderWithTxn.GetPiFrom()]

			orderWithTxn.RemitterBankName = bankDetail.bankName
			orderWithTxn.RemitterAccountType = bankDetail.accountType.String()
			orderWithTxn.RemitterMaskedAccountNumber = bankDetail.maskedAccountNumber

			bankDetail, err := t.fetchBankDetailsForPi(ctx, orderWithTxn.GetPiTo())
			if err != nil && !storageV2.IsRecordNotFoundError(err) {
				return fmt.Errorf("error while fetching bank details for pi-id = %v , err = %w", orderWithTxn.GetPiTo(), err)
			}

			if bankDetail != nil {
				orderWithTxn.BeneficiaryBankName = bankDetail.bankName
				orderWithTxn.BeneficiaryAccountType = bankDetail.accountType.String()
				orderWithTxn.BeneficiaryMaskedAccountNumber = bankDetail.maskedAccountNumber
			}

		case piMappingExists(orderWithTxn.GetPiTo(), piToBankDetailsMap):
			// fetch bank detail for the pi-id and populate in the OrderWithTxn entry
			bankDetail, _ := piToBankDetailsMap[orderWithTxn.GetPiTo()]

			orderWithTxn.BeneficiaryBankName = bankDetail.bankName
			orderWithTxn.BeneficiaryAccountType = bankDetail.accountType.String()
			orderWithTxn.BeneficiaryMaskedAccountNumber = bankDetail.maskedAccountNumber

			bankDetail, err := t.fetchBankDetailsForPi(ctx, orderWithTxn.GetPiFrom())
			if err != nil && !storageV2.IsRecordNotFoundError(err) {
				return fmt.Errorf("error while fetching bank details for pi-id = %v , err = %w", orderWithTxn.GetPiFrom(), err)
			}

			if bankDetail != nil {
				orderWithTxn.RemitterBankName = bankDetail.bankName
				orderWithTxn.RemitterAccountType = bankDetail.accountType.String()
				orderWithTxn.RemitterMaskedAccountNumber = bankDetail.maskedAccountNumber
			}
		default:
			// this case shouldn't happen ideally
			// since one of the PI should be in our system.
			continue
		}

	}
	return nil
}

// fetchBankDetailsForPi: fetches bank details for given pi-id
func (t *TransactionsDataCollectorHelper) fetchBankDetailsForPi(ctx context.Context, piId string) (*bankDetails, error) {
	getByPiIdRes, err := t.accountPiClient.GetByPiId(ctx, &accountPiPb.GetByPiIdRequest{
		PiId: piId,
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error while fetching account pi by pi-id = %v, err = %w", piId, err)
	case getByPiIdRes.GetStatus().IsRecordNotFound():
		return nil, epifierrors.ErrRecordNotFound
	case !getByPiIdRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("non-success code while fetching account pi by pi-id = %v, err = %w", piId, rpcPb.StatusAsError(getByPiIdRes.GetStatus()))
	}

	getAccountRes, err := t.upiOnbClient.GetAccount(ctx, &upiOnboardingPb.GetAccountRequest{
		AccountId: getByPiIdRes.GetAccountId(),
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("error while fetching upi account by id = %v, err = %w", getByPiIdRes.GetAccountId(), err)
	case !getAccountRes.GetStatus().IsSuccess() && !getAccountRes.GetStatus().IsRecordNotFound():
		return nil, fmt.Errorf("non-success code while fetching upi account by id = %v, err = %w", getByPiIdRes.GetAccountId(), rpcPb.StatusAsError(getAccountRes.GetStatus()))
	}

	if getAccountRes.GetAccount() != nil {
		return &bankDetails{
			bankName:            getAccountRes.GetAccount().GetBankName(),
			accountType:         getAccountRes.GetAccount().GetAccountType(),
			maskedAccountNumber: getAccountRes.GetAccount().GetMaskedAccountNumber(),
		}, nil
	}

	getSavingsAccountEssentialsRes, err := t.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     getByPiIdRes.GetActorId(),
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error while calling GetSavingsAccountEssentials to fetch savings account for actor = %v , err = %w", getByPiIdRes.GetActorId(), err)
	case getSavingsAccountEssentialsRes.GetStatus().IsRecordNotFound():
		return nil, epifierrors.ErrRecordNotFound
	case !getSavingsAccountEssentialsRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("non-success code while fetching GetSavingsAccountEssentials for actor = %v , err = %w", getByPiIdRes.GetActorId(), rpcPb.StatusAsError(getSavingsAccountEssentialsRes.GetStatus()))
	}

	return &bankDetails{
		bankName:            payPkg.DefaultFederalBankActorName,
		maskedAccountNumber: maskPkg.GetMaskedAccountNumber(getSavingsAccountEssentialsRes.GetAccount().GetAccountNo(), "X"),
		accountType:         accounts.Type_SAVINGS,
	}, nil
}

func piMappingExists(piId string, piToBankDetailsMap map[string]*bankDetails) bool {
	_, ok := piToBankDetailsMap[piId]
	return ok
}

// generatePiToBankDetailsMapForActor:
// 1. fetches all the accountPis for an actor
// 2. for each accountPi, upi account is fetched
// 3. generates the mapping of pi to bank details
func (t *TransactionsDataCollectorHelper) generatePiToBankDetailsMapForActor(ctx context.Context, actorId string, savingsAccount *savingsPb.Account) (map[string]*bankDetails, error) {
	var (
		piToBankDetailsMap = make(map[string]*bankDetails)
		err                error
	)

	accountPisForActorRes, err := t.accountPiClient.GetByActorId(ctx, &accountPiPb.GetByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(accountPisForActorRes, err); err != nil {
		return nil, fmt.Errorf("error while fetching account Pis for actor = %v, err = %w", actorId, err)
	}

	for _, accountPi := range accountPisForActorRes.GetAccountPis() {
		getAccountRes, err := t.upiOnbClient.GetAccount(ctx, &upiOnboardingPb.GetAccountRequest{
			AccountId: accountPi.GetAccountId(),
		})

		switch {
		case err != nil:
			return nil, fmt.Errorf("error while calling GetAccount to fetch upi account for account-id = %v, err = %v",
				accountPi.GetAccountId(), err.Error())

		case getAccountRes.GetStatus().IsRecordNotFound() && savingsAccount.GetId() == accountPi.GetAccountId():
			piToBankDetailsMap[accountPi.GetPiId()] = &bankDetails{
				bankName:            payPkg.DefaultFederalBankActorName,
				maskedAccountNumber: maskPkg.GetMaskedAccountNumber(savingsAccount.GetAccountNo(), "X"),
				accountType:         accounts.Type_SAVINGS,
			}
			continue
		case getAccountRes.GetStatus().IsRecordNotFound():
			// will happen in case user is not a part of our system.
			continue
		case !getAccountRes.GetStatus().IsSuccess():
			return nil, fmt.Errorf("error while fetching upi account for account-id = %v, err = %w", accountPi.GetAccountId(), rpcPb.StatusAsError(getAccountRes.GetStatus()))
		}

		piToBankDetailsMap[accountPi.GetPiId()] = &bankDetails{
			bankName:            getAccountRes.GetAccount().GetBankName(),
			maskedAccountNumber: getAccountRes.GetAccount().GetMaskedAccountNumber(),
			accountType:         getAccountRes.GetAccount().GetAccountType(),
		}
	}

	return piToBankDetailsMap, nil
}

func (t *TransactionsDataCollectorHelper) FetchOrderWithTxnList(ctx context.Context, orderWithTxnReq *orderPb.GetOrdersWithTransactionsRequest, actorId string) ([]*txnDcPb.OrderWithTransaction, error) {
	var (
		orderWithTxnList []*txnDcPb.OrderWithTransaction
	)
	resp, err := t.orderClient.GetOrdersWithTransactions(ctx, orderWithTxnReq)

	if te := epifigrpc.RPCError(resp, err); te != nil {
		// don't throw error for record not found, due to client side handling
		if len(resp.GetOrderWithTransactions()) == 0 || resp.GetStatus().IsRecordNotFound() {
			cxLogger.Info(ctx, "no record found for given order IDs", zap.String(logger.ACTOR_ID_V2, actorId))
			return orderWithTxnList, nil
		}
		cxLogger.Error(ctx, "unable to get order with transactions for an actor by order id", zap.Error(te),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, errors.Wrap(te, "error while fetching transactions")
	}

	// iterate on all orders and store order identifier in request object
	for _, orderWithTxn := range resp.GetOrderWithTransactions() {
		for _, txn := range orderWithTxn.GetTransactions() {
			orderWithTxnList = append(orderWithTxnList, t.BuildTxnDetailsProto(ctx, orderWithTxn.GetOrder(), txn, actorId, true))
		}
	}
	return orderWithTxnList, nil
}

// Errors are logged and not thrown since lot of data is being processed, returning partial data in case of intermediate failures
//
//nolint:funlen
func (t *TransactionsDataCollectorHelper) BuildTxnDetailsProto(ctx context.Context, order *orderPb.Order, txn *paymentPb.Transaction, currentActorId string, isMaskingRequired bool) *txnDcPb.OrderWithTransaction {
	orderWithTransaction := &txnDcPb.OrderWithTransaction{
		OrderId:             order.GetId(),
		FromActorId:         order.GetFromActorId(),
		ToActorId:           order.GetToActorId(),
		OrderWorkflow:       order.GetWorkflow().String(),
		OrderProvenance:     order.GetProvenance().String(),
		Amount:              order.GetAmount(),
		OrderCreatedAt:      order.GetCreatedAt(),
		OrderUpdatedAt:      order.GetUpdatedAt(),
		OrderDeletedAt:      order.GetDeletedAt(),
		OrderExpireAt:       order.GetExpireAt(),
		OrderExternalId:     order.GetExternalId(),
		TxnMiscData:         getTxnMiscData(order),
		InternalTxnId:       txn.GetId(),
		PiFrom:              txn.GetPiFrom(),
		PiTo:                txn.GetPiTo(),
		PartnerRefId:        txn.GetPartnerRefId(),
		Utr:                 txn.GetUtr(),
		PartnerBank:         txn.GetPartnerBank().String(),
		ParentTransactionId: txn.GetParentTransactionId(),
		TxnStatus:           txn.GetStatus().String(),
		PaymentProtocol:     txn.GetPaymentProtocol().String(),
		TxnCreatedAt:        txn.GetCreatedAt(),
		TxnUpdatedAt:        txn.GetUpdatedAt(),
		PartnerExecutedAt:   txn.GetPartnerExecutedAt(),
		TxnDeletedAt:        txn.GetDeletedAt(),
		ProtocolStatus:      txn.GetProtocolStatus().String(),
		TxnDisputedAt:       txn.GetDisputedAt(),
		FundsDebitedAt:      txn.GetDebitedAt(),
		AttachEntityMeta: &ticketPb.AttachEntityMeta{
			Meta: &ticketPb.AttachEntityMeta_OrderWithTransactionMeta{
				OrderWithTransactionMeta: &ticketPb.OrderWithTransactionMeta{
					TransactionId: txn.GetId(),
				},
			},
		},
		// populate remarks only for p2m txn
		// populate merchant name
	}
	if isMaskingRequired {
		orderWithTransaction.OrderExternalId = t.dataCollectorHelper.MaskStringExceptLast4(orderWithTransaction.GetOrderExternalId())
	}

	for _, tag := range order.GetTags() {
		if tag == orderPb.OrderTag_MERCHANT {
			orderWithTransaction.Remarks = txn.GetRemarks()
			var merchantActorId string
			if order.GetFromActorId() == currentActorId {
				merchantActorId = order.GetToActorId()
			} else {
				merchantActorId = order.GetFromActorId()
			}
			merchantDetailsRes, err := t.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
				ActorId: merchantActorId,
			})
			if te := epifigrpc.RPCError(merchantDetailsRes, err); te != nil {
				cxLogger.Error(ctx, "unable to get merchant details", zap.String("merchantActorID", merchantActorId), zap.Error(te))
				break
			}
			orderWithTransaction.MerchantDetails = &txnDcPb.MerchantDetails{
				MerchantId:   merchantActorId,
				MerchantName: merchantDetailsRes.GetName(),
			}
			break
		}
	}

	num, ok := txnDcPb.OrderStatus_value[order.GetStatus().String()]
	if !ok {
		cxLogger.Error(ctx, "order status enum received does not exist in cx service, please sync")
	} else {
		orderWithTransaction.OrderStatus = txnDcPb.OrderStatus(num)
	}
	for _, tag := range order.GetTags() {
		orderWithTransaction.OrderTags = append(orderWithTransaction.OrderTags, tag.String())
	}
	var txnDetailedStatusList []*txnDcPb.TransactionDetailedStatus_DetailedStatus
	for _, ds := range txn.GetDetailedStatus().GetDetailedStatusList() {
		txnDetailedStatus := &txnDcPb.TransactionDetailedStatus_DetailedStatus{
			StatusCodePayer:        ds.GetStatusCodePayer(),
			StatusDescriptionPayer: ds.GetStatusDescriptionPayer(),
			StatusCodePayee:        ds.GetStatusCodePayee(),
			StatusDescriptionPayee: ds.GetStatusDescriptionPayee(),
			ErrorCategory:          ds.GetErrorCategory().String(),
			CreatedAt:              ds.GetCreatedAt(),
			State:                  ds.GetState().String(),
			RawStatusCode:          ds.GetRawStatusCode(),
			RawStatusDescription:   ds.GetRawStatusDescription(),
			SystemErrorDescription: ds.GetSystemErrorDescription(),
		}
		var customerLevelStatusList []*txnDcPb.TransactionDetailedStatus_DetailedStatus_CustomerLevelStatus
		for _, cs := range ds.GetCustomerLevelStatusList() {
			customerLevelStatus := &txnDcPb.TransactionDetailedStatus_DetailedStatus_CustomerLevelStatus{
				RawStatusCode:         cs.GetRawStatusCode(),
				RawStatusDescription:  cs.GetRawStatusDescription(),
				CustomerType:          cs.GetCustomerType().String(),
				RawReversalStatusCode: cs.GetRawReversalStatusCode(),
			}
			customerLevelStatusList = append(customerLevelStatusList, customerLevelStatus)
		}
		txnDetailedStatus.CustomerLevelStatusList = customerLevelStatusList
		txnDetailedStatusList = append(txnDetailedStatusList, txnDetailedStatus)
	}
	orderWithTransaction.DetailedStatus = &txnDcPb.TransactionDetailedStatus{
		Details:            txn.GetDetailedStatus().GetDetails(),
		DetailedStatusList: txnDetailedStatusList,
		// add txn particulars
	}
	fromPiDetails, err := t.dataCollectorHelper.GetPiDetails(ctx, txn.GetPiFrom(), order.GetFromActorId(), isMaskingRequired)
	if err != nil {
		cxLogger.Info(ctx, "unable to get from PI details", zap.Error(err))
		return orderWithTransaction
	}
	toPiDetails, err := t.dataCollectorHelper.GetPiDetails(ctx, txn.GetPiTo(), order.GetToActorId(), isMaskingRequired)
	if err != nil {
		cxLogger.Info(ctx, "unable to get to PI details", zap.Error(err))
		return orderWithTransaction
	}
	orderWithTransaction.FromPiDetails = fromPiDetails
	orderWithTransaction.ToPiDetails = toPiDetails
	txnType, err := deriveTxnType(ctx, order.GetFromActorId(), order.GetToActorId(), currentActorId)
	if err != nil {
		cxLogger.Error(ctx, "cannot derive txn type using from actor and to actor from order", zap.Error(err))
	}
	orderWithTransaction.TransactionType = txnType
	orderWithTransaction.DebitParticulars, orderWithTransaction.CreditParticulars = t.getTxnParticulars(fromPiDetails, toPiDetails, txn)
	return orderWithTransaction
}

func deriveTxnType(ctx context.Context, fromActorId string, toActorId string, currActorId string) (txnDcPb.TransactionType, error) {
	// From actor id in order response is always the one who got debited hence this logic
	if fromActorId == currActorId {
		return txnDcPb.TransactionType_DEBIT, nil
	} else if toActorId == currActorId {
		return txnDcPb.TransactionType_CREDIT, nil
	}
	cxLogger.Error(ctx, "from actor and to actor received from order service do match with that in request header")
	return txnDcPb.TransactionType_UNSPECIFIED, fmt.Errorf("from actor and to actor received from order service do match with that in request header")
}

func GetFromAndToDate(firstOrLast txnDcPb.GetFirstOrLastNTransactionsRequest_Type, account *savingsPb.Account) (*datePb.Date, *datePb.Date, error) {
	var fromDate, toDate *datePb.Date
	thirtyDaysDuration := time.Hour * 720
	twoHundredDaysDuration := 200 * 24 * time.Hour
	switch firstOrLast {
	case txnDcPb.GetFirstOrLastNTransactionsRequest_FIRST:
		fromTime := account.GetCreationInfo().GetFiCreationSucceededAt().AsTime()
		// Add 60 days to from time
		toTime := fromTime.Add(thirtyDaysDuration)

		fromDate = datetime.TimeToDate(&fromTime)
		toDate = datetime.TimeToDate(&toTime)
	case txnDcPb.GetFirstOrLastNTransactionsRequest_LAST:
		// need to fetch txns in last 200 days
		fromTime := time.Now().Add(-1 * twoHundredDaysDuration)
		toTime := time.Now().Add(24 * time.Hour)

		fromDate = datetime.TimeToDate(&fromTime)
		toDate = datetime.TimeToDate(&toTime)
	default:
		return nil, nil, errors.New("invalid type in request")
	}
	return fromDate, toDate, nil
}

func (t *TransactionsDataCollectorHelper) getTxnParticulars(fromPiDetails *txnDcPb.PIDetails, toPiDetails *txnDcPb.PIDetails, txn *paymentPb.Transaction) (string, string) {
	// populate txn particular only if from pi or to pi is generic
	if fromPiDetails.GetPiType() == piPb.PaymentInstrumentType_GENERIC.String() ||
		toPiDetails.GetPiType() == piPb.PaymentInstrumentType_GENERIC.String() {
		var creditNotificationDetails, debitNotificationDetails *paymentPb.NotificationDetails
		for k, v := range txn.GetRawNotificationDetails() {
			switch k {
			case "DEBIT":
				debitNotificationDetails = v
			case "CREDIT":
				creditNotificationDetails = v
			}
		}
		return debitNotificationDetails.GetParticulars(), creditNotificationDetails.GetParticulars()
	}
	return "", ""
}

// getTxnMiscData generates the list of label-value pairs for display purpose.
// What does it hold currently?
// 1. Label: "OffApp", Value (string type): commontypes.BooleanEnum (string)
// 2. Label: "Order Workflow", Value (string type): orderPb.Order_Workflow (string)
func getTxnMiscData(order *orderPb.Order) []*webui.LabelValueV2 {
	offAppStringValue := ""
	switch {
	case order.IsOffAppOrder() == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED:
		offAppStringValue = "Unknown"
	default:
		offAppStringValue = order.IsOffAppOrder().String()
	}

	return []*webui.LabelValueV2{
		{
			Label:    "OffApp",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value:    &webui.LabelValueV2_StringValue{StringValue: offAppStringValue},
		},
		{
			Label:    "Order Workflow",
			DataType: webui.LabelValueV2_DATA_TYPE_STRING,
			Value:    &webui.LabelValueV2_StringValue{StringValue: order.GetWorkflow().String()},
		},
	}
}
