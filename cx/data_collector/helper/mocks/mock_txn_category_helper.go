// Code generated by MockGen. DO NOT EDIT.
// Source: ./txn_category_helper.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockTxnCategoryDataCollectorHelper is a mock of TxnCategoryDataCollectorHelper interface.
type MockTxnCategoryDataCollectorHelper struct {
	ctrl     *gomock.Controller
	recorder *MockTxnCategoryDataCollectorHelperMockRecorder
}

// MockTxnCategoryDataCollectorHelperMockRecorder is the mock recorder for MockTxnCategoryDataCollectorHelper.
type MockTxnCategoryDataCollectorHelperMockRecorder struct {
	mock *MockTxnCategoryDataCollectorHelper
}

// NewMockTxnCategoryDataCollectorHelper creates a new mock instance.
func NewMockTxnCategoryDataCollectorHelper(ctrl *gomock.Controller) *MockTxnCategoryDataCollectorHelper {
	mock := &MockTxnCategoryDataCollectorHelper{ctrl: ctrl}
	mock.recorder = &MockTxnCategoryDataCollectorHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTxnCategoryDataCollectorHelper) EXPECT() *MockTxnCategoryDataCollectorHelperMockRecorder {
	return m.recorder
}

// GetCategoriesForOrders mocks base method.
func (m *MockTxnCategoryDataCollectorHelper) GetCategoriesForOrders(ctx context.Context, actorId string, orderIds []string) (map[string][]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoriesForOrders", ctx, actorId, orderIds)
	ret0, _ := ret[0].(map[string][]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoriesForOrders indicates an expected call of GetCategoriesForOrders.
func (mr *MockTxnCategoryDataCollectorHelperMockRecorder) GetCategoriesForOrders(ctx, actorId, orderIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoriesForOrders", reflect.TypeOf((*MockTxnCategoryDataCollectorHelper)(nil).GetCategoriesForOrders), ctx, actorId, orderIds)
}
