package helper

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/test"
	"github.com/epifi/gamma/api/categorizer"
	categorizerMocksPb "github.com/epifi/gamma/api/categorizer/mocks"
)

type mockTcatHelperFields struct {
	mockCategorizerClient *categorizerMocksPb.MockTxnCategorizerClient
}

func initMocks(ctrl *gomock.Controller) *mockTcatHelperFields {
	return &mockTcatHelperFields{
		mockCategorizerClient: categorizerMocksPb.NewMockTxnCategorizerClient(ctrl),
	}
}

func TestTxnCategoryDataCollectorHelperImpl_GetCategoriesForOrders(t1 *testing.T) {
	t1.Parallel()
	type args struct {
		ctx      context.Context
		actorId  string
		orderIds []string
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *mockTcatHelperFields)
		want       map[string][]string
		wantErr    bool
	}{
		{
			name: "successfully fetch categories for an order",
			args: args{
				ctx:      context.Background(),
				actorId:  "actorId",
				orderIds: []string{"order1"},
			},
			setupMocks: func(f *mockTcatHelperFields) {
				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), &categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order1",
						},
					},
				}).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						TransactionId: "txnId",
						ActorId:       "actorId",
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_APPLIANCES,
							},
							{
								DisplayCategory: categorizer.DisplayCategory_DEPOSITS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetCategoriesInfo(gomock.Any(), &categorizer.GetCategoriesInfoRequest{}).Return(&categorizer.GetCategoriesInfoResponse{
					Status: rpcPb.StatusOk(),
					CategoriesInfo: []*categorizer.CategoryInfo{
						{
							DisplayCategory: categorizer.DisplayCategory_APPLIANCES,
							DisplayName:     "Appliances",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_DEPOSITS,
							DisplayName:     "Deposits",
						},
					},
				}, nil)
			},
			want: map[string][]string{
				"order1": {"Appliances", "Deposits"},
			},
			wantErr: false,
		},
		{
			name: "successfully fetch categories for multiple orders",
			args: args{
				ctx:      context.Background(),
				actorId:  "actorId",
				orderIds: []string{"order1", "order2", "order3", "order4", "order5", "order6"},
			},
			setupMocks: func(f *mockTcatHelperFields) {
				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order1",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						TransactionId: "txnId",
						ActorId:       "actorId",
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_APPLIANCES,
							},
							{
								DisplayCategory: categorizer.DisplayCategory_DEPOSITS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order2",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_FOOD_DRINKS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order3",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_HOUSING_BILLS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order4",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_GIFTS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order5",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_INTEREST,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order6",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_STOCKS_MUTUAL_FUNDS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetCategoriesInfo(gomock.Any(), &categorizer.GetCategoriesInfoRequest{}).Return(&categorizer.GetCategoriesInfoResponse{
					Status: rpcPb.StatusOk(),
					CategoriesInfo: []*categorizer.CategoryInfo{
						{
							DisplayCategory: categorizer.DisplayCategory_APPLIANCES,
							DisplayName:     "Appliances",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_DEPOSITS,
							DisplayName:     "Deposits",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_STOCKS_MUTUAL_FUNDS,
							DisplayName:     "Stocks & Mutual funds",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_INTEREST,
							DisplayName:     "Interest",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_GIFTS,
							DisplayName:     "Gifts",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_HOUSING_BILLS,
							DisplayName:     "Housing & Bills",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_FOOD_DRINKS,
							DisplayName:     "Food & Drinks",
						},
					},
				}, nil)
			},
			want: map[string][]string{
				"order1": {"Appliances", "Deposits"},
				"order2": {"Food & Drinks"},
				"order3": {"Housing & Bills"},
				"order4": {"Gifts"},
				"order5": {"Interest"},
				"order6": {"Stocks & Mutual funds"},
			},
			wantErr: false,
		},
		{
			name: "successfully fetch categories for multiple orders (categories do not exist for a few orders)",
			args: args{
				ctx:      context.Background(),
				actorId:  "actorId",
				orderIds: []string{"order1", "order2", "order3", "order4"},
			},
			setupMocks: func(f *mockTcatHelperFields) {
				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order1",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						TransactionId: "txnId",
						ActorId:       "actorId",
						Ontologies:    nil,
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order2",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_FOOD_DRINKS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order3",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order4",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_GIFTS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetCategoriesInfo(gomock.Any(), &categorizer.GetCategoriesInfoRequest{}).Return(&categorizer.GetCategoriesInfoResponse{
					Status: rpcPb.StatusOk(),
					CategoriesInfo: []*categorizer.CategoryInfo{
						{
							DisplayCategory: categorizer.DisplayCategory_APPLIANCES,
							DisplayName:     "Appliances",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_DEPOSITS,
							DisplayName:     "Deposits",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_STOCKS_MUTUAL_FUNDS,
							DisplayName:     "Stocks & Mutual funds",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_INTEREST,
							DisplayName:     "Interest",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_GIFTS,
							DisplayName:     "Gifts",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_HOUSING_BILLS,
							DisplayName:     "Housing & Bills",
						},
						{
							DisplayCategory: categorizer.DisplayCategory_FOOD_DRINKS,
							DisplayName:     "Food & Drinks",
						},
					},
				}, nil)
			},
			want: map[string][]string{
				"order1": {},
				"order2": {"Food & Drinks"},
				"order3": {},
				"order4": {"Gifts"},
			},
			wantErr: false,
		},
		{
			name: "failed to fetch categories for some orders",
			args: args{
				ctx:      context.Background(),
				actorId:  "actorId",
				orderIds: []string{"order1", "order2"},
			},
			setupMocks: func(f *mockTcatHelperFields) {
				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order1",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						TransactionId: "txnId",
						ActorId:       "actorId",
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_APPLIANCES,
							},
							{
								DisplayCategory: categorizer.DisplayCategory_DEPOSITS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order2",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed to fetch category display names",
			args: args{
				ctx:      context.Background(),
				actorId:  "actorId",
				orderIds: []string{"order1", "order2"},
			},
			setupMocks: func(f *mockTcatHelperFields) {
				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order1",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						TransactionId: "txnId",
						ActorId:       "actorId",
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_APPLIANCES,
							},
							{
								DisplayCategory: categorizer.DisplayCategory_DEPOSITS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order2",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_FOOD_DRINKS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetCategoriesInfo(gomock.Any(), &categorizer.GetCategoriesInfoRequest{}).Return(&categorizer.GetCategoriesInfoResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "category display names are missing",
			args: args{
				ctx:      context.Background(),
				actorId:  "actorId",
				orderIds: []string{"order1"},
			},
			setupMocks: func(f *mockTcatHelperFields) {
				f.mockCategorizerClient.EXPECT().GetTxnCategoryDetails(gomock.Any(), test.NewProtoArgMatcher(&categorizer.GetTxnCategoryDetailsRequest{
					ActorId:    "actorId",
					Provenance: categorizer.Provenance_DS,
					ActivityId: &categorizer.ActivityId{
						Id: &categorizer.ActivityId_OrderId{
							OrderId: "order1",
						},
					},
				})).Return(&categorizer.GetTxnCategoryDetailsResponse{
					Status: rpcPb.StatusOk(),
					TxnCategories: &categorizer.TransactionCategories{
						TransactionId: "txnId",
						ActorId:       "actorId",
						Ontologies: []*categorizer.OntologyDetails{
							{
								DisplayCategory: categorizer.DisplayCategory_APPLIANCES,
							},
							{
								DisplayCategory: categorizer.DisplayCategory_FOOD_DRINKS,
							},
						},
					},
				}, nil)

				f.mockCategorizerClient.EXPECT().GetCategoriesInfo(gomock.Any(), &categorizer.GetCategoriesInfoRequest{}).Return(&categorizer.GetCategoriesInfoResponse{
					Status:         rpcPb.StatusOk(),
					CategoriesInfo: []*categorizer.CategoryInfo{},
				}, nil)
			},
			want: map[string][]string{
				"order1": {"APPLIANCES", "FOOD_DRINKS"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			ctrl := gomock.NewController(t1)
			defer ctrl.Finish()

			f := initMocks(ctrl)
			tt.setupMocks(f)

			s := NewTxnCategoriesHelperImpl(f.mockCategorizerClient)
			got, err := s.GetCategoriesForOrders(tt.args.ctx, tt.args.actorId, tt.args.orderIds)
			if (err != nil) != tt.wantErr {
				t1.Errorf("GetCategoriesForOrders() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t1.Errorf("GetCategoriesForOrders() got = %v \n want %v \n diff %v", got, tt.want, diff)
			}
		})
	}
}
