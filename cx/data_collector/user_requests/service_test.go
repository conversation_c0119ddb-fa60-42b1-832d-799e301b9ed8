package userReqPb

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts/statement"
	mocks3 "github.com/epifi/gamma/api/accounts/statement/mocks"
	"github.com/epifi/gamma/api/comms"
	mock_comms "github.com/epifi/gamma/api/comms/mocks"
	"github.com/epifi/gamma/api/cx"
	userReqPb "github.com/epifi/gamma/api/cx/data_collector/user_requests"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/cx/config"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/data_collector/user_requests/account_details_collector"
	mockAuthEng "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
	mockCollector "github.com/epifi/gamma/cx/test/mocks/data_collector/user_requests/account_details_collector"
	"github.com/epifi/be-common/pkg/epifierrors"
	mockEvent "github.com/epifi/be-common/pkg/events/mocks"
)

type UserRequestServiceTestSuite struct {
	conf	*config.SherlockUserRequestsConfig
	genConf	*cxGenConf.Config
}

var (
	UserRequestServiceTS		UserRequestServiceTestSuite
	actorId				= "ABC"
	getAccountStatementFormDetails1	= &userReqPb.GetAccountStatementFormDetailsRequest{
		Header: &cx.Header{
			Actor:			&types.Actor{Id: actorId},
			InformationLevel:	cx.InformationLevel_HIGHLY_SENSITIVE,
		},
	}
	formDetails1	= &userReqPb.GetAccountStatementFormDetailsResponse{
		Status:		rpcPb.StatusOk(),
		AccountTypes:	[]string{"Federal Savings"},
		AccountTypeToDetailsMap: map[string]*userReqPb.Account{
			"Federal Savings": {AccountType: userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS, AccountNumberLastFourDigits: "X1234"},
		},
	}
	sendStatement1	= &userReqPb.SendAccountStatementRequest{
		Header: &cx.Header{
			Actor:			&types.Actor{Id: actorId},
			InformationLevel:	cx.InformationLevel_HIGHLY_SENSITIVE,
		},
		AccountType:	userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS,
		StartTime:	startTime,
		EndTime:	endTime,
		Reason:		"Monthly",
	}
	sendStatement2	= &userReqPb.SendAccountStatementRequest{
		Header: &cx.Header{
			Actor:			&types.Actor{Id: actorId},
			InformationLevel:	cx.InformationLevel_HIGHLY_SENSITIVE,
		},
		AccountType:	userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS,
		StartTime:	endTime,
		EndTime:	startTime,
		Reason:		"Monthly",
	}
	accountDetails1	= &account_details_collector.AccountDetails{
		AccountId:	"1234",
		AccountNum:	"A1234",
	}
	startTime	= timestamp.New(time.Now().Add(-10 * time.Hour))
	endTime		= timestamp.Now()
	errResp1	= &userReqPb.SendAccountStatementResponse{Status: rpcPb.StatusInternal(), StatusMessage: GenericError}
	errResp2	= &userReqPb.SendAccountStatementResponse{Status: rpcPb.StatusInvalidArgument(), StatusMessage: InvalidAccountTypeErr}

	promptList	= []string{"Debit Card Request & Tracking", "Debit Card Usage", "Chequebook Request & Tracking"}
	promptToDesc	= map[string]string{
		"Debit Card Request & Tracking":	"Request for physical debit card or tracking debit card",
		"Debit Card Usage":			"Enable online, enable contactless, enable ATM withdrawals, enable POS, Enable International Usage",
		"Chequebook Request & Tracking":	"Request chequebook, Request cancelled chequebook and track chequebook, Tax statement ELSS",
	}
)

func TestService_GetAccountStatementFormDetails(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockAuthEngine := mockAuthEng.NewMockIAuthEngine(ctr)
	mockAccountDetailsFac := mockCollector.NewMockAccountDetailsFactory(ctr)
	mockAccountDetailsCollector := mockCollector.NewMockAccountDetailsCollector(ctr)

	type args struct {
		ctx	context.Context
		req	*userReqPb.GetAccountStatementFormDetailsRequest
		mocks	[]any
	}
	tests := []struct {
		name	string
		args	args
		want	*userReqPb.GetAccountStatementFormDetailsResponse
	}{
		{
			name:	"success: access denied action required",
			args: args{
				req:	getAccountStatementFormDetails1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), getAccountStatementFormDetails1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(true, nil),
				},
			},
			want: &userReqPb.GetAccountStatementFormDetailsResponse{
				Status: rpcPb.StatusOk(),
			},
		},
		{
			name:	"error: no collector found for given type",
			args: args{
				req:	getAccountStatementFormDetails1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), getAccountStatementFormDetails1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(nil, errors.New("mock error")),
				},
			},
			want: &userReqPb.GetAccountStatementFormDetailsResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name:	"error: while fetching account details",
			args: args{
				req:	getAccountStatementFormDetails1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), getAccountStatementFormDetails1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(mockAccountDetailsCollector, nil),
					mockAccountDetailsCollector.EXPECT().GetAccountDetails(gomock.Any(), actorId).Return(accountDetails1, errors.New("mock error")),
				},
			},
			want: &userReqPb.GetAccountStatementFormDetailsResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name:	"error: no account found",
			args: args{
				req:	getAccountStatementFormDetails1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), getAccountStatementFormDetails1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(mockAccountDetailsCollector, nil),
					mockAccountDetailsCollector.EXPECT().GetAccountDetails(gomock.Any(), actorId).Return(accountDetails1, errors.Wrap(epifierrors.ErrRecordNotFound, "mock error")),
				},
			},
			want: &userReqPb.GetAccountStatementFormDetailsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name:	"success",
			args: args{
				req:	getAccountStatementFormDetails1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), getAccountStatementFormDetails1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(mockAccountDetailsCollector, nil),
					mockAccountDetailsCollector.EXPECT().GetAccountDetails(gomock.Any(), actorId).Return(accountDetails1, nil),
				},
			},
			want:	formDetails1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewUserRequestsService(UserRequestServiceTS.conf, mockAuthEngine, nil, nil, mockAccountDetailsFac, nil, nil)
			got, _ := s.GetAccountStatementFormDetails(tt.args.ctx, tt.args.req)
			if !doesAccountStatementFormDetailsMatch(got, tt.want) {
				t.Errorf("GetAccountStatementFormDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func doesAccountStatementFormDetailsMatch(got *userReqPb.GetAccountStatementFormDetailsResponse, want *userReqPb.GetAccountStatementFormDetailsResponse) bool {
	got.StartTime = want.StartTime
	got.EndTime = want.EndTime
	return proto.Equal(got, want)
}

func TestService_SendAccountStatement(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockAuthEngine := mockAuthEng.NewMockIAuthEngine(ctr)
	mockStatementClient := mocks3.NewMockAccountStatementClient(ctr)
	mockBroker := mockEvent.NewMockBroker(ctr)
	mockAccountDetailsFac := mockCollector.NewMockAccountDetailsFactory(ctr)
	mockAccountDetailsCollector := mockCollector.NewMockAccountDetailsCollector(ctr)

	type args struct {
		ctx	context.Context
		req	*userReqPb.SendAccountStatementRequest
		mocks	[]any
	}

	tests := []struct {
		name	string
		args	args
		want	*userReqPb.SendAccountStatementResponse
	}{
		{
			name:	"success: required auth level not present",
			args: args{
				req:	sendStatement1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), getAccountStatementFormDetails1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(true, nil),
				},
			},
			want: &userReqPb.SendAccountStatementResponse{
				Status: rpcPb.StatusOk(),
			},
		},
		{
			name:	"invalid start and end date",
			args: args{
				req:	sendStatement2,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), sendStatement2.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
				},
			},
			want: &userReqPb.SendAccountStatementResponse{
				Status:		rpcPb.StatusInvalidArgument(),
				StatusMessage:	BadRequestErrMessage,
			},
		},
		{
			name:	"failed to get account details collector",
			args: args{
				req:	sendStatement1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), sendStatement1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(nil, errors.New("mock error")),
				},
			},
			want:	errResp2,
		},
		{
			name:	"error while fetching account details",
			args: args{
				req:	sendStatement1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(),
						gomock.Any()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(mockAccountDetailsCollector, nil),
					mockAccountDetailsCollector.EXPECT().GetAccountDetails(gomock.Any(), actorId).Return(accountDetails1, errors.New("error")),
				},
			},
			want:	errResp1,
		},
		{
			name:	"account statement generation failed",
			args: args{
				req:	sendStatement1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), sendStatement1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(mockAccountDetailsCollector, nil),
					mockAccountDetailsCollector.EXPECT().GetAccountDetails(gomock.Any(), actorId).Return(accountDetails1, nil),
					mockStatementClient.EXPECT().GenerateAccountStatement(gomock.Any(), gomock.Any()).
						Return(&statement.GenerateAccountStatementResponse{Status: rpcPb.StatusInternal()}, nil),
				},
			},
			want:	errResp1,
		},
		{
			name:	"success",
			args: args{
				req:	sendStatement1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), sendStatement1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(mockAccountDetailsCollector, nil),
					mockAccountDetailsCollector.EXPECT().GetAccountDetails(gomock.Any(), actorId).Return(accountDetails1, nil),
					mockStatementClient.EXPECT().GenerateAccountStatement(gomock.Any(), gomock.Any()).
						Return(&statement.GenerateAccountStatementResponse{Status: rpcPb.StatusOk()}, nil),
					mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()),
				},
			},
			want: &userReqPb.SendAccountStatementResponse{
				Status:		rpcPb.StatusOk(),
				StatusMessage:	"Generating account statement, please wait for 10-12 seconds",
			},
		},
		{
			name:	"error: account closed",
			args: args{
				req:	sendStatement1,
				mocks: []any{
					mockAuthEngine.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), sendStatement1.GetHeader(),
						getAccountStatementFormDetails1.GetHeader().GetInformationLevel()).Return(false, nil),
					mockAccountDetailsFac.EXPECT().GetAccountDetailsCollector(gomock.Any(), userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS).Return(mockAccountDetailsCollector, nil),
					mockAccountDetailsCollector.EXPECT().GetAccountDetails(gomock.Any(), actorId).Return(accountDetails1, nil),
					mockStatementClient.EXPECT().GenerateAccountStatement(gomock.Any(), gomock.Any()).
						Return(&statement.GenerateAccountStatementResponse{Status: rpcPb.NewStatusWithoutDebug(
							uint32(statement.GenerateAccountStatementResponse_ACCOUNT_ALREADY_CLOSED),
							"account is already closed",
						)}, nil),
				},
			},
			want: &userReqPb.SendAccountStatementResponse{
				Status:		rpcPb.StatusInvalidArgument(),
				StatusMessage:	InvalidAccountTypeErr,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewUserRequestsService(UserRequestServiceTS.conf, mockAuthEngine, mockStatementClient,
				mockBroker, mockAccountDetailsFac, nil, nil)
			got, _ := s.SendAccountStatement(tt.args.ctx, tt.args.req)
			if !proto.Equal(got, tt.want) {
				t.Errorf("SendAccountStatement() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetAgentPrompts(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx	context.Context
		in	*userReqPb.GetAgentPromptsRequest
	}
	tests := []struct {
		name	string
		args	args
		want	*userReqPb.GetAgentPromptsResponse
		wantErr	bool
	}{
		{
			name:	"Success: prompts returned successfully",
			args:	args{ctx: context.Background()},
			want: &userReqPb.GetAgentPromptsResponse{
				Status:			rpcPb.StatusOk(),
				AgentPrompts:		promptList,
				PromptDescription:	promptToDesc,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewUserRequestsService(nil, nil, nil, nil, nil, UserRequestServiceTS.genConf, nil)
			got, err := s.GetAgentPrompts(tt.args.ctx, tt.args.in)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAgentPrompts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isAgentPromptResponseMatching(got, tt.want) {
				t.Errorf("GetAgentPrompts() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isAgentPromptResponseMatching(got *userReqPb.GetAgentPromptsResponse, want *userReqPb.GetAgentPromptsResponse) bool {
	if len(got.GetAgentPrompts()) != len(want.GetAgentPrompts()) {
		return false
	}
	for key, value := range want.GetPromptDescription() {
		if value != got.GetPromptDescription()[key] {
			return false
		}
	}
	return true
}

func TestService_SendPrompt(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCommsClient := mock_comms.NewMockCommsClient(ctr)
	type args struct {
		ctx	context.Context
		request	*userReqPb.SendPromptRequest
	}
	tests := []struct {
		name	string
		args	args
		mock	[]interface{}
		want	*userReqPb.SendPromptResponse
		wantErr	bool
	}{
		{
			name:	"no prompt type selected",
			args: args{
				ctx:		context.Background(),
				request:	&userReqPb.SendPromptRequest{},
			},
			want: &userReqPb.SendPromptResponse{
				Status:		rpcPb.StatusInvalidArgument(),
				Message:	"Selected prompt is not supported",
			},
		},
		{
			name:	"failed to send comms",
			args: args{
				ctx:	context.Background(),
				request: &userReqPb.SendPromptRequest{
					PromptType: "Debit Card Request & Tracking",
				},
			},
			mock: []interface{}{
				mockCommsClient.EXPECT().SendMessageBatch(gomock.Any(), gomock.Any()).
					Return(&comms.SendMessageBatchResponse{Status: rpcPb.StatusInternal()}, nil),
			},
			want: &userReqPb.SendPromptResponse{
				Status:		rpcPb.StatusInternal(),
				Message:	"Please, retry unknown error happened",
			},
		},
		{
			name:	"success",
			args: args{
				ctx:	context.Background(),
				request: &userReqPb.SendPromptRequest{
					PromptType: "Debit Card Request & Tracking",
				},
			},
			mock: []interface{}{
				mockCommsClient.EXPECT().SendMessageBatch(gomock.Any(), gomock.Any()).
					Return(&comms.SendMessageBatchResponse{Status: rpcPb.StatusOk()}, nil),
			},
			want: &userReqPb.SendPromptResponse{
				Status:		rpcPb.StatusOk(),
				Message:	"Notification successfully triggered to user, they should receive it in sometime",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewUserRequestsService(nil, nil, nil, nil, nil, UserRequestServiceTS.genConf, mockCommsClient)
			got, err := s.SendPrompt(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("SendPrompt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("SendPrompt() got = %v, want %v", got, tt.want)
			}
		})
	}
}
