package userReqPb

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/statement"
	"github.com/epifi/gamma/api/comms"
	userReqPb "github.com/epifi/gamma/api/cx/data_collector/user_requests"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/config"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/data_collector/user_requests/account_details_collector"
	cxEvents "github.com/epifi/gamma/cx/events"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
)

type Service struct {
	userReqPb.UnimplementedUserRequestsServer
	conf                    *config.SherlockUserRequestsConfig
	authEngine              auth_engine.IAuthEngine
	statementClient         statement.AccountStatementClient
	broker                  events.Broker
	accountCollectorFactory account_details_collector.AccountDetailsFactory
	genConf                 *cxGenConf.Config
	commsClient             comms.CommsClient
}

const (
	StartTime = "Start time"
	EndTime   = "End time"

	SuccessMessage        = "Generating account statement, please wait for 10-12 seconds"
	InvalidAccountTypeErr = "Selected account type is not supported"
	GenericError          = "Something went wrong please try again"
	BadRequestErrMessage  = "Selected time range is invalid"
)

var (
	// custom status returned by GenerateAccountStatement RPC when start date is after account closure
	accountClosureStatus = rpcPb.NewStatusWithoutDebug(
		uint32(statement.GenerateAccountStatementResponse_ACCOUNT_ALREADY_CLOSED),
		"account is already closed",
	)

	// map to convert cx account type enum to actual account type enum
	accountTypeMapping = map[userReqPb.AccountType]accounts.Type{
		userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS: accounts.Type_SAVINGS,
	}

	promptToDeeplink = map[userReqPb.AgentPromptType]*deeplinkPb.Deeplink{
		userReqPb.AgentPromptType_AGENT_PROMPT_TYPE_DEBIT_CARD_REQUEST_AND_TRACKING: {Screen: deeplinkPb.Screen_CARD_HOME_SCREEN},
		userReqPb.AgentPromptType_AGENT_PROMPT_TYPE_DEBIT_CARD_USAGE:                {Screen: deeplinkPb.Screen_CARD_SETTINGS_SCREEN},
		userReqPb.AgentPromptType_AGENT_PROMPT_TYPE_CHEQUEBOOK_REQUEST_AND_TRACKING: {Screen: deeplinkPb.Screen_ALFRED_REQUEST_CHOICE},
	}
)

func NewUserRequestsService(conf *config.SherlockUserRequestsConfig, authEngine auth_engine.IAuthEngine,
	statementClient statement.AccountStatementClient, broker events.Broker,
	accountCollectorFactory account_details_collector.AccountDetailsFactory, genConf *cxGenConf.Config,
	commsClient comms.CommsClient) *Service {
	return &Service{
		conf:                    conf,
		authEngine:              authEngine,
		statementClient:         statementClient,
		broker:                  broker,
		accountCollectorFactory: accountCollectorFactory,
		genConf:                 genConf,
		commsClient:             commsClient,
	}
}

var _ userReqPb.UserRequestsServer = &Service{}

//nolint:funlen
func (s *Service) GetAccountStatementFormDetails(ctx context.Context,
	req *userReqPb.GetAccountStatementFormDetailsRequest) (*userReqPb.GetAccountStatementFormDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &userReqPb.GetAccountStatementFormDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	var (
		actorId                 = req.GetHeader().GetActor().GetId()
		accountTypeToDetailsMap = make(map[string]*userReqPb.Account)
		accountTypeOptions      []string
	)

	// keep track of successfully found or not found account details
	accountNotFoundCnt, accountFoundCnt := 0, 0
	// iterate over all account type which are supported for account statement generation
	for accountTypeEnum, accountTypeStr := range s.conf.AccountTypeEnumToString {
		accountType := userReqPb.AccountType(userReqPb.AccountType_value[strings.ToUpper(accountTypeEnum)])
		accountDetailsCollector, factoryErr := s.accountCollectorFactory.GetAccountDetailsCollector(ctx, accountType)
		if factoryErr != nil {
			// no returning error and just logging as we want to keep fetching details for other account types
			cxLogger.Error(ctx, "error while getting account details collector implementation", zap.Error(factoryErr),
				zap.Any(logger.ACCOUNT_TYPE, accountTypeOptions))
			continue
		}
		accountDetails, err := accountDetailsCollector.GetAccountDetails(ctx, actorId)
		if err != nil || accountDetails == nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				// In case of not found, incrementing the count. No specific error handling is required as we are populating on best effort basis
				accountNotFoundCnt++
			}
			// not returning error and just logging as we want to keep fetching details for other account types
			cxLogger.Error(ctx, "error while fetching account details", zap.Error(err), zap.Any(logger.ACCOUNT_TYPE, accountType))
			continue
		}
		accountFoundCnt++
		accountTypeToDetailsMap[accountTypeStr] = &userReqPb.Account{
			AccountType:                 accountType,
			AccountNumberLastFourDigits: mask.GetMaskedString(mask.DontMaskLastFourChars, accountDetails.AccountNum),
		}
		accountTypeOptions = append(accountTypeOptions, accountTypeStr)
	}

	switch {
	// If account details were fetched successfully even for one type, return the details
	case accountFoundCnt > 0:
		return &userReqPb.GetAccountStatementFormDetailsResponse{
			Status:                  rpcPb.StatusOk(),
			AccountTypes:            accountTypeOptions,
			AccountTypeToDetailsMap: accountTypeToDetailsMap,
			StartTime:               s.getDateTimePicker(StartTime),
			EndTime:                 s.getDateTimePicker(EndTime),
		}, nil
	// If details were not fetched successfully even for single account type, and we got not found return not found status
	case accountNotFoundCnt > 0:
		logger.Error(ctx, "error account not found for any account type")
		return &userReqPb.GetAccountStatementFormDetailsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	// Account details were not fetched for even a single account type network / rpc failures while fetching account details
	default:
		logger.Error(ctx, "error while fetching account details for all types")
		return &userReqPb.GetAccountStatementFormDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
}

func (s *Service) SendAccountStatement(ctx context.Context, req *userReqPb.SendAccountStatementRequest) (*userReqPb.SendAccountStatementResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &userReqPb.SendAccountStatementResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()

	validationErr := s.validateSendAccountStatementRequest(req)
	if validationErr != nil {
		cxLogger.Error(ctx, "invalid request for sending account statement", zap.Error(validationErr))
		return &userReqPb.SendAccountStatementResponse{
			Status:           rpcPb.StatusInvalidArgument(),
			StatusMessage:    BadRequestErrMessage,
			SherlockDeepLink: nil,
		}, nil
	}

	generateStatementErr := s.generateAccountStatement(ctx, req.GetHeader().GetActor().GetId(), req.GetAccountType(), req.GetStartTime(), req.GetEndTime())
	if generateStatementErr != nil {
		cxLogger.Error(ctx, "error while sending account statement", zap.Error(generateStatementErr))
		if errors.Is(generateStatementErr, epifierrors.ErrInvalidArgument) {
			return &userReqPb.SendAccountStatementResponse{
				Status:        rpcPb.StatusInvalidArgument(),
				StatusMessage: InvalidAccountTypeErr,
			}, nil
		}
		return &userReqPb.SendAccountStatementResponse{
			Status:        rpcPb.StatusInternal(),
			StatusMessage: GenericError,
		}, nil
	}

	s.broker.AddToBatch(ctx, cxEvents.NewAccountStatementGenerated(actorId, req.GetAccountType().String(), req.GetReason(),
		strconv.FormatInt(req.GetHeader().GetTicket().GetId(), 10)))
	return &userReqPb.SendAccountStatementResponse{
		Status:        rpcPb.StatusOk(),
		StatusMessage: SuccessMessage,
	}, nil
}

func (s *Service) generateAccountStatement(ctx context.Context, actorId string, accountType userReqPb.AccountType, startTime, endTime *timestamp.Timestamp) error {
	accountDetailsCollector, factoryErr := s.accountCollectorFactory.GetAccountDetailsCollector(ctx, accountType)
	if factoryErr != nil {
		return errors.Wrap(epifierrors.ErrInvalidArgument, factoryErr.Error())
	}
	accountDetails, err := accountDetailsCollector.GetAccountDetails(ctx, actorId)
	if err != nil || accountDetails == nil {
		return errors.Wrap(err, "error while fetching account number")
	}
	convertedAccountType, ok := accountTypeMapping[accountType]
	if !ok {
		return errors.New(fmt.Sprintf("%v account type is not valid", accountType))
	}
	resp, err := s.statementClient.GenerateAccountStatement(ctx, &statement.GenerateAccountStatementRequest{
		AccountId:         accountDetails.AccountId,
		FromDate:          getDateFromTimestamp(startTime),
		ToDate:            getDateFromTimestamp(endTime),
		Format:            statement.StatementFormat_PDF,
		AccountType:       convertedAccountType,
		VendorRequestType: enums.VendorRequestType_VENDOR_REQUEST_TYPE_REAL_TIME_API,
		RequestType:       statement.RequestType_USER_REQUESTED,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		switch {
		case resp.GetStatus().IsInvalidArgument():
			return errors.Wrap(epifierrors.ErrInvalidArgument, "invalid arguments passed while generating account statement")
		case resp.GetStatus().GetCode() == accountClosureStatus.GetCode():
			return errors.Wrap(epifierrors.ErrInvalidArgument, "cannot generate account statement when start date is after account closure date")
		default:
			return errors.Wrap(te, "error while generating account statement")
		}
	}
	return nil
}

func getDateFromTimestamp(t *timestamp.Timestamp) *date.Date {
	year, month, day := t.AsTime().Date()
	return &date.Date{
		Year:  int32(year),
		Month: int32(month),
		Day:   int32(day),
	}
}

func (s *Service) getDateTimePicker(placeholder string) *webui.DateTimePicker {
	defaultDisplayValue := time.Now()
	if placeholder == StartTime {
		// we want to set it to last months first date
		defaultDisplayValue = defaultDisplayValue.Add(-24 * time.Hour * 30)
		year, month, _ := defaultDisplayValue.Date()
		// setting default start time as last month's first date
		defaultDisplayValue = time.Date(year, month, 1, 0, 0, 0, 0, time.Local)
	}
	if placeholder == EndTime {
		defaultDisplayValue = defaultDisplayValue.Add(-24 * time.Hour)
	}
	return &webui.DateTimePicker{
		MaxValue:            timestamp.Now(),
		MinValue:            timestamp.New(s.conf.DateTimePickerConfig.MinValue),
		DefaultDisplayValue: timestamp.New(defaultDisplayValue),
		DateFormat:          s.conf.DateTimePickerConfig.DateFormat,
		IsTimeAllowed:       s.conf.DateTimePickerConfig.IsTimeAllowed,
		PlaceholderText:     placeholder,
		IsDisabled:          s.conf.DateTimePickerConfig.IsDisabled,
	}
}

func (s *Service) validateSendAccountStatementRequest(req *userReqPb.SendAccountStatementRequest) error {
	if req.GetStartTime().AsTime().After(req.GetEndTime().AsTime()) {
		return errors.New("start time cannot be after end time")
	}
	if req.GetAccountType() == userReqPb.AccountType_ACCOUNT_TYPE_UNSPECIFIED {
		return errors.New("account type cannot be unspecified")
	}
	return nil
}

func (s *Service) GetAgentPrompts(ctx context.Context, _ *userReqPb.GetAgentPromptsRequest) (*userReqPb.GetAgentPromptsResponse, error) {
	var promptList []string
	promptToDescription := make(map[string]string)

	s.genConf.AgentPromptConfig().AgentPromptInfoMap().Range(func(promptStr string, promptInfo *cxGenConf.AgentPromptInfo) (continueRange bool) {
		_, found := userReqPb.AgentPromptType_value[strings.ToUpper(promptStr)]
		if !found {
			cxLogger.Error(ctx, "prompt type not found in config", zap.String("PromptType", promptStr))
		}
		if promptInfo.IsPromptEnabled() {
			promptList = append(promptList, promptInfo.PromptValueForAgent())
			promptToDescription[promptInfo.PromptValueForAgent()] = promptInfo.Description()
		}
		return true
	})

	return &userReqPb.GetAgentPromptsResponse{
		Status:            rpcPb.StatusOk(),
		AgentPrompts:      promptList,
		PromptDescription: promptToDescription,
	}, nil
}

func (s *Service) SendPrompt(ctx context.Context, request *userReqPb.SendPromptRequest) (*userReqPb.SendPromptResponse, error) {
	var promptConfig *cxGenConf.AgentPromptInfo
	var promptTypeEnum userReqPb.AgentPromptType
	s.genConf.AgentPromptConfig().AgentPromptInfoMap().Range(func(promptStr string, promptInfo *cxGenConf.AgentPromptInfo) (continueRange bool) {
		// if value selected by agent is matching the value present in the config
		if promptInfo.PromptValueForAgent() == request.GetPromptType() {
			promptConfig = promptInfo
			promptTypeEnum = userReqPb.AgentPromptType(userReqPb.AgentPromptType_value[strings.ToUpper(promptStr)])
			return false
		}
		return true
	})
	if promptTypeEnum == userReqPb.AgentPromptType_AGENT_PROMPT_TYPE_UNSPECIFIED {
		cxLogger.Error(ctx, "invalid prompt type", zap.String("PromptType", request.GetPromptType()))
		return &userReqPb.SendPromptResponse{
			Status:  rpcPb.StatusInvalidArgument(),
			Message: "Selected prompt is not supported",
		}, nil
	}

	// using batch RPC as it is likely possible we want to send comms on multiple mediums
	resp, err := s.commsClient.SendMessageBatch(ctx, &comms.SendMessageBatchRequest{
		Type: comms.QoS_BEST_EFFORT,
		UserIdentifier: &comms.SendMessageBatchRequest_UserId{
			UserId: request.GetHeader().GetUser().GetId(),
		},
		CommunicationList: []*comms.Communication{
			{
				Medium:  comms.Medium_NOTIFICATION,
				Message: s.getSystemTrayNotification(promptTypeEnum, promptConfig.PromptCommsTemplate()),
			},
			{
				Medium:  comms.Medium_NOTIFICATION,
				Message: s.getInAppNotification(promptTypeEnum, promptConfig.PromptCommsTemplate()),
			},
		},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while sending notification to user", zap.Error(te))
		return &userReqPb.SendPromptResponse{
			Status:  rpcPb.StatusInternal(),
			Message: "Please, retry unknown error happened",
		}, nil
	}
	cxLogger.Info(ctx, "manual comms triggered by agent", zap.String(logger.TRIGGER, promptTypeEnum.String()))
	return &userReqPb.SendPromptResponse{
		Status:  rpcPb.StatusOk(),
		Message: "Notification successfully triggered to user, they should receive it in sometime",
	}, nil
}

func (s *Service) getSystemTrayNotification(promptType userReqPb.AgentPromptType, commsTemplate *cxGenConf.PromptCommsTemplate) *comms.Communication_Notification {
	return &comms.Communication_Notification{
		Notification: &comms.NotificationMessage{
			Notification: &fcmPb.Notification{
				NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
				NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
					SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
						CommonTemplateFields: &fcmPb.CommonTemplateFields{
							Title:    commsTemplate.Title(),
							Body:     commsTemplate.Description(),
							Deeplink: promptToDeeplink[promptType],
						},
					},
				},
			},
		},
	}
}

func (s *Service) getInAppNotification(promptType userReqPb.AgentPromptType, commsTemplate *cxGenConf.PromptCommsTemplate) *comms.Communication_Notification {
	return &comms.Communication_Notification{
		Notification: &comms.NotificationMessage{
			Notification: &fcmPb.Notification{
				NotificationType: fcmPb.NotificationType_IN_APP,
				NotificationTemplates: &fcmPb.Notification_InAppTemplate{
					InAppTemplate: &fcmPb.InAppTemplate{
						CommonTemplateFields: &fcmPb.CommonTemplateFields{
							TitleV2: &commontypes.Text{
								DisplayValue: &commontypes.Text_PlainString{PlainString: commsTemplate.Title()},
							},
							Deeplink: promptToDeeplink[promptType],
						},
						NotificationPriority: fcmPb.InAppNotificationPriority_NOTIFICATION_PRIORITY_LOW,
					},
				},
			},
		},
	}
}
