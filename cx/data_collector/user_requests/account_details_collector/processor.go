//go:generate mockgen -source=processor.go -destination=../../../test/mocks/data_collector/user_requests/account_details_collector/mock_processor.go

package account_details_collector

import (
	"context"
)

// AccountDetailsCollector exposes methods that can be used to fetch details for any type of accounts
type AccountDetailsCollector interface {
	// GetAccountDetails method fetches essential account details for given actor's account
	GetAccountDetails(ctx context.Context, actorId string) (*AccountDetails, error)
}

type AccountDetails struct {
	AccountNum string
	AccountId  string
}
