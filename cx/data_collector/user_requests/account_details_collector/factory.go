//go:generate mockgen -source=factory.go -destination=../../../test/mocks/data_collector/user_requests/account_details_collector/mock_factor.go

package account_details_collector

import (
	"context"
	"errors"
	"fmt"

	userReqPb "github.com/epifi/gamma/api/cx/data_collector/user_requests"
)

type AccountDetailsFactory interface {
	GetAccountDetailsCollector(ctx context.Context, accountType userReqPb.AccountType) (AccountDetailsCollector, error)
}

type AccountDetailsFactoryImpl struct {
	savingsAccountCollector *SavingsAccountCollector
}

var _ AccountDetailsFactory = &AccountDetailsFactoryImpl{}

func NewAccountDetailsFactory(savingsAccountCollector *SavingsAccountCollector) *AccountDetailsFactoryImpl {
	return &AccountDetailsFactoryImpl{savingsAccountCollector: savingsAccountCollector}
}

func (a *AccountDetailsFactoryImpl) GetAccountDetailsCollector(ctx context.Context, accountType userReqPb.AccountType) (AccountDetailsCollector, error) {
	switch accountType {
	case userReqPb.AccountType_ACCOUNT_TYPE_SAVINGS:
		return a.savingsAccountCollector, nil
	default:
		return nil, errors.New(fmt.Sprintf("%v account type is not supported", accountType))
	}
}
