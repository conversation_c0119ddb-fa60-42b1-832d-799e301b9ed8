package account_details_collector

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/pkg/errors"

	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type SavingsAccountCollector struct {
	savingsClient savingsPb.SavingsClient
}

func NewSavingsAccountCollector(savingsClient savingsPb.SavingsClient) *SavingsAccountCollector {
	return &SavingsAccountCollector{savingsClient: savingsClient}
}

var _ AccountDetailsCollector = &SavingsAccountCollector{}

func (s *SavingsAccountCollector) GetAccountDetails(ctx context.Context, actorId string) (*AccountDetails, error) {
	accountResp, accountErr := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if te := epifigrpc.RPCError(accountResp, accountErr); te != nil {
		if accountResp.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "savings account details not found")
		}
		return nil, errors.Wrap(te, "error while fetching savings account details")
	}
	return &AccountDetails{
		AccountNum: accountResp.GetAccount().GetAccountNo(),
		AccountId:  accountResp.GetAccount().GetId(),
	}, nil
}
