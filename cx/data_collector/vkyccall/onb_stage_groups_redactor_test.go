package vkyccall

import (
	"context"
	"testing"

	"go.uber.org/zap"
	latLngPb "google.golang.org/genproto/googleapis/type/latlng"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"

	vkyccalltypespb "github.com/epifi/gamma/api/vkyccall/types"
)

func Test_OnbStageGroupsRedactor(t *testing.T) {
	t.Skip("comment out to test logs in local")
	logger.Init("test")
	onbStageGroups := []*vkyccalltypespb.OnboardingStageGroup{
		{
			StageName: "LIVENESS",
			Title:     "VKYC - Liveness Check",
			Stages: []*vkyccalltypespb.OnboardingStage{
				{
					StageType: vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_TYPE_QUESTION_ANSWER,
					StageData: &vkyccalltypespb.OnboardingStage_QuestionAnswer{
						QuestionAnswer: &vkyccalltypespb.OnboardingStageDataQuestionAnswer{
							Questions: []*vkyccalltypespb.StageQuestion{
								{
									QuestionType:   "questionTypeIncome",
									Question:       "What is your monthly income?",
									ExpectedAnswer: "HOLA",
								},
								{
									QuestionType:   "questionTypeOccupation",
									Question:       "What is your occupation?",
									ExpectedAnswer: "HOLA",
								},
								{
									QuestionType:   "questionTypePermanentAddress",
									Question:       "What is your permanent address?",
									ExpectedAnswer: "HOLA",
								},
								{
									QuestionType:   "questionTypeLivenessCheck",
									Question:       "Please read out the 4 digit number appearing on your screen",
									ExpectedAnswer: "HOLA",
								},
							},
						},
					},
				},
			},
		},
		{
			StageName: "stage location",
			Title:     "VKYC - Location",
			Stages: []*vkyccalltypespb.OnboardingStage{
				{
					StageType: vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_LOCATION,
					StageData: &vkyccalltypespb.OnboardingStage_Location{
						Location: &vkyccalltypespb.OnboardingStageDataLocation{
							Location: &vkyccalltypespb.Location{
								Coordinates: &latLngPb.LatLng{
									Latitude:  100,
									Longitude: 100,
								},
								RecordedAt: timestamppb.Now(),
							},
							LocationDetails: []*vkyccalltypespb.LocationDetail{
								{
									Label:          "Customer Live Location",
									ExpectedAnswer: "HOLA",
									IsValid:        true,
								},
								{
									Label:          "IP Address",
									ExpectedAnswer: "HOLA",
									IsValid:        false,
								},
								{
									Label:          "Distance from Current Address",
									ExpectedAnswer: "HOLA",
									IsValid:        true,
								},
							},
							QuestionType: "ABC",
						},
					},
				},
			},
		},
		{
			StageName: "stageNamePanCapture",
			Title:     "VKYC - PAN Capture",
			Stages: []*vkyccalltypespb.OnboardingStage{
				{
					StageType: vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_OCR_EXTRACTION,
					// OCR Data will be extracted from the captured image with a separate RPC call.
					StageData: &vkyccalltypespb.OnboardingStage_OcrExtraction{
						OcrExtraction: &vkyccalltypespb.OnboardingStageDataOcrExtraction{
							Instruction: "Check details extracted from photo",
						},
					},
				},
				{
					// Result of matching will be displayed by making call to a separate RPC.
					StageType: vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_OCR_VALIDATION,
					StageData: &vkyccalltypespb.OnboardingStage_OcrValidation{
						OcrValidation: &vkyccalltypespb.OnboardingStageDataOcrValidation{
							Instruction: "PAN details match with database",
							FormData: &vkyccalltypespb.KVPair{
								Data: map[string]string{
									"panNumber": "PAN123",
									"Name":      "JOLLY JOSEPH",
								},
							},
						},
					},
				},
			},
		},
	}
	ctx := context.Background()
	logRedactedOnbStages(ctx, onbStageGroups, "", "")
	logger.Info(ctx, "original onbStageGroups", zap.Any("onbStageGroups", onbStageGroups))
}
