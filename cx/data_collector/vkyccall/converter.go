package vkyccall

import (
	"context"
	"fmt"

	"google.golang.org/protobuf/proto"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	cxvkyccallpb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	vkyccallpb "github.com/epifi/gamma/api/vkyccall"
)

/*
	Note: all the converter functions clone the request/response object to avoid issues due to mutation of the original object
*/

func toBEPerformClientActionReq(ctx context.Context, req *cxvkyccallpb.PerformClientActionRequest) (*vkyccallpb.PerformClientActionRequest, error) {
	req = proto.Clone(req).(*cxvkyccallpb.PerformClientActionRequest)
	actType, err := toBERequestClientAction(ctx, req.GetRequestClientAction())
	if err != nil {
		return nil, err
	}
	return &vkyccallpb.PerformClientActionRequest{
		AgentId:             req.GetHeader().GetAgentEmail(),
		MeetingId:           req.GetMeetingId(),
		RequestClientAction: actType,
		ActionId:            req.GetActionId(),
	}, nil
}

func toBEInitiateAgentCallReq(req *cxvkyccallpb.InitiateAgentCallRequest) *vkyccallpb.InitiateAgentCallRequest {
	req = proto.Clone(req).(*cxvkyccallpb.InitiateAgentCallRequest)
	return &vkyccallpb.InitiateAgentCallRequest{
		AgentId: req.GetHeader().GetAgentEmail(),
	}
}

func toCXInitiateAgentCallResp(resp *vkyccallpb.InitiateAgentCallResponse) *cxvkyccallpb.InitiateAgentCallResponse {
	resp = proto.Clone(resp).(*vkyccallpb.InitiateAgentCallResponse)
	return &cxvkyccallpb.InitiateAgentCallResponse{
		Status:             resp.GetStatus(),
		MeetingId:          resp.GetMeetingId(),
		RecordingUploadUrl: resp.GetRecordingUploadUrl(),
		JwtToken:           resp.GetJwtToken(),
		VendorAgentId:      resp.GetVendorAgentId(),
		OmegleCallId:       resp.GetOmegleCallId(),
	}
}

func toBEConcludeVkycCallReq(req *cxvkyccallpb.ConcludeVkycCallRequest) *vkyccallpb.ConcludeVkycCallRequest {
	req = proto.Clone(req).(*cxvkyccallpb.ConcludeVkycCallRequest)
	reason := req.GetData().GetRejectionReason()
	return &vkyccallpb.ConcludeVkycCallRequest{
		MeetingId: req.GetMeetingId(),
		AgentId:   req.GetHeader().GetAgentEmail(),
		Data: &vkyccallpb.VkycCallReport{
			OverallAccepted: req.GetData().GetOverallAccepted(),
			RejectionReason: &reason,
			AgentRemarks:    req.GetData().GetAgentRemarks(),
			CallRemarksMap:  req.GetData().GetCallRemarksMap(),
		},
	}
}

func toCXGetUserLocationResp(resp *vkyccallpb.GetUserLocationResponse) *cxvkyccallpb.GetUserLocationResponse {
	resp = proto.Clone(resp).(*vkyccallpb.GetUserLocationResponse)
	return &cxvkyccallpb.GetUserLocationResponse{
		Status:    resp.GetStatus(),
		Location:  resp.GetLocation(),
		IpAddress: resp.GetIpAddress(),
	}
}

func toBEGetUserLocationReq(req *cxvkyccallpb.GetUserLocationRequest) *vkyccallpb.GetUserLocationRequest {
	req = proto.Clone(req).(*cxvkyccallpb.GetUserLocationRequest)
	return &vkyccallpb.GetUserLocationRequest{
		MeetingId: req.GetMeetingId(),
		AgentId:   req.GetHeader().GetAgentEmail(),
	}
}

func toCXCaptureScreenshotResp(resp *vkyccallpb.CaptureScreenshotResponse) *cxvkyccallpb.CaptureScreenshotResponse {
	resp = proto.Clone(resp).(*vkyccallpb.CaptureScreenshotResponse)
	return &cxvkyccallpb.CaptureScreenshotResponse{
		Status:          resp.GetStatus(),
		ImageUrl:        resp.GetImageUrl(),
		ImageIdentifier: resp.GetImageIdentifier(),
	}
}

func toBECaptureScreenshotReq(req *cxvkyccallpb.CaptureScreenshotRequest) *vkyccallpb.CaptureScreenshotRequest {
	req = proto.Clone(req).(*cxvkyccallpb.CaptureScreenshotRequest)
	return &vkyccallpb.CaptureScreenshotRequest{
		MeetingId: req.GetMeetingId(),
		AgentId:   req.GetHeader().GetAgentEmail(),
	}
}

func toCXConcludeVkycCallResp(resp *vkyccallpb.ConcludeVkycCallResponse) *cxvkyccallpb.ConcludeVkycCallResponse {
	resp = proto.Clone(resp).(*vkyccallpb.ConcludeVkycCallResponse)
	return &cxvkyccallpb.ConcludeVkycCallResponse{
		Status: resp.GetStatus(),
	}
}

func toBERequestClientAction(ctx context.Context, t cxvkyccallpb.RequestClientActionType) (vkyccallpb.RequestClientActionType, error) {
	str := cxvkyccallpb.RequestClientActionType_name[int32(t)]
	val, exist := vkyccallpb.RequestClientActionType_value[str]
	if !exist {
		logger.Error(ctx, "no match for RequestClientActionType in backend protos", zap.String("RequestClientActionType", str))
		return 0, fmt.Errorf("no match for RequestClientActionType in backend protos %q", str)
	}
	return vkyccallpb.RequestClientActionType(val), nil
}

func toCXPerformClientActionCallResp(resp *vkyccallpb.PerformClientActionResponse) *cxvkyccallpb.PerformClientActionResponse {
	resp = proto.Clone(resp).(*vkyccallpb.PerformClientActionResponse)
	return &cxvkyccallpb.PerformClientActionResponse{
		Status: resp.GetStatus(),
	}
}
