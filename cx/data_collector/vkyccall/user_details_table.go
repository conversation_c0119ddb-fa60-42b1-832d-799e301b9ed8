package vkyccall

import (
	"strconv"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/datetime"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	vkyccalltypes "github.com/epifi/gamma/api/vkyccall/types"
)

const dateLayout = "02 Jan 2006"

const (
	userDetailsHeaderKey       string = "user_details"
	applicantFormDataHeaderKey string = "applicant_form_data"
	panDataHeaderKey           string = "pan_data"
	passportDataHeaderKey      string = "passport_data"
	emiratesIDDataHeaderKey    string = "emirates_id_data"
	matchScoreHeaderKey        string = "match_score"
)

func productOf3Percentages(a, b, c float32) float64 {
	return (float64(a) * float64(b) * float64(c)) / 10000
}

// nolint: funlen
func (s *Service) getUserDetailsTable(beReport *vkyccalltypes.Report) (*webui.Table, error) {

	applicantPanDetails, panErr := s.getPanCardDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if panErr != nil {
		return nil, panErr
	}

	applicantPassportDetails, passportErr := s.getPassportDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if passportErr != nil {
		return nil, passportErr
	}

	applicantEmiratesIdDetails, emiratesIdErr := s.getEmiratesIDDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if emiratesIdErr != nil {
		return nil, emiratesIdErr
	}

	nameRow := s.getUserDetailsTableRow("NAME",
		beReport.GetApplicantDetails().GetApplicantName().ToString(),
		beReport.GetPanDocumentResults().GetOcrDocumentDetails().GetPanDetails().GetName().ToString(),
		beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetName().ToString(),
		beReport.GetEmiratesIdDocumentResults().GetOcrDocumentDetails().GetEmiratesIdDetails().GetName().ToString(),
		strconv.FormatFloat(
			productOf3Percentages(
				beReport.GetPanDocumentResults().GetPanMatchResult().GetName().GetMatchScorePercent(),
				beReport.GetPassportDocumentResults().GetPassportMatchResult().GetName().GetMatchScorePercent(),
				beReport.GetEmiratesIdDocumentResults().GetEmiratesIdMatchResult().GetName().GetMatchScorePercent(),
			),
			'f', 1, 64)+"%",
	)

	dobRow := s.getUserDetailsTableRow("DATE OF BIRTH",
		datetime.DateToString(beReport.GetApplicantDetails().GetDob(), dateLayout, nil),
		datetime.DateToString(beReport.GetPanDocumentResults().GetOcrDocumentDetails().GetPanDetails().GetDateOfBirth(), dateLayout, nil),
		datetime.DateToString(types.GetBeDate(beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetDateOfBirth()), dateLayout, nil),
		datetime.DateToString(types.GetBeDate(beReport.GetEmiratesIdDocumentResults().GetOcrDocumentDetails().GetEmiratesIdDetails().GetDob()), dateLayout, nil),
		strconv.FormatFloat(
			productOf3Percentages(
				beReport.GetPanDocumentResults().GetPanMatchResult().GetDateOfBirth().GetMatchScorePercent(),
				beReport.GetPassportDocumentResults().GetPassportMatchResult().GetDateOfBirth().GetMatchScorePercent(),
				beReport.GetEmiratesIdDocumentResults().GetEmiratesIdMatchResult().GetDateOfBirth().GetMatchScorePercent(),
			),
			'f', 1, 64)+"%",
	)
	parentsNameRow := s.getUserDetailsTableRow("PARENT'S NAME",
		beReport.GetApplicantDetails().GetGuardianDetails().GetFatherName().ToString()+","+
			beReport.GetApplicantDetails().GetGuardianDetails().GetMotherName().ToString(),
		beReport.GetPanDocumentResults().GetOcrDocumentDetails().GetPanDetails().GetGuardianInfo().GetGuardianName().ToString(),
		"-",
		"-",
		strconv.FormatFloat(
			float64(beReport.GetPanDocumentResults().GetPanMatchResult().GetParentName().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	panNumRow := s.getUserDetailsTableRow("PAN NUMBER",
		applicantPanDetails.GetId(),
		beReport.GetPanDocumentResults().GetOcrDocumentDetails().GetPanDetails().GetId(),
		"-",
		"-",
		strconv.FormatFloat(
			float64(beReport.GetPanDocumentResults().GetPanMatchResult().GetPanNumber().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	passportNumRow := s.getUserDetailsTableRow("PASSPORT NUMBER",
		applicantPassportDetails.GetPassportNumber(),
		"-",
		beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetPassportNumber(),
		"-",
		strconv.FormatFloat(
			float64(beReport.GetPassportDocumentResults().GetPassportMatchResult().GetPassportNumber().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	emiratesIdRow := s.getUserDetailsTableRow("EMIRATES ID",
		applicantEmiratesIdDetails.GetId(),
		"-",
		"-",
		beReport.GetEmiratesIdDocumentResults().GetOcrDocumentDetails().GetEmiratesIdDetails().GetIdentityNumber(),
		strconv.FormatFloat(
			float64(beReport.GetEmiratesIdDocumentResults().GetEmiratesIdMatchResult().GetId().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	t := &webui.Table{
		TableRows: []*webui.TableRow{nameRow, dobRow, parentsNameRow, panNumRow, passportNumRow, emiratesIdRow},
		TableHeaders: []*webui.TableHeader{
			{
				Label:     "User Details",
				HeaderKey: userDetailsHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Applicant form data",
				HeaderKey: applicantFormDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "PAN data",
				HeaderKey: panDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "Passport data",
				HeaderKey: passportDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "Emirates ID data",
				HeaderKey: emiratesIDDataHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Match Score",
				HeaderKey: matchScoreHeaderKey,
				IsVisible: true,
			},
		},
	}

	return t, nil
}

func (s *Service) getUserDetailsTableRow(userDetails, applicationData, panData, passportData, emiratesIDData, matchScore string) *webui.TableRow {
	return &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			userDetailsHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: userDetails},
			},
			applicantFormDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: applicationData},
			},
			panDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: panData},
			},
			passportDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: passportData},
			},
			emiratesIDDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: emiratesIDData},
			},
			matchScoreHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: matchScore},
			},
		},
	}
}

func (s *Service) getPanCardDetails(documentDetails []*types.DocumentDetails) (*types.PanDocumentDetails, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_PAN {
			return documentDetail.GetPanDetails(), nil
		}
	}
	return nil, errors.New("no pan details found in the fetched documents")
}

func (s *Service) getPassportDetails(documentDetails []*types.DocumentDetails) (*types.PassportData, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_PASSPORT {
			return documentDetail.GetPassportDetails(), nil
		}
	}
	return nil, errors.New("no passport details found in the fetched documents")
}

func (s *Service) getEmiratesIDDetails(documentDetails []*types.DocumentDetails) (*types.CountryIdDetails, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_COUNTRY_ID || documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_EMIRATES_ID {
			return documentDetail.GetCountryIdDetails(), nil
		}
	}
	return nil, errors.New("no country ID details found in the fetched documents")
}
