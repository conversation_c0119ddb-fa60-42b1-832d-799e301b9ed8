// nolint: dupl
package serviceprovider

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cxvkyccallpb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	types "github.com/epifi/gamma/api/typesv2"
	typespb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	vgNcPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vkyccalltypes "github.com/epifi/gamma/api/vkyccall/types"
	"github.com/epifi/gamma/pkg/address"
)

type FederalNRIQatarServiceProvider struct {
	vgNameCheckClient vgNcPb.UNNameCheckClient
}

func NewFederalNRIQatarServiceProvider(vgNameCheckClient vgNcPb.UNNameCheckClient) *FederalNRIQatarServiceProvider {
	return &FederalNRIQatarServiceProvider{
		vgNameCheckClient: vgNameCheckClient,
	}
}

var _ ServiceProvider = &FederalNRIQatarServiceProvider{}

// nolint:funlen
func (f *FederalNRIQatarServiceProvider) GetCXReport(_ context.Context, req *GetCXReportRequest) (*GetCXReportResponse, error) {
	beReport := req.BeReport
	// todo (saiteja): revisit post computing match scores
	formattedUserDetails, err := f.getUserDetailsTable(beReport)
	if err != nil {
		return nil, fmt.Errorf("error in getting formatted user details: %w", err)
	}
	return &GetCXReportResponse{
		CXReport: &cxvkyccallpb.Report{
			UserDetails:      formattedUserDetails,
			UserImageDetails: beReport.GetUserImageDetails(),
			QuestionAnswers:  beReport.GetQuestionAnswers(),
			LocationCheckReport: &cxvkyccallpb.LocationCheckReport{
				FormattedAddress:         address.ConvertPostalAddressToString(beReport.GetLocationCheckReport().GetAddress()),
				IpAddress:                beReport.GetLocationCheckReport().GetIpAddress(),
				Distance:                 getDistanceString(beReport.GetLocationCheckReport().GetIsDistanceWithinRange(), beReport.GetLocationCheckReport().GetDistanceInKm()),
				FormattedLocationDetails: beReport.GetLocationCheckReport().GetLocationDetails(),
			},
			BrowserAndIpDetails: &cxvkyccallpb.BrowserAndIPDetails{
				InternetServiceProvider: beReport.GetBrowserAndIpDetails().GetInternetServiceProvider(),
				IpCountryCode:           beReport.GetBrowserAndIpDetails().GetIpCountryCode(),
			},
			MeetingId:    beReport.GetMeetingId(),
			OmegleCallId: beReport.GetOmegleCallId(),
			AgentRemarks: beReport.GetAgentRemarks(),
			FaceMatchResults: &cxvkyccallpb.FaceMatchResults{
				ImageMatchResults: []*cxvkyccallpb.FaceMatchResults_ImageMatchResult{
					{
						ReferenceImageDetails:      beReport.GetUserImageDetails().GetPanUserImageUrl(),
						InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
						MatchScorePercent:          beReport.GetPanDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
						ReferenceImageDocumentType: types.DocumentType_DOCUMENT_TYPE_PAN,
						Title:                      "Face match with PAN",
					},
					{
						ReferenceImageDetails:      beReport.GetUserImageDetails().GetPassportUserImageUrl(),
						InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
						MatchScorePercent:          beReport.GetPassportDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
						ReferenceImageDocumentType: types.DocumentType_DOCUMENT_TYPE_PASSPORT,
						Title:                      "Face match with Passport",
					},
					{
						ReferenceImageDetails:      beReport.GetUserImageDetails().GetQatarIdUserImageUrl(),
						InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
						MatchScorePercent:          beReport.GetQatarIdDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
						ReferenceImageDocumentType: types.DocumentType_DOCUMENT_TYPE_QATAR_ID,
						Title:                      "Face match with Qatar ID",
					},
				},
			},
			AddressMatchResult: &cxvkyccallpb.AddressMatchResult{
				Title:                      "Address Match with Passport",
				ReferenceImageDetails:      beReport.GetUserImageDetails().GetPassportRearImageUrl(),
				ReferenceImageDocumentType: types.DocumentType_DOCUMENT_TYPE_PASSPORT_BACK,
				PermanentAddress:           address.ConvertPostalAddressToString(beReport.GetApplicantDetails().GetAddressDetails().GetPermanentAddress()),
			},
			VerifiedDocumentDetails: []*cxvkyccallpb.VerifiedDocumentDetails{
				{
					DocumentType: types.DocumentType_DOCUMENT_TYPE_QATAR_ID,
					Title:        "Qatar ID",
					FrontImage:   beReport.GetUserImageDetails().GetQatarIdFrontImageUrl(),
					BackImage:    beReport.GetUserImageDetails().GetQatarIdBackImageUrl(),
				},
			},
		},
	}, nil
}

func (f *FederalNRIQatarServiceProvider) GetPassportOcrDetailsMatchResult(ctx context.Context, req *GetPassportOcrDetailsMatchResultRequest) (*GetPassportOcrDetailsMatchResultResponse, error) {
	ocrDetailsMatchResults := &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				Label:     "User Details",
				HeaderKey: userDetailsHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Applicant form data",
				HeaderKey: applicantFormDataHeaderKey,
				IsVisible: true,
			},
			{
				// OCR data
				Label:     "Passport Data",
				HeaderKey: passportDataHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Qatar Data",
				HeaderKey: qatarIDDataHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Match Score",
				HeaderKey: "match_score",
				IsVisible: true,
			},
		},
		TableRows: []*webui.TableRow{},
		TableName: "Match Results",
	}
	passportDetails := req.GetApplicantDocumentDetails().GetPassportDetails()
	matchResult := req.GetPassportMatchResult()
	qatarIdDetails, err := getQatarIdDetails(req.GetBeReport().GetApplicantDetails().GetDocumentDetails())
	if err != nil {
		logger.Error(ctx, "emirates ID details not found", zap.Error(err))
		return nil, err
	}

	nameMatchScore := productOf2Percentages(float64(matchResult.GetName().GetMatchScorePercent()),
		f.nameMatch(ctx, passportDetails.GetName().ToString(), qatarIdDetails.GetName().ToString())*100)
	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Name",
		applicantFormDataHeaderKey: passportDetails.GetName().ToString(),
		passportDataHeaderKey:      req.GetExtractedPassportDetails().GetPassportDetails().GetName().ToString(),
		qatarIDDataHeaderKey:       qatarIdDetails.GetName().ToString(),
		matchScoreHeaderKey:        strconv.FormatFloat(nameMatchScore, 'f', 1, 64) + "%",
	}))

	passportPanDobScore := 0
	if datetime.DateEquals(typespb.GetBeDate(passportDetails.GetDateOfBirth()), typespb.GetBeDate(qatarIdDetails.GetDateOfBirth())) {
		passportPanDobScore = 100
	}
	dobMatchScore := productOf2Percentages(float64(matchResult.GetDateOfBirth().GetMatchScorePercent()), float64(passportPanDobScore))
	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Date of Birth",
		applicantFormDataHeaderKey: datetime.DateToDDMMYYYY(typespb.GetBeDate(passportDetails.GetDateOfBirth())),
		passportDataHeaderKey:      datetime.DateToDDMMYYYY(typespb.GetBeDate(req.GetExtractedPassportDetails().GetPassportDetails().GetDateOfBirth())),
		qatarIDDataHeaderKey:       datetime.DateToDDMMYYYY(typespb.GetBeDate(qatarIdDetails.GetDateOfBirth())),
		matchScoreHeaderKey:        strconv.FormatFloat(dobMatchScore, 'f', 2, 64) + "%",
	}))

	passportNumberScore := float64(0)
	if strings.EqualFold(req.GetExtractedPassportDetails().GetPassportDetails().GetPassportNumber(), qatarIdDetails.GetPassportNumber()) {
		passportNumberScore = 100
	}
	passportNumberScore = productOfNPercentages(matchResult.GetPassportNumber().GetMatchScorePercent(), float32(passportNumberScore))

	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Passport Number",
		applicantFormDataHeaderKey: passportDetails.GetPassportNumber(),
		passportDataHeaderKey:      req.GetExtractedPassportDetails().GetPassportDetails().GetPassportNumber(),
		qatarIDDataHeaderKey:       qatarIdDetails.GetPassportNumber(),
		matchScoreHeaderKey:        strconv.FormatFloat(passportNumberScore, 'f', 2, 64) + "%",
	}))

	passportDateOfExpiryScore := float64(0)
	if proto.Equal(req.GetExtractedPassportDetails().GetPassportDetails().GetDateOfExpiry(), qatarIdDetails.GetDateOfExpiry()) {
		passportDateOfExpiryScore = 100
	}
	passportDateOfExpiryScore = productOfNPercentages(matchResult.GetDateOfExpiry().GetMatchScorePercent(), float32(passportDateOfExpiryScore))

	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Passport expiry date",
		applicantFormDataHeaderKey: datetime.DateToDDMMYYYY(typespb.GetBeDate(passportDetails.GetDateOfExpiry())),
		passportDataHeaderKey:      datetime.DateToDDMMYYYY(typespb.GetBeDate(req.GetExtractedPassportDetails().GetPassportDetails().GetDateOfExpiry())),
		qatarIDDataHeaderKey:       datetime.DateToDDMMYYYY(typespb.GetBeDate(qatarIdDetails.GetPassportExpiry())),
		matchScoreHeaderKey:        strconv.FormatFloat(passportDateOfExpiryScore, 'f', 2, 64) + "%",
	}))
	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Qatar ID",
		applicantFormDataHeaderKey: qatarIdDetails.GetDocumentNumber(),
		passportDataHeaderKey:      "",
		qatarIDDataHeaderKey:       qatarIdDetails.GetDocumentNumber(),
		matchScoreHeaderKey:        "100%",
	}))
	ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), getOcrTableDataRow(map[string]string{
		userDetailsHeaderKey:       "Qatar ID expiry date",
		applicantFormDataHeaderKey: datetime.DateToDDMMYYYY(typespb.GetBeDate(qatarIdDetails.GetDateOfExpiry())),
		passportDataHeaderKey:      "",
		qatarIDDataHeaderKey:       datetime.DateToDDMMYYYY(typespb.GetBeDate(qatarIdDetails.GetDateOfExpiry())),
		matchScoreHeaderKey:        "100%",
	}))

	return &GetPassportOcrDetailsMatchResultResponse{
		OcrDetailsMatchResult: ocrDetailsMatchResults,
	}, nil
}

// nolint: funlen
func (f *FederalNRIQatarServiceProvider) getUserDetailsTable(beReport *vkyccalltypes.Report) (*webui.Table, error) {

	applicantPanDetails, panErr := getPanCardDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if panErr != nil {
		return nil, panErr
	}

	applicantPassportDetails, passportErr := getPassportDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if passportErr != nil {
		return nil, passportErr
	}

	applicantQatarIdDetails, qatarIdErr := getQatarIdDetails(beReport.GetApplicantDetails().GetDocumentDetails())
	if qatarIdErr != nil {
		return nil, qatarIdErr
	}

	panDetails := getPanDocumentData(beReport.GetPanDocumentResults())

	if panDetails == nil {
		return nil, errors.New("pan details not found in the fetched documents")
	}

	nameRow := f.getUserDetailsTableRow("NAME",
		beReport.GetApplicantDetails().GetApplicantName().ToString(),
		panDetails.Name.ToString(),
		beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetName().ToString(),
		beReport.GetQatarIdDocumentResults().GetOcrDocumentDetails().GetQatarIdDetails().GetName().ToString(),
		strconv.FormatFloat(
			productOfNPercentages(
				beReport.GetPanDocumentResults().GetPanMatchResult().GetName().GetMatchScorePercent(),
				beReport.GetPassportDocumentResults().GetPassportMatchResult().GetName().GetMatchScorePercent(),
				beReport.GetQatarIdDocumentResults().GetQatarIdMatchResult().GetName().GetMatchScorePercent(),
			),
			'f', 1, 64)+"%",
	)

	dobRow := f.getUserDetailsTableRow("DATE OF BIRTH",
		datetime.DateToString(beReport.GetApplicantDetails().GetDob(), dateLayout, nil),
		datetime.DateToString(panDetails.DateOfBirth, dateLayout, nil),
		datetime.DateToString(types.GetBeDate(beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetDateOfBirth()), dateLayout, nil),
		datetime.DateToString(types.GetBeDate(beReport.GetQatarIdDocumentResults().GetOcrDocumentDetails().GetQatarIdDetails().GetDateOfBirth()), dateLayout, nil),
		strconv.FormatFloat(
			productOfNPercentages(
				beReport.GetPanDocumentResults().GetPanMatchResult().GetDateOfBirth().GetMatchScorePercent(),
				beReport.GetPassportDocumentResults().GetPassportMatchResult().GetDateOfBirth().GetMatchScorePercent(),
				beReport.GetQatarIdDocumentResults().GetQatarIdMatchResult().GetDateOfBirth().GetMatchScorePercent(),
			),
			'f', 1, 64)+"%",
	)
	parentsNameRow := f.getUserDetailsTableRow("PARENT'S NAME",
		beReport.GetApplicantDetails().GetGuardianDetails().GetFatherName().ToString()+","+
			beReport.GetApplicantDetails().GetGuardianDetails().GetMotherName().ToString(),
		panDetails.GuardianName.ToString(),
		"-",
		"-",
		strconv.FormatFloat(
			float64(beReport.GetPanDocumentResults().GetPanMatchResult().GetParentName().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	panNumRow := f.getUserDetailsTableRow("PAN NUMBER",
		applicantPanDetails.GetId(),
		panDetails.PanNumber,
		"-",
		"-",
		strconv.FormatFloat(
			float64(beReport.GetPanDocumentResults().GetPanMatchResult().GetPanNumber().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	passportNumRow := f.getUserDetailsTableRow("PASSPORT NUMBER",
		applicantPassportDetails.GetPassportNumber(),
		"-",
		beReport.GetPassportDocumentResults().GetOcrDocumentDetails().GetPassportDetails().GetPassportNumber(),
		beReport.GetQatarIdDocumentResults().GetOcrDocumentDetails().GetQatarIdDetails().GetPassportNumber(),
		strconv.FormatFloat(
			float64(beReport.GetPassportDocumentResults().GetPassportMatchResult().GetPassportNumber().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	qatarIdRow := f.getUserDetailsTableRow("QATAR ID",
		applicantQatarIdDetails.GetIdentityNumber(),
		"-",
		"-",
		beReport.GetQatarIdDocumentResults().GetOcrDocumentDetails().GetQatarIdDetails().GetIdentityNumber(),
		strconv.FormatFloat(
			float64(beReport.GetQatarIdDocumentResults().GetQatarIdMatchResult().GetId().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	t := &webui.Table{
		TableRows: []*webui.TableRow{nameRow, dobRow, parentsNameRow, panNumRow, passportNumRow, qatarIdRow},
		TableHeaders: []*webui.TableHeader{
			{
				Label:     "User Details",
				HeaderKey: userDetailsHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Applicant form data",
				HeaderKey: applicantFormDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     string(panDetails.PanType) + " data",
				HeaderKey: panDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "Passport data",
				HeaderKey: passportDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "Qatar ID data",
				HeaderKey: qatarIDDataHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Match Score",
				HeaderKey: matchScoreHeaderKey,
				IsVisible: true,
			},
		},
	}

	return t, nil
}

func (f *FederalNRIQatarServiceProvider) getUserDetailsTableRow(userDetails, applicationData, panData, passportData, qatarIdData, matchScore string) *webui.TableRow {
	return &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			userDetailsHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: userDetails},
			},
			applicantFormDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: applicationData},
			},
			panDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: panData},
			},
			passportDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: passportData},
			},
			qatarIDDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: qatarIdData},
			},
			matchScoreHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: matchScore},
			},
		},
	}
}

func getQatarIdDetails(documentDetails []*types.DocumentDetails) (*types.QatarIdDetails, error) {
	for _, documentDetail := range documentDetails {
		if documentDetail.GetDocumentType() == types.DocumentType_DOCUMENT_TYPE_QATAR_ID {
			return documentDetail.GetQatarIdDetails(), nil
		}
	}
	return nil, errors.New("no country ID details found in the fetched documents")
}

func (f *FederalNRIQatarServiceProvider) nameMatch(ctx context.Context, name1 string, name2 string) float64 {
	nameMatchRes, err := f.vgNameCheckClient.NameMatch(ctx, &vgNcPb.NameMatchRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_IN_HOUSE,
		},
		Name_1: name1,
		Name_2: name2,
	})
	if rpcErr := epifigrpc.RPCError(nameMatchRes, err); rpcErr != nil {
		logger.Error(ctx, "error in name match", zap.Error(rpcErr))
		return 0
	}
	return float64(nameMatchRes.GetScore())
}
