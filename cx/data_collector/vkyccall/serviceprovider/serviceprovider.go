package serviceprovider

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/logger"

	omegleEnumsPb "github.com/epifi/gamma/api/omegle/enums"
	verifiPkg "github.com/epifi/gamma/verifi/pkg"
)

type Factory interface {
	GetCXReport(ctx context.Context, req *GetCXReportRequest) (*GetCXReportResponse, error)
	GetPassportOcrDetailsMatchResult(ctx context.Context, req *GetPassportOcrDetailsMatchResultRequest) (*GetPassportOcrDetailsMatchResultResponse, error)
}

type FactoryImpl struct {
	// todo: cleanup post callers are migrated to channelToSvcProviderMap
	tenantToSvcProviderMap  map[string]ServiceProvider
	channelToSvcProviderMap map[omegleEnumsPb.Channel]ServiceProvider
}

func (f *FactoryImpl) GetCXReport(ctx context.Context, req *GetCXReportRequest) (*GetCXReportResponse, error) {
	channel := req.BeReport.GetApplicantDetails().GetChannel()
	svcProvider, ok := f.channelToSvcProviderMap[channel]
	if !ok {
		logger.Info(ctx, fmt.Sprintf("no service provider found for channel: %s", channel))
		tenant, err := verifiPkg.GetTenantFromCasbinAccessLevel(req.AccessLevel)
		if err != nil {
			return nil, err
		}
		svcProvider, ok = f.tenantToSvcProviderMap[tenant]
		if !ok {
			return nil, fmt.Errorf("error in getting service provider for channel: %s", channel)
		}
	}
	return svcProvider.GetCXReport(ctx, req)
}

// nolint: dupl
func (f *FactoryImpl) GetPassportOcrDetailsMatchResult(ctx context.Context, req *GetPassportOcrDetailsMatchResultRequest) (*GetPassportOcrDetailsMatchResultResponse, error) {
	channel := req.GetBeReport().GetApplicantDetails().GetChannel()
	svcProvider, ok := f.channelToSvcProviderMap[channel]
	if !ok {
		logger.Info(ctx, fmt.Sprintf("no service provider found for channel: %s", channel))
		tenant, err := verifiPkg.GetTenantFromCasbinAccessLevel(req.AccessLevel)
		if err != nil {
			return nil, err
		}
		svcProvider, ok = f.tenantToSvcProviderMap[tenant]
		if !ok {
			return nil, fmt.Errorf("error in getting service provider for channel: %s", channel)
		}
	}
	return svcProvider.GetPassportOcrDetailsMatchResult(ctx, req)
}

var _ Factory = &FactoryImpl{}

func NewFactory(federalNRISvcProvider *FederalNRIServiceProvider, sgSvcProvider *StockGuardianServiceProvider, federalNRIQatarSvcProvider *FederalNRIQatarServiceProvider) Factory {
	tenantToSvcProviderMap := map[string]ServiceProvider{
		verifiPkg.EpifiTechTenant:     federalNRISvcProvider,
		verifiPkg.StockguardianTenant: sgSvcProvider,
	}
	channelToSvcProviderMap := map[omegleEnumsPb.Channel]ServiceProvider{
		omegleEnumsPb.Channel_CHANNEL_NRI_ONBOARDING:       federalNRISvcProvider,
		omegleEnumsPb.Channel_CHANNEL_NBFC_ONBOARDING:      sgSvcProvider,
		omegleEnumsPb.Channel_CHANNEL_NRI_ONBOARDING_QATAR: federalNRIQatarSvcProvider,
	}
	return &FactoryImpl{
		tenantToSvcProviderMap:  tenantToSvcProviderMap,
		channelToSvcProviderMap: channelToSvcProviderMap,
	}
}

type ServiceProvider interface {
	GetCXReport(ctx context.Context, req *GetCXReportRequest) (*GetCXReportResponse, error)
	GetPassportOcrDetailsMatchResult(ctx context.Context, req *GetPassportOcrDetailsMatchResultRequest) (*GetPassportOcrDetailsMatchResultResponse, error)
}
