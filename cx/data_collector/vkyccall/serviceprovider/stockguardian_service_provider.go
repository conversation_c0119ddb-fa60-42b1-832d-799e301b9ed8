package serviceprovider

import (
	"context"
	"strconv"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"

	cxvkyccallpb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	typespb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	vkyccalltypes "github.com/epifi/gamma/api/vkyccall/types"
	"github.com/epifi/gamma/pkg/address"
)

type StockGuardianServiceProvider struct {
}

func NewStockGuardianServiceProvider() *StockGuardianServiceProvider {
	return &StockGuardianServiceProvider{}
}

func (s *StockGuardianServiceProvider) GetCXReport(_ context.Context, req *GetCXReportRequest) (*GetCXReportResponse, error) {
	beReport := req.BeReport
	formattedUserDetails, err := s.getUserDetailsTable(beReport)
	if err != nil {
		return nil, err
	}
	// TODO(verifi) : Cleanup deprecated fields
	return &GetCXReportResponse{
		CXReport: &cxvkyccallpb.Report{
			UserDetails:      formattedUserDetails,
			UserImageDetails: beReport.GetUserImageDetails(),
			QuestionAnswers:  beReport.GetQuestionAnswers(),
			LocationCheckReport: &cxvkyccallpb.LocationCheckReport{
				FormattedAddress:         address.ConvertPostalAddressToString(beReport.GetLocationCheckReport().GetAddress()),
				IpAddress:                beReport.GetLocationCheckReport().GetIpAddress(),
				Distance:                 getDistanceString(beReport.GetLocationCheckReport().GetIsDistanceWithinRange(), beReport.GetLocationCheckReport().GetDistanceInKm()),
				FormattedLocationDetails: beReport.GetLocationCheckReport().GetLocationDetails(),
			},
			BrowserAndIpDetails: &cxvkyccallpb.BrowserAndIPDetails{
				InternetServiceProvider: beReport.GetBrowserAndIpDetails().GetInternetServiceProvider(),
				IpCountryCode:           beReport.GetBrowserAndIpDetails().GetIpCountryCode(),
			},
			MeetingId:    beReport.GetMeetingId(),
			OmegleCallId: beReport.GetOmegleCallId(),
			AgentRemarks: beReport.GetAgentRemarks(),
			FaceMatchResults: &cxvkyccallpb.FaceMatchResults{
				ImageMatchResults: s.getAvailableFaceMatchResults(beReport),
			},
		},
	}, nil
}

func (s *StockGuardianServiceProvider) GetPassportOcrDetailsMatchResult(ctx context.Context, req *GetPassportOcrDetailsMatchResultRequest) (*GetPassportOcrDetailsMatchResultResponse, error) {
	return nil, epifierrors.ErrUnimplemented
}

var _ ServiceProvider = &StockGuardianServiceProvider{}

// nolint: funlen
func (s *StockGuardianServiceProvider) getUserDetailsTable(beReport *vkyccalltypes.Report) (*webui.Table, error) {
	// Determine which data source to use - prioritize CKYC over Digilocker
	var ckycName, digilockerName string
	var ckycDob, digilockerDob string

	// Get CKYC data if available
	if beReport.GetCkycDocumentResults() != nil && beReport.GetCkycDocumentResults().GetFormDataDocumentDetails() != nil {
		ckycData := beReport.GetCkycDocumentResults().GetFormDataDocumentDetails().GetCkycData()
		if ckycData != nil && ckycData.GetPayload() != nil {
			ckycName = ckycData.GetPayload().GetPersonalData().GetName().ToString()
			ckycDob = datetime.DateToString(ckycData.GetPayload().GetPersonalData().GetBirthDate(), dateLayout, nil)
		}
	}

	// Get Digilocker data if available
	if beReport.GetAadharDigilockerDocumentResult() != nil && beReport.GetAadharDigilockerDocumentResult().GetFormDataDocumentDetails() != nil {
		digilockerData := beReport.GetAadharDigilockerDocumentResult().GetFormDataDocumentDetails().GetAadhaarDigilockerData()
		if digilockerData != nil {
			digilockerName = digilockerData.GetName().ToString()
			digilockerDob = datetime.DateToString(digilockerData.GetDateOfBirth(), dateLayout, nil)
		}
	}

	// Use CKYC data if available, otherwise use Digilocker data
	var documentName, documentDob string
	if ckycName != "" {
		documentName = ckycName
		documentDob = ckycDob
	} else {
		documentName = digilockerName
		documentDob = digilockerDob
	}

	nameRow := s.getUserDetailsTableRow("NAME",
		beReport.GetApplicantDetails().GetApplicantName().ToString(),
		beReport.GetPanDocumentResults().GetOcrDocumentDetails().GetPanDetails().GetName().ToString(),
		documentName,
		strconv.FormatFloat(
			float64(beReport.GetPanDocumentResults().GetPanMatchResult().GetName().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)

	dobRow := s.getUserDetailsTableRow("DATE OF BIRTH",
		datetime.DateToString(beReport.GetApplicantDetails().GetDob(), dateLayout, nil),
		datetime.DateToString(beReport.GetPanDocumentResults().GetOcrDocumentDetails().GetPanDetails().GetDateOfBirth(), dateLayout, nil),
		documentDob,
		strconv.FormatFloat(
			float64(beReport.GetPanDocumentResults().GetPanMatchResult().GetDateOfBirth().GetMatchScorePercent()),
			'f', 1, 64)+"%",
	)
	t := &webui.Table{
		TableRows: []*webui.TableRow{nameRow, dobRow},
		TableHeaders: []*webui.TableHeader{
			{
				Label:     "User Details",
				HeaderKey: userDetailsHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Applicant form data",
				HeaderKey: applicantFormDataHeaderKey,
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "PAN data",
				HeaderKey: panDataHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "CKYC Document Data",
				HeaderKey: ckycDocHeaderKey,
				IsVisible: true,
			},
			{
				Label:     "Match Score",
				HeaderKey: matchScoreHeaderKey,
				IsVisible: true,
			},
		},
	}

	return t, nil
}

func (s *StockGuardianServiceProvider) getAvailableFaceMatchResults(beReport *vkyccalltypes.Report) []*cxvkyccallpb.FaceMatchResults_ImageMatchResult {
	var faceMatchResults []*cxvkyccallpb.FaceMatchResults_ImageMatchResult

	// Add PAN face match if available
	if beReport.GetUserImageDetails().GetPanUserImageUrl() != nil && beReport.GetPanDocumentResults() != nil {
		faceMatchResults = append(faceMatchResults, &cxvkyccallpb.FaceMatchResults_ImageMatchResult{
			ReferenceImageDetails:      beReport.GetUserImageDetails().GetPanUserImageUrl(),
			InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
			MatchScorePercent:          beReport.GetPanDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
			ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_PAN,
			Title:                      "Face match with PAN",
		})
	}

	// Add CKYC face match if available
	if beReport.GetUserImageDetails().GetCkycUserImageUrl() != nil && beReport.GetCkycDocumentResults() != nil {
		faceMatchResults = append(faceMatchResults, &cxvkyccallpb.FaceMatchResults_ImageMatchResult{
			ReferenceImageDetails:      beReport.GetUserImageDetails().GetCkycUserImageUrl(),
			InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
			MatchScorePercent:          beReport.GetCkycDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
			ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_CKYC_RECORD,
			Title:                      "Face match with CKYC User Image",
		})
	}

	// Add Digilocker face match if available
	if beReport.GetUserImageDetails().GetDigilockerUserImageUrl() != nil && beReport.GetAadharDigilockerDocumentResult() != nil {
		faceMatchResults = append(faceMatchResults, &cxvkyccallpb.FaceMatchResults_ImageMatchResult{
			ReferenceImageDetails:      beReport.GetUserImageDetails().GetDigilockerUserImageUrl(),
			InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
			MatchScorePercent:          beReport.GetAadharDigilockerDocumentResult().GetFaceMatchResult().GetMatchScorePercent(),
			ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_AADHAAR,
			Title:                      "Face match with Digilocker User Image",
		})
	}

	return faceMatchResults
}

func (s *StockGuardianServiceProvider) getUserDetailsTableRow(userDetails, applicationData, panData, ckycDocumentData, matchScore string) *webui.TableRow {
	return &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			userDetailsHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: userDetails},
			},
			applicantFormDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: applicationData},
			},
			panDataHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: panData},
			},
			matchScoreHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: matchScore},
			},
			ckycDocHeaderKey: {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: ckycDocumentData},
			},
		},
	}
}
