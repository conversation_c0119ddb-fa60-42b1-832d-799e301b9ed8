package serviceprovider

import (
	casbinPb "github.com/epifi/gamma/api/casbin"
	"github.com/epifi/gamma/api/typesv2"
	vkyccalltypes "github.com/epifi/gamma/api/vkyccall/types"
)

func (r *GetCXReportRequest) GetBeReport() *vkyccalltypes.Report {
	if r == nil {
		return nil
	}
	return r.BeReport
}

func (r *GetCXReportRequest) GetAccessLevel() casbinPb.AccessLevel {
	if r == nil {
		return 0
	}
	return r.AccessLevel
}

func (r *GetPassportOcrDetailsMatchResultRequest) GetBeReport() *vkyccalltypes.Report {
	if r == nil {
		return nil
	}
	return r.BeReport
}

func (r *GetPassportOcrDetailsMatchResultRequest) GetAccessLevel() casbinPb.AccessLevel {
	if r == nil {
		return 0
	}
	return r.AccessLevel
}

func (r *GetPassportOcrDetailsMatchResultRequest) GetApplicantDocumentDetails() *typesv2.DocumentDetails {
	if r == nil {
		return nil
	}
	return r.ApplicantDocumentDetails
}

func (r *GetPassportOcrDetailsMatchResultRequest) GetPassportMatchResult() *vkyccalltypes.MatchPassportResult {
	if r == nil {
		return nil
	}
	return r.PassportMatchResult
}

func (r *GetPassportOcrDetailsMatchResultRequest) GetExtractedPassportDetails() *typesv2.DocumentDetails {
	if r == nil {
		return nil
	}
	return r.ExtractedPassportDetails
}
