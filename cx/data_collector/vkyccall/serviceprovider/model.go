package serviceprovider

import (
	casbinPb "github.com/epifi/gamma/api/casbin"
	cxvkyccallpb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	vkyccalltypes "github.com/epifi/gamma/api/vkyccall/types"
)

type GetCXReportRequest struct {
	BeReport *vkyccalltypes.Report
	// todo (saiteja): added for backward compatibility, remove once callers are migrated to new logic
	AccessLevel casbinPb.AccessLevel
}

type GetCXReportResponse struct {
	CXReport *cxvkyccallpb.Report
}

type GetPassportOcrDetailsMatchResultRequest struct {
	BeReport *vkyccalltypes.Report
	// todo (saiteja): added for backward compatibility, remove once callers are migrated to new logic
	AccessLevel              casbinPb.AccessLevel
	ApplicantDocumentDetails *typesv2.DocumentDetails
	PassportMatchResult      *vkyccalltypes.MatchPassportResult
	ExtractedPassportDetails *typesv2.DocumentDetails
}

type GetPassportOcrDetailsMatchResultResponse struct {
	OcrDetailsMatchResult *webui.Table
}
