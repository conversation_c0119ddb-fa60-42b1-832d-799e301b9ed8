package vkyccall

import "github.com/epifi/gamma/api/typesv2/webui"

func getOcrTableDataRow(headerKeyCellMap map[string]string) *webui.TableRow {
	webUiheaderKeyCellMap := make(map[string]*webui.TableCell)
	for key, value := range headerKeyCellMap {
		webUiheaderKeyCellMap[key] = &webui.TableCell{
			DataType: webui.TableCell_DATA_TYPE_STRING,
			ValueV2:  &webui.TableCell_StringValue{StringValue: value},
		}
	}
	return &webui.TableRow{
		HeaderKeyCellMap: webUiheaderKeyCellMap,
	}
}
