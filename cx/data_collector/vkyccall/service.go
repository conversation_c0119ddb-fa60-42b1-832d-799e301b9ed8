package vkyccall

import (
	"context"
	"errors"
	"io"
	"strconv"
	"time"

	"github.com/mohae/deepcopy"
	"github.com/samber/lo"
	"go.uber.org/zap"
	json "google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor"

	"github.com/epifi/be-common/pkg/epificontext"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	rpcStatus "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	goutils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx"
	cxvkyccallpb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	"github.com/epifi/gamma/api/omegle/enums"
	omgOcr "github.com/epifi/gamma/api/omegle/ocr"
	typespb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	vgNcPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vkyccallpb "github.com/epifi/gamma/api/vkyccall"
	vkyccalltypespb "github.com/epifi/gamma/api/vkyccall/types"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/data_collector/vkyccall/serviceprovider"
	cxPkg "github.com/epifi/gamma/cx/pkg"
	"github.com/epifi/gamma/pkg/address"
	verifiPkg "github.com/epifi/gamma/verifi/pkg"
)

const (
	LOG_KEY_CALL_ID    = "CALL_ID"
	LOG_KEY_AUDITOR_ID = "AUDITOR_ID"
)

type Service struct {
	cxvkyccallpb.UnimplementedVkycCallServer
	beVKYCCallClientWrapper *verifiPkg.VKycCallClientWrapper
	authEngine              auth_engine.IAuthEngine
	svcProviderFactory      serviceprovider.Factory
	vgNameCheckClient       vgNcPb.UNNameCheckClient
}

func NewService(beVKYCCallClientWrapper *verifiPkg.VKycCallClientWrapper, authEngine auth_engine.IAuthEngine, svcProviderFactory serviceprovider.Factory,
	vgNameCheckClient vgNcPb.UNNameCheckClient) *Service {
	return &Service{
		beVKYCCallClientWrapper: beVKYCCallClientWrapper,
		authEngine:              authEngine,
		svcProviderFactory:      svcProviderFactory,
		vgNameCheckClient:       vgNameCheckClient,
	}
}

func (s *Service) InitiateAgentCall(ctx context.Context, req *cxvkyccallpb.InitiateAgentCallRequest) (*cxvkyccallpb.InitiateAgentCallResponse, error) {
	var cancelCtx func()
	ctx, cancelCtx = context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(90*time.Second))
	defer cancelCtx()
	resp, err := s.beVKYCCallClientWrapper.InitiateAgentCall(ctx, toBEInitiateAgentCallReq(req), getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return &cxvkyccallpb.InitiateAgentCallResponse{
				Status: rpcStatus.StatusRecordNotFoundWithDebugMsg(resp.GetStatus().GetDebugMessage()),
			}, nil
		}
		logger.Error(ctx, "error while initiating agent call", zap.Error(err2))
		return &cxvkyccallpb.InitiateAgentCallResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(err2.Error()),
		}, nil
	}
	return toCXInitiateAgentCallResp(resp), nil
}

func (s *Service) PerformClientAction(ctx context.Context, req *cxvkyccallpb.PerformClientActionRequest) (*cxvkyccallpb.PerformClientActionResponse, error) {
	beReq, _ := toBEPerformClientActionReq(ctx, req)
	resp, err := s.beVKYCCallClientWrapper.PerformClientAction(ctx, beReq, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "error while performing client action", zap.Error(err2))
		return &cxvkyccallpb.PerformClientActionResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(err2.Error()),
		}, nil
	}
	return toCXPerformClientActionCallResp(resp), nil
}

func (s *Service) EndVkycCall(ctx context.Context, req *cxvkyccallpb.EndVkycCallRequest) (*cxvkyccallpb.EndVkycCallResponse, error) {
	resp, err := s.beVKYCCallClientWrapper.EndVkycCall(ctx, &vkyccallpb.EndVkycCallRequest{
		MeetingId:    req.GetMeetingId(),
		IsVkycFailed: req.GetIsVkycFailed(),
		AgentId:      req.GetHeader().GetAgentEmail(),
	}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "error while ending agent call", zap.Error(err2))
		return &cxvkyccallpb.EndVkycCallResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(err2.Error()),
		}, nil
	}
	return &cxvkyccallpb.EndVkycCallResponse{Status: rpcStatus.StatusOk()}, nil

}

func (s *Service) ConcludeVkycCall(ctx context.Context, req *cxvkyccallpb.ConcludeVkycCallRequest) (*cxvkyccallpb.ConcludeVkycCallResponse, error) {
	resp, err := s.beVKYCCallClientWrapper.ConcludeVkycCall(ctx, toBEConcludeVkycCallReq(req), getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "error while concluding agent call", zap.Error(err2))
		return &cxvkyccallpb.ConcludeVkycCallResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(err2.Error()),
		}, nil

	}
	return toCXConcludeVkycCallResp(resp), nil
}

func (s *Service) GetUserLocation(ctx context.Context, req *cxvkyccallpb.GetUserLocationRequest) (*cxvkyccallpb.GetUserLocationResponse, error) {
	resp, err := s.beVKYCCallClientWrapper.GetUserLocation(ctx, toBEGetUserLocationReq(req), getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "error while getting user location", zap.Error(err2))
		return &cxvkyccallpb.GetUserLocationResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(err2.Error()),
		}, nil
	}
	return toCXGetUserLocationResp(resp), nil
}

func (s *Service) CaptureScreenshot(ctx context.Context, req *cxvkyccallpb.CaptureScreenshotRequest) (*cxvkyccallpb.CaptureScreenshotResponse, error) {
	resp, err := s.beVKYCCallClientWrapper.CaptureScreenshot(ctx, toBECaptureScreenshotReq(req), getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "error while capturing screenshot", zap.Error(err2))
		return &cxvkyccallpb.CaptureScreenshotResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(err2.Error()),
		}, nil
	}
	return toCXCaptureScreenshotResp(resp), nil
}

func (s *Service) GetAvailableCalls(req *cxvkyccallpb.GetAvailableCallsRequest, server cxvkyccallpb.VkycCall_GetAvailableCallsServer) error {
	ctx := server.Context()
	beCl, err := s.beVKYCCallClientWrapper.GetAvailableCalls(ctx, &vkyccallpb.GetAvailableCallsRequest{}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if err != nil {
		logger.Error(ctx, "error while fetching available calls", zap.Error(err))
		return err
	}
	logger.Info(ctx, "GetAvailableCalls: stream started from cx")
	msg := &vkyccallpb.GetAvailableCallsResponse{}
	for {
		msg.Reset()
		err := beCl.RecvMsg(msg)
		if errors.Is(err, io.EOF) {
			logger.Info(ctx, "GetAvailableCalls: Stream ended from vkyccall server")
			break
		}
		if err != nil {
			logger.Error(ctx, "GetAvailableCalls: receive error from server", zap.Error(err))
			return err
		}
		err = server.Send(&cxvkyccallpb.GetAvailableCallsResponse{
			WaitingCalls: msg.GetWaitingCalls(),
		})
		if err != nil {
			logger.Error(ctx, "error while sending available calls", zap.Error(err))
			return err
		}
	}
	return nil
}

// nolint:dupl
func (s *Service) GetAvailableCallsCount(ctx context.Context, req *cxvkyccallpb.GetAvailableCallsCountRequest) (*cxvkyccallpb.GetAvailableCallsCountResponse, error) {
	beRes, err := s.beVKYCCallClientWrapper.GetAvailableCallsCount(ctx, &vkyccallpb.GetAvailableCallsCountRequest{}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if rpcErr := epifigrpc.RPCError(beRes, err); rpcErr != nil {
		logger.Error(ctx, "error in fetch available calls", zap.Error(rpcErr))
		return &cxvkyccallpb.GetAvailableCallsCountResponse{Status: rpcStatus.StatusInternalWithDebugMsg(rpcErr.Error())}, nil
	}
	return &cxvkyccallpb.GetAvailableCallsCountResponse{Status: rpcStatus.StatusOk(), WaitingCalls: beRes.GetWaitingCalls()}, nil
}

func (s *Service) GetOnboardingStages(ctx context.Context, req *cxvkyccallpb.GetOnboardingStagesRequest) (*cxvkyccallpb.GetOnboardingStagesResponse, error) {
	beRes, err := s.beVKYCCallClientWrapper.GetOnboardingStages(ctx, &vkyccallpb.GetOnboardingStagesRequest{
		MeetingId: req.GetMeetingId(),
		AgentId:   req.GetHeader().GetAgentEmail(),
	}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if rpcErr := epifigrpc.RPCError(beRes, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching onboarding stages", zap.Error(rpcErr))
		return &cxvkyccallpb.GetOnboardingStagesResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}
	onbStages := addRejectionReasonToEachStage(beRes.GetOnboardingStages())
	logRedactedOnbStages(ctx, onbStages, req.GetMeetingId(), req.GetHeader().GetAgentEmail())
	return &cxvkyccallpb.GetOnboardingStagesResponse{
		Status:           beRes.GetStatus(),
		OnboardingStages: onbStages,
	}, nil
}

func logRedactedOnbStages(ctx context.Context, onbStageGroups []*vkyccalltypespb.OnboardingStageGroup, meetingId string, agentId string) {
	logger.Info(ctx, "redacted VKYC script: stage data for which redaction is supported are logged", zap.String(LOG_KEY_CALL_ID, meetingId), zap.String(logger.AGENT_EMAIL, agentId))
	allowedStageTypes := []vkyccalltypespb.OnboardingStageType{
		vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_TYPE_QUESTION_ANSWER,
		vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_LOCATION,
		vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_IMAGE_CAPTURE,
		vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_OCR_EXTRACTION,
		vkyccalltypespb.OnboardingStageType_ONBOARDING_STAGE_FACE_MATCH,
	}
	copyOfStageGroups := deepcopy.Copy(onbStageGroups).([]*vkyccalltypespb.OnboardingStageGroup)

	for _, onbStageGroup := range copyOfStageGroups {
		for _, stage := range onbStageGroup.GetStages() {
			if !lo.Contains(allowedStageTypes, stage.GetStageType()) {
				// emptying as redaction is not handled for this stage type
				stage.StageData = nil
			}
		}
		res, err := json.Marshal(onbStageGroup)
		if err != nil {
			logger.Error(ctx, "error in marshalling onboarding stage", zap.Error(err))
			continue
		}
		jsonRedactor := redactor.JsonRedactor{}
		redactedJson, err := jsonRedactor.Redact(res, map[string]mask.MaskingStrategy{
			"expectedAnswer": mask.MaskToStaticValue,
			"location":       mask.MaskToStaticValue,
			"latitude":       mask.MaskToStaticValue,
			"longitude":      mask.MaskToStaticValue,
		})
		if err != nil {
			logger.Error(ctx, "error in redacting onboarding stage", zap.Error(err))
			continue
		}
		logger.Info(ctx, "redacted Onboarding Stage Group details", zap.String(logger.STAGE, onbStageGroup.GetStageName()),
			zap.String("redactedJson", string(redactedJson)),
			zap.String(LOG_KEY_CALL_ID, meetingId), zap.String(logger.AGENT_EMAIL, agentId))
	}
}

func addRejectionReasonToEachStage(ongStageGroups []*vkyccalltypespb.OnboardingStageGroup) []*vkyccalltypespb.OnboardingStageGroup {
	for _, ongStageGroup := range ongStageGroups {
		ongStageGroup.RejectionCategories = getAgentRejectionReason()
	}
	return ongStageGroups
}

func (s *Service) ExtractDataFromDocumentImage(ctx context.Context, req *cxvkyccallpb.ExtractDataFromDocumentImageRequest) (*cxvkyccallpb.ExtractDataFromDocumentImageResponse, error) {
	var (
		zapReqType = zap.String(logger.REQUEST_TYPE, req.GetDocumentType().String())
		zapMeetId  = zap.String(logger.REFERENCE_ID, req.GetMeetingId())
	)
	beRes, beErr := s.beVKYCCallClientWrapper.ExtractDataFromDocumentImage(ctx, &vkyccallpb.ExtractDataFromDocumentImageRequest{
		DocumentType:           req.GetDocumentType(),
		FrontImageIdentifier:   req.GetFrontImageIdentifier(),
		BackImageIdentifier:    req.GetBackImageIdentifier(),
		ShouldExtractFaceImage: req.GetShouldExtractFaceImage(),
		MeetingId:              req.GetMeetingId(),
		AgentId:                req.GetHeader().GetAgentEmail(),
	}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if rpcErr := epifigrpc.RPCError(beRes, beErr); rpcErr != nil {
		switch {
		case rpcStatus.StatusFromError(rpcErr).GetCode() == uint32(omgOcr.ExtractDocumentsDetailsResponse_DOCUMENT_OCR_FAILED):
			logger.Info(ctx, "doc extraction: ocr failed", zapReqType, zapMeetId)
			return &cxvkyccallpb.ExtractDataFromDocumentImageResponse{
				Status: rpcStatus.StatusFromError(rpcErr),
				ErrorView: cxPkg.GetModalTypeErrorView(getOcrErrorMsgBasedOnDocumentType(req.GetDocumentType()), []*webui.CTA{
					{
						Label:        "Cancel",
						DisplayTheme: webui.CTA_DISPLAY_THEME_SECONDARY,
					},
					{
						Label:        "Retry",
						DisplayTheme: webui.CTA_DISPLAY_THEME_PRIMARY,
					},
				}),
			}, nil
		case rpcStatus.StatusFromError(rpcErr).GetCode() == uint32(omgOcr.ExtractDocumentsDetailsResponse_DOCUMENT_TYPE_NOT_SUPPORTED):
			logger.Info(ctx, "doc extraction: doc type not supported", zapReqType, zapMeetId)
			return &cxvkyccallpb.ExtractDataFromDocumentImageResponse{
				Status: rpcStatus.StatusFromError(rpcErr),
				ErrorView: cxPkg.GetModalTypeErrorView("Unsupported document type. Please try again with a valid document type", []*webui.CTA{
					{
						Label:        "Cancel",
						DisplayTheme: webui.CTA_DISPLAY_THEME_PRIMARY,
					},
				}),
			}, nil
		default:
			logger.Error(ctx, "doc extraction: error in extracting data from image", zap.Error(rpcErr), zapReqType, zapMeetId)
			return &cxvkyccallpb.ExtractDataFromDocumentImageResponse{
				Status:    rpcStatus.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: cxPkg.GetToastTypeErrorView("Something went wrong. Please try again."),
			}, nil
		}
	}

	logger.Info(ctx, "ok response for doc extraction", zapReqType, zapMeetId)
	return &cxvkyccallpb.ExtractDataFromDocumentImageResponse{
		Status:          rpcStatus.StatusOk(),
		DocumentDetails: beRes.GetDocumentDetails(),
		// Directly access the fields since they are optional
		// nolint:protogetter
		FaceImageUrl: beRes.FaceImageUrl,
		// nolint:protogetter
		FaceImageIdentifier: beRes.FaceImageIdentifier,
		DisplayDetails:      beRes.GetDisplayDetails(),
	}, nil
}

func getOcrErrorMsgBasedOnDocumentType(documentType typespb.DocumentType) string {
	switch documentType {
	case typespb.DocumentType_DOCUMENT_TYPE_PAN:
		return "Extracting details from PAN Card failed. Please retry capturing"
	case typespb.DocumentType_DOCUMENT_TYPE_PASSPORT:
		return "Extracting details from Passport failed. Please retry capturing"
	case typespb.DocumentType_DOCUMENT_TYPE_PASSPORT_FRONT:
		return "Extracting details from Passport front page failed. Please retry capturing"
	case typespb.DocumentType_DOCUMENT_TYPE_PASSPORT_BACK:
		return "Extracting details from Passport back page failed. Please retry capturing"
	case typespb.DocumentType_DOCUMENT_TYPE_COUNTRY_ID:
		return "Extracting details from Emirates ID failed. Please retry capturing"
	default:
		return "Extracting details from document failed. Please retry capturing"
	}
}

// nolint: dupl
func (s *Service) GetMatchScore(ctx context.Context, req *cxvkyccallpb.GetMatchScoreRequest) (*cxvkyccallpb.GetMatchScoreResponse, error) {
	beRes, beErr := s.beVKYCCallClientWrapper.GetMatchScore(ctx, &vkyccallpb.GetMatchScoreRequest{
		LivenessFaceImageIdentifier: req.GetLivenessFaceImageIdentifier(),
		DocumentFaceImageIdentifier: req.GetDocumentFaceImageIdentifier(),
		ExtractedDocumentDetails:    req.GetExtractedDocumentDetails(),
		MeetingId:                   req.GetMeetingId(),
		AgentId:                     req.GetHeader().GetAgentEmail(),
	}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))

	if rpcErr := epifigrpc.RPCError(beRes, beErr); rpcErr != nil {
		errStr := "error in matching document details against OCR output"
		logger.Error(ctx, errStr, zap.Error(rpcErr))
		return &cxvkyccallpb.GetMatchScoreResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(errStr),
		}, nil
	}
	ocrDetailsMatchResults := &webui.Table{
		TableHeaders: []*webui.TableHeader{
			{
				Label:     "User Details",
				HeaderKey: "user_details",
				IsVisible: true,
			},
			{
				Label:     "Applicant form data",
				HeaderKey: "applicant_form_data",
				IsVisible: true,
			},
			{
				// Filled inside the type switch
				Label:     "",
				HeaderKey: "ocr_data",
				IsVisible: true,
			},
			{
				Label:     "Match Score",
				HeaderKey: "match_score",
				IsVisible: true,
			},
		},
		TableRows: []*webui.TableRow{},
		TableName: "Match Results",
	}

	// Make RPC call to Omegle Matcher service and get the response
	switch req.GetExtractedDocumentDetails().GetDocumentType() {
	case typespb.DocumentType_DOCUMENT_TYPE_PAN:
		panDetails := beRes.GetApplicantDocumentDetails().GetPanDetails()
		matchResult := beRes.GetPanMatchResult()
		// TODO(Sundeep): Move the document details fetch to InitiateAgentCall RPC where we fetch the applicant details itself.

		ocrDetailsMatchResults.TableHeaders[2].Label = "PAN data"
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Name",
			panDetails.GetName().ToString(),
			req.GetExtractedDocumentDetails().GetPanDetails().GetName().ToString(),
			strconv.FormatFloat(float64(matchResult.GetName().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Date of Birth",
			datetime.DateToDDMMYYYY(panDetails.GetDateOfBirth()),
			datetime.DateToDDMMYYYY(req.GetExtractedDocumentDetails().GetPanDetails().GetDateOfBirth()),
			strconv.FormatFloat(float64(matchResult.GetDateOfBirth().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
		wrapperOpts := getWrapperOptsFromAccessLevel(ctx, req.GetHeader())
		// todo(verifi): refactor this properly
		if wrapperOpts.GetTenant() != verifiPkg.StockguardianTenant {
			ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
				"Parent's Name",
				panDetails.GetGuardianInfo().GetGuardianName().ToString(),
				req.GetExtractedDocumentDetails().GetPanDetails().GetGuardianInfo().GetGuardianName().ToString(),
				strconv.FormatFloat(float64(matchResult.GetParentName().GetMatchScorePercent()), 'f', 2, 64)+"%",
			))
		}

		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"PAN Number",
			panDetails.GetId(),
			req.GetExtractedDocumentDetails().GetPanDetails().GetId(),
			strconv.FormatFloat(float64(matchResult.GetPanNumber().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
	case typespb.DocumentType_DOCUMENT_TYPE_PASSPORT:
		reportDataRes, err := s.beVKYCCallClientWrapper.GetReportData(ctx, &vkyccallpb.GetReportDataRequest{
			MeetingId:             req.GetMeetingId(),
			AgentId:               req.GetHeader().GetAgentEmail(),
			IgnoreQuestionAnswers: commontypes.BooleanEnum_TRUE,
		}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
		if rpcErr := epifigrpc.RPCError(reportDataRes, err); rpcErr != nil {
			logger.Error(ctx, "error in getting report data", zap.Error(rpcErr))
			return &cxvkyccallpb.GetMatchScoreResponse{
				Status: rpcStatus.StatusInternalWithDebugMsg(rpcErr.Error()),
			}, nil
		}
		res, err := s.svcProviderFactory.GetPassportOcrDetailsMatchResult(ctx, &serviceprovider.GetPassportOcrDetailsMatchResultRequest{
			BeReport:                 reportDataRes.GetReport(),
			ApplicantDocumentDetails: beRes.GetApplicantDocumentDetails(),
			PassportMatchResult:      beRes.GetPassportMatchResult(),
			ExtractedPassportDetails: req.GetExtractedDocumentDetails(),
			AccessLevel:              req.GetHeader().GetAccessLevel(),
		})
		if err != nil {
			logger.Error(ctx, "error in passport OCR details match result", zap.Error(err))
			return nil, err
		}
		ocrDetailsMatchResults = res.OcrDetailsMatchResult
	case typespb.DocumentType_DOCUMENT_TYPE_COUNTRY_ID, typespb.DocumentType_DOCUMENT_TYPE_EMIRATES_ID:

		emiratesIDDetails := beRes.GetApplicantDocumentDetails().GetCountryIdDetails()
		matchResult := beRes.GetEmiratesIdMatchResult()
		ocrDetailsMatchResults.TableHeaders[2].Label = "Emirates ID data"
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Name",
			emiratesIDDetails.GetName().ToString(),
			req.GetExtractedDocumentDetails().GetEmiratesIdDetails().GetName().ToString(),
			strconv.FormatFloat(float64(matchResult.GetName().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Date of Birth",
			datetime.DateToDDMMYYYY(typespb.GetBeDate(emiratesIDDetails.GetDateOfBirth())),
			datetime.DateToDDMMYYYY(typespb.GetBeDate(req.GetExtractedDocumentDetails().GetEmiratesIdDetails().GetDob())),
			strconv.FormatFloat(float64(matchResult.GetDateOfBirth().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
		// ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
		//	"Nationality",
		//	s.convertNationalityToString(emiratesIDDetails.GetNationality()),
		//	s.convertNationalityToString(req.GetExtractedDocumentDetails().GetEmiratesIdDetails().GetNationality()),
		//	strconv.FormatFloat(float64(matchResult.GetNationality().GetMatchScorePercent()), 'f', 2, 64)+"%",
		// ))
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Document Number",
			emiratesIDDetails.GetId(),
			req.GetExtractedDocumentDetails().GetEmiratesIdDetails().GetIdentityNumber(),
			strconv.FormatFloat(float64(matchResult.GetId().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Date of Expiry",
			datetime.DateToDDMMYYYY(typespb.GetBeDate(emiratesIDDetails.GetDateOfExpiry())),
			datetime.DateToDDMMYYYY(typespb.GetBeDate(req.GetExtractedDocumentDetails().GetEmiratesIdDetails().GetDateOfExpiry())),
			strconv.FormatFloat(float64(matchResult.GetDateOfExpiry().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
	case typespb.DocumentType_DOCUMENT_TYPE_CKYC_RECORD:
		// TODO(verifi): check if extra details need to be populated since we are not doing ocr for ckyc record
	case typespb.DocumentType_DOCUMENT_TYPE_EPAN:
		panDetails := beRes.GetApplicantDocumentDetails().GetPanDetails()
		ePanDocDetails := beRes.GetExtractedDocumentDetails().GetEpanDetails()
		matchResult := beRes.GetPanMatchResult()

		ocrDetailsMatchResults.TableHeaders[2].Label = "EPAN data"
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Name",
			panDetails.GetName().ToString(),
			ePanDocDetails.GetName().ToString(),
			strconv.FormatFloat(float64(matchResult.GetName().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Date of Birth",
			datetime.DateToDDMMYYYY(panDetails.GetDateOfBirth()),
			datetime.DateToDDMMYYYY(ePanDocDetails.GetDob()),
			strconv.FormatFloat(float64(matchResult.GetDateOfBirth().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"Parent's Name",
			panDetails.GetGuardianInfo().GetGuardianName().ToString(),
			ePanDocDetails.GetGuardianInfo().GetGuardianName().ToString(),
			strconv.FormatFloat(float64(matchResult.GetParentName().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))
		ocrDetailsMatchResults.TableRows = append(ocrDetailsMatchResults.GetTableRows(), s.getMatchTableRow(
			"PAN Number",
			panDetails.GetId(),
			ePanDocDetails.GetPanNumber(),
			strconv.FormatFloat(float64(matchResult.GetPanNumber().GetMatchScorePercent()), 'f', 2, 64)+"%",
		))

	default:
		return &cxvkyccallpb.GetMatchScoreResponse{
			Status: rpcStatus.StatusInvalidArgumentWithDebugMsg("unsupported document type"),
		}, nil
	}

	return &cxvkyccallpb.GetMatchScoreResponse{
		Status: beRes.GetStatus(),
		FaceImageMatchResult: &cxvkyccallpb.GetMatchScoreResponse_ImageMatchResult{
			MatchScorePercent: beRes.GetFaceMatchResult().GetMatchScorePercent(),
		},
		OcrDetailsMatchResults: ocrDetailsMatchResults,
	}, nil
}

func (s *Service) getMatchTableRow(userDetails, applicationData, ocrData, matchScore string) *webui.TableRow {
	return &webui.TableRow{
		HeaderKeyCellMap: map[string]*webui.TableCell{
			"user_details": {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: userDetails},
			},
			"applicant_form_data": {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: applicationData},
			},
			"ocr_data": {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: ocrData},
			},
			"match_score": {
				DataType: webui.TableCell_DATA_TYPE_STRING,
				ValueV2:  &webui.TableCell_StringValue{StringValue: matchScore},
			},
		},
	}
}

func (s *Service) nameMatch(ctx context.Context, name1 string, name2 string) float64 {
	nameMatchRes, err := s.vgNameCheckClient.NameMatch(ctx, &vgNcPb.NameMatchRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_IN_HOUSE,
		},
		Name_1: name1,
		Name_2: name2,
	})
	if rpcErr := epifigrpc.RPCError(nameMatchRes, err); rpcErr != nil {
		logger.Error(ctx, "error in name match", zap.Error(rpcErr))
		return 0
	}
	return float64(nameMatchRes.GetScore())
}

func (s *Service) convertNationalityToString(nationality typespb.Nationality) string {
	switch nationality {
	case typespb.Nationality_NATIONALITY_INDIAN:
		return "Indian"
	case typespb.Nationality_NATIONALITY_OTHERS:
		return "Others"
	default:
		return "-"
	}
}

func (s *Service) GenerateVKYCCallReport(ctx context.Context, req *cxvkyccallpb.GenerateVKYCCallReportRequest) (*cxvkyccallpb.GenerateVKYCCallReportResponse, error) {
	beRes, beErr := s.beVKYCCallClientWrapper.GetReportData(ctx, &vkyccallpb.GetReportDataRequest{
		MeetingId:       req.GetMeetingId(),
		QuestionAnswers: req.GetQuestionAnswers(),
		AgentId:         req.GetHeader().GetAgentEmail(),
	}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if rpcErr := epifigrpc.RPCError(beRes, beErr); rpcErr != nil {
		errStr := "error in generating vkyc call report"
		logger.Error(ctx, errStr, zap.Error(rpcErr))
		return &cxvkyccallpb.GenerateVKYCCallReportResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(errStr),
		}, nil
	}
	cxReportResp, getErr := s.svcProviderFactory.GetCXReport(ctx, &serviceprovider.GetCXReportRequest{
		AccessLevel: req.GetHeader().GetAccessLevel(),
		BeReport:    beRes.GetReport(),
	})
	if getErr != nil {
		logger.Error(ctx, "error getting formatted report from be report", zap.Error(getErr))
		return &cxvkyccallpb.GenerateVKYCCallReportResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(getErr.Error()),
		}, nil
	}
	resp := &cxvkyccallpb.GenerateVKYCCallReportResponse{
		Status:              rpcStatus.StatusOk(),
		Report:              cxReportResp.CXReport,
		RejectionCategories: getAgentRejectionReason(),
	}

	return resp, nil
}

func (s *Service) GetAvailableReports(req *cxvkyccallpb.GetAvailableReportsRequest, server cxvkyccallpb.VkycCall_GetAvailableReportsServer) error {
	ctx := server.Context()
	beCl, err := s.beVKYCCallClientWrapper.GetVkycReportsForAuditorReview(ctx, &vkyccallpb.GetVkycReportsForAuditorReviewRequest{}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if err != nil {
		logger.Error(ctx, "error while fetching available reports", zap.Error(err))
		return err
	}
	msg := &vkyccallpb.GetVkycReportsForAuditorReviewResponse{}
	for {
		msg.Reset()
		err := beCl.RecvMsg(msg)
		if errors.Is(err, io.EOF) {
			logger.Debug(ctx, "Stream ended...")
			break
		}
		if err != nil {
			return err
		}
		err = server.Send(&cxvkyccallpb.GetAvailableReportsResponse{
			Status:         rpcStatus.StatusOk(),
			WaitingReports: msg.GetPendingReports(),
		})
		if err != nil {
			logger.Error(ctx, "error while sending available reports", zap.Error(err))
			return err
		}
	}
	return nil
}

// nolint:dupl
func (s *Service) GetAvailableReportsCount(ctx context.Context, req *cxvkyccallpb.GetAvailableReportsCountRequest) (*cxvkyccallpb.GetAvailableReportsCountResponse, error) {
	beRes, beErr := s.beVKYCCallClientWrapper.GetVkycReportsForAuditorReviewCount(ctx, &vkyccallpb.GetVkycReportsForAuditorReviewCountRequest{}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if rpcErr := epifigrpc.RPCError(beRes, beErr); rpcErr != nil {
		errStr := "error in fetch available reports for auditor review"
		logger.Error(ctx, errStr, zap.Error(rpcErr))
		return &cxvkyccallpb.GetAvailableReportsCountResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(errStr),
		}, nil
	}
	return &cxvkyccallpb.GetAvailableReportsCountResponse{
		Status:         rpcStatus.StatusOk(),
		WaitingReports: beRes.GetPendingReports(),
	}, nil
}

func (s *Service) InitiateAuditorReview(ctx context.Context, req *cxvkyccallpb.InitiateAuditorReviewRequest) (*cxvkyccallpb.InitiateAuditorReviewResponse, error) {
	beRes, beErr := s.beVKYCCallClientWrapper.InitiateVkycReportAuditorReview(ctx, &vkyccallpb.InitiateVkycReportAuditorReviewRequest{
		AuditorId: req.GetHeader().GetAgentEmail(),
	}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if rpcErr := epifigrpc.RPCError(beRes, beErr); rpcErr != nil {
		if beRes.GetStatus().IsRecordNotFound() {
			return &cxvkyccallpb.InitiateAuditorReviewResponse{
				Status: rpcStatus.StatusRecordNotFoundWithDebugMsg("no pending reports for auditor review"),
			}, nil
		}
		errStr := "error in initiating auditor review"
		logger.Error(ctx, errStr, zap.Error(rpcErr))
		return &cxvkyccallpb.InitiateAuditorReviewResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(errStr),
		}, nil
	}
	report, reportErr := s.svcProviderFactory.GetCXReport(ctx, &serviceprovider.GetCXReportRequest{
		AccessLevel: req.GetHeader().GetAccessLevel(),
		BeReport:    beRes.GetReport(),
	})
	if reportErr != nil {
		errStr := "error in retrieving formatted report"
		logger.Error(ctx, errStr, zap.Error(reportErr), zap.String(LOG_KEY_AUDITOR_ID, req.GetHeader().GetAgentEmail()), zap.String(LOG_KEY_CALL_ID, beRes.GetReport().GetMeetingId()))
		return &cxvkyccallpb.InitiateAuditorReviewResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(errStr),
		}, nil
	}
	return &cxvkyccallpb.InitiateAuditorReviewResponse{
		Status:              rpcStatus.StatusOk(),
		ScreenRecordingUrl:  beRes.GetRecordingUrl(),
		Report:              report.CXReport,
		MeetingId:           beRes.GetReport().GetMeetingId(),
		RejectionCategories: getAuditorRejectionReason(),
	}, nil
}

func (s *Service) ConcludeAuditorReview(ctx context.Context, req *cxvkyccallpb.ConcludeAuditorReviewRequest) (*cxvkyccallpb.ConcludeAuditorReviewResponse, error) {
	rejectionReason := goutils.Enum(req.GetRejectionReason(), enums.AuditorRejectionReason_value, enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_UNSPECIFIED)
	if !req.GetIsApproved() {
		if rejectionReason == enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_UNSPECIFIED {
			return &cxvkyccallpb.ConcludeAuditorReviewResponse{
				Status: rpcStatus.StatusInvalidArgumentWithDebugMsg("rejection reason is not specified"),
			}, nil
		}
		if rejectionReason == enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_OTHERS && req.GetAuditorRemarks() == "" {
			return &cxvkyccallpb.ConcludeAuditorReviewResponse{
				Status: rpcStatus.StatusInvalidArgumentWithDebugMsg("Remarks can't be empty for rejection reason Others"),
			}, nil
		}
	}
	beRes, beErr := s.beVKYCCallClientWrapper.SubmitAuditorReview(ctx, &vkyccallpb.SubmitAuditorReviewRequest{
		AuditorId:       req.GetHeader().GetAgentEmail(),
		MeetingId:       req.GetMeetingId(),
		IsApproved:      req.GetIsApproved(),
		RejectionReason: rejectionReason,
		Remarks:         req.GetAuditorRemarks(),
	}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	if rpcErr := epifigrpc.RPCError(beRes, beErr); rpcErr != nil {
		errStr := "error in concluding auditor review"
		logger.Error(ctx, errStr, zap.Error(rpcErr))
		return &cxvkyccallpb.ConcludeAuditorReviewResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg(errStr),
		}, nil
	}
	return &cxvkyccallpb.ConcludeAuditorReviewResponse{
		Status: rpcStatus.StatusOk(),
	}, nil
}

func (s *Service) getCXReport(beReport *vkyccalltypespb.Report) (*cxvkyccallpb.Report, error) {
	formattedUserDetails, err := s.getUserDetailsTable(beReport)
	if err != nil {
		return nil, err
	}
	// TODO(verifi) : Cleanup deprecated fields
	return &cxvkyccallpb.Report{
		UserDetails:      formattedUserDetails,
		UserImageDetails: beReport.GetUserImageDetails(),
		QuestionAnswers:  beReport.GetQuestionAnswers(),
		LocationCheckReport: &cxvkyccallpb.LocationCheckReport{
			FormattedAddress:         address.ConvertPostalAddressToString(beReport.GetLocationCheckReport().GetAddress()),
			IpAddress:                beReport.GetLocationCheckReport().GetIpAddress(),
			Distance:                 s.getDistanceString(beReport.GetLocationCheckReport().GetIsDistanceWithinRange(), beReport.GetLocationCheckReport().GetDistanceInKm()),
			FormattedLocationDetails: beReport.GetLocationCheckReport().GetLocationDetails(),
		},
		BrowserAndIpDetails: &cxvkyccallpb.BrowserAndIPDetails{
			InternetServiceProvider: beReport.GetBrowserAndIpDetails().GetInternetServiceProvider(),
			IpCountryCode:           beReport.GetBrowserAndIpDetails().GetIpCountryCode(),
		},
		MeetingId: beReport.GetMeetingId(),
		FaceMatchResults: &cxvkyccallpb.FaceMatchResults{
			ImageMatchResults: []*cxvkyccallpb.FaceMatchResults_ImageMatchResult{
				{
					ReferenceImageDetails:      beReport.GetUserImageDetails().GetPanUserImageUrl(),
					InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
					MatchScorePercent:          beReport.GetPanDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
					ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_PAN,
					Title:                      "Face match with PAN",
				},
				{
					ReferenceImageDetails:      beReport.GetUserImageDetails().GetPassportUserImageUrl(),
					InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
					MatchScorePercent:          beReport.GetPassportDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
					ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_PASSPORT,
					Title:                      "Face match with Passport",
				},
				{
					ReferenceImageDetails:      beReport.GetUserImageDetails().GetEmiratesIdUserImageUrl(),
					InputImageDetails:          beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
					MatchScorePercent:          beReport.GetEmiratesIdDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
					ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_EMIRATES_ID,
					Title:                      "Face match with Emirates ID",
				},
			},
			PanFaceMatchResult: &cxvkyccallpb.FaceMatchResults_ImageMatchResult{
				ReferenceImageDetails: beReport.GetUserImageDetails().GetPanUserImageUrl(),
				InputImageDetails:     beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
				MatchScorePercent:     beReport.GetPanDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
			},
			PassportFaceMatchResult: &cxvkyccallpb.FaceMatchResults_ImageMatchResult{
				ReferenceImageDetails: beReport.GetUserImageDetails().GetPassportUserImageUrl(),
				InputImageDetails:     beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
				MatchScorePercent:     beReport.GetPassportDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
			},
			EmiratesIdFaceMatchResult: &cxvkyccallpb.FaceMatchResults_ImageMatchResult{
				ReferenceImageDetails: beReport.GetUserImageDetails().GetEmiratesIdUserImageUrl(),
				InputImageDetails:     beReport.GetUserImageDetails().GetCapturedUserImageUrl(),
				MatchScorePercent:     beReport.GetEmiratesIdDocumentResults().GetFaceMatchResult().GetMatchScorePercent(),
			},
		},
		PermanentAddress: address.ConvertPostalAddressToString(beReport.GetApplicantDetails().GetAddressDetails().GetPermanentAddress()),
		AddressMatchResult: &cxvkyccallpb.AddressMatchResult{
			Title:                      "Address Match with Passport",
			ReferenceImageDetails:      beReport.GetUserImageDetails().GetPassportRearImageUrl(),
			ReferenceImageDocumentType: typespb.DocumentType_DOCUMENT_TYPE_PASSPORT_BACK,
			PermanentAddress:           address.ConvertPostalAddressToString(beReport.GetApplicantDetails().GetAddressDetails().GetPermanentAddress()),
		},
		VerifiedDocumentDetails: []*cxvkyccallpb.VerifiedDocumentDetails{
			{
				DocumentType: typespb.DocumentType_DOCUMENT_TYPE_EMIRATES_ID,
				Title:        "Emirates ID",
				FrontImage:   beReport.GetUserImageDetails().GetEmiratesIdFrontImageUrl(),
				BackImage:    beReport.GetUserImageDetails().GetEmiratesIdBackImageUrl(),
			},
		},
	}, nil
}

func (s *Service) getDistanceString(doesMatch bool, distance float64) string {
	if doesMatch {
		return "< " + strconv.FormatFloat(distance, 'f', 0, 64) + " KM"
	}
	return "> " + strconv.FormatFloat(distance, 'f', 0, 64) + " KM"
}

func getWrapperOptsFromAccessLevel(ctx context.Context, header *cx.Header) *verifiPkg.WrapperOpts {
	wrapperOpts, wrapperErr := verifiPkg.GetWrapperOptsFromCasbinAccessLevel(header.GetAccessLevel())
	if wrapperErr != nil {
		logger.Error(ctx, "error in getting wrapper options from casbin access level", zap.Error(wrapperErr))
		// not returning error for easy integration with existing APIs
		// Client wrapper will error out anyways without proper tenant
	}
	return wrapperOpts
}

func (s *Service) MeetingHealthCheck(ctx context.Context, req *cxvkyccallpb.MeetingHealthCheckRequest) (*cxvkyccallpb.MeetingHealthCheckResponse, error) {
	beRes, beErr := s.beVKYCCallClientWrapper.MeetingHealthCheck(ctx, &vkyccallpb.MeetingHealthCheckRequest{
		MeetingId: req.GetMeetingId(),
	}, getWrapperOptsFromAccessLevel(ctx, req.GetHeader()))
	switch {
	case beRes.GetStatus().IsSuccess():
		return &cxvkyccallpb.MeetingHealthCheckResponse{
			Status: rpcStatus.StatusOk(),
		}, nil
	case beRes.GetStatus().GetCode() == uint32(vkyccallpb.MeetingHealthCheckResponse_NO_MEETING_FOUND):
		return &cxvkyccallpb.MeetingHealthCheckResponse{
			Status: rpcStatus.NewStatus(uint32(cxvkyccallpb.MeetingHealthCheckResponse_NO_MEETING_FOUND), beRes.GetStatus().GetShortMessage(), ""),
		}, nil
	case beRes.GetStatus().GetCode() == uint32(vkyccallpb.MeetingHealthCheckResponse_USER_NOT_JOINED):
		return &cxvkyccallpb.MeetingHealthCheckResponse{
			Status: rpcStatus.NewStatus(uint32(cxvkyccallpb.MeetingHealthCheckResponse_USER_NOT_JOINED), beRes.GetStatus().GetShortMessage(), ""),
		}, nil
	case beRes.GetStatus().GetCode() == uint32(vkyccallpb.MeetingHealthCheckResponse_AGENT_NOT_JOINED):
		return &cxvkyccallpb.MeetingHealthCheckResponse{
			Status: rpcStatus.NewStatus(uint32(cxvkyccallpb.MeetingHealthCheckResponse_AGENT_NOT_JOINED), beRes.GetStatus().GetShortMessage(), ""),
		}, nil
	default:
		rpcErr := epifigrpc.RPCError(beRes, beErr)
		if rpcErr != nil {
			logger.Error(ctx, "error in meeting health check", zap.Error(rpcErr))
			return &cxvkyccallpb.MeetingHealthCheckResponse{
				Status: rpcStatus.StatusInternalWithDebugMsg("error in meeting health check"),
			}, nil
		}
		logger.Error(ctx, "unexpected response in meeting health check")
		return &cxvkyccallpb.MeetingHealthCheckResponse{
			Status: rpcStatus.StatusInternalWithDebugMsg("unexpected response in meeting health check"),
		}, nil
	}
}
