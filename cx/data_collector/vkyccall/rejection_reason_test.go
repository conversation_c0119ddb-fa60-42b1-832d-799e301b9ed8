package vkyccall

import (
	"testing"

	"github.com/stretchr/testify/assert"

	omegleEnumsPb "github.com/epifi/gamma/api/omegle/enums"
)

// Prevents adding invalid agent failure reasons
func TestAgentRejectionReason(t *testing.T) {
	for _, rejectionCategory := range getAgentRejectionReason() {
		for _, rejectionReason := range rejectionCategory.GetRejectionReasons() {
			_, ok := omegleEnumsPb.FailureReason_value[rejectionReason.GetRejectionReasonId()]
			assert.Truef(t, ok, "Rejection reason id %s not found in omegleEnumsPb.FailureReason", rejectionReason.GetRejectionReasonId())
		}
	}
}

// Prevents adding invalid auditor rejection reasons
func TestAuditorRejectionReason(t *testing.T) {
	for _, rejectionCategory := range getAuditorRejectionReason() {
		for _, rejectionReason := range rejectionCategory.GetRejectionReasons() {
			_, ok := omegleEnumsPb.AuditorRejectionReason_value[rejectionReason.GetRejectionReasonId()]
			assert.Truef(t, ok, "Rejection reason id %s not found in omegleEnumsPb.AuditorRejectionReason", rejectionReason.GetRejectionReasonId())
		}
	}
}
