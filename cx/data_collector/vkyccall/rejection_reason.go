package vkyccall

import (
	"github.com/epifi/gamma/api/omegle/enums"
	"github.com/epifi/gamma/api/vkyccall/types"
)

//nolint:dupl, funlen
func getAuditorRejectionReason() []*types.RejectionCategory {
	return []*types.RejectionCategory{
		{
			Title: "Document issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "PAN is not fully captured",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_PAN_IS_NOT_FULLY_CAPTURED.String(),
				},
				{
					RejectionReason:   "PAN photo not clear",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_PAN_PHOTO_NOT_CLEAR.String(),
				},
				{
					RejectionReason:   "PAN OCR issue",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_PAN_OCR_ISSUE.String(),
				},
				{
					RejectionReason:   "Passport is not fully captured",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_PASSPORT_IS_NOT_FULLY_CAPTURED.String(),
				},
				{
					RejectionReason:   "Passport photo not clear",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_PASSPORT_PHOTO_NOT_CLEAR.String(),
				},
				{
					RejectionReason:   "Passport OCR issue",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_PASSPORT_OCR_ISSUE.String(),
				},
			},
		},
		{
			Title: "Customer detail issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Location issue",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_LOCATION_ISSUE.String(),
				},
				{
					RejectionReason:   "Parent name mismatch",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_PARENTS_NAME_MISMATCH.String(),
				},
				{
					RejectionReason:   "Customer name mismatch",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_CUSTOMER_NAME_MISMATCH.String(),
				},
				{
					RejectionReason:   "DOB mismatch",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_DOB_MISMATCH.String(),
				},
				{
					RejectionReason:   "Gender mismatch",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_GENDER_MISMATCH.String(),
				},
				{
					RejectionReason:   "Salary mismatch",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_INCOME_MISMATCH.String(),
				},
				{
					RejectionReason:   "Current address mismatch",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_CURRENT_ADDRESS_MISMATCH.String(),
				},
			},
		},
		{
			Title: "Technical issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Video not available",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_VIDEO_NOT_AVAILABLE.String(),
				},
				{
					RejectionReason:   "Customer network issues",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_NETWORK_ISSUES.String(),
				},
				{
					RejectionReason:   "Video not clear/not present",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_VIDEO_NOT_CLEAR_OR_NOT_PRESENT.String(),
				},
				{
					RejectionReason:   "Audio not clear/not present",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_AUDIO_NOT_CLEAR_OR_NOT_PRESENT.String(),
				},
				{
					RejectionReason:   "User device camera faulty",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_USER_DEVICE_CAMERA_FAULTY.String(),
				},
				{
					RejectionReason:   "User side background noise",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_USER_SIDE_BACKGROUND_NOISE.String(),
				},
				{
					RejectionReason:   "Incompatible browser",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_INCOMPATIBLE_BROWSER.String(),
				},
				{
					RejectionReason:   "IP risk detected",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_IP_RISK_DETECTED.String(),
				},
				{
					RejectionReason:   "Location access issue",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_LOCATION_ACCESS_ISSUE.String(),
				},
				{
					RejectionReason:   "User face not visible",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_USER_FACE_NOT_VISIBLE.String(),
				},
				{
					RejectionReason:   "User internet weak",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_USER_INTERNET_WEAK.String(),
				},
				{
					RejectionReason:   "User not audible",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_USER_NOT_AUDIBLE.String(),
				},
				{
					RejectionReason:   "Audio related issue",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_AUDIO_RELATED_ISSUE.String(),
				},
				{
					RejectionReason:   "Black screen issue",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_BLACK_SCREEN_ISSUE.String(),
				},
				{
					RejectionReason:   "User unable to hear agent",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_USER_UNABLE_TO_HEAR_AGENT.String(),
				},
				{
					RejectionReason:   "Screen recording issue",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_SCREEN_RECORDING_ISSUE.String(),
				},
				{
					RejectionReason:   "Unable to fetch live location",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_UNABLE_TO_FETCH_LIVE_LOCATION.String(),
				},
				{
					RejectionReason:   "VPN/Proxy detected",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_VPN_PROXY_DETECTED.String(),
				},
			},
		},
		{
			Title: "Customer-specific issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Third party prompting in the call",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_THIRD_PARTY_PROMPTING_IN_THE_CALL.String(),
				},
			},
		},
		{
			Title: "Reason not listed",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Other",
					RejectionReasonId: enums.AuditorRejectionReason_AUDITOR_REJECTION_REASON_OTHERS.String(),
				},
			},
		},
	}
}

//nolint:dupl, funlen
func getAgentRejectionReason() []*types.RejectionCategory {
	return []*types.RejectionCategory{
		{
			Title: "Document issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "User does not have PAN",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PAN_NOT_AVAILABLE.String(),
				},
				{
					RejectionReason:   "User does not have Passport",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PASSPORT_NOT_AVAILABLE.String(),
				},
				{
					RejectionReason:   "PAN is not fully captured",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PAN_IS_NOT_FULLY_CAPTURED.String(),
				},
				{
					RejectionReason:   "PAN photo not clear",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PAN_PHOTO_NOT_CLEAR.String(),
				},
				{
					RejectionReason:   "PAN OCR issue",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PAN_OCR_ISSUE.String(),
				},
				{
					RejectionReason:   "Passport is not fully captured",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PASSPORT_IS_NOT_FULLY_CAPTURED.String(),
				},
				{
					RejectionReason:   "Passport photo not clear",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PASSPORT_PHOTO_NOT_CLEAR.String(),
				},
				{
					RejectionReason:   "Passport OCR issue",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PASSPORT_OCR_ISSUE.String(),
				},
				{
					RejectionReason:   "Duplicate invalid/tampered PAN",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_DUPLICATE_INVALID_TAMPERED_PAN.String(),
				},
				{
					RejectionReason:   "PAN photo face match low score",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PAN_PHOTO_FACE_MATCH_LOW_SCORE.String(),
				},
				{
					RejectionReason:   "Minor PAN",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_MINOR_PAN.String(),
				},
				{
					RejectionReason:   "Aadhaar validity expired",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_AADHAAR_VALIDITY_EXPIRED.String(),
				},
				{
					RejectionReason:   "Aadhaar photo face match low score",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_AADHAAR_PHOTO_FACE_MATCH_LOW_SCORE.String(),
				},
				{
					RejectionReason:   "PAN signature capturing failed",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PAN_SIGNATURE_CAPTURING_FAILED.String(),
				},
				{
					RejectionReason:   "PAN status not verified",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PAN_STATUS_NOT_VERIFIED.String(),
				},
				{
					RejectionReason:   "Unable to fetch CKYC document",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_UNABLE_TO_FETCH_CKYC_DOCUMENT.String(),
				},
				{
					RejectionReason:   "Wrong CKYC document fetched",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_WRONG_CKYC_DOCUMENT_FETCHED.String(),
				},
			},
		},
		{
			Title: "Customer detail issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Location issue",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_LOCATION_ISSUE.String(),
				},
				{
					RejectionReason:   "Parent name mismatch",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PARENTS_NAME_MISMATCH.String(),
				},
				{
					RejectionReason:   "Customer name mismatch",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_CUSTOMER_NAME_MISMATCH.String(),
				},
				{
					RejectionReason:   "DOB mismatch",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_DOB_MISMATCH.String(),
				},
				{
					RejectionReason:   "Gender mismatch",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_GENDER_MISMATCH.String(),
				},
				{
					RejectionReason:   "Salary mismatch",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_INCOME_MISMATCH.String(),
				},
				{
					RejectionReason:   "Occupation mismatch",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_OCCUPATION_MISMATCH.String(),
				},
				{
					RejectionReason:   "Pan number mismatch",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_PAN_NUMBER_MISMATCH.String(),
				},
				{
					RejectionReason:   "User current address mismatch",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_CURRENT_ADDRESS_MISMATCH.String(),
				},
			},
		},
		{
			Title: "Technical issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Video not available",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_VIDEO_NOT_AVAILABLE.String(),
				},
				{
					RejectionReason:   "Customer network issues",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_NETWORK_ISSUES.String(),
				},
				{
					RejectionReason:   "Video not clear/not present",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_VIDEO_NOT_CLEAR_OR_NOT_PRESENT.String(),
				},
				{
					RejectionReason:   "Audio not clear/not present",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_AUDIO_NOT_CLEAR_OR_NOT_PRESENT.String(),
				},
			},
		},
		{
			Title: "Agent-specific issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Third party prompting in the call",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_THIRD_PARTY_PROMPTING_IN_THE_CALL.String(),
				},
				{
					RejectionReason:   "Language barrier",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_LANGUAGE_BARRIER.String(),
				},
				{
					RejectionReason:   "Agent camera mic access issue",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_AGENT_CAMERA_MIC_ACCESS_ISSUE.String(),
				},
			},
		},
		{
			Title: "User-specific issues",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Third party prompting in the call",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_THIRD_PARTY_PROMPTING_IN_THE_CALL.String(),
				},
				{
					RejectionReason:   "User reading script",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_USER_READING_SCRIPT.String(),
				},
				{
					RejectionReason:   "User is unaware about scheme",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_USER_IS_UNAWARE_ABOUT_SCHEME.String(),
				},
				{
					RejectionReason:   "User will recall",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_USER_WILL_RECALL.String(),
				},
				{
					RejectionReason:   "User disconnected",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_USER_DISCONNECTED.String(),
				},
				{
					RejectionReason:   "User without PAN",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_USER_WITHOUT_PAN.String(),
				},
				{
					RejectionReason:   "User inactive",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_USER_INACTIVE.String(),
				},
				{
					RejectionReason:   "User unable to follow instructions",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_USER_UNABLE_TO_FOLLOW_INSTRUCTIONS.String(),
				},
			},
		},
		{
			Title: "Reason not listed",
			RejectionReasons: []*types.RejectionReason{
				{
					RejectionReason:   "Other",
					RejectionReasonId: enums.FailureReason_FAILURE_REASON_OTHER.String(),
				},
			},
		},
	}
}
