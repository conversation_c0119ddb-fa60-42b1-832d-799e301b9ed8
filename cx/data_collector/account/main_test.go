package account

import (
	"flag"
	"os"
	"testing"

	mockActor "github.com/epifi/gamma/api/actor/mocks"
	mockTicket "github.com/epifi/gamma/api/cx/ticket/mocks"
	mockWatson "github.com/epifi/gamma/api/cx/watson/mocks"
	mockExtAcct "github.com/epifi/gamma/api/savings/extacct/mocks"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/test"

	"github.com/golang/mock/gomock"
)

var caTs *ClosedAccountsTestSuite

type mockedDependencies struct {
	actorClient   *mockActor.MockActorClient
	savingsClient *mockSavings.MockSavingsClient
	watsonClient  *mockWatson.MockWatsonClient
	extAcctClient *mockExtAcct.MockExternalAccountsClient
	ticketClient  *mockTicket.MockTicketClient
}

type ClosedAccountsTestSuite struct {
	conf *config.Config
}

func newServerWithMocks(t *testing.T) (*Service, *mockedDependencies) {
	ctr := gomock.NewController(t)
	actorClient := mockActor.NewMockActorClient(ctr)
	savingsClient := mockSavings.NewMockSavingsClient(ctr)
	watsonClient := mockWatson.NewMockWatsonClient(ctr)
	extAcctClient := mockExtAcct.NewMockExternalAccountsClient(ctr)
	ticketClient := mockTicket.NewMockTicketClient(ctr)

	md := &mockedDependencies{
		actorClient:   actorClient,
		savingsClient: savingsClient,
		watsonClient:  watsonClient,
		extAcctClient: extAcctClient,
		ticketClient:  ticketClient,
	}

	svc := &Service{
		actorClient:   actorClient,
		savingsClient: savingsClient,
		cxConf:        caTs.conf,
		watsonClient:  watsonClient,
		extAcctClient: extAcctClient,
		ticketClient:  ticketClient,
	}

	return svc, md
}

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, _, teardown := test.InitTestServer(false)

	caTs = &ClosedAccountsTestSuite{
		conf: conf,
	}

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
