package account

import (
	"bytes"
	"context"
	"strings"
	"time"

	"github.com/gocarina/gocsv"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	cxAPb "github.com/epifi/gamma/api/cx/data_collector/account"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	depositPb "github.com/epifi/gamma/api/deposit"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	"github.com/epifi/gamma/api/frontend/header"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	userPb "github.com/epifi/gamma/api/user"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/data_collector/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/pkg/pay"
)

type Service struct {
	dc                  depositPb.DepositClient
	authEngine          auth_engine.IAuthEngine
	paymentClient       paymentPb.PaymentClient
	piClient            paymentinstrument.PiClient
	dataCollectorHelper helper.IDataCollectorHelper
	extAcctClient       extacct.ExternalAccountsClient
	actorClient         actor.ActorClient
	usersClient         userPb.UsersClient
	savingsClient       savings.SavingsClient
	cxConf              *config.Config
	commsClient         commsPb.CommsClient
	authClient          authPb.AuthClient
	vgDepositClient     vgDepositPb.DepositClient
	bcCLient            bankCustomerPb.BankCustomerServiceClient
	watsonClient        watsonPb.WatsonClient
	ticketClient        ticketPb.TicketClient
	saClosureClient     saClosurePb.SavingsAccountClosureClient
}

func NewService(dc depositPb.DepositClient, authEngine auth_engine.IAuthEngine, paymentClient paymentPb.PaymentClient,
	piClient paymentinstrument.PiClient, dataCollectorHelper helper.IDataCollectorHelper, extAcctClient extacct.ExternalAccountsClient,
	actorClient actor.ActorClient, usersClient userPb.UsersClient, savingsClient savings.SavingsClient, cxConf *config.Config,
	commsClient commsPb.CommsClient, authClient authPb.AuthClient, vgDepositClient vgDepositPb.DepositClient,
	bcClient bankCustomerPb.BankCustomerServiceClient, watsonClient watsonPb.WatsonClient, ticketClient ticketPb.TicketClient, saClosureClient saClosurePb.SavingsAccountClosureClient) *Service {
	return &Service{
		dc:                  dc,
		authEngine:          authEngine,
		paymentClient:       paymentClient,
		piClient:            piClient,
		dataCollectorHelper: dataCollectorHelper,
		extAcctClient:       extAcctClient,
		actorClient:         actorClient,
		usersClient:         usersClient,
		savingsClient:       savingsClient,
		cxConf:              cxConf,
		commsClient:         commsClient,
		authClient:          authClient,
		vgDepositClient:     vgDepositClient,
		bcCLient:            bcClient,
		watsonClient:        watsonClient,
		ticketClient:        ticketClient,
		saClosureClient:     saClosureClient,
	}
}

var _ cxAPb.AccountServer = &Service{}

func (s *Service) GetDepositsForCustomer(ctx context.Context, req *cxAPb.GetDepositsForCustomerRequest) (*cxAPb.GetDepositsForCustomerResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxAPb.GetDepositsForCustomerResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Info(ctx, "actor information not present in header")
		return &cxAPb.GetDepositsForCustomerResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found"),
		}, nil
	}
	validationResp := validateRequest(req)
	if validationResp != nil {
		cxLogger.Info(ctx, "request is not valid to fetch accounts")
		return validationResp, nil
	}
	resp, err := s.dc.ListDepositAccounts(ctx, &depositPb.ListDepositAccountsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		States: []depositPb.DepositState{depositPb.DepositState_PRECLOSED, depositPb.DepositState_IN_PROGRESS, depositPb.DepositState_CLOSED,
			depositPb.DepositState_FAILED, depositPb.DepositState_CREATED, depositPb.DepositState_PRECLOSE_PENDING},
		Types:       []accountsPb.Type{req.GetType()},
		Provenances: []depositPb.DepositAccountProvenance{depositPb.DepositAccountProvenance_USER_APP, depositPb.DepositAccountProvenance_REWARDS_APP, depositPb.DepositAccountProvenance_CREDIT_CARD},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Info(ctx, "error calling deposit service", zap.Error(err))
		return &cxAPb.GetDepositsForCustomerResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("cannot fetch accounts for customer"),
		}, nil
	}
	depositList := s.getFilteredDeposits(ctx, resp.GetAccounts(), req)
	return &cxAPb.GetDepositsForCustomerResponse{
		Status:             rpcPb.StatusOk(),
		DepositAccountList: depositList,
	}, nil
}

func validateRequest(req *cxAPb.GetDepositsForCustomerRequest) *cxAPb.GetDepositsForCustomerResponse {
	if req.GetHeader().GetActor() == nil {
		return &cxAPb.GetDepositsForCustomerResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor not present in header")}
	}
	if req.GetType() == accountsPb.Type_TYPE_UNSPECIFIED {
		return &cxAPb.GetDepositsForCustomerResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("deposit type is mandatory")}
	}
	if req.GetLastXCharDepositNumber() == "" && req.GetFromCreationDate() == nil && req.GetFromMaturityDate() == nil &&
		req.GetToCreationDate() == nil && req.GetToMaturityDate() == nil {
		return &cxAPb.GetDepositsForCustomerResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ome of the filter apart from deposit type is mandatory"),
		}
	}
	if req.GetLastXCharDepositNumber() != "" && len(req.GetLastXCharDepositNumber()) < 4 {
		return &cxAPb.GetDepositsForCustomerResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("len of deposit number cannot be less than 4"),
		}
	}
	return nil
}

func (s *Service) getFilteredDeposits(ctx context.Context, accountList []*depositPb.DepositAccount, req *cxAPb.GetDepositsForCustomerRequest) []*cxAPb.DepositAccount {
	var depositList []*cxAPb.DepositAccount
	for _, deposit := range accountList {
		// Apply filters passed in request on each deposit
		if req.GetLastXCharDepositNumber() != "" {
			if !strings.HasSuffix(deposit.AccountNumber, req.GetLastXCharDepositNumber()) {
				continue
			}
		}
		if req.GetFromMaturityDate() != nil {
			if deposit.GetMaturityDate().GetSeconds() < (req.GetFromMaturityDate().GetSeconds()) {
				continue
			}
		}
		if req.GetToMaturityDate() != nil {
			if req.GetToMaturityDate().GetSeconds() < deposit.GetMaturityDate().GetSeconds() {
				continue
			}
		}
		if req.GetToCreationDate() != nil {
			if req.GetToCreationDate().GetSeconds() < deposit.GetCreatedAt().GetSeconds() {
				continue
			}
		}
		if req.GetFromCreationDate() != nil {
			if deposit.GetCreatedAt().GetSeconds() < req.GetFromCreationDate().GetSeconds() {
				continue
			}
		}
		depositList = append(depositList, s.convertToDepositProto(ctx, deposit))
	}
	return depositList
}

func (s *Service) convertToDepositProto(ctx context.Context, account *depositPb.DepositAccount) *cxAPb.DepositAccount {
	cxDeposit := &cxAPb.DepositAccount{
		DepositId:     account.GetId(),
		AccountNumber: maskDepositNumber(account.GetAccountNumber()),
		Type:          account.GetType(),
		State:         account.GetState(),
		MaturityDate:  account.GetMaturityDate(),
		InterestRate:  account.GetInterestRate(),
		CreationDate:  account.GetCreatedAt(),
	}
	// fetch deposit request details
	resp, err := s.dc.GetDepositRequestsForActor(ctx, &depositPb.GetDepositRequestsForActorRequest{
		ActorId:          account.GetActorId(),
		DepositAccountId: account.GetId(),
		PageSize:         30,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching deposit requests", zap.Error(err))
		// mask error and return the deposit details without request details
		return cxDeposit
	}
	if len(resp.GetDepositRequests()) < 1 {
		cxLogger.Error(ctx, "no deposit request found for desposit id",
			zap.String(logger.DEPOSIT_ACCOUNT_ID, account.GetId()),
		)
		// mask error and return the deposit details without request details
		return cxDeposit
	}
	depositReq := resp.GetDepositRequests()[0]
	cxDeposit.AccountDetails = convertToDepositDetailsProto(depositReq)
	cxDeposit.AttachEntityMeta = &ticketPb.AttachEntityMeta{
		Meta: &ticketPb.AttachEntityMeta_DepositAccountMeta{
			DepositAccountMeta: &ticketPb.DepositAccountMeta{
				DepositId: cxDeposit.GetDepositId(),
			},
		},
	}

	// use deposit name from ListDepositAccounts RPC response as name field in GetDepositRequestsForActor response is buggy for pre-closed deposits
	if cxDeposit.GetAccountDetails().GetDepositInfo() != nil {
		cxDeposit.AccountDetails.DepositInfo.Name = account.GetName()
	}
	return cxDeposit
}

func maskDepositNumber(number string) string {
	rs := []rune(number)
	for i := 0; i < len(rs)-3; i++ {
		rs[i] = 'X'
	}
	maskedNumber := string(rs)
	return maskedNumber
}

func (s *Service) GetDepositDetails(ctx context.Context, req *cxAPb.GetDepositsDetailsRequest) (*cxAPb.GetDepositsDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxAPb.GetDepositsDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Info(ctx, "actor information not present in header")
		return &cxAPb.GetDepositsDetailsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found"),
		}, nil
	}
	resp, err := s.dc.GetDepositRequestsForActor(ctx, &depositPb.GetDepositRequestsForActorRequest{
		ActorId:          actorId,
		DepositAccountId: req.GetDepositId(),
		PageSize:         30,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching deposit requests", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &cxAPb.GetDepositsDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching deposit requests"),
		}, nil
	}
	if len(resp.GetDepositRequests()) < 1 {
		cxLogger.Info(ctx, "no deposit request found for desposit id",
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
		return &cxAPb.GetDepositsDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("no deposit request found for desposit id"),
		}, nil
	}
	depositReq := resp.GetDepositRequests()[0]

	return buildDepositDetailsResp(depositReq), nil
}

func buildDepositDetailsResp(dr *depositPb.DepositRequest) *cxAPb.GetDepositsDetailsResponse {
	return &cxAPb.GetDepositsDetailsResponse{
		Status:                rpcPb.StatusOk(),
		DepositAccountDetails: convertToDepositDetailsProto(dr),
	}
}

func convertToDepositDetailsProto(dr *depositPb.DepositRequest) *cxAPb.DepositAccountDetails {
	return &cxAPb.DepositAccountDetails{
		Id:               dr.GetId(),
		RequestId:        dr.GetRequestId(),
		ClientRequestId:  dr.GetClientRequestId(),
		DepositAccountId: dr.GetDepositAccountId(),
		Type:             dr.GetType().String(),
		State:            dr.GetState().String(),
		DepositInfo:      dr.GetDepositInfo(),
		PartnerBank:      dr.GetPartnerBank(),
		ActorId:          dr.GetActorId(),
		LastAttempt:      dr.GetLastAttempt(),
		DetailedStatus:   dr.GetDetailedStatus(),
		CreatedAt:        dr.GetCreatedAt(),
		UpdatedAt:        dr.GetUpdatedAt(),
		DeletedAt:        dr.GetDeletedAt(),
	}
}

func (s *Service) GetDepositTransactions(ctx context.Context, req *cxAPb.GetDepositTransactionsRequest) (*cxAPb.GetDepositTransactionsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxAPb.GetDepositTransactionsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Info(ctx, "actor information not present in header")
		return &cxAPb.GetDepositTransactionsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found"),
		}, nil
	}
	// get deposit details
	depositResp, err := s.dc.GetById(ctx, &depositPb.GetByIdRequest{
		Id: req.GetDepositId(),
	})
	if te := epifigrpc.RPCError(depositResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching deposits details", zap.Error(te),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
		return &cxAPb.GetDepositTransactionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching deposits details"),
		}, nil
	}
	// get pi for deposit account
	piResp, err := s.piClient.GetPi(ctx, &paymentinstrument.GetPiRequest{
		Type: paymentinstrument.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &paymentinstrument.GetPiRequest_AccountRequestParams_{
			AccountRequestParams: &paymentinstrument.GetPiRequest_AccountRequestParams{
				ActualAccountNumber: depositResp.GetAccount().GetAccountNumber(),
				IfscCode:            depositResp.GetAccount().GetIfscCode(),
			},
		},
	})
	if te := epifigrpc.RPCError(piResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching pi details", zap.Error(te),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
		return &cxAPb.GetDepositTransactionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching pi details"),
		}, nil
	}

	// TODO (sachin): Add pagination support once transaction rpc moves to token based pagination
	// fetch all the transaction for the pi
	txnResp, err := s.paymentClient.GetTxnsByPi(ctx, &paymentPb.GetTxnsByPiRequest{
		PiTo:          piResp.GetPaymentInstrument().GetId(),
		FromTimestamp: depositResp.GetAccount().GetCreatedAt(),
		ToTimestamp:   timestamppb.New(time.Now()),
		SortBy:        paymentPb.TransactionFieldMask_UPDATED_AT,
		SortDesc:      true,
		PageSize:      30,
	})

	if te := epifigrpc.RPCError(piResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching transactions for deposit", zap.Error(te),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
		return &cxAPb.GetDepositTransactionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching transactions for deposit"),
		}, nil
	}

	piToTxnsList := s.buildTxnResponse(ctx, txnResp)

	// fetch closure transaction for the deposit
	clsTxnResp, err := s.paymentClient.GetTxnsByPi(ctx, &paymentPb.GetTxnsByPiRequest{
		PiFrom:        piResp.GetPaymentInstrument().GetId(),
		FromTimestamp: depositResp.GetAccount().GetCreatedAt(),
		ToTimestamp:   timestamppb.Now(),
		SortBy:        paymentPb.TransactionFieldMask_UPDATED_AT,
		SortDesc:      true,
		PageSize:      10,
	})

	// just log the error for this RPC call and not return status error
	// this is because not every deposit will have a closure transaction
	if te := epifigrpc.RPCError(clsTxnResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching closure transactions for deposit", zap.Error(te),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
	}

	filteredTransactions, err := s.getDepositTransactionsToSavingsAccount(ctx, req, depositResp)
	if err != nil {
		cxLogger.Error(ctx, "error in getDepositTransactionsToSavingsAccount", zap.Error(err),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
	}

	if len(filteredTransactions) > 0 {
		clsTxnResp.Transactions = append(clsTxnResp.Transactions, filteredTransactions...)
	}

	piFromTxnsList := s.buildTxnResponse(ctx, clsTxnResp)

	var allTxnList []*cxAPb.TransactionDetails
	allTxnList = append(allTxnList, piToTxnsList...)
	allTxnList = append(allTxnList, piFromTxnsList...)

	if len(allTxnList) == 0 {
		return &cxAPb.GetDepositTransactionsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("transactions details not found for deposit"),
		}, nil
	}

	return &cxAPb.GetDepositTransactionsResponse{
		Status:       rpcPb.StatusOk(),
		Transactions: allTxnList,
	}, nil

}

func (s *Service) getDepositTransactionsToSavingsAccount(ctx context.Context, req *cxAPb.GetDepositTransactionsRequest,
	depositResponse *depositPb.GetByIdResponse) ([]*paymentPb.Transaction, error) {
	var filteredTxnsForDeposits []*paymentPb.Transaction

	userResp, err := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: depositResponse.GetAccount().GetActorId()}})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		cxLogger.Error(ctx, "error while GetUser", zap.Error(te),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
		return filteredTxnsForDeposits, te
	}

	savingsResp, err := s.savingsClient.GetAccount(ctx, &savings.GetAccountRequest{Identifier: &savings.GetAccountRequest_PrimaryUserId{
		PrimaryUserId: userResp.GetUser().GetId(),
	}})
	if err != nil {
		cxLogger.Error(ctx, "error while fetching GetAccount", zap.Error(err),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
		return filteredTxnsForDeposits, err
	}

	// get pi for deposit account
	savingsPIResp, err := s.piClient.GetPi(ctx, &paymentinstrument.GetPiRequest{
		Type: paymentinstrument.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &paymentinstrument.GetPiRequest_AccountRequestParams_{
			AccountRequestParams: &paymentinstrument.GetPiRequest_AccountRequestParams{
				ActualAccountNumber: savingsResp.GetAccount().GetAccountNo(),
				IfscCode:            savingsResp.GetAccount().GetIfscCode(),
			},
		},
	})
	if te := epifigrpc.RPCError(savingsPIResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching GetPi for savings", zap.Error(te),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
		return filteredTxnsForDeposits, te
	}

	// Fetch the transactions that were triggered from federal's account to users savings account.
	// This is because for interest credits to savings account for a deposit, a credit is from generic federal PI.
	savingsTxnResp, err := s.paymentClient.GetTxnsByPi(ctx, &paymentPb.GetTxnsByPiRequest{
		PiFrom:        pay.GenericFederalPiId,
		PiTo:          savingsPIResp.GetPaymentInstrument().GetId(),
		FromTimestamp: depositResponse.GetAccount().GetCreatedAt(),
		ToTimestamp:   timestamppb.New(time.Now()),
		SortBy:        paymentPb.TransactionFieldMask_UPDATED_AT,
		SortDesc:      true,
		PageSize:      100,
	})

	if te := epifigrpc.RPCError(savingsTxnResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching GetTxnsByPi for savings", zap.Error(te),
			zap.String(logger.DEPOSIT_ACCOUNT_ID, req.GetDepositId()),
		)
		return filteredTxnsForDeposits, te
	}

	// Out of all transactions where from_PI = federal and to_PI=savings, filter the ones for deposits if the particular
	// has the deposit transaction_id present.
	for _, val := range savingsTxnResp.Transactions {
		for _, rawNotificationDetails := range val.RawNotificationDetails {
			if strings.Contains(rawNotificationDetails.Particulars, depositResponse.GetAccount().GetAccountNumber()) {
				filteredTxnsForDeposits = append(filteredTxnsForDeposits, val)
			}
		}
	}
	return filteredTxnsForDeposits, nil
}
func (s *Service) buildTxnResponse(ctx context.Context, resp *paymentPb.GetTxnsByPiResponse) []*cxAPb.TransactionDetails {
	var txnDetailsList []*cxAPb.TransactionDetails
	for _, txn := range resp.GetTransactions() {
		txnDetailsProto := &cxAPb.TransactionDetails{
			Id:              txn.GetId(),
			PiFrom:          txn.GetPiFrom(),
			PiTo:            txn.GetPiTo(),
			Utr:             txn.GetUtr(),
			PartnerBank:     txn.GetPartnerBank(),
			Status:          txn.GetStatus().String(),
			PaymentProtocol: txn.GetPaymentProtocol().String(),
			CreatedAt:       txn.GetCreatedAt(),
			UpdatedAt:       txn.GetUpdatedAt(),
		}
		// Add external id and pi details
		orderResp, err := s.dataCollectorHelper.FetchOrderForTxnId(ctx, txn.GetId())
		if err != nil {
			txnDetailsList = append(txnDetailsList, txnDetailsProto)
			continue
		}
		fromPIDetails, err := s.dataCollectorHelper.GetPiDetails(ctx, txn.PiFrom, orderResp.GetOrder().GetFromActorId(), true)
		if err != nil {
			txnDetailsList = append(txnDetailsList, txnDetailsProto)
			continue
		}
		toPIDetails, err := s.dataCollectorHelper.GetPiDetails(ctx, txn.PiTo, orderResp.GetOrder().GetToActorId(), true)
		if err != nil {
			txnDetailsList = append(txnDetailsList, txnDetailsProto)
			continue
		}
		txnDetailsProto.FromPiDetails = fromPIDetails
		txnDetailsProto.ToPiDetails = toPIDetails
		txnDetailsProto.OrderExternalId = s.dataCollectorHelper.MaskStringExceptLast4(orderResp.GetOrder().GetExternalId())
		txnDetailsList = append(txnDetailsList, txnDetailsProto)
	}
	return txnDetailsList
}

func (s *Service) GetDepositRequestsForCustomer(ctx context.Context, req *cxAPb.GetDepositRequestsForCustomerRequest) (*cxAPb.GetDepositRequestsForCustomerResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxAPb.GetDepositRequestsForCustomerResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Info(ctx, "actor information not present in header")
		return &cxAPb.GetDepositRequestsForCustomerResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("customer not found"),
		}, nil
	}
	// TODO (sachin): Add pagination support once deposit rpc moves to token based pagination
	resp, err := s.dc.GetDepositRequestsForActor(ctx, &depositPb.GetDepositRequestsForActorRequest{
		ActorId:     actorId,
		RequestType: depositPb.RequestType_CREATE,
		FromTime:    req.GetFromTime(),
		ToTime:      req.GetToTime(),
		PageSize:    30,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching deposit requests", zap.Error(err))
		return &cxAPb.GetDepositRequestsForCustomerResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching deposit requests"),
		}, nil
	}
	return buildDepositRequestsForCustomerResponse(resp.GetDepositRequests()), nil
}

func buildDepositRequestsForCustomerResponse(depositRequests []*depositPb.DepositRequest) *cxAPb.GetDepositRequestsForCustomerResponse {
	var depositDetailsList []*cxAPb.DepositAccountDetails
	for _, req := range depositRequests {
		// return the result only if last attempt is true(to see only final attempt) and account id is empty(deposit is not created yet)
		if req.GetLastAttempt() == true && req.GetDepositAccountId() == "" {
			depositDetailsList = append(depositDetailsList, convertToDepositDetailsProto(req))
		}
	}
	return &cxAPb.GetDepositRequestsForCustomerResponse{
		Status:                    rpcPb.StatusOk(),
		DepositAccountDetailsList: depositDetailsList,
	}
}

// IsSavingsAccountClosureAllowed RPC to be used to check if a closure of savings account is allowed or not.
// nolint: funlen
func (s *Service) IsSavingsAccountClosureAllowed(ctx context.Context, req *cxAPb.IsSavingsAccountClosureAllowedRequest) (*cxAPb.IsSavingsAccountClosureAllowedResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxAPb.IsSavingsAccountClosureAllowedResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	if req.GetHeader().GetUser() == nil {
		cxLogger.Error(ctx, "user information not present in header of request")
		return &cxAPb.IsSavingsAccountClosureAllowedResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("user not present in header")}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Error(ctx, "actor information not present in header of request")
		return &cxAPb.IsSavingsAccountClosureAllowedResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor not present in header")}, nil
	}

	saClosureResp, saClosureRespErr := s.saClosureClient.EvaluateUserForClosureEligibility(ctx, &saClosurePb.EvaluateUserForClosureEligibilityRequest{
		Req:     &header.RequestHeader{},
		ActorId: req.GetHeader().GetActor().GetId(),
	})

	if saClosureRespErr != nil {
		cxLogger.Error(ctx, "error while getting sa closure response")
		return &cxAPb.IsSavingsAccountClosureAllowedResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while getting sa closure response")}, nil
	}

	isClosureAllowed := true
	if saClosureResp.GetFailedCriteriaCheck() || saClosureResp.GetHasPendingCharges() || saClosureResp.GetHasLien() || saClosureResp.GetHasFreeze() {
		isClosureAllowed = false
	}

	return &cxAPb.IsSavingsAccountClosureAllowedResponse{
		Status:           rpcPb.StatusOk(),
		IsClosureAllowed: isClosureAllowed,
		FailureReasons:   saClosureResp.GetFailureReasons(),
	}, nil
}

// GetAccountFreezeInfo rpc to be used for fetching info related to Account Freeze status, Account Freeze reason and Lein Amount.
func (s *Service) GetAccountFreezeInfo(ctx context.Context, req *cxAPb.GetAccountFreezeInfoRequest) (*cxAPb.GetAccountFreezeInfoResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxAPb.GetAccountFreezeInfoResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	accountFreezeInfo, err := s.dataCollectorHelper.GetAccountFreezeAndBalanceInfo(ctx, req.GetHeader().GetUser().GetId(), req.GetHeader().GetActor().GetId())
	if err != nil {
		cxLogger.Error(ctx, "error while fetching account freeze details", zap.Error(err))
		return &cxAPb.GetAccountFreezeInfoResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching account freeze details")}, nil
	}
	// removing balance info from response since it's sensitive data
	// TODO(Diparth): remove the balance field from response proto
	accountFreezeInfo.AvailableBalance = nil

	return &cxAPb.GetAccountFreezeInfoResponse{
		Status:            rpcPb.StatusOk(),
		AccountFreezeInfo: accountFreezeInfo,
	}, nil
}

// BulkAccountValidations rpc validates given list of bank account details shared by users
func (s *Service) BulkAccountValidations(ctx context.Context, req *cxAPb.BulkAccountValidationsRequest) (*cxAPb.BulkAccountValidationsResponse, error) {
	if len(req.GetValidateAccountsCsv()) == 0 || req.GetHeader().GetAgentEmail() == "" {
		cxLogger.Error(ctx, "mandatory params are missing in request")
		return &cxAPb.BulkAccountValidationsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("checker email and csv file can't be empty"),
		}, nil
	}
	var requestVals []*Validation
	// extracts data from csv file and dumps it to requestVals array
	if loadErr := gocsv.Unmarshal(bytes.NewReader(req.GetValidateAccountsCsv()), &requestVals); loadErr != nil {
		cxLogger.Error(ctx, "error while parsing the csv file", zap.Error(loadErr))
		return &cxAPb.BulkAccountValidationsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("csv file is invalid"),
		}, nil
	}
	// performs account validations for all the entries in the input csv file
	accVeriResults, federalCloseAccDetails := s.validateAccounts(ctx, requestVals, req.GetHeader().GetAgentEmail())
	// convert results to csv files to share with the agents
	allVerificationsFile, allVerificationsBytes, err := createAndConvertToBytesCsvFile(ctx, accVeriResults, "verification_results")
	if err != nil {
		cxLogger.Error(ctx, "error creating csv for account validation results", zap.Error(err))
		return &cxAPb.BulkAccountValidationsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error creating/reading csv file"),
		}, nil
	}
	federalFile, federalBytes, err := createAndConvertToBytesCsvFile(ctx, federalCloseAccDetails, "successful_validations")
	if err != nil {
		cxLogger.Error(ctx, "error creating csv for successful account validation", zap.Error(err))
		return &cxAPb.BulkAccountValidationsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error creating/reading csv file"),
		}, nil
	}
	// send email
	if err = s.populateAndSendEmail(ctx, allVerificationsFile, federalFile, allVerificationsBytes, federalBytes,
		int64(len(accVeriResults)), int64(len(federalCloseAccDetails))); err != nil {
		return &cxAPb.BulkAccountValidationsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while emailing bulk user details CSV"),
		}, nil
	}
	return &cxAPb.BulkAccountValidationsResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) StoreClosedAccountBalance(ctx context.Context, req *cxAPb.StoreClosedAccountBalanceRequest) (*cxAPb.StoreClosedAccountBalanceResponse, error) {
	if len(req.GetBalancesCsv()) == 0 || req.GetHeader().GetAgentEmail() == "" {
		cxLogger.Error(ctx, "mandatory params are missing in request")
		return &cxAPb.StoreClosedAccountBalanceResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("checker email and csv file can't be empty"),
		}, nil
	}

	var rows []*ClosedAccountBalances
	if loadErr := gocsv.Unmarshal(bytes.NewReader(req.GetBalancesCsv()), &rows); loadErr != nil {
		cxLogger.Error(ctx, "error while parsing the csv file", zap.Error(loadErr))
		return &cxAPb.StoreClosedAccountBalanceResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("csv file is invalid"),
		}, nil
	}

	failures := s.storeClosureBalances(ctx, rows)

	if len(failures) == 0 {
		return &cxAPb.StoreClosedAccountBalanceResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}

	// sending internal rather than OK since web has a default handling which depends on
	// rpc status being not OK to show proper UI for failures
	return &cxAPb.StoreClosedAccountBalanceResponse{
		Status:          rpcPb.StatusInternal(),
		FailureMessages: failures,
	}, nil
}

func (s *Service) StoreClosedAccBalTransferUtr(ctx context.Context, req *cxAPb.StoreClosedAccBalTransferUtrRequest) (*cxAPb.StoreClosedAccBalTransferUtrResponse, error) {
	if len(req.GetUtrCsv()) == 0 || req.GetHeader().GetAgentEmail() == "" {
		cxLogger.Error(ctx, "mandatory params are missing in request")
		return &cxAPb.StoreClosedAccBalTransferUtrResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("checker email and csv file can't be empty"),
		}, nil
	}

	var rows []*ClosedAccountUTRs
	if loadErr := gocsv.Unmarshal(bytes.NewReader(req.GetUtrCsv()), &rows); loadErr != nil {
		cxLogger.Error(ctx, "error while parsing the csv file", zap.Error(loadErr))
		return &cxAPb.StoreClosedAccBalTransferUtrResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("csv file is invalid"),
		}, nil
	}

	failures := s.storeBalTransferUtrsAndResolveIncident(ctx, rows, req.GetHeader().GetAgentEmail())

	if len(failures) == 0 {
		return &cxAPb.StoreClosedAccBalTransferUtrResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}

	// sending internal rather than OK since web has a default handling which depends on
	// rpc status being not OK to show proper UI for failures
	return &cxAPb.StoreClosedAccBalTransferUtrResponse{
		Status:          rpcPb.StatusInternal(),
		FailureMessages: failures,
	}, nil
}

func (s *Service) GetSavingsAccountDetails(_ context.Context, _ *cxAPb.GetSavingsAccountDetailsRequest) (*cxAPb.GetSavingsAccountDetailsResponse, error) {
	// TODO (RISHU SAHU) Implement me
	return &cxAPb.GetSavingsAccountDetailsResponse{
		Status: rpcPb.StatusUnimplemented(),
	}, nil
}
