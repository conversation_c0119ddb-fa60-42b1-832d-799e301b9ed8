package account

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	actorPb "github.com/epifi/gamma/api/actor"
	ticketpb "github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	types "github.com/epifi/gamma/api/typesv2"

	"github.com/golang/mock/gomock"
	gmoney "google.golang.org/genproto/googleapis/type/money"
)

const (
	bavId                                    = "bav-id"
	minKycExpiryBalanceRefundIssueCategoryId = "46c4f9a2-4c2c-5e03-8ffc-2b818a6c1cb5"
)

func TestService_storeBalTransferUtr(t *testing.T) {
	savId := "sav-id"
	utrObj := &ClosedAccountUTRs{
		FiAccountNo:  "fi-acc",
		FiIfsc:       "fi-ifsc",
		AltAccountNo: "alt-acc",
		AltIfsc:      "alt-ifsc",
		Utr:          "utr",
	}
	cbtFailed := &savingsPb.ClosedAccountBalanceTransfer{
		Id:               "cbt-failed",
		SavingsAccountId: savId,
		ReportedClosureBalance: &gmoney.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		Utr:                      "failed-utr",
		TransactionStatus:        savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_FAILED,
		TransactionFailureReason: "don't like user",
	}
	cbtEmpty := &savingsPb.ClosedAccountBalanceTransfer{
		Id:               "cbt",
		SavingsAccountId: savId,
		ReportedClosureBalance: &gmoney.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		Utr: "",
	}
	type args struct {
		savAccId string
		utrObj   *ClosedAccountUTRs
		bavId    string
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockedDependencies)
		wantErr error
	}{
		{
			name: "successfully stored balance (ignoring failed entry and updating existing)",
			args: args{
				savAccId: savId,
				utrObj:   utrObj,
				bavId:    bavId,
			},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status:  rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{cbtFailed, cbtEmpty},
				}, nil)
				m.savingsClient.EXPECT().UpdateClosedAccountBalTransferData(gomock.Any(),
					&savingsPb.UpdateClosedAccountBalTransferDataRequest{
						Data: &savingsPb.ClosedAccountBalanceTransfer{
							Id:               "cbt",
							SavingsAccountId: savId,
							ReportedClosureBalance: &gmoney.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							BavId:             bavId,
							Utr:               utrObj.Utr,
							TransactionStatus: savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS,
						},
						FieldMasks: []savingsPb.CbtFieldMask{
							savingsPb.CbtFieldMask_CBT_FIELD_MASK_TRANSACTION_DETAILS,
							savingsPb.CbtFieldMask_CBT_FIELD_MASK_TRANSACTION_STATUS,
							savingsPb.CbtFieldMask_CBT_FIELD_MASK_BAV_ID},
					}).Return(&savingsPb.UpdateClosedAccountBalTransferDataResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: nil,
		},
		{
			name: "successfully stored balance (ignoring failed entry and creating new)",
			args: args{
				savAccId: savId,
				utrObj:   utrObj,
				bavId:    bavId,
			},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status:  rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{cbtFailed},
				}, nil)
				m.savingsClient.EXPECT().StoreClosedAccountBalTransferData(gomock.Any(),
					&savingsPb.StoreClosedAccountBalTransferDataRequest{
						Data: &savingsPb.ClosedAccountBalanceTransfer{
							SavingsAccountId:  savId,
							BavId:             bavId,
							Utr:               utrObj.Utr,
							TransactionStatus: savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS,
						},
					}).Return(&savingsPb.StoreClosedAccountBalTransferDataResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: nil,
		},
		{
			name: "successfully stored balance (no prior entry, creating new)",
			args: args{
				savAccId: savId,
				utrObj:   utrObj,
				bavId:    bavId,
			},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				m.savingsClient.EXPECT().StoreClosedAccountBalTransferData(gomock.Any(),
					&savingsPb.StoreClosedAccountBalTransferDataRequest{
						Data: &savingsPb.ClosedAccountBalanceTransfer{
							SavingsAccountId:  savId,
							BavId:             bavId,
							Utr:               utrObj.Utr,
							TransactionStatus: savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS,
						},
					}).Return(&savingsPb.StoreClosedAccountBalTransferDataResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: nil,
		},
		{
			name: "error while getting cbts",
			args: args{
				savAccId: savId,
				utrObj:   utrObj,
				bavId:    bavId,
			},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(nil, epifierrors.ErrInvalidSQL)
			},
			wantErr: fmt.Errorf("error while getting closed account balance transfer data: %v", epifierrors.ErrInvalidSQL.Error()),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServerWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			err := svc.storeBalTransferUtr(context.Background(), tt.args.savAccId, tt.args.bavId, tt.args.utrObj)
			if !compareErrors(err, tt.wantErr) {
				t.Errorf("storeBalTransferUtr() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func compareErrors(a, b error) bool {
	if a != nil && b != nil {
		return strings.EqualFold(a.Error(), b.Error())
	}
	return a == b
}

func TestService_storeBalTransferUtrs(t *testing.T) {
	agentEmail := "<EMAIL>"
	savId := "sav-id"
	userId := "user-id"
	actorId := "actor-id"
	utrObj := &ClosedAccountUTRs{
		FiAccountNo:  "fi-acc",
		FiIfsc:       "fi-ifsc",
		AltAccountNo: "alt-acc",
		AltIfsc:      "alt-ifsc",
		Utr:          "utr",
	}
	cbtFailed := &savingsPb.ClosedAccountBalanceTransfer{
		Id:               "cbt-failed",
		SavingsAccountId: savId,
		ReportedClosureBalance: &gmoney.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		Utr:                      "failed-utr",
		TransactionStatus:        savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_FAILED,
		TransactionFailureReason: "don't like user",
	}
	cbtEmpty := &savingsPb.ClosedAccountBalanceTransfer{
		Id:               "cbt",
		SavingsAccountId: savId,
		ReportedClosureBalance: &gmoney.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		Utr: "",
	}
	successfulBav := &extacct.BankAccountVerification{
		Id:            bavId,
		ActorId:       actorId,
		AccountNumber: utrObj.AltAccountNo,
		Ifsc:          utrObj.AltIfsc,
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_SUCCESS,
		Vendor:        extacct.Vendor_KARZA,
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	ticketFilters := &ticketpb.TicketFilters{
		StatusList:          ticketpb.GetActiveStatusList(),
		ProductCategoryList: []ticketpb.ProductCategory{ticketpb.ProductCategory_PRODUCT_CATEGORY_ACCOUNTS},
		ActorIdList:         []string{actorId},
	}
	type args struct {
		utrs       []*ClosedAccountUTRs
		agentEmail string
	}
	tests := []struct {
		name  string
		args  args
		mocks func(*mockedDependencies, *sync.WaitGroup)
		want  []string
	}{
		{
			name: "successful",
			args: args{
				utrs:       []*ClosedAccountUTRs{utrObj},
				agentEmail: agentEmail,
			},
			mocks: func(m *mockedDependencies, wg *sync.WaitGroup) {
				wg.Add(1)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ExternalId{
						ExternalId: &savingsPb.BankAccountIdentifier{
							AccountNo: utrObj.FiAccountNo,
							IfscCode:  utrObj.FiIfsc,
						},
					},
				}).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						Id:                   savId,
						AccountNo:            utrObj.FiAccountNo,
						IfscCode:             utrObj.FiIfsc,
						PrimaryAccountHolder: userId,
					}}, nil)
				m.actorClient.EXPECT().GetActorByEntityId(gomock.Any(), &actorPb.GetActorByEntityIdRequest{
					Type:     types.Actor_USER,
					EntityId: userId,
				}).Return(&actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id: actorId,
					},
				}, nil)
				m.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), &extacct.GetBankAccountsRequest{ActorId: actorId}).
					Return(&extacct.GetBankAccountsResponse{
						Status: rpc.StatusOk(),
						BankAccounts: []*extacct.BankAccount{{
							Ifsc:          successfulBav.GetIfsc(),
							AccountNumber: successfulBav.GetAccountNumber(),
						}},
						BankAccountVerifications: []*extacct.BankAccountVerification{successfulBav},
					}, nil)
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status:  rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{cbtFailed, cbtEmpty},
				}, nil)
				m.savingsClient.EXPECT().UpdateClosedAccountBalTransferData(gomock.Any(),
					&savingsPb.UpdateClosedAccountBalTransferDataRequest{
						Data: &savingsPb.ClosedAccountBalanceTransfer{
							Id:               "cbt",
							SavingsAccountId: savId,
							ReportedClosureBalance: &gmoney.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							BavId:             bavId,
							Utr:               utrObj.Utr,
							TransactionStatus: savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS,
						},
						FieldMasks: []savingsPb.CbtFieldMask{
							savingsPb.CbtFieldMask_CBT_FIELD_MASK_TRANSACTION_DETAILS,
							savingsPb.CbtFieldMask_CBT_FIELD_MASK_TRANSACTION_STATUS,
							savingsPb.CbtFieldMask_CBT_FIELD_MASK_BAV_ID,
						},
					}).Return(&savingsPb.UpdateClosedAccountBalTransferDataResponse{Status: rpc.StatusOk()}, nil)
				m.ticketClient.EXPECT().GetSupportTickets(gomock.Any(), &ticketpb.GetSupportTicketsRequest{
					TicketFilters: ticketFilters,
					PageContextRequest: &rpcPb.PageContextRequest{
						Token: &rpcPb.PageContextRequest_AfterToken{
							AfterToken: "",
						},
						PageSize: 50,
					},
				}).Return(&ticketpb.GetSupportTicketsResponse{
					Status: rpc.StatusOk(),
					Tickets: []*ticketpb.Ticket{{
						Id:   123,
						Tags: []string{""},
						CustomFieldWithValue: &ticketpb.CustomFieldsWithValue{
							SubCategory: "Balance refund",
						},
					}}}, nil)
				m.ticketClient.EXPECT().UpdateTicketAsync(gomock.Any(), &ticketpb.UpdateTicketAsyncRequest{
					Ticket: &ticketpb.Ticket{
						Id:     123,
						Status: ticketpb.Status_STATUS_RESOLVED,
						Tags:   []string{"Auto Resolved"},
						CustomFields: &ticketpb.CustomFields{
							Utr:                   utrObj.Utr,
							SavingsAccountBalance: ticketpb.SavingsAccountBalance_SAVINGS_ACCOUNT_BALANCE_OTHER,
						},
					},
				}).Return(&ticketpb.UpdateTicketAsyncResponse{Status: rpc.StatusOk()}, nil)
				m.watsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), &watsonPb.GetIncidentsForClientRequest{
					IncidentFilter: &watsonPb.IncidentFiltersForClient{
						ActorId:         actorId,
						Client:          types.ServiceName_SAVINGS_SERVICE,
						ResponseLimit:   5,
						ClientRequestId: actorId,
						IssueCategoryId: minKycExpiryBalanceRefundIssueCategoryId,
						IncidentStates: []watsonPb.IncidentState{
							watsonPb.IncidentState_INCIDENT_STATE_LOGGED_IN_DB,
							watsonPb.IncidentState_INCIDENT_STATE_DROPPED,
							watsonPb.IncidentState_INCIDENT_STATE_TICKET_CREATED,
							watsonPb.IncidentState_INCIDENT_STATE_INCIDENT_CREATION_COMMS_SENT,
						},
					},
				}).Return(&watsonPb.GetIncidentsForClientResponse{
					Status: rpc.StatusOk(),
					Incidents: []*watsonPb.IncidentDetailsForClient{{
						Client:          types.ServiceName_SAVINGS_SERVICE,
						ActorId:         actorId,
						ClientRequestId: actorId,
					}},
				}, nil)
				m.watsonClient.EXPECT().IngestEvent(gomock.Any(), &watsonPb.IngestEventRequest{
					EventType:       watsonPb.EventType_EVENT_TYPE_INCIDENT_RESOLUTION,
					Client:          types.ServiceName_SAVINGS_SERVICE,
					ActorId:         actorId,
					ClientRequestId: actorId,
					IssueCategoryId: minKycExpiryBalanceRefundIssueCategoryId,
				}).Return(&watsonPb.IngestEventResponse{Status: rpc.StatusOk()}, nil)
				wg.Done()
			},
			want: nil,
		},
		{
			name: "failure :(",
			args: args{
				utrs:       []*ClosedAccountUTRs{utrObj},
				agentEmail: agentEmail,
			},
			mocks: func(m *mockedDependencies, wg *sync.WaitGroup) {
				wg.Add(1)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ExternalId{
						ExternalId: &savingsPb.BankAccountIdentifier{
							AccountNo: utrObj.FiAccountNo,
							IfscCode:  utrObj.FiIfsc,
						},
					},
				}).Return(nil, nil)
				wg.Done()
			},
			want: []string{fmt.Sprintf("error at row %v: %v", 2, fmt.Errorf("error while getting savings account by account number and ifsc").Error())},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServerWithMocks(t)
			wg := &sync.WaitGroup{}
			if tt.mocks != nil {
				tt.mocks(mocks, wg)
			}
			if got := svc.storeBalTransferUtrsAndResolveIncident(context.Background(), tt.args.utrs, tt.args.agentEmail); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("storeBalTransferUtrs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getOrCreateBavIdForExtAcct(t *testing.T) {
	agentEmail := "<EMAIL>"
	accNo := "accNum"
	ifsc := "ifsc"
	actorId := "actor-id"
	account := &extacct.BankAccount{
		Ifsc:          ifsc,
		AccountNumber: accNo,
	}
	successfulBav := &extacct.BankAccountVerification{
		Id:            bavId,
		ActorId:       actorId,
		AccountNumber: accNo,
		Ifsc:          ifsc,
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_SUCCESS,
		Vendor:        extacct.Vendor_KARZA,
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	unsuccessfulBav := &extacct.BankAccountVerification{
		Id:            bavId,
		ActorId:       actorId,
		AccountNumber: accNo,
		Ifsc:          ifsc,
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_FAILURE,
		Vendor:        extacct.Vendor_KARZA,
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	}
	createdBav := &extacct.BankAccountVerification{
		Id:            bavId,
		ActorId:       actorId,
		AccountNumber: accNo,
		Ifsc:          ifsc,
		OverallStatus: extacct.OverallStatus_OVERALL_STATUS_SUCCESS,
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_FEDERAL_UTR_SHEET,
			Email:  agentEmail,
		},
	}

	type args struct {
		actorId    string
		accNo      string
		ifsc       string
		agentEmail string
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockedDependencies)
		want    string
		wantErr error
	}{
		{
			name: "did not find successful bav, creating a new one",
			args: args{
				actorId:    actorId,
				accNo:      accNo,
				ifsc:       ifsc,
				agentEmail: agentEmail,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), &extacct.GetBankAccountsRequest{ActorId: actorId}).
					Return(&extacct.GetBankAccountsResponse{
						Status:                   rpc.StatusOk(),
						BankAccountVerifications: []*extacct.BankAccountVerification{unsuccessfulBav},
					}, nil)
				m.extAcctClient.EXPECT().AddBankAccount(gomock.Any(), &extacct.AddBankAccountRequest{
					ActorId:       actorId,
					AccountNumber: accNo,
					Ifsc:          ifsc,
					Caller: &extacct.Caller{
						Source: extacct.Source_SOURCE_FEDERAL_UTR_SHEET,
						Email:  agentEmail,
					},
					AddWithoutVerification: true,
				}).Return(&extacct.AddBankAccountResponse{Status: rpc.StatusOk()}, nil)
				m.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), &extacct.GetBankAccountsRequest{ActorId: actorId}).
					Return(&extacct.GetBankAccountsResponse{
						Status:                   rpc.StatusOk(),
						BankAccounts:             []*extacct.BankAccount{account},
						BankAccountVerifications: []*extacct.BankAccountVerification{createdBav},
					}, nil)
			},
			want:    bavId,
			wantErr: nil,
		},
		{
			name: "successful bav already exists, use that",
			args: args{
				actorId:    actorId,
				accNo:      accNo,
				ifsc:       ifsc,
				agentEmail: agentEmail,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), &extacct.GetBankAccountsRequest{ActorId: actorId}).
					Return(&extacct.GetBankAccountsResponse{
						Status:                   rpc.StatusOk(),
						BankAccounts:             []*extacct.BankAccount{account},
						BankAccountVerifications: []*extacct.BankAccountVerification{successfulBav},
					}, nil)
			},
			want:    bavId,
			wantErr: nil,
		},
		{
			name: "error while adding new account",
			args: args{
				actorId:    actorId,
				accNo:      accNo,
				ifsc:       ifsc,
				agentEmail: agentEmail,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), &extacct.GetBankAccountsRequest{ActorId: actorId}).
					Return(&extacct.GetBankAccountsResponse{
						Status:                   rpc.StatusOk(),
						BankAccountVerifications: []*extacct.BankAccountVerification{unsuccessfulBav},
					}, nil)
				m.extAcctClient.EXPECT().AddBankAccount(gomock.Any(), &extacct.AddBankAccountRequest{
					ActorId:       actorId,
					AccountNumber: accNo,
					Ifsc:          ifsc,
					Caller: &extacct.Caller{
						Source: extacct.Source_SOURCE_FEDERAL_UTR_SHEET,
						Email:  agentEmail,
					},
					AddWithoutVerification: true,
				}).Return(nil, epifierrors.ErrInvalidSQL)
			},
			want:    "",
			wantErr: fmt.Errorf("error while adding external account for actor: %w", epifierrors.ErrInvalidSQL),
		},
		{
			name: "could not fetch successful entry after creating one",
			args: args{
				actorId:    actorId,
				accNo:      accNo,
				ifsc:       ifsc,
				agentEmail: agentEmail,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), &extacct.GetBankAccountsRequest{ActorId: actorId}).
					Return(&extacct.GetBankAccountsResponse{
						Status:                   rpc.StatusOk(),
						BankAccountVerifications: []*extacct.BankAccountVerification{unsuccessfulBav},
					}, nil)
				m.extAcctClient.EXPECT().AddBankAccount(gomock.Any(), &extacct.AddBankAccountRequest{
					ActorId:       actorId,
					AccountNumber: accNo,
					Ifsc:          ifsc,
					Caller: &extacct.Caller{
						Source: extacct.Source_SOURCE_FEDERAL_UTR_SHEET,
						Email:  agentEmail,
					},
					AddWithoutVerification: true,
				}).Return(&extacct.AddBankAccountResponse{Status: rpc.StatusOk()}, nil)
				m.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), &extacct.GetBankAccountsRequest{ActorId: actorId}).
					Return(&extacct.GetBankAccountsResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			want:    "",
			wantErr: fmt.Errorf("could not fetch successful bav after storing it in db"),
		},
		{
			name: "error while fetching bavs of actor",
			args: args{
				actorId:    actorId,
				accNo:      accNo,
				ifsc:       ifsc,
				agentEmail: agentEmail,
			},
			mocks: func(m *mockedDependencies) {
				m.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), &extacct.GetBankAccountsRequest{ActorId: actorId}).
					Return(nil, epifierrors.ErrInvalidSQL)
			},
			want:    "",
			wantErr: fmt.Errorf("error while getting external account for actor: %w", epifierrors.ErrInvalidSQL),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServerWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			got, err := svc.getOrCreateBavIdForExtAcct(context.Background(), tt.args.actorId, tt.args.accNo, tt.args.ifsc, tt.args.agentEmail)
			if !compareErrors(err, tt.wantErr) {
				t.Errorf("getOrCreateBavIdForExtAcct() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !strings.EqualFold(got, tt.want) {
				t.Errorf("getOrCreateBavIdForExtAcct() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_storeClosedAccBalance(t *testing.T) {
	savId := "sav-id"
	balObj := &ClosedAccountBalances{
		FiAccountNo:     "fi-acc-no",
		FiIfsc:          "fi-ifsc",
		ReportedBalance: 100,
	}
	cbtEmpty := &savingsPb.ClosedAccountBalanceTransfer{
		Id:               "cbt",
		SavingsAccountId: savId,
	}
	type args struct {
		savAccId string
		balObj   *ClosedAccountBalances
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mockedDependencies)
		wantErr error
	}{
		{
			name: "stored data (updating old entries)",
			args: args{
				savAccId: savId,
				balObj:   balObj,
			},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status:  rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{cbtEmpty},
				}, nil)
				m.savingsClient.EXPECT().UpdateClosedAccountBalTransferData(gomock.Any(), &savingsPb.UpdateClosedAccountBalTransferDataRequest{
					Data: &savingsPb.ClosedAccountBalanceTransfer{
						Id:               "cbt",
						SavingsAccountId: savId,
						ReportedClosureBalance: &gmoney.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
					FieldMasks: []savingsPb.CbtFieldMask{savingsPb.CbtFieldMask_CBT_FIELD_MASK_REPORTED_CLOSURE_BALANCE},
				}).Return(&savingsPb.UpdateClosedAccountBalTransferDataResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: nil,
		},
		{
			name: "stored data (new entry)",
			args: args{
				savAccId: savId,
				balObj:   balObj,
			},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				m.savingsClient.EXPECT().StoreClosedAccountBalTransferData(gomock.Any(), &savingsPb.StoreClosedAccountBalTransferDataRequest{
					Data: &savingsPb.ClosedAccountBalanceTransfer{
						SavingsAccountId: savId,
						ReportedClosureBalance: &gmoney.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
				}).Return(&savingsPb.StoreClosedAccountBalTransferDataResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: nil,
		},
		{
			name: "error getting cbt",
			args: args{
				savAccId: savId,
				balObj:   balObj,
			},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(nil, epifierrors.ErrRequestCanceled)
			},
			wantErr: fmt.Errorf("error while getting closed account balance transfer data: %v", epifierrors.ErrRequestCanceled),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServerWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			err := svc.storeClosedAccBalance(context.Background(), tt.args.savAccId, tt.args.balObj)
			if !compareErrors(err, tt.wantErr) {
				t.Errorf("storeClosedAccBalance() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestService_storeClosureBalances(t *testing.T) {
	savId := "sav-id"
	userId := "user-id"
	actorId := "actor-id"
	balObj := &ClosedAccountBalances{
		FiAccountNo:     "fi-acc-no",
		FiIfsc:          "fi-ifsc",
		ReportedBalance: 100,
	}
	ticketFilters := &ticketpb.TicketFilters{
		StatusList:          ticketpb.GetActiveStatusList(),
		ProductCategoryList: []ticketpb.ProductCategory{ticketpb.ProductCategory_PRODUCT_CATEGORY_ACCOUNTS},
		ActorIdList:         []string{actorId},
	}
	tests := []struct {
		name  string
		mocks func(m *mockedDependencies)
		req   []*ClosedAccountBalances
		want  []string
	}{
		{
			name: "successfully stored (no resolution)",
			req:  []*ClosedAccountBalances{balObj},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ExternalId{
						ExternalId: &savingsPb.BankAccountIdentifier{
							AccountNo: balObj.FiAccountNo,
							IfscCode:  balObj.FiIfsc,
						},
					},
				}).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						Id:                   savId,
						AccountNo:            balObj.FiAccountNo,
						IfscCode:             balObj.FiIfsc,
						PrimaryAccountHolder: userId,
					}}, nil)
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				m.savingsClient.EXPECT().StoreClosedAccountBalTransferData(gomock.Any(), &savingsPb.StoreClosedAccountBalTransferDataRequest{
					Data: &savingsPb.ClosedAccountBalanceTransfer{
						SavingsAccountId: savId,
						ReportedClosureBalance: &gmoney.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
					},
				}).Return(&savingsPb.StoreClosedAccountBalTransferDataResponse{Status: rpc.StatusOk()}, nil)
			},
			want: nil,
		},
		{
			name: "successfully stored (resolved ticket as well)",
			req: []*ClosedAccountBalances{{
				FiAccountNo:     balObj.FiAccountNo,
				FiIfsc:          balObj.FiIfsc,
				ReportedBalance: 0.5,
			}},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ExternalId{
						ExternalId: &savingsPb.BankAccountIdentifier{
							AccountNo: balObj.FiAccountNo,
							IfscCode:  balObj.FiIfsc,
						},
					},
				}).Return(&savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{
						Id:                   savId,
						AccountNo:            balObj.FiAccountNo,
						IfscCode:             balObj.FiIfsc,
						PrimaryAccountHolder: userId,
					}}, nil)
				m.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: savId,
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				m.savingsClient.EXPECT().StoreClosedAccountBalTransferData(gomock.Any(), &savingsPb.StoreClosedAccountBalTransferDataRequest{
					Data: &savingsPb.ClosedAccountBalanceTransfer{
						SavingsAccountId: savId,
						ReportedClosureBalance: &gmoney.Money{
							CurrencyCode: "INR",
							Units:        0,
							Nanos:        *********,
						},
					},
				}).Return(&savingsPb.StoreClosedAccountBalTransferDataResponse{Status: rpc.StatusOk()}, nil)
				m.actorClient.EXPECT().GetActorByEntityId(gomock.Any(), &actorPb.GetActorByEntityIdRequest{
					Type:     types.Actor_USER,
					EntityId: userId,
				}).Return(&actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id: actorId,
					},
				}, nil)
				m.watsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), &watsonPb.GetIncidentsForClientRequest{
					IncidentFilter: &watsonPb.IncidentFiltersForClient{
						ActorId:         actorId,
						Client:          types.ServiceName_SAVINGS_SERVICE,
						ResponseLimit:   5,
						ClientRequestId: actorId,
						IssueCategoryId: minKycExpiryBalanceRefundIssueCategoryId,
						IncidentStates: []watsonPb.IncidentState{
							watsonPb.IncidentState_INCIDENT_STATE_LOGGED_IN_DB,
							watsonPb.IncidentState_INCIDENT_STATE_DROPPED,
							watsonPb.IncidentState_INCIDENT_STATE_TICKET_CREATED,
							watsonPb.IncidentState_INCIDENT_STATE_INCIDENT_CREATION_COMMS_SENT,
						},
					},
				}).Return(&watsonPb.GetIncidentsForClientResponse{
					Status: rpc.StatusOk(),
					Incidents: []*watsonPb.IncidentDetailsForClient{{
						Client:          types.ServiceName_SAVINGS_SERVICE,
						ActorId:         actorId,
						ClientRequestId: actorId,
					}},
				}, nil)
				m.watsonClient.EXPECT().IngestEvent(gomock.Any(), &watsonPb.IngestEventRequest{
					EventType:       watsonPb.EventType_EVENT_TYPE_INCIDENT_RESOLUTION,
					Client:          types.ServiceName_SAVINGS_SERVICE,
					ActorId:         actorId,
					ClientRequestId: actorId,
					IssueCategoryId: minKycExpiryBalanceRefundIssueCategoryId,
				}).Return(&watsonPb.IngestEventResponse{Status: rpc.StatusOk()}, nil)
				m.ticketClient.EXPECT().GetSupportTickets(gomock.Any(), &ticketpb.GetSupportTicketsRequest{
					TicketFilters: ticketFilters,
					PageContextRequest: &rpcPb.PageContextRequest{
						Token: &rpcPb.PageContextRequest_AfterToken{
							AfterToken: "",
						},
						PageSize: 50,
					},
				}).Return(&ticketpb.GetSupportTicketsResponse{
					Status: rpc.StatusOk(),
					Tickets: []*ticketpb.Ticket{{
						Id:   123,
						Tags: []string{""},
						CustomFieldWithValue: &ticketpb.CustomFieldsWithValue{
							SubCategory: "Balance refund",
						},
					}}}, nil)
				m.ticketClient.EXPECT().UpdateTicketAsync(gomock.Any(), &ticketpb.UpdateTicketAsyncRequest{
					Ticket: &ticketpb.Ticket{
						Id:     123,
						Status: ticketpb.Status_STATUS_RESOLVED,
						Tags:   []string{"Auto Resolved"},
						CustomFields: &ticketpb.CustomFields{
							Utr:                   "",
							SavingsAccountBalance: ticketpb.SavingsAccountBalance_SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1,
						},
					},
				}).Return(&ticketpb.UpdateTicketAsyncResponse{Status: rpc.StatusOk()}, nil)
			},
			want: nil,
		},
		{
			name: "failure :(",
			req: []*ClosedAccountBalances{
				balObj,
			},
			mocks: func(m *mockedDependencies) {
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ExternalId{
						ExternalId: &savingsPb.BankAccountIdentifier{
							AccountNo: balObj.FiAccountNo,
							IfscCode:  balObj.FiIfsc,
						},
					},
				}).Return(nil, nil)
			},
			want: []string{fmt.Sprintf("error at row %v: %v", 2, fmt.Errorf("error while getting savings account by account number and ifsc").Error())},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServerWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			if got := svc.storeClosureBalances(context.Background(), tt.req); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("storeClosureBalances() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getMoneyFromFloat(t *testing.T) {
	tests := []struct {
		name   string
		amount float32
		want   *gmoney.Money
	}{
		{
			name:   "money with decimal",
			amount: 3123.12,
			want: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        3123,
				Nanos:        *********,
			},
		},
		{
			name:   "money with decimal 2",
			amount: 3123.50,
			want: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        3123,
				Nanos:        *********,
			},
		},
		{
			name:   "money with decimal 3",
			amount: 3123.50124,
			want: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        3123,
				Nanos:        *********,
			},
		},
		{
			name:   "money without decimal",
			amount: 412,
			want: &gmoney.Money{
				CurrencyCode: "INR",
				Units:        412,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getMoneyFromFloat(tt.amount); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getMoneyFromFloat() = %v, want %v", got, tt.want)
			}
		})
	}
}
