package account

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/cx"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	cxAPb "github.com/epifi/gamma/api/cx/data_collector/account"
	"github.com/epifi/gamma/api/frontend/account/sa_closure"
	"github.com/epifi/gamma/api/frontend/account/sa_closure/mocks"
	"github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/savings"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/typesv2"

	customerAuth "github.com/epifi/gamma/api/cx/customer_auth"
	user "github.com/epifi/gamma/api/user"
	authEngineMocks "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
)

var (
	sherlockDeepLink1	= func() *customerAuth.SherlockDeepLink {
		return &customerAuth.SherlockDeepLink{Screen: caPb.Screen_SCREEN_UNSPECIFIED}
	}
	failureReasons	= []string{"key1 : value1,key2: value2"}
)

type saClosureMockedDependencies struct {
	savingsClient		*mockSavings.MockSavingsClient
	authEngineClient	*authEngineMocks.MockIAuthEngine
	saClosureClient		*mocks.MockSavingsAccountClosureClient
}

func saClosureNewServerWithMocks(t *testing.T) (*Service, *saClosureMockedDependencies) {
	ctrl := gomock.NewController(t)
	savingsClient := mockSavings.NewMockSavingsClient(ctrl)
	authEngineClient := authEngineMocks.NewMockIAuthEngine(ctrl)
	saClosureClient := mocks.NewMockSavingsAccountClosureClient(ctrl)

	md := &saClosureMockedDependencies{
		savingsClient:		savingsClient,
		authEngineClient:	authEngineClient,
		saClosureClient:	saClosureClient,
	}

	svc := &Service{
		savingsClient:		savingsClient,
		authEngine:		authEngineClient,
		saClosureClient:	saClosureClient,
	}

	return svc, md
}

func TestService_IsSavingsAccountClosureAllowed(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx	context.Context
		req	*cxAPb.IsSavingsAccountClosureAllowedRequest
		mocks	func(m *saClosureMockedDependencies)
	}
	tests := []struct {
		name	string
		args	args
		want	*cxAPb.IsSavingsAccountClosureAllowedResponse
		wantErr	bool
	}{
		{
			name:	"#1 Auth Action Required For Information Level",
			args: args{
				ctx:	context.Background(),
				mocks: func(m *saClosureMockedDependencies) {
					m.authEngineClient.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, sherlockDeepLink1())
				},
			},
			want: &cxAPb.IsSavingsAccountClosureAllowedResponse{
				Status:			rpcPb.StatusOk(),
				SherlockDeepLink:	sherlockDeepLink1(),
			},
			wantErr:	false,
		},
		{
			name:	"#2 Missing User Information",
			args: args{
				ctx:	context.Background(),
				mocks: func(m *saClosureMockedDependencies) {
					m.authEngineClient.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				},
				req: &cxAPb.IsSavingsAccountClosureAllowedRequest{
					Header: &cx.Header{
						User: nil,
					},
				},
			},
			want:		&cxAPb.IsSavingsAccountClosureAllowedResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("user not present in header")},
			wantErr:	false,
		},
		{
			name:	"#3 Missing Actor Information",
			args: args{
				ctx:	context.Background(),
				mocks: func(m *saClosureMockedDependencies) {
					m.authEngineClient.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				},
				req: &cxAPb.IsSavingsAccountClosureAllowedRequest{
					Header: &cx.Header{
						User:	&user.User{},
						Actor:	nil,
					},
				},
			},
			want:		&cxAPb.IsSavingsAccountClosureAllowedResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor not present in header")},
			wantErr:	false,
		},
		{
			name:	"#4 Error: Evaluate User For Closure Eligibility",
			args: args{
				ctx:	context.Background(),
				mocks: func(m *saClosureMockedDependencies) {
					m.authEngineClient.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
					m.saClosureClient.EXPECT().EvaluateUserForClosureEligibility(gomock.Any(), gomock.Any(), gomock.Any()).Return(&sa_closure.EvaluateUserForClosureEligibilityResponse{
						RespHeader: &header.ResponseHeader{
							Status: rpc.StatusInternalWithDebugMsg("failed to get account essentials"),
						},
					}, fmt.Errorf("error"))
					m.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pb.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprint("error")),
					}, nil).AnyTimes()
				},
				req: &cxAPb.IsSavingsAccountClosureAllowedRequest{
					Header: &cx.Header{
						User:	&user.User{},
						Actor:	&typesv2.Actor{},
					},
				},
			},
			want:		&cxAPb.IsSavingsAccountClosureAllowedResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while getting sa closure response")},
			wantErr:	false,
		},
		{
			name:	"#5 Success: Evaluate User For Closure Eligibility: HasFreeze: Account Not Closable",
			args: args{
				ctx:	context.Background(),
				mocks: func(m *saClosureMockedDependencies) {
					m.authEngineClient.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
					m.saClosureClient.EXPECT().EvaluateUserForClosureEligibility(gomock.Any(), gomock.Any(), gomock.Any()).Return(&sa_closure.EvaluateUserForClosureEligibilityResponse{
						RespHeader: &header.ResponseHeader{
							Status: rpc.StatusOk(),
						},
						HasFreeze:	true,
						FailureReasons:	failureReasons,
					}, nil)
					m.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pb.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusOk(),
					}, nil).AnyTimes()
				},
				req: &cxAPb.IsSavingsAccountClosureAllowedRequest{
					Header: &cx.Header{
						User:	&user.User{},
						Actor:	&typesv2.Actor{},
					},
				},
			},
			want: &cxAPb.IsSavingsAccountClosureAllowedResponse{
				Status:			rpc.StatusOk(),
				IsClosureAllowed:	false,
				FailureReasons:		failureReasons,
			},
			wantErr:	false,
		},
		{
			name:	"#6 Success: Evaluate User For Closure Eligibility: HasPendingCharges: Account Not Closable",
			args: args{
				ctx:	context.Background(),
				mocks: func(m *saClosureMockedDependencies) {
					m.authEngineClient.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
					m.saClosureClient.EXPECT().EvaluateUserForClosureEligibility(gomock.Any(), gomock.Any(), gomock.Any()).Return(&sa_closure.EvaluateUserForClosureEligibilityResponse{
						RespHeader: &header.ResponseHeader{
							Status: rpc.StatusOk(),
						},
						FailureReasons:		failureReasons,
						HasPendingCharges:	true,
					}, nil)
					m.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pb.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusOk(),
					}, nil).AnyTimes()
				},
				req: &cxAPb.IsSavingsAccountClosureAllowedRequest{
					Header: &cx.Header{
						User:	&user.User{},
						Actor:	&typesv2.Actor{},
					},
				},
			},
			want: &cxAPb.IsSavingsAccountClosureAllowedResponse{
				Status:			rpc.StatusOk(),
				IsClosureAllowed:	false,
				FailureReasons:		failureReasons,
			},
			wantErr:	false,
		},
		{
			name:	"#7 Success: Evaluate User For Closure Eligibility: Account Closable",
			args: args{
				ctx:	context.Background(),
				mocks: func(m *saClosureMockedDependencies) {
					m.authEngineClient.EXPECT().IsAuthActionRequiredForInformationLevel(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
					m.saClosureClient.EXPECT().EvaluateUserForClosureEligibility(gomock.Any(), gomock.Any(), gomock.Any()).Return(&sa_closure.EvaluateUserForClosureEligibilityResponse{
						RespHeader: &header.ResponseHeader{
							Status: rpc.StatusOk(),
						},
						HasFreeze:		false,
						FailedCriteriaCheck:	false,
						HasLien:		false,
						FailureReasons:		failureReasons,
						HasPendingCharges:	false,
					}, nil)
					m.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), gomock.Any(), gomock.Any()).Return(&pb.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusOk(),
					}, nil).AnyTimes()
				},
				req: &cxAPb.IsSavingsAccountClosureAllowedRequest{
					Header: &cx.Header{
						User:	&user.User{},
						Actor:	&typesv2.Actor{},
					},
				},
			},
			want: &cxAPb.IsSavingsAccountClosureAllowedResponse{
				Status:			rpc.StatusOk(),
				IsClosureAllowed:	true,
				FailureReasons:		failureReasons,
			},
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := saClosureNewServerWithMocks(t)
			if tt.args.mocks != nil {
				tt.args.mocks(mocks)
			}
			got, err := svc.IsSavingsAccountClosureAllowed(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsSavingsAccountClosureAllowed() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IsSavingsAccountClosureAllowed() got = %v, want %v", got, tt.want)
			}
		})
	}
}
