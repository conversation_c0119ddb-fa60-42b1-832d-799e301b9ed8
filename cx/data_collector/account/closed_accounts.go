package account

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	types "github.com/epifi/gamma/api/typesv2"
)

// ClosedAccountBalances is the csv format in which the sherlock agent uploads data to store vendor reported closure balance
type ClosedAccountBalances struct {
	FiAccountNo     string  `csv:"Account Number"`
	FiIfsc          string  `csv:"Ifsc"`
	ReportedBalance float32 `csv:"Balance"`
}

func (c *ClosedAccountBalances) validate() error {
	if c.FiAccountNo == "" || c.FiIfsc == "" {
		return fmt.Errorf("account number or ifsc not mentioned")
	}
	return nil
}

const (
	// For freshdesk ticket category Accounts >> Min KYC expiry >> Subcategory Balance Refund
	subCategory = "Balance refund"
	// Used to differntiate manual ticket from auto ID ticket with tag
	autoId     = "Auto ID"
	maxThreads = 5
)

// storeClosureBalances stores account balances of closed accounts, and resolves account closure balance transfer
// incidents if balance is < auto resolve threshold
func (s *Service) storeClosureBalances(ctx context.Context, balanceObjs []*ClosedAccountBalances) []string {
	var (
		resp  []string
		guard = make(chan struct{}, maxThreads)
		wg    sync.WaitGroup
	)
	for idx, balObj := range balanceObjs {
		idx := idx
		balObj := balObj
		wg.Add(1)
		guard <- struct{}{}
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer func() {
				wg.Done()
				<-guard
			}()

			if valErr := balObj.validate(); valErr != nil {
				resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, valErr.Error()))
				return
			}

			savAcc, err := s.getAccountByNumIfsc(ctx, balObj.FiAccountNo, balObj.FiIfsc)
			if err != nil {
				resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, err.Error()))
				return
			}

			if err = s.storeClosedAccBalance(ctx, savAcc.GetId(), balObj); err != nil {
				resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, err.Error()))
				return
			}

			if balObj.ReportedBalance <= s.cxConf.ClosedAccountConfig.AutoResolveAmount {
				actorId, actorErr := s.getActorIdByUserId(ctx, savAcc.GetPrimaryAccountHolder())
				if actorErr != nil {
					resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, actorErr.Error()))
					return
				}
				if err = s.resolveTickets(ctx, strings.Fields(actorId), "", ticketPb.SavingsAccountBalance_SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1); err != nil {
					resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, err.Error()))
				}
				if err = s.resolveClosureIncidentIfExists(ctx, actorId); err != nil {
					resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, err.Error()))
					return
				}
				logger.Info(ctx, "resolved incident since balance less than auto resolve threshold",
					zap.String(logger.ACCOUNT_ID, savAcc.GetId()), zap.Float32("threshold", s.cxConf.ClosedAccountConfig.AutoResolveAmount))
			}
		})
	}
	waitgroup.SafeWait(&wg, 60*time.Second)
	return resp
}

// storeClosedAccBalance updates the reported balance for cbt entries for which utr was not stored.
// If no such records were found (or no cbt found), a new entry is created with just the reported balance.
func (s *Service) storeClosedAccBalance(ctx context.Context, savAccId string, balObj *ClosedAccountBalances) error {
	cbtResp, cbtErr := s.savingsClient.GetClosedAccountBalTransferData(ctx, &savingsPb.GetClosedAccountBalTransferDataRequest{SavingsAccountId: savAccId})
	if err := epifigrpc.RPCError(cbtResp, cbtErr); err != nil && !cbtResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while getting closed account balance transfer data", zap.Error(err), zap.String(logger.ACCOUNT_ID, savAccId))
		return fmt.Errorf("error while getting closed account balance transfer data: %v", err)
	}

	return s.upsertBalance(ctx, cbtResp.GetEntries(), savAccId, balObj.ReportedBalance)
}

// upsertBalance updates balance of cbt whose record does not contain UTR data
// else creates a new record
func (s *Service) upsertBalance(ctx context.Context, existingData []*savingsPb.ClosedAccountBalanceTransfer, savAccId string, balance float32) error {
	var updateCbt *savingsPb.ClosedAccountBalanceTransfer
	for _, cbt := range existingData {
		// do not alter records that have utr data stored
		if doesCbtContainUtr(cbt) {
			continue
		}
		updateCbt = cbt
		break
	}

	// no CBT entry exists without transaction data, create a new one
	if updateCbt == nil {
		return s.storeCbt(ctx, &savingsPb.ClosedAccountBalanceTransfer{
			SavingsAccountId:       savAccId,
			ReportedClosureBalance: getMoneyFromFloat(balance),
		})
	}

	updateCbt.ReportedClosureBalance = getMoneyFromFloat(balance)
	updResp, updErr := s.savingsClient.UpdateClosedAccountBalTransferData(ctx, &savingsPb.UpdateClosedAccountBalTransferDataRequest{
		Data:       updateCbt,
		FieldMasks: []savingsPb.CbtFieldMask{savingsPb.CbtFieldMask_CBT_FIELD_MASK_REPORTED_CLOSURE_BALANCE},
	})
	if err := epifigrpc.RPCError(updResp, updErr); err != nil {
		logger.Error(ctx, "error while updating balance", zap.Error(err), zap.String(logger.ID, updateCbt.GetId()))
		return fmt.Errorf("error while updating balance %w", err)
	}
	return nil
}

// ClosedAccountUTRs is the csv format in which the sherlock agent uploads data to store balance transfer UTRs
type ClosedAccountUTRs struct {
	FiAccountNo  string `csv:"Fi Account Number"`
	FiIfsc       string `csv:"Fi Ifsc Code"`
	AltAccountNo string `csv:"Alternate Account Number"`
	AltIfsc      string `csv:"Alternate Ifsc"`
	Utr          string `csv:"UTR"`
}

func (c *ClosedAccountUTRs) validate() error {
	switch {
	case c.FiAccountNo == "", c.FiIfsc == "":
		return fmt.Errorf("fi account number or ifsc not mentioned")
	case c.AltAccountNo == "", c.AltIfsc == "":
		return fmt.Errorf("alternate account number or ifsc not mentioned")
	case c.Utr == "":
		return fmt.Errorf("utr not mentioned")
	}
	return nil

}

func (s *Service) storeBalTransferUtrsAndResolveIncident(ctx context.Context, utrObjs []*ClosedAccountUTRs, agentEmail string) []string {
	var (
		resp  []string
		guard = make(chan struct{}, maxThreads)
		wg    sync.WaitGroup
	)
	for idx, utrObj := range utrObjs {
		idx := idx
		utrObj := utrObj
		wg.Add(1)
		guard <- struct{}{}
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer func() {
				wg.Done()
				<-guard
			}()

			if valErr := utrObj.validate(); valErr != nil {
				resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, valErr.Error()))
				return
			}

			savAcc, err := s.getAccountByNumIfsc(ctx, utrObj.FiAccountNo, utrObj.FiIfsc)
			if err != nil {
				resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, err.Error()))
				return
			}

			actorId, actorErr := s.getActorIdByUserId(ctx, savAcc.GetPrimaryAccountHolder())
			if err != nil {
				resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, actorErr.Error()))
				return
			}

			bavId, bavErr := s.getOrCreateBavIdForExtAcct(ctx, actorId, utrObj.AltAccountNo, utrObj.AltIfsc, agentEmail)
			if err != nil {
				resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, bavErr.Error()))
				return
			}

			if err = s.storeBalTransferUtr(ctx, savAcc.GetId(), bavId, utrObj); err != nil {
				resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, err.Error()))
				return
			}
			if utrObj.Utr != "" {
				if err = s.resolveTickets(ctx, strings.Fields(actorId), utrObj.Utr, ticketPb.SavingsAccountBalance_SAVINGS_ACCOUNT_BALANCE_OTHER); err != nil {
					resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, err.Error()))
				}
				if err = s.resolveClosureIncidentIfExists(ctx, actorId); err != nil {
					resp = append(resp, fmt.Sprintf("error at row %v: %v", idx+2, err.Error()))
					return
				}
				logger.Info(ctx, "resolved incident since utr shared by vendor", zap.String(logger.ACCOUNT_ID, savAcc.GetId()))
			}
		})
	}
	waitgroup.SafeWait(&wg, 60*time.Second)
	return resp
}

// storeBalTransferUtr stores utr and transaction status of the balance transfer transaction along with foreign key
// to bank_account_verifications (bavId) in closed_accounts_balance_transfer table.
// We update existing entry which does not have utr and transaction status.
// We create a new row when:
// 1. No entry exists
// 2. All entries already have utr or transaction status already populated (to maintain old transaction history)
// We return already exists error message when cbt with given utr already exists
func (s *Service) storeBalTransferUtr(ctx context.Context, savAccId, bavId string, utrObj *ClosedAccountUTRs) error {
	cbtResp, cbtErr := s.savingsClient.GetClosedAccountBalTransferData(ctx, &savingsPb.GetClosedAccountBalTransferDataRequest{
		SavingsAccountId: savAccId,
	})
	if err := epifigrpc.RPCError(cbtResp, cbtErr); err != nil && !cbtResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while getting closed account balance transfer data", zap.Error(err), zap.String(logger.ACCOUNT_ID, savAccId))
		return fmt.Errorf("error while getting closed account balance transfer data: %v", err)
	}

	return s.upsertUtr(ctx, cbtResp.GetEntries(), savAccId, utrObj.Utr, bavId)
}

// getOrCreateBavIdForExtAcct fetches bavId of successful validation of external account with the same account number,
// and ifsc. If it does not exist, it adds an external account validation and then returns bavId of that newly created
func (s *Service) getOrCreateBavIdForExtAcct(ctx context.Context, actorId, accNo, ifsc, agentEmail string) (string, error) {
	bavId, err := s.getSuccessfulBavIdForActor(ctx, actorId, accNo, ifsc)
	switch {
	case err != nil:
		return "", err
	case bavId != "":
		return bavId, nil
	}

	addResp, addErr := s.extAcctClient.AddBankAccount(ctx, &extacct.AddBankAccountRequest{
		ActorId:       actorId,
		Ifsc:          ifsc,
		AccountNumber: accNo,
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_FEDERAL_UTR_SHEET,
			Email:  agentEmail,
		},
		AddWithoutVerification: true,
	})
	if err = epifigrpc.RPCError(addResp, addErr); err != nil {
		logger.Error(ctx, "error while adding external account for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return "", fmt.Errorf("error while adding external account for actor: %w", err)
	}

	bavId, err = s.getSuccessfulBavIdForActor(ctx, actorId, accNo, ifsc)
	switch {
	case err != nil:
		return "", err
	case bavId != "":
		return bavId, nil
	default:
		return "", fmt.Errorf("could not fetch successful bav after storing it in db")
	}
}

func (s *Service) getSuccessfulBavIdForActor(ctx context.Context, actorId string, accNo string, ifsc string) (string, error) {
	gbaResp, gbaErr := s.extAcctClient.GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{ActorId: actorId})
	if err := epifigrpc.RPCError(gbaResp, gbaErr); err != nil {
		logger.Error(ctx, "error while getting external account for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return "", fmt.Errorf("error while getting external account for actor: %w", err)
	}

	if len(gbaResp.GetBankAccounts()) > 0 {
		for _, bav := range gbaResp.GetBankAccountVerifications() {
			if isSameAccount(bav, actorId, accNo, ifsc) {
				return bav.GetId(), nil
			}
		}
	}
	return "", nil
}

func isSameAccount(bav *extacct.BankAccountVerification, actorId, accNo, ifsc string) bool {
	return strings.EqualFold(bav.GetAccountNumber(), accNo) && strings.EqualFold(bav.GetIfsc(), ifsc) && strings.EqualFold(bav.GetActorId(), actorId) && bav.GetOverallStatus() == extacct.OverallStatus_OVERALL_STATUS_SUCCESS
}

func (s *Service) getAccountByNumIfsc(ctx context.Context, accountNo, ifsc string) (*savingsPb.Account, error) {
	accResp, accErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ExternalId{
			ExternalId: &savingsPb.BankAccountIdentifier{
				AccountNo: accountNo,
				IfscCode:  ifsc,
			},
		},
	})
	if accErr != nil || accResp == nil || accResp.GetAccount() == nil {
		logger.Error(ctx, "error while getting savings account by account number and ifsc", zap.String(logger.ACCOUNT_NUMBER, accountNo), zap.String(logger.IFSC_CODE, ifsc))
		return nil, fmt.Errorf("error while getting savings account by account number and ifsc")
	}
	return accResp.GetAccount(), nil
}

// upsertUtr filters through list of cbts that exist for a savings account, and:
// 1. Returns already exists error if cbt with same utr exists
// 2. Creates a new record if there exists no cbt with transaction data already filled (to maintain history)
// 3. Updates existing record that does not have transaction data
func (s *Service) upsertUtr(ctx context.Context, existingData []*savingsPb.ClosedAccountBalanceTransfer, savAccId, utr, bavId string) error {
	var updateCbt *savingsPb.ClosedAccountBalanceTransfer
	for _, cbt := range existingData {
		// if there already exists a CBT with utr or transaction status, then do not update that entry (create new one)
		if doesCbtContainUtr(cbt) {
			// if cbt with same data is already stored, do not create / update data
			if strings.EqualFold(cbt.GetUtr(), utr) && cbt.GetTransactionStatus() == savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS {
				return fmt.Errorf("data already stored with same details in closed accounts table")
			}
			// TODO: when we also start storing failed transfers, alter this logic
			continue
		}
		updateCbt = cbt
		break
	}

	// no CBT entry exists without transaction data, create a new one
	if updateCbt == nil {
		return s.storeCbt(ctx, &savingsPb.ClosedAccountBalanceTransfer{
			SavingsAccountId:  savAccId,
			BavId:             bavId,
			Utr:               utr,
			TransactionStatus: savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS,
		})
	}

	// there exists CBT entry without transaction data, update that
	updateCbt.BavId = bavId
	updateCbt.Utr = utr
	updateCbt.TransactionStatus = savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_SUCCESS
	updResp, updErr := s.savingsClient.UpdateClosedAccountBalTransferData(ctx, &savingsPb.UpdateClosedAccountBalTransferDataRequest{
		Data: updateCbt,
		FieldMasks: []savingsPb.CbtFieldMask{
			savingsPb.CbtFieldMask_CBT_FIELD_MASK_TRANSACTION_DETAILS,
			savingsPb.CbtFieldMask_CBT_FIELD_MASK_TRANSACTION_STATUS,
			savingsPb.CbtFieldMask_CBT_FIELD_MASK_BAV_ID,
		},
	})
	if err := epifigrpc.RPCError(updResp, updErr); err != nil {
		logger.Error(ctx, "error while updating utr", zap.Error(err), zap.String(logger.ID, updateCbt.GetId()))
		return fmt.Errorf("error while updating utr %w", err)
	}
	return nil
}

// storeCbt calls StoreClosedAccountBalTransferData rpc that creates a new entry in database
func (s *Service) storeCbt(ctx context.Context, cbt *savingsPb.ClosedAccountBalanceTransfer) error {
	storeResp, storeErr := s.savingsClient.StoreClosedAccountBalTransferData(ctx, &savingsPb.StoreClosedAccountBalTransferDataRequest{
		Data: cbt,
	})
	if err := epifigrpc.RPCError(storeResp, storeErr); err != nil {
		logger.Error(ctx, "error while storing cbt (new entry)", zap.Error(err), zap.String(logger.ACCOUNT_ID, cbt.GetSavingsAccountId()))
		return fmt.Errorf("error while storing cbt (new entry): %w", err)
	}
	return nil
}

// resolveClosureIncidentIfExists marks incident created in watson service as resolved
func (s *Service) resolveClosureIncidentIfExists(ctx context.Context, actorId string) error {
	inciResp, inciErr := s.watsonClient.GetIncidentsForClient(ctx, &watsonPb.GetIncidentsForClientRequest{
		IncidentFilter: &watsonPb.IncidentFiltersForClient{
			ActorId:         actorId,
			Client:          types.ServiceName_SAVINGS_SERVICE,
			ResponseLimit:   5,
			ClientRequestId: actorId,
			IssueCategoryId: s.cxConf.ClosedAccountConfig.IssueCategoryId,
			IncidentStates: []watsonPb.IncidentState{
				watsonPb.IncidentState_INCIDENT_STATE_LOGGED_IN_DB,
				watsonPb.IncidentState_INCIDENT_STATE_DROPPED,
				watsonPb.IncidentState_INCIDENT_STATE_TICKET_CREATED,
				watsonPb.IncidentState_INCIDENT_STATE_INCIDENT_CREATION_COMMS_SENT,
			},
		},
	})
	if err := epifigrpc.RPCError(inciResp, inciErr); err != nil {
		if inciResp.GetStatus().IsRecordNotFound() {
			// if not watson incident was found, then return success as script intends to store data and resolve
			// incident/ticket if they exist
			return nil
		}
		logger.Error(ctx, "error while getting watson incidents for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return err
	}

	if incidents := len(inciResp.GetIncidents()); incidents != 1 {
		logger.Error(ctx, "exactly one min kyc incident was not found", zap.Int("incident_count", incidents), zap.String(logger.ACTOR_ID_V2, actorId))
	}

	// since the client request id is unique (actorId), we only need to send resolution request once
	// for one ticket that should exist
	resp, ingErr := s.watsonClient.IngestEvent(ctx, &watsonPb.IngestEventRequest{
		EventType:       watsonPb.EventType_EVENT_TYPE_INCIDENT_RESOLUTION,
		Client:          types.ServiceName_SAVINGS_SERVICE,
		ActorId:         actorId,
		ClientRequestId: actorId,
		IssueCategoryId: s.cxConf.ClosedAccountConfig.IssueCategoryId,
	})
	if err := epifigrpc.RPCError(resp, ingErr); err != nil {
		logger.Error(ctx, "error while resolving watson incident", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return fmt.Errorf("error while resolving watson incident: %w", err)
	}
	return nil
}

func (s *Service) getActorIdByUserId(ctx context.Context, userId string) (string, error) {
	resp, getErr := s.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: userId,
	})
	if err := epifigrpc.RPCError(resp, getErr); err != nil {
		logger.Error(ctx, "error while getting actor by user id", zap.Error(err), zap.String(logger.USER_ID, userId))
		return "", fmt.Errorf("error while getting actor by user id: %w", err)
	}
	return resp.GetActor().GetId(), nil
}

func doesCbtContainUtr(cbt *savingsPb.ClosedAccountBalanceTransfer) bool {
	return cbt.GetUtr() != "" || cbt.GetTransactionStatus() != savingsPb.CbtTransactionStatus_CBT_TRANSACTION_STATUS_UNSPECIFIED
}

func getMoneyFromFloat(amount float32) *money.Money {
	iPart := int64(amount)
	var decPart string
	if amount-float32(iPart) != 0 {
		decPart = fmt.Sprintf("%.2g", amount-float32(iPart))[2:]
		if len(decPart) == 1 {
			decPart = decPart + "0"
		}
	}
	nano, _ := strconv.ParseInt(decPart, 10, 32)
	return &money.Money{
		CurrencyCode: pkgMoney.RupeeCurrencyCode,
		Units:        iPart,
		Nanos:        int32(nano) * ********,
	}
}

func getTicketFilters(actorIds []string) *ticketPb.TicketFilters {
	return &ticketPb.TicketFilters{
		StatusList:          ticketPb.GetActiveStatusList(),
		ProductCategoryList: []ticketPb.ProductCategory{ticketPb.ProductCategory_PRODUCT_CATEGORY_ACCOUNTS},
		ActorIdList:         actorIds,
	}
}

func (s *Service) resolveTickets(ctx context.Context, actorId []string, utr string, balance ticketPb.SavingsAccountBalance) error {

	resp, err := s.ticketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: getTicketFilters(actorId),
		PageContextRequest: &rpcPb.PageContextRequest{
			Token: &rpcPb.PageContextRequest_AfterToken{
				AfterToken: "",
			},
			PageSize: 50,
		},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if resp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "no active tickets found")
			return nil
		}
		logger.Info(ctx, "error while fetching tickets from cx", zap.Error(te))
		return fmt.Errorf("error while fetching tickets from cx: %w", te)

	}
	for _, ticket := range resp.GetTickets() {

		if ticket.GetCustomFieldWithValue().GetSubCategory() == subCategory {
			if !lo.Contains(ticket.GetTags(), autoId) {
				ticketres, err := s.ticketClient.UpdateTicketAsync(ctx, &ticketPb.UpdateTicketAsyncRequest{
					Ticket: &ticketPb.Ticket{
						Id:     ticket.GetId(),
						Status: ticketPb.Status_STATUS_RESOLVED,
						Tags:   []string{"Auto Resolved"},
						CustomFields: &ticketPb.CustomFields{
							Utr:                   utr,
							SavingsAccountBalance: balance,
						},
					},
				})
				if te := epifigrpc.RPCError(ticketres, err); te != nil {
					logger.Error(ctx, "error in updating ticket status", zap.Error(te))
					return fmt.Errorf("error while updating ticket status: %w", te)
				}
			}
		}
	}
	return nil
}
