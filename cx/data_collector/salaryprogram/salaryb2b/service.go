package salaryb2b

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"fmt"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	authPb "github.com/epifi/gamma/api/auth"
	commsPb "github.com/epifi/gamma/api/comms"
	salaryb2bPb "github.com/epifi/gamma/api/cx/data_collector/salaryprogram/salaryb2b"
	vgLeadsquaredPb "github.com/epifi/gamma/api/vendorgateway/crm/leadsquared"
	"github.com/epifi/gamma/cx/config"
	wireTypes "github.com/epifi/gamma/cx/wire/types"

	rpcPb "github.com/epifi/be-common/api/rpc"
)

type SalaryB2BService struct {
	vgLeadManagementClient            vgLeadsquaredPb.LeadManagementClient
	salaryProgramNonProdS3Client      wireTypes.SalaryProgramNonProdS3Client
	salaryProgramB2BClient            wireTypes.SalaryProgramB2BS3Client
	authClient                        authPb.AuthClient
	commsClient                       commsPb.CommsClient
	salaryProgramLeadManagementConfig *config.SalaryProgramLeadManagementConfig
}

func NewSalaryB2BService(
	vgLeadManagementClient vgLeadsquaredPb.LeadManagementClient,
	salaryProgramNonProdS3Client wireTypes.SalaryProgramNonProdS3Client,
	salaryProgramB2BClient wireTypes.SalaryProgramB2BS3Client,
	authClient authPb.AuthClient,
	commsClient commsPb.CommsClient,
	salaryProgramLeadManagementConfig *config.SalaryProgramLeadManagementConfig,
) *SalaryB2BService {
	return &SalaryB2BService{
		vgLeadManagementClient:            vgLeadManagementClient,
		salaryProgramNonProdS3Client:      salaryProgramNonProdS3Client,
		salaryProgramB2BClient:            salaryProgramB2BClient,
		authClient:                        authClient,
		commsClient:                       commsClient,
		salaryProgramLeadManagementConfig: salaryProgramLeadManagementConfig,
	}
}

// compile time check to ensure Service implements leadsquared.LeadManagementServer
var _ salaryb2bPb.SalaryB2BServer = &SalaryB2BService{}

const (
	EmailAddressAttribute = "EmailAddress"
	FirstNameAttribute    = "FirstName"
	LastNameAttribute     = "LastName"
	RetryTimerSeconds     = 30
)

// nolint: funlen
func (s *SalaryB2BService) CreateLeadAndSendOTP(ctx context.Context, createLeadAndSendOTPRequest *salaryb2bPb.CreateLeadAndSendOTPRequest) (*salaryb2bPb.CreateLeadAndSendOTPResponse, error) {

	// get email address of the lead
	var emailAddress string
	for _, leadDetail := range createLeadAndSendOTPRequest.GetLeadDetails() {
		if leadDetail.GetAttribute() == EmailAddressAttribute {
			emailAddress = leadDetail.GetValue()
		}
	}

	// generate otp and map the response
	generateOtpRes, err := s.GenerateOtp(ctx, emailAddress, createLeadAndSendOTPRequest.GetOtpToken())
	if err != nil {
		logger.Error(ctx, "error in GenerateOtp rpc", zap.Error(err))
		return &salaryb2bPb.CreateLeadAndSendOTPResponse{
			Status: rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.CreateLeadAndSendOTPResponse_OTP_GENERATION_FAILURE), "failure in send otp"),
		}, nil
	}
	generateOtpRpcRes := s.MapGenerateOtpResponseToCreateLeadAndSendOTPRpcStatus(ctx, generateOtpRes)
	if !generateOtpRpcRes.IsSuccess() {
		return &salaryb2bPb.CreateLeadAndSendOTPResponse{
			Status: generateOtpRpcRes,
		}, nil

	}
	generateOtpRes.RetryTimerSeconds = RetryTimerSeconds

	// after successfully otp generation, update the lead details with vendor
	var vgLeadDetails []*vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail
	for _, leadDetail := range createLeadAndSendOTPRequest.GetLeadDetails() {
		vgLeadDetails = append(vgLeadDetails, &vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail{
			Attribute: leadDetail.GetAttribute(),
			Value:     leadDetail.GetValue(),
		})
	}
	// setting verified field as false, as by default it stays empty
	vgLeadDetails = append(vgLeadDetails, &vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail{
		Attribute: "mx_Verified",
		Value:     "false",
	})

	vgCreateOrUpdateLeadResponse, vgCreateOrUpdateLeadErr := s.vgLeadManagementClient.CreateOrUpdateLead(ctx, &vgLeadsquaredPb.CreateOrUpdateLeadRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_LEADSQUARED,
		},
		LeadDetails: vgLeadDetails,
	})
	err = epifigrpc.RPCError(vgCreateOrUpdateLeadResponse, vgCreateOrUpdateLeadErr)
	if err != nil {
		logger.Error(ctx, "error in vgLeadManagementClient CreateOrUpdateLead rpc, making entry in excel", zap.Error(err))
	}

	vgLeadDetails = append(vgLeadDetails, &vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail{
		Attribute: "LeadStage",
		Value:     "CreateLeadAndSendOTP",
	})

	errPushToExcel := s.pushLeadDetailsToExcel(ctx, vgLeadDetails)
	if errPushToExcel != nil {
		logger.SecureError(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "uploading lead details to excel failed", zap.Error(errPushToExcel), zap.Any("leadDetails", vgLeadDetails), zap.String("email", emailAddress))
	}

	return &salaryb2bPb.CreateLeadAndSendOTPResponse{
		Status:              rpc.StatusOk(),
		OtpToken:            generateOtpRes.GetToken(),
		RetryTimerInSeconds: generateOtpRes.GetRetryTimerSeconds(),
	}, nil

}

// nolint: funlen
func (s *SalaryB2BService) VerifyLeadOTP(ctx context.Context, verifyOTPRequest *salaryb2bPb.VerifyLeadOTPRequest) (*salaryb2bPb.VerifyLeadOTPResponse, error) {

	var (
		emailAddress string
		firstName    string
		lastName     string
	)
	for _, leadDetail := range verifyOTPRequest.GetLeadDetails() {
		switch leadDetail.GetAttribute() {
		case EmailAddressAttribute:
			emailAddress = leadDetail.GetValue()
		case FirstNameAttribute:
			firstName = leadDetail.GetValue()
		case LastNameAttribute:
			lastName = leadDetail.GetValue()
		}
	}

	authReq := &authPb.VerifyOtpRequest{
		Token: verifyOTPRequest.GetOtpToken(),
		Otp:   verifyOTPRequest.GetOtp(),
		Email: emailAddress,
		Device: &commontypes.Device{
			Manufacturer: "NA",
			Model:        "NA",
			HwVersion:    "NA",
			SwVersion:    "NA",
			OsApiVersion: "NA",
			DeviceId:     "NA",
			Platform:     commontypes.Platform_WEB,
		},
		Mediums: []commsPb.Medium{
			commsPb.Medium_EMAIL,
		},
	}

	verifyOtpRes, err := s.authClient.VerifyOtp(ctx, authReq)
	if err != nil {
		logger.Error(ctx, "error in invoking VerifyOtp rpc of Auth server", zap.Error(err))
		return &salaryb2bPb.VerifyLeadOTPResponse{
			Status: rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.VerifyLeadOTPResponse_FAILURE), "failure in verify otp"),
		}, nil
	}
	verifyOtpRpcResponse := s.MapVerifyOtpResponseToVerifyOTPRpcStatus(ctx, verifyOtpRes)

	if verifyOtpRpcResponse.GetCode() == uint32(salaryb2bPb.VerifyLeadOTPResponse_OTP_INCORRECT_LOCKED) {
		err = s.SendWorkEmailNotVerifiedMail(ctx, emailAddress, firstName+" "+lastName)
		if err != nil {
			logger.Error(ctx, "Error in sending email", zap.Error(err))
		}
	}
	if !verifyOtpRpcResponse.IsSuccess() {
		return &salaryb2bPb.VerifyLeadOTPResponse{
			Status: verifyOtpRpcResponse,
		}, nil
	}

	var vgLeadDetails []*vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail
	vgLeadDetails = append(vgLeadDetails, &vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail{
		Attribute: EmailAddressAttribute,
		Value:     emailAddress,
	},
		&vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail{
			Attribute: "mx_Verified",
			Value:     "true",
		},
		&vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail{
			Attribute: "mx_Date_of_Verification",
			Value:     time.Now().Format("2006-01-02 15:04:05"),
		},
	)

	vgCreateOrUpdateLeadResponse, vgCreateOrUpdateLeadErr := s.vgLeadManagementClient.CreateOrUpdateLead(ctx, &vgLeadsquaredPb.CreateOrUpdateLeadRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_LEADSQUARED,
		},
		LeadDetails: vgLeadDetails,
	})
	err = epifigrpc.RPCError(vgCreateOrUpdateLeadResponse, vgCreateOrUpdateLeadErr)
	if err == nil {
		err = s.SendOtpVerifiedSlotNotBookedMail(ctx, emailAddress, firstName+" "+lastName)
		if err != nil {
			logger.Error(ctx, "Error in sending email", zap.Error(err))
		}
		return &salaryb2bPb.VerifyLeadOTPResponse{
			Status: rpc.StatusOk(),
		}, nil
	}

	logger.Error(ctx, "error in vgLeadManagementClient CreateOrUpdateLead rpc, making enrty in excel", zap.Error(err))

	// pushing lead details to excel in case of vendor failure
	vgLeadDetails = append(vgLeadDetails, &vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail{
		Attribute: "LeadStage",
		Value:     "VerifyOTP",
	})
	err = s.pushLeadDetailsToExcel(ctx, vgLeadDetails)
	if err != nil {
		logger.Error(ctx, "uploading lead details to excel failed", zap.Error(err), zap.Any("leadDetails", vgLeadDetails))
		return &salaryb2bPb.VerifyLeadOTPResponse{
			Status: rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.VerifyLeadOTPResponse_FAILURE), fmt.Sprintf("uploading lead details to excel failed %s", err.Error())),
		}, nil
	}

	err = s.SendOtpVerifiedSlotNotBookedMail(ctx, emailAddress, firstName+" "+lastName)
	if err != nil {
		logger.Error(ctx, "Error in sending email", zap.Error(err))
	}
	return &salaryb2bPb.VerifyLeadOTPResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *SalaryB2BService) SendDemoBookingMail(ctx context.Context, sendBookingConfirmationMailRequest *salaryb2bPb.SendDemoBookingMailRequest) (*salaryb2bPb.SendDemoBookingMailResponse, error) {

	var (
		emailAddress string
		firstName    string
		lastName     string
	)
	for _, leadDetail := range sendBookingConfirmationMailRequest.GetLeadDetails() {
		switch leadDetail.GetAttribute() {
		case EmailAddressAttribute:
			emailAddress = leadDetail.GetValue()
		case FirstNameAttribute:
			firstName = leadDetail.GetValue()
		case LastNameAttribute:
			lastName = leadDetail.GetValue()
		}
	}

	if sendBookingConfirmationMailRequest.GetEmail() == salaryb2bPb.SendDemoBookingMailRequest_OTP_VERIFIED_SLOT_NOT_BOOKED {
		err := s.SendOtpVerifiedSlotBookedMail(ctx, emailAddress, firstName+" "+lastName)
		if err != nil {
			logger.Error(ctx, "Error in sending email", zap.Error(err))
			return &salaryb2bPb.SendDemoBookingMailResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}

	return &salaryb2bPb.SendDemoBookingMailResponse{
		Status: rpc.StatusOk(),
	}, nil

}

// nolint: dupl
func (s *SalaryB2BService) SendOtpVerifiedSlotNotBookedMail(ctx context.Context, emailId string, name string) error {
	const (
		fromEmailId   = "<EMAIL>"
		fromEmailName = "Fi"
	)

	emailMsg := &commsPb.SendMessageRequest_Email{
		Email: &commsPb.EmailMessage{
			FromEmailId:   fromEmailId,
			FromEmailName: fromEmailName,
			EmailOption: &commsPb.EmailOption{
				Option: &commsPb.EmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption{
					SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOption: &commsPb.SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOption{
						EmailType: commsPb.EmailType_SALARY_B2B_LEAD_OTP_VERIFIED_SLOT_NOT_BOOKED,
						Option: &commsPb.SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOption_SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOptionV1{
							SalaryB_2BLeadOtpVerifiedSlotNotBookedEmailOptionV1: &commsPb.SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOptionV1{
								Name:            name,
								TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							},
						},
					},
				},
			},
		},
	}
	emailReq := &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{EmailId: emailId},
		Message:        emailMsg,
	}
	res, err := s.commsClient.SendMessage(ctx, emailReq)
	// handle error to record metrics
	if e := epifigrpc.RPCError(res, err); e != nil {
		return e
	}

	return nil
}

// nolint: dupl
func (s *SalaryB2BService) SendWorkEmailNotVerifiedMail(ctx context.Context, emailId string, name string) error {
	const (
		fromEmailId   = "<EMAIL>"
		fromEmailName = "Fi"
	)

	emailMsg := &commsPb.SendMessageRequest_Email{
		Email: &commsPb.EmailMessage{
			FromEmailId:   fromEmailId,
			FromEmailName: fromEmailName,
			EmailOption: &commsPb.EmailOption{
				Option: &commsPb.EmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOption{
					SalaryB_2BLeadWorkEmailNotVerifiedEmailOption: &commsPb.SalaryB2BLeadWorkEmailNotVerifiedEmailOption{
						EmailType: commsPb.EmailType_SALARY_B2B_LEAD_WORK_EMAIL_NOT_VERIFIED,
						Option: &commsPb.SalaryB2BLeadWorkEmailNotVerifiedEmailOption_SalaryB_2BLeadWorkEmailNotVerifiedEmailOptionV1{
							SalaryB_2BLeadWorkEmailNotVerifiedEmailOptionV1: &commsPb.SalaryB2BLeadWorkEmailNotVerifiedEmailOptionV1{
								Name:            name,
								TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							},
						},
					},
				},
			},
		},
	}
	emailReq := &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{EmailId: emailId},
		Message:        emailMsg,
	}
	res, err := s.commsClient.SendMessage(ctx, emailReq)
	// handle error to record metrics
	if e := epifigrpc.RPCError(res, err); e != nil {
		return e
	}

	return nil
}

// nolint: dupl
func (s *SalaryB2BService) SendOtpVerifiedSlotBookedMail(ctx context.Context, emailId string, name string) error {
	const (
		fromEmailId   = "<EMAIL>"
		fromEmailName = "Fi"
	)

	emailMsg := &commsPb.SendMessageRequest_Email{
		Email: &commsPb.EmailMessage{
			FromEmailId:   fromEmailId,
			FromEmailName: fromEmailName,
			EmailOption: &commsPb.EmailOption{
				Option: &commsPb.EmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption{
					SalaryB_2BLeadOtpVerifiedSlotBookedEmailOption: &commsPb.SalaryB2BLeadOTPVerifiedSlotBookedEmailOption{
						EmailType: commsPb.EmailType_SALARY_B2B_LEAD_OTP_VERIFIED_SLOT_BOOKED,
						Option: &commsPb.SalaryB2BLeadOTPVerifiedSlotBookedEmailOption_SalaryB_2BLeadOtpVerifiedSlotBookedEmailOptionV1{
							SalaryB_2BLeadOtpVerifiedSlotBookedEmailOptionV1: &commsPb.SalaryB2BLeadOTPVerifiedSlotBookedEmailOptionV1{
								Name:            name,
								TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
							},
						},
					},
				},
			},
		},
	}
	emailReq := &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{EmailId: emailId},
		Message:        emailMsg,
	}
	res, err := s.commsClient.SendMessage(ctx, emailReq)
	// handle error to record metrics
	if e := epifigrpc.RPCError(res, err); e != nil {
		return e
	}

	return nil
}

func (s *SalaryB2BService) MapVerifyOtpResponseToVerifyOTPRpcStatus(_ context.Context, verifyOtpResponse *authPb.VerifyOtpResponse) *rpc.Status {

	switch {
	case verifyOtpResponse.GetStatus().IsSuccess():
		return rpc.StatusOk()
	case verifyOtpResponse.GetStatus().GetCode() == uint32(authPb.VerifyOtpResponse_TOKEN_EXPIRY):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.VerifyLeadOTPResponse_OTP_EXPIRED), "given otp token expired")
	case verifyOtpResponse.GetStatus().GetCode() == uint32(authPb.VerifyOtpResponse_OTP_VERIFY_LIMIT_EXCEEDED):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.VerifyLeadOTPResponse_OTP_VERIFY_LIMIT_EXCEEDED), "Too many verification attempts on the token. Generate new token")
	case verifyOtpResponse.GetStatus().GetCode() == uint32(authPb.VerifyOtpResponse_OTP_INCORRECT):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.VerifyLeadOTPResponse_OTP_INCORRECT), "Incorrect OTP")
	case verifyOtpResponse.GetStatus().GetCode() == uint32(authPb.VerifyOtpResponse_OTP_INCORRECT_LAST_ATTEMPT):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.VerifyLeadOTPResponse_OTP_INCORRECT_LAST_ATTEMPT), "Incorrect OTP last attempt left")
	case verifyOtpResponse.GetStatus().GetCode() == uint32(authPb.VerifyOtpResponse_OTP_INCORRECT_LOCKED):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.VerifyLeadOTPResponse_OTP_INCORRECT_LOCKED), "Incorrect OTP, no more attempts left")
	default:
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.VerifyLeadOTPResponse_FAILURE), "failure in verify otp")
	}

}

func (s *SalaryB2BService) GenerateOtp(ctx context.Context, email string, token string) (*authPb.GenerateOtpResponse, error) {

	authReq := &authPb.GenerateOtpRequest{
		Token: token,
		Email: email,
		Device: &commontypes.Device{
			Manufacturer: "NA",
			Model:        "NA",
			HwVersion:    "NA",
			SwVersion:    "NA",
			OsApiVersion: "NA",
			DeviceId:     "NA",
			Platform:     commontypes.Platform_WEB,
		},
		GenerateOtpFlow: authPb.GenerateOTPFlow_GENERATE_OTP_SALARY_B2B_LEAD_CAPTURE_FLOW,
		Mediums: []commsPb.Medium{
			commsPb.Medium_EMAIL,
		},
	}
	authRes, err := s.authClient.GenerateOtp(ctx, authReq)
	if err != nil {
		logger.Error(ctx, "Error in invoking GenerateOtp of Auth server", zap.Error(err))
		return nil, fmt.Errorf("error in invoking GenerateOtp of Auth server, err: %w", err)
	}

	return authRes, nil
}

func (s *SalaryB2BService) MapGenerateOtpResponseToCreateLeadAndSendOTPRpcStatus(_ context.Context, generateOtpResponse *authPb.GenerateOtpResponse) *rpc.Status {

	switch {
	case generateOtpResponse.GetStatus().IsSuccess():
		return rpc.StatusOk()
	case generateOtpResponse.GetStatus().GetCode() == uint32(codes.ResourceExhausted):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.CreateLeadAndSendOTPResponse_GENERATE_OTP_ATTEMPTS_EXHAUSTED), "generate otp attempts exhausted for user")
	case generateOtpResponse.GetStatus().GetCode() == uint32(authPb.GenerateOtpResponse_RESEND_LIMIT):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.CreateLeadAndSendOTPResponse_RESEND_OTP_ATTEMPTS_EXCEEDED), "retry attempts exceeded for user")
	case generateOtpResponse.GetStatus().GetCode() == uint32(authPb.GenerateOtpResponse_TOKEN_EXPIRY):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.CreateLeadAndSendOTPResponse_OTP_EXPIRED), "given otp token expired")
	case generateOtpResponse.GetStatus().GetCode() == uint32(authPb.GenerateOtpResponse_RESEND_REQ_TOO_SOON):
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.CreateLeadAndSendOTPResponse_RESEND_REQ_TOO_SOON), "resend otp requested too soon")
	default:
		return rpc.NewStatusWithoutDebug(uint32(salaryb2bPb.CreateLeadAndSendOTPResponse_OTP_GENERATION_FAILURE), "failure in send otp")
	}

}

// nolint: funlen
func (s *SalaryB2BService) pushLeadDetailsToExcel(ctx context.Context, leadDetails []*vgLeadsquaredPb.CreateOrUpdateLeadRequest_LeadDetail) error {

	fileByteData, err := s.salaryProgramB2BClient.Read(ctx, s.salaryProgramLeadManagementConfig.LeadDetailsExcelSheetPathB2B)
	file, openReaderErr := excelize.OpenReader(bytes.NewReader(fileByteData))

	if openReaderErr != nil {
		return fmt.Errorf("cannot open excel file, err: %w", err)
	}

	sheet1 := "Sheet1"
	col := []string{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"}
	createdAt := "Created At"

	rows, getRowsErr := file.GetRows(sheet1)
	if getRowsErr != nil {
		logger.Error(ctx, "error in file.GetRows func", zap.Error(getRowsErr))
		return fmt.Errorf("error in file.GetRows func, err: %w", getRowsErr)
	}
	headerMap := make(map[string]int)
	headerMap[createdAt] = 0
	// make map of already existing headers in sheet
	if len(rows) > 0 {
		for idx, header := range rows[0] {
			headerMap[header] = idx
		}
	}

	// add created at as first header by default
	err = file.SetCellStr(sheet1, s.getCellPosition(1, col[headerMap[createdAt]]), createdAt)
	if err != nil {
		logger.Error(ctx, "error in SetCellStr call", zap.Error(err))
		return fmt.Errorf("error in SetCellStr call, err: %w", err)
	}

	// append new headers in lead details to the map
	for _, leadDetail := range leadDetails {
		if _, ok := headerMap[leadDetail.GetAttribute()]; !ok {
			headerMap[leadDetail.GetAttribute()] = len(headerMap)
			err = file.SetCellStr(sheet1, s.getCellPosition(1, col[headerMap[leadDetail.GetAttribute()]]), leadDetail.GetAttribute())
			if err != nil {
				logger.Error(ctx, "error in SetCellStr call", zap.Error(err))
				return fmt.Errorf("error in SetCellStr call, err: %w", err)
			}
		}
	}

	// add new entry in excel
	rows, err = file.GetRows(sheet1)
	if err != nil {
		logger.Error(ctx, "error in file.GetRows func", zap.Error(err))
		return fmt.Errorf("error in file.GetRows func, err: %w", err)
	}
	timeNow := time.Now().Format("02 Jan 2006 15:04:05")
	err = file.SetCellStr(sheet1, s.getCellPosition(len(rows)+1, col[headerMap[createdAt]]), timeNow)
	if err != nil {
		logger.Error(ctx, "error in SetCellStr call", zap.Error(err))
		return fmt.Errorf("error in SetCellStr call, err: %w", err)
	}
	for _, leadDetail := range leadDetails {
		err = file.SetCellStr(sheet1, s.getCellPosition(len(rows)+1, col[headerMap[leadDetail.GetAttribute()]]), leadDetail.GetValue())
		if err != nil {
			logger.Error(ctx, "error in updating row in excel", zap.Error(err))
			return fmt.Errorf("error in updating row in excel, err: %w", err)
		}
	}

	bytesBuffer, err := file.WriteToBuffer()
	if err != nil {
		logger.Error(ctx, "error converting file to byte buffer", zap.Error(err))
		return fmt.Errorf("error converting file to byte buffer, err: %w", err)
	}

	err = s.salaryProgramB2BClient.Write(ctx, s.salaryProgramLeadManagementConfig.LeadDetailsExcelSheetPathB2B, bytesBuffer.Bytes(), "bucket-owner-full-control")
	if err != nil {
		logger.Error(ctx, "error writing file to s3 bucket", zap.Error(err))
		return fmt.Errorf("error writing file to s3 bucket, err: %w", err)
	}

	return nil
}

func (s *SalaryB2BService) getCellPosition(row int, col string) string {
	return fmt.Sprintf("%s%d", col, row)
}

func (s *SalaryB2BService) GetFileFromBucket(ctx context.Context, req *salaryb2bPb.GetFileFromBucketRequest) (*salaryb2bPb.GetFileFromBucketResponse, error) {
	if len(req.GetS3Paths()) == 0 {
		logger.Error(ctx, "empty input s3 paths")
		return &salaryb2bPb.GetFileFromBucketResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty input s3 paths"),
		}, nil
	}
	var signedUrls []string
	for _, path := range req.GetS3Paths() {
		signedUrl, err := s.salaryProgramB2BClient.GetPreSignedUrl(ctx, path, 10*60*time.Second)
		if err != nil {
			logger.Error(ctx, "error in getting signed url", zap.Error(err))
			continue
		}
		signedUrls = append(signedUrls, signedUrl)
	}

	if len(signedUrls) == 0 {
		return &salaryb2bPb.GetFileFromBucketResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no signed urls found"),
		}, nil
	}

	return &salaryb2bPb.GetFileFromBucketResponse{
		Status:     rpcPb.StatusOk(),
		SignedUrls: signedUrls,
	}, nil
}
