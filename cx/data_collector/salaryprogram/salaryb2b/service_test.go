package salaryb2b

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	mocksS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"

	salaryb2bPb "github.com/epifi/gamma/api/cx/data_collector/salaryprogram/salaryb2b"
	genConf "github.com/epifi/gamma/cx/config/genconf"
)

// service test suite
type svcTestSuite struct {
	conf *genConf.Config
}

func newSvcTestSuite(conf *genConf.Config) *svcTestSuite {
	return &svcTestSuite{
		conf: conf,
	}
}

var (
	svcTS *svcTestSuite
)

type mockedDependencies struct {
	mockSalaryProgramB2BClient *mocksS3.MockS3Client
}

func newServerWithMocks(t *testing.T) (*SalaryB2BService, *mockedDependencies) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSalaryProgramB2BClient := mocksS3.NewMockS3Client(ctrl)
	s := NewSalaryB2BService(nil, nil, mockSalaryProgramB2BClient, nil, nil, nil)
	return s, &mockedDependencies{
		mockSalaryProgramB2BClient: mockSalaryProgramB2BClient,
	}
}

func TestSalaryB2BService_GetFileFromBucket(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx   context.Context
		req   *salaryb2bPb.GetFileFromBucketRequest
		mocks func(m *mockedDependencies)
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryb2bPb.GetFileFromBucketResponse
		wantErr bool
	}{
		{
			name: "#1 Empty s3 Path",
			args: args{
				ctx: context.Background(),
				req: &salaryb2bPb.GetFileFromBucketRequest{S3Paths: []string{}},
				mocks: func(m *mockedDependencies) {

				},
			},
			want: &salaryb2bPb.GetFileFromBucketResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty input s3 paths"),
			},
			wantErr: false,
		},
		{
			name: "#2 No Signed URLs found",
			args: args{
				ctx: context.Background(),
				req: &salaryb2bPb.GetFileFromBucketRequest{S3Paths: []string{"LeadDetailsTestIncorrect.xlsx"}},
				mocks: func(m *mockedDependencies) {
					m.mockSalaryProgramB2BClient.EXPECT().GetPreSignedUrl(gomock.Any(), gomock.Any(), gomock.Any()).Return("", fmt.Errorf("error"))
				},
			},
			want: &salaryb2bPb.GetFileFromBucketResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("no signed urls found"),
			},
			wantErr: false,
		},
		{
			name: "#3 Success in Finding Signed URLs",
			args: args{
				ctx: context.Background(),
				req: &salaryb2bPb.GetFileFromBucketRequest{S3Paths: []string{"LeadDetailsTest.xlsx"}},
				mocks: func(m *mockedDependencies) {
					m.mockSalaryProgramB2BClient.EXPECT().GetPreSignedUrl(gomock.Any(), gomock.Any(), gomock.Any()).Return("https://s3.ap-south-1.amazonaws.com/bucket-name/djd?X-Amz-Algorithm=AWS4-HMAC-SHA256", nil)
				},
			},
			want: &salaryb2bPb.GetFileFromBucketResponse{
				Status:     rpcPb.StatusOk(),
				SignedUrls: []string{"https://s3.ap-south-1.amazonaws.com/bucket-name/djd?X-Amz-Algorithm=AWS4-HMAC-SHA256"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks1 := newServerWithMocks(t)
			if tt.args.mocks != nil {
				tt.args.mocks(mocks1)
			}
			got, err := svc.GetFileFromBucket(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFileFromBucket() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFileFromBucket() got = %v, want %v", got, tt.want)
			}
		})
	}
}
