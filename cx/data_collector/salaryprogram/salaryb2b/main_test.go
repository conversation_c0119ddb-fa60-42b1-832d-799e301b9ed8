package salaryb2b

import (
	"flag"
	"os"
	"testing"

	gormv2 "gorm.io/gorm"

	genConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
)

var (
	conf *genConf.Config
	db   *gormv2.DB
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer

// nolint
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	_, conf, db, teardown = test.InitTestServer(false)

	svcTS = newSvcTestSuite(conf)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
