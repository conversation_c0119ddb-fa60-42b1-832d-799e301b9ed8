package internationalfundtransfer

import (
	"context"
	"encoding/base64"
	"fmt"
	"regexp"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

func (s *Service) DownloadFile(ctx context.Context, req *cxDcIftPb.DownloadFileRequest) (*cxDcIftPb.DownloadFileResponse, error) {
	cxLogger.Info(ctx, "downloading file", zap.String(logger.PATH, req.GetPath()))
	path := req.GetPath()
	path = strings.TrimPrefix(path, "/")
	s3FilePath := createS3FilePathFromSherlockFilePath(path)
	data, err := s.s3Client.Read(ctx, s3FilePath)
	if err != nil {
		cxLogger.Error(ctx, "error downloading file from S3", zap.String(logger.PATH, s3FilePath), zap.Error(err))
		return &cxDcIftPb.DownloadFileResponse{Status: rpc.StatusRecordNotFound()}, nil
	}
	fileName, fileExt, err := parseFileNameAndExtFromPath(req.GetPath())
	if err != nil {
		cxLogger.Error(ctx, "error getting file name and type", zap.Error(err))
		return &cxDcIftPb.DownloadFileResponse{Status: rpc.StatusInternal()}, nil
	}
	return &cxDcIftPb.DownloadFileResponse{
		Status:             rpc.StatusOk(),
		FileData:           base64.StdEncoding.EncodeToString(data),
		ContentType:        getContentType(fileExt),
		DownloadedFileName: fileName,
		ContentDisposition: inlineContentDisposition,
	}, nil
}

func createS3FilePathFromSherlockFilePath(path string) string {
	return fmt.Sprintf("%s/%s", vendorName, path)
}

func parseFileNameAndExtFromPath(path string) (string, string, error) {
	r, err := regexp.Compile(fileNameInPathRegex)
	if err != nil {
		return "", "", err
	}
	fileName := r.FindString(path)
	fileExt := strings.Split(fileName, ".")[1]
	return fileName, fileExt, nil
}

func getContentType(fileExt string) string {
	switch fileExt {
	case csvExtension:
		return csvContentTypeHTTPHeader
	case pdfExtension:
		return pdfContentTypeHTTPHeader
	default:
		return defaultContentTypeHTTPHeader
	}
}
