package internationalfundtransfer

import (
	"context"

	"github.com/epifi/be-common/api/rpc"

	casbinPb "github.com/epifi/gamma/api/casbin"
	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
)

func (s *Service) GetNavigationBarEntities(_ context.Context, req *cxDcIftPb.GetNavigationBarEntitiesRequest) (*cxDcIftPb.GetNavigationBarEntitiesResponse, error) {
	navBarEntities, defaultNavBarEntity := getAvailableNavBarEntities(req.GetHeader().GetAccessLevel())
	return &cxDcIftPb.GetNavigationBarEntitiesResponse{
		Status:              rpc.StatusOk(),
		NavBarEntities:      navBarEntities,
		DefaultNavBarEntity: defaultNavBarEntity,
	}, nil
}

func getAvailableNavBarEntities(accessLevel casbinPb.AccessLevel) ([]*cxDcIftPb.NavBarLabelValue, *cxDcIftPb.NavBarLabelValue) {
	switch accessLevel {
	case casbinPb.AccessLevel_FEDERAL_OUTWARD_REMITTER:
		return getOutwardRemitterNavBarEntries()
	case casbinPb.AccessLevel_FEDERAL_INWARD_REMITTER:
		return getInwardRemitterNavBarEntities()
	case casbinPb.AccessLevel_FEDERAL_MASTER_REMITTER, casbinPb.AccessLevel_FEDERAL_MASTER_REMITTANCE_OPS:
		outwardNavBarEntities, defaultNavBarEntity := getOutwardRemitterNavBarEntries()
		inwardNavBarEntities, _ := getInwardRemitterNavBarEntities()
		return append(outwardNavBarEntities, inwardNavBarEntities...), defaultNavBarEntity
	default:
		return nil, nil
	}
}

func getOutwardRemitterNavBarEntries() ([]*cxDcIftPb.NavBarLabelValue, *cxDcIftPb.NavBarLabelValue) {
	defaultVal := &cxDcIftPb.NavBarLabelValue{
		Label: lrsLimitCheckNavBarLabel,
		Value: cxDcIftPb.FileType_FILE_TYPE_LRS_CHECK,
	}
	val := []*cxDcIftPb.NavBarLabelValue{
		{
			Label: outwardRemittanceParentNavBarLabel,
			NestedValues: []*cxDcIftPb.NavBarLabelValue{
				{
					Label: lrsLimitCheckNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_LRS_CHECK,
				},
				{
					Label: swiftTransferNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_SWIFT_TRANSFER,
				},
				{
					Label: mt199MessageAttachmentNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_MT199_MESSAGE_ATTACHMENT,
				},
				{
					Label: refundTransferNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_REFUND_TRANSFER,
				},
				{
					Label: swiftTaxTransferNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_TAX_TTM,
				},
			},
		},
		{
			Label: reportingFilesNavBarParentLabel,
			NestedValues: []*cxDcIftPb.NavBarLabelValue{
				{
					Label: lrsReportingNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_LRS_REPORTING,
				},
				{
					Label: outwardGstReportingNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_GST_REPORTING,
				},
				{
					Label: tcsReportingNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_TCS_REPORTING,
				},
			},
		},
		{
			Label: preLaunchLrsCheckNavBarLabel,
			Value: cxDcIftPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK,
		},
	}
	return val, defaultVal
}

func getInwardRemitterNavBarEntities() ([]*cxDcIftPb.NavBarLabelValue, *cxDcIftPb.NavBarLabelValue) {
	defaultNavBarEntity := &cxDcIftPb.NavBarLabelValue{
		Label: inwardFundTransferNavBarLabel,
		Value: cxDcIftPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
	}
	val := []*cxDcIftPb.NavBarLabelValue{
		{
			Label: inwardRemittanceParentNavBarLabel,
			NestedValues: []*cxDcIftPb.NavBarLabelValue{
				{
					Label: inwardFundTransferNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
				},
				{
					Label: inwardGstReportingNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_GST_REPORTING_INWARD,
				},
				{
					Label: inwardGstFundTransferNavBarLabel,
					Value: cxDcIftPb.FileType_FILE_TYPE_INWARD_GST_TTUM,
				},
			},
		},
	}
	return val, defaultNavBarEntity
}
