package internationalfundtransfer

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	payIftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"

	"go.uber.org/zap"
)

// GetLRSCheckEssentials proxies the request to the pay/internationalfundtransfer service.
func (s *Service) GetLRSCheckEssentials(ctx context.Context, req *cxDcIftPb.GetLRSCheckEssentialsRequest) (*cxDcIftPb.GetLRSCheckEssentialsResponse, error) {
	logger.Info(ctx, "GetLRSCheckEssentials request received")
	payReq := &payIftPb.GetLRSCheckEssentialsRequest{
		FileId: req.GetFileId(),
	}
	payResp, err := s.payIftClient.GetLRSCheckEssentials(ctx, payReq)
	if rpcErr := epifigrpc.RPCError(payResp, err); rpcErr != nil {
		logger.Error(ctx, "error in GetLRSCheckEssentials API", zap.Error(rpcErr))
		return nil, rpcErr
	}

	res := &cxDcIftPb.GetLRSCheckEssentialsResponse{
		Status:             rpc.StatusOk(),
		LrsCheckEssentials: make([]*cxDcIftPb.GetLRSCheckEssentialsResponse_LRSCheckEssential, 0),
	}
	for _, essentials := range payResp.GetLrsCheckEssentials() {
		cxLrsCheckEssential := &cxDcIftPb.GetLRSCheckEssentialsResponse_LRSCheckEssential{
			Pan:                 essentials.GetPan(),
			ConsumedLrsLimit:    essentials.GetConsumedLrsLimit(),
			TransactionInfoList: make([]*cxDcIftPb.GetLRSCheckEssentialsResponse_TransactionInfo, 0),
		}

		for _, transactionInfo := range essentials.GetTransactionInfoList() {
			cxLrsCheckEssential.TransactionInfoList = append(cxLrsCheckEssential.GetTransactionInfoList(), &cxDcIftPb.GetLRSCheckEssentialsResponse_TransactionInfo{
				TransactionId:     transactionInfo.GetTransactionId(),
				BeneficiaryAmount: transactionInfo.GetBeneficiaryAmount(),
			})
		}
		res.LrsCheckEssentials = append(res.GetLrsCheckEssentials(), cxLrsCheckEssential)
	}

	return res, nil
}
