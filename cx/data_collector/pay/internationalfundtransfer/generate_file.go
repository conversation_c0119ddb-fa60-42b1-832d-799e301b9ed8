package internationalfundtransfer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/casbin"
	"github.com/epifi/gamma/api/cx"
	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	fileGeneratorPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

// nolint: funlen
func (s *Service) GenerateFile(ctx context.Context, req *cxDcIftPb.GenerateFileRequest) (*cxDcIftPb.GenerateFileResponse, error) {
	var fileEntry *fileGeneratorPb.FileGenerationAttempt
	if req.GetFileEntryId() != "" {
		cxLogger.Debug(ctx, "file entry to be fetched", zap.String(logger.CLIENT_REQUEST_ID, req.GetFileEntryId()))
		resp, err := s.fileGeneratorClient.GetFileGenerationAttempt(ctx, &fileGeneratorPb.GetFileGenerationAttemptRequest{
			Identifier:      &fileGeneratorPb.GetFileGenerationAttemptRequest_ClientRequestId{ClientRequestId: req.GetFileEntryId()},
			IsS3UrlRequired: true,
		})
		if err = epifigrpc.RPCError(resp, err); err != nil {
			cxLogger.Error(ctx, "error getting file gen attempt", zap.Error(err))
			return &cxDcIftPb.GenerateFileResponse{Status: rpc.StatusInternalWithDebugMsg("error getting file gen attempt")}, nil
		}
		fileEntry = resp.FileGenerationAttempt
	} else {
		agentName := strings.Split(req.GetHeader().GetAgentEmail(), "@")[0]
		clientRequestId := getClientRequestId(agentName, req.GetFileType())
		var err error
		switch req.GetFileType() {
		case cxDcIftPb.FileType_FILE_TYPE_LRS_CHECK:
			fileEntry, err = s.generateLrsCheckFile(ctx, clientRequestId, fileGeneratorPb.FileType_FILE_TYPE_LRS_CHECK)
		case cxDcIftPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK:
			fileEntry, err = s.generateLrsCheckFile(ctx, clientRequestId, fileGeneratorPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK)
		case cxDcIftPb.FileType_FILE_TYPE_SOF_CHECK:
			fileEntry, err = s.generateSofCheckFile(ctx, clientRequestId)
		case cxDcIftPb.FileType_FILE_TYPE_SWIFT_TRANSFER:
			if !s.isSwiftFileGenerationAllowed(ctx) {
				return &cxDcIftPb.GenerateFileResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("file is being processed, please wait"))}, nil
			}
			fileEntry, err = s.generateSwiftTransferFile(ctx, clientRequestId)
		case cxDcIftPb.FileType_FILE_TYPE_REFUND_TRANSFER:
			fileEntry, err = s.generateRefundTransferFile(ctx, clientRequestId)
		case cxDcIftPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER:
			fileEntry, err = s.generateInwardFundTransferFile(ctx, clientRequestId)
		case cxDcIftPb.FileType_FILE_TYPE_LRS_REPORTING:
			fileEntry, err = s.generateLrsReportingFile(ctx, clientRequestId)
		case cxDcIftPb.FileType_FILE_TYPE_TAX_TTM:
			fileEntry, err = s.generateTaxTTMReportingFile(ctx, clientRequestId)
		case cxDcIftPb.FileType_FILE_TYPE_GST_REPORTING:
			fileEntry, err = s.generateGSTReportingFile(ctx, clientRequestId)
		case cxDcIftPb.FileType_FILE_TYPE_TCS_REPORTING:
			fileEntry, err = s.generateTCSReportingFile(ctx, clientRequestId)
		case cxDcIftPb.FileType_FILE_TYPE_GST_REPORTING_INWARD:
			fileEntry, err = s.generateGSTReportingInwardFile(ctx, clientRequestId)
		default:
			return &cxDcIftPb.GenerateFileResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("invalid request for generate file: %v", req.GetFileType()))}, nil
		}
		if err != nil {
			cxLogger.Error(ctx, "error generating file", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.String(logger.FILE_TYPE, req.GetFileType().String()))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				// handling record-not-found errors gracefully
				return &cxDcIftPb.GenerateFileResponse{
					Status:     rpc.StatusOk(),
					LabelValue: getRecordNotFoundLabel(),
				}, nil
			}
			return &cxDcIftPb.GenerateFileResponse{Status: rpc.StatusInternalWithDebugMsg("error generating file")}, nil
		}
	}
	// graceful handling if file entry is nil
	if fileEntry == nil {
		cxLogger.Error(ctx, "file entry is nil")
		return &cxDcIftPb.GenerateFileResponse{Status: rpc.StatusOk()}, nil
	}
	cxLogger.Debug(ctx, "file entry", zap.Any("file entry", fileEntry))
	labelValues, err := s.getLabelValuesForFileEntry(ctx, req.GetHeader(), fileEntry)
	if err != nil {
		cxLogger.Error(ctx, "failed to create file generation entries", zap.Error(err))
		return &cxDcIftPb.GenerateFileResponse{Status: rpc.StatusInternalWithDebugMsg("convert error: error in generating file")}, nil
	}
	return &cxDcIftPb.GenerateFileResponse{
		Status:     rpc.StatusOk(),
		LabelValue: labelValues,
	}, nil
}

func getClientRequestId(agentName string, fileType cxDcIftPb.FileType) string {
	clientRequestIdPattern := "%s_%s_%s"
	switch fileType {
	case cxDcIftPb.FileType_FILE_TYPE_LRS_CHECK:
		return fmt.Sprintf(clientRequestIdPattern, "lrscheck", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK:
		return fmt.Sprintf(clientRequestIdPattern, "proactivelrscheck", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_SOF_CHECK:
		return fmt.Sprintf(clientRequestIdPattern, "sofcheck", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_SWIFT_TRANSFER:
		return fmt.Sprintf(clientRequestIdPattern, "swifttransfer", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER:
		return fmt.Sprintf(clientRequestIdPattern, "inwardfundtransfer", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_REFUND_TRANSFER:
		return fmt.Sprintf(clientRequestIdPattern, "refundtransfer", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_LRS_REPORTING:
		return fmt.Sprintf(clientRequestIdPattern, "lrsreporting", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_TAX_TTM:
		return fmt.Sprintf(clientRequestIdPattern, "tax", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_GST_REPORTING:
		return fmt.Sprintf(clientRequestIdPattern, "gst", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	case cxDcIftPb.FileType_FILE_TYPE_TCS_REPORTING:
		return fmt.Sprintf(clientRequestIdPattern, "tcs", agentName, getFormattedTimestampVal(time.Now().In(datetime.IST)))
	default:
		return ""
	}
}

// for time 2022-11-23T10:07:27+05:30 generated string: 20221123T100727
// Note: If changing the logic, check if similar changes are needed for A2 form
func getFormattedTimestampVal(t time.Time) string {
	ts := t.Format(time.RFC3339)
	s := strings.ReplaceAll(strings.ReplaceAll(ts, ":", ""), "-", "")
	return s[:len(s)-5]
}

func (s *Service) generateLrsCheckFile(ctx context.Context, clientRequestId string, fileType fileGeneratorPb.FileType) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateLRSCheckFile(ctx, &fileGeneratorPb.GenerateLRSCheckFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
		FileType:        fileType,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error generating LRS limit check file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateSofCheckFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateSOFCheckFile(ctx, &fileGeneratorPb.GenerateSOFCheckFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, errors.Wrap(err, "error generating SOF check file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateSwiftTransferFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateSwiftTransferFile(ctx, &fileGeneratorPb.GenerateSwiftTransferFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error generating outward SWIFT transfer file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateRefundTransferFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateRefundTransferFile(ctx, &fileGeneratorPb.GenerateRefundTransferFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error generating refund transfer file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateInwardFundTransferFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateInwardFundTransferFile(ctx, &fileGeneratorPb.GenerateInwardFundTransferFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
		FileType:        fileGeneratorPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, errors.Wrap(err, "error generating inward bulk fund transfer file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateLrsReportingFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateLRSReportingFile(ctx, &fileGeneratorPb.GenerateLRSReportingFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, errors.Wrap(err, "error generating LRS limit reporting file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateTCSReportingFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateTCSReportingFile(ctx, &fileGeneratorPb.GenerateTCSReportingFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, errors.Wrap(err, "error generating outward TCS reporting file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateGSTReportingFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateGSTReportingFile(ctx, &fileGeneratorPb.GenerateGSTReportingFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, errors.Wrap(err, "error generating outward GST reporting file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateTaxTTMReportingFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateTaxTTMFile(ctx, &fileGeneratorPb.GenerateTaxTTMFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, errors.Wrap(err, "error generating outward bulk tax payment file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) generateGSTReportingInwardFile(ctx context.Context, clientRequestId string) (*fileGeneratorPb.FileGenerationAttempt, error) {
	resp, err := s.fileGeneratorClient.GenerateGSTReportingInwardFile(ctx, &fileGeneratorPb.GenerateGSTReportingInwardFileRequest{
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		ClientRequestId: clientRequestId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, errors.Wrap(err, "error generating inward GST reporting file")
	}
	return resp.FileGenerationAttempt, nil
}

func (s *Service) isSwiftFileGenerationAllowed(ctx context.Context) bool {
	resp, err := s.fileGeneratorClient.PreCheckForSwiftFileGeneration(ctx, &fileGeneratorPb.PreCheckForSwiftFileGenerationRequest{})
	// handling gracefully since ui need not be affected and CX person can acknowledge old files
	if err = epifigrpc.RPCError(resp, err); err != nil {
		cxLogger.Error(ctx, "error checking if swift file generation is allowed", zap.Error(err))
		return false
	}
	return resp.GetAvalibleToGenerate()
}

func getRecordNotFoundLabel() []*cxDcIftPb.LabelValue {
	return []*cxDcIftPb.LabelValue{
		{
			Label: "File Generation Error",
			Value: "There are no expected records in the system to generate this file",
		},
	}
}

// To be used when we need to show only one file entry on the page in proper format
func (s *Service) getLabelValuesForFileEntry(ctx context.Context, reqHeader *cx.Header, fileEntry *fileGeneratorPb.FileGenerationAttempt) ([]*cxDcIftPb.LabelValue, error) {
	var labelValues []*cxDcIftPb.LabelValue
	fileProcessingStatus := getFileProcessingStatusForDisplay(fileEntry.GetFileStatus())
	labelValues = []*cxDcIftPb.LabelValue{
		{
			Label: "File Id",
			Value: fileEntry.ClientRequestId,
		},
		{
			Label: "File Name",
			Value: fileEntry.FileName,
		},
		{
			Label: "File Status",
			Value: fileProcessingStatus.String(),
		},
		{
			Label: "Created At",
			Value: fileEntry.CreatedAt.AsTime().In(datetime.IST).Format(time.RFC822),
		},
	}

	// if it is remittance ops role then we are not showing download url
	if isRemittanceOpsRole(reqHeader) {
		return labelValues, nil
	}

	// add download url label according to file type
	downloadUrlLabel, err := addDownloadUrlLabels(fileEntry)
	if err != nil {
		return nil, err
	}
	cxLogger.Info(ctx, "downloadUrlLabel in GenerateFile", zap.Any("downloadUrlLabel", downloadUrlLabel))

	labelValues = append(labelValues, downloadUrlLabel...)
	cxLogger.Debug(ctx, "file entry as label value", zap.Any("labelValues", labelValues))
	return labelValues, nil
}

func isRemittanceOpsRole(reqHeader *cx.Header) bool {
	return reqHeader.GetAccessLevel() == casbin.AccessLevel_FEDERAL_MASTER_REMITTANCE_OPS
}

func addDownloadUrlLabels(fileEntry *fileGeneratorPb.FileGenerationAttempt) ([]*cxDcIftPb.LabelValue, error) {
	var labelValues []*cxDcIftPb.LabelValue
	urlLinkFormat := `<a href="%s"> Download File </a>`
	var url, uploadedFileUrl string
	switch fileEntry.GetFileType() {
	case fileGeneratorPb.FileType_FILE_TYPE_LRS_CHECK, fileGeneratorPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK:
		url = fileEntry.GetFileProcessingInfo().GetLrsFileProcessingInfo().GetGeneratedFileUrl()
		uploadedFileUrl = fileEntry.GetFileProcessingInfo().GetLrsFileProcessingInfo().GetUploadedFileUrl()
	case fileGeneratorPb.FileType_FILE_TYPE_SOF_CHECK:
		url = fileEntry.GetFileProcessingInfo().GetSofFileProcessingInfo().GetGeneratedFileUrl()
	case fileGeneratorPb.FileType_FILE_TYPE_SWIFT_TRANSFER:
		url = fileEntry.GetFileProcessingInfo().GetSwiftTransferFileProcessingInfo().GetGeneratedFileUrl()
		mt199url := fmt.Sprintf(urlLinkFormat, fileEntry.GetFileProcessingInfo().GetSwiftTransferFileProcessingInfo().GetZipUrlMt199Files())
		labelValues = append(labelValues, &cxDcIftPb.LabelValue{
			Label: "MT199 Files",
			Value: mt199url,
		})
	case fileGeneratorPb.FileType_FILE_TYPE_MT199_MESSAGE_ATTACHMENT:
		url = fileEntry.GetFileProcessingInfo().GetMt199MessageAttachment().GetGeneratedFileUrl()
		roundedUsdAmount, err := money.ToString(fileEntry.GetFileProcessingInfo().GetMt199MessageAttachment().GetTotalAmountInUsd(), 2)
		if err != nil {
			return nil, errors.Wrap(err, "error converting total USD amount in MT 199 message to string")
		}
		additionalLabels := []*cxDcIftPb.LabelValue{
			{
				Label: "File Name",
				Value: fileEntry.GetFileName(),
			},
			{
				Label: "SWIFT Transfer Reference",
				Value: fileEntry.GetClientRequestId(),
			},
			{
				Label: "Broker Code-Num of transactions-USD Amount",
				// TODO(Brijesh): Fetch value from IFT after accommodating for clients with non-broker requirements too
				Value: fmt.Sprintf("AL-%d-%s",
					fileEntry.GetFileProcessingInfo().GetMt199MessageAttachment().GetNumOfTransactions(),
					roundedUsdAmount,
				),
			},
			// TODO(Brijesh): Don't show password to non-remitter roles
			{
				Label: "Password",
				Value: fileEntry.GetFileProcessingInfo().GetMt199MessageAttachment().GetPassword(),
			},
		}
		labelValues = append(labelValues, additionalLabels...)
	case fileGeneratorPb.FileType_FILE_TYPE_REFUND_TRANSFER:
		url = fileEntry.GetFileProcessingInfo().GetRefundTransferFileProcessingInfo().GetGeneratedFileUrl()
	case fileGeneratorPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER, fileGeneratorPb.FileType_FILE_TYPE_INWARD_GST_TTUM:
		url = fileEntry.GetFileProcessingInfo().GetInwardFundTransferFileProcessingInfo().GetGeneratedFileUrl()
	case fileGeneratorPb.FileType_FILE_TYPE_LRS_REPORTING:
		url = fileEntry.GetFileProcessingInfo().GetLrsReportingFileProcessingInfo().GetGeneratedFileUrl()
	case fileGeneratorPb.FileType_FILE_TYPE_TAX_TTM:
		url = fileEntry.GetFileProcessingInfo().GetTaxTtmFileProcessingInfo().GetGeneratedFileUrl()
	case fileGeneratorPb.FileType_FILE_TYPE_GST_REPORTING:
		url = fileEntry.GetFileProcessingInfo().GetGstReportingFileProcessingInfo().GetGeneratedFileUrl()
	case fileGeneratorPb.FileType_FILE_TYPE_TCS_REPORTING:
		url = fileEntry.GetFileProcessingInfo().GetTcsReportingFileProcessingInfo().GetGeneratedFileUrl()
	case fileGeneratorPb.FileType_FILE_TYPE_GST_REPORTING_INWARD:
		url = fileEntry.GetFileProcessingInfo().GetGstReportingInwardFileProcessingInfo().GetGeneratedFileUrl()
	default:
		return nil, fmt.Errorf("invalid filetype for converting file entry to label value: %s", fileEntry.GetFileType().String())
	}

	if url != "" {
		url = fmt.Sprintf(urlLinkFormat, url)
		labelValues = append(labelValues, &cxDcIftPb.LabelValue{
			Label: generatedFileUrlLabel,
			Value: url,
		})
	}
	if uploadedFileUrl != "" {
		uploadedFileUrl = fmt.Sprintf(urlLinkFormat, uploadedFileUrl)
		labelValues = append(labelValues, &cxDcIftPb.LabelValue{
			Label: acknowledgedFileUrlLabel,
			Value: uploadedFileUrl,
		})
	}
	return labelValues, nil
}
