package internationalfundtransfer

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/cx/developer/db_state"
)

// GetFiltersForFileType returns the list of filters for all file types
// Note: The label "ready to acknowledge" is used only for inward fund transfer files
// Using it on other file types is equivalent to not filtering on file generation status at all
// The labels are expected to be called only once for all file types, hence all labels are sent
// irrespective of the navigation entities
func (s *Service) GetFiltersForFileType(_ context.Context, _ *cxDcIftPb.GetFiltersForFileTypeRequest) (*cxDcIftPb.GetFiltersForFileTypeResponse, error) {
	fileProcessingStatusOptions := []string{createdLabel, acknowledgedLabel, readyToAcknowledgeLabel}
	paramList := []*db_state.ParameterMeta{
		{
			Name:            fileProcessingStatusParam,
			Label:           "File Processing State",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         fileProcessingStatusOptions,
		},
		{
			Name:            fromDateParam,
			Label:           "From Date",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            toDateParam,
			Label:           "To Date",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return &cxDcIftPb.GetFiltersForFileTypeResponse{
		Status:        rpc.StatusOk(),
		ParameterList: paramList,
	}, nil
}
