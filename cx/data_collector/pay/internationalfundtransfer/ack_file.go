package internationalfundtransfer

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	iftFileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) AcknowledgeFileEntry(ctx context.Context, req *cxDcIftPb.AcknowledgeFileEntryRequest) (*cxDcIftPb.AcknowledgeFileEntryResponse, error) {
	clientRequestId := req.Id
	cxLogger.Info(ctx, "acknowledge file", zap.String(logger.CLIENT_REQUEST_ID, clientRequestId))
	fileEntryResp, err := s.fileGeneratorClient.GetFileGenerationAttempt(ctx, &iftFileGenPb.GetFileGenerationAttemptRequest{
		Identifier:      &iftFileGenPb.GetFileGenerationAttemptRequest_ClientRequestId{ClientRequestId: clientRequestId},
		IsS3UrlRequired: false,
	})
	if err = epifigrpc.RPCError(fileEntryResp, err); err != nil {
		cxLogger.Error(ctx, "error getting file gen attempt", zap.Error(err))
		return &cxDcIftPb.AcknowledgeFileEntryResponse{Status: rpc.StatusInternalWithDebugMsg("error getting file gen attempt")}, nil
	}
	ackResp, err := s.fileGeneratorClient.AcknowledgeFileGenerationAttempt(ctx, &iftFileGenPb.AcknowledgeFileGenerationAttemptRequest{FgaEntry: fileEntryResp.GetFileGenerationAttempt()})
	if err = epifigrpc.RPCError(ackResp, err); err != nil {
		cxLogger.Error(ctx, "error acknowledging file gen attempt", zap.Error(err))
		return &cxDcIftPb.AcknowledgeFileEntryResponse{Status: rpc.StatusInternalWithDebugMsg("error acknowledging file gen attempt")}, nil
	}
	return &cxDcIftPb.AcknowledgeFileEntryResponse{Status: rpc.StatusOk()}, nil
}
