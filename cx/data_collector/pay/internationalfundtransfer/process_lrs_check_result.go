package internationalfundtransfer

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	payIftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
)

// ProcessLRSCheckResult proxies the request to the pay/internationalfundtransfer service.
func (s *Service) ProcessLRSCheckResult(ctx context.Context, req *cxDcIftPb.ProcessLRSCheckResultRequest) (*cxDcIftPb.ProcessLRSCheckResultResponse, error) {
	logger.Info(ctx, "ProcessLRSCheckResult request received")

	payReq := &payIftPb.ProcessLRSCheckResultRequest{
		FileId:                             req.GetFileId(),
		PanToTransactionLrsCheckResultList: make(map[string]*payIftPb.ProcessLRSCheckResultRequest_LRSCheckResultList),
	}

	for pan, lrsCheckResultList := range req.GetPanToTransactionLrsCheckResultList() {
		payLrsCheckResultList := make([]*payIftPb.ProcessLRSCheckResultRequest_LRSCheckResult, 0)
		for _, lrsCheckResult := range lrsCheckResultList.GetLrsCheckResults() {
			payLrsCheckResultList = append(payLrsCheckResultList, &payIftPb.ProcessLRSCheckResultRequest_LRSCheckResult{
				TransactionId:  lrsCheckResult.GetTransactionId(),
				LrsCheckStatus: lrsCheckResult.GetLrsCheckStatus().String(),
			})
		}
		payReq.PanToTransactionLrsCheckResultList[pan] = &payIftPb.ProcessLRSCheckResultRequest_LRSCheckResultList{
			LrsCheckResults: payLrsCheckResultList,
		}
	}

	payResp, err := s.payIftClient.ProcessLRSCheckResult(ctx, payReq)
	if rpcErr := epifigrpc.RPCError(payResp, err); rpcErr != nil {
		logger.Error(ctx, "error in ProcessLRSCheckResult API", zap.Error(rpcErr))
		return &cxDcIftPb.ProcessLRSCheckResultResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}

	res := &cxDcIftPb.ProcessLRSCheckResultResponse{
		Status: rpc.StatusOk(),
	}

	return res, nil
}
