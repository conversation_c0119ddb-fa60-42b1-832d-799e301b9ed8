package internationalfundtransfer

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	"github.com/epifi/be-common/pkg/logger"
)

// for e.g LRS check file have an additional action exposed to the agent to Generate the LRS check file,
func (s *Service) GetAdditionalActions(ctx context.Context, req *cxDcIftPb.GetAdditionalActionsRequest) (*cxDcIftPb.GetAdditionalActionsResponse, error) {
	return &cxDcIftPb.GetAdditionalActionsResponse{
		Status:  rpc.StatusOk(),
		Actions: getAdditionalActionsBasedOnFileType(ctx, req.GetFileType()),
	}, nil
}

func getAdditionalActionsBasedOnFileType(ctx context.Context, fileType cxDcIftPb.FileType) []*cxDcIftPb.ScreenActions {
	switch fileType {
	case cxDcIftPb.FileType_FILE_TYPE_LRS_CHECK, cxDcIftPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK:
		return []*cxDcIftPb.ScreenActions{
			{
				Label:          "Generate LRS Check File",
				NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
				// TODO(Brijesh): Discuss with web team to handle nil screen data values and then remove below default values
				ScreenData: &cxDcIftPb.ScreenData{
					ActionData: &cxDcIftPb.ScreenData_FileEntryScreen{
						FileEntryScreen: &cxDcIftPb.FileEntryScreenActionData{
							Label:   "",
							Message: "",
						},
					},
				},
			},
		}
	case cxDcIftPb.FileType_FILE_TYPE_SWIFT_TRANSFER:
		return []*cxDcIftPb.ScreenActions{
			{
				Label:          "Generate International Transfer File",
				NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
				ScreenData: &cxDcIftPb.ScreenData{
					ActionData: &cxDcIftPb.ScreenData_FileEntryScreen{
						FileEntryScreen: &cxDcIftPb.FileEntryScreenActionData{
							Label:   "",
							Message: "",
						},
					},
				},
			},
		}
	case cxDcIftPb.FileType_FILE_TYPE_REFUND_TRANSFER:
		return []*cxDcIftPb.ScreenActions{
			{
				Label:          "Generate Refund File",
				NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
				ScreenData: &cxDcIftPb.ScreenData{
					ActionData: &cxDcIftPb.ScreenData_FileEntryScreen{
						FileEntryScreen: &cxDcIftPb.FileEntryScreenActionData{
							Label:   "",
							Message: "",
						},
					},
				},
			},
		}
	default:
		logger.Info(ctx, "no additional actions found for file type", zap.String(logger.FILE_TYPE, fileType.String()))
		return nil
	}
}
