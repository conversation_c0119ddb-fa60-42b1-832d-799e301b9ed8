package internationalfundtransfer

import (
	"github.com/epifi/be-common/pkg/aws/v2/s3"

	payIftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	iftFileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
)

type Service struct {
	authEngine          auth_engine.IAuthEngine
	fileGeneratorClient iftFileGenPb.FileGeneratorClient
	s3Client            s3.S3Client
	payIftClient        payIftPb.InternationalFundTransferClient
	conf                *cxGenConf.Config
}

// TODO: refactor this whole file using Factory Pattern for each file type for neater impl
func NewInternationalFundTransferService(
	authEngine auth_engine.IAuthEngine,
	fgClient iftFileGenPb.FileGeneratorClient,
	s3Client s3.S3Client,
	payIftClient payIftPb.InternationalFundTransferClient,
	conf *cxGenConf.Config,
) *Service {
	return &Service{
		authEngine:          authEngine,
		fileGeneratorClient: fgClient,
		s3Client:            s3Client,
		payIftClient:        payIftClient,
		conf:                conf,
	}
}

type FileProcessingStatus int

const (
	Unspecified FileProcessingStatus = iota
	Created
	Failed
	Acknowledged
	ReadyToAcknowledge
	Invalidated
)

func (s FileProcessingStatus) String() string {
	return [...]string{"Unspecified", "Created", "Failed", "Acknowledged", "ReadyToAcknowledge", "Invalidated"}[s]
}

const (
	vendorName                   = "FEDERAL_BANK"
	csvExtension                 = "csv"
	pdfExtension                 = "pdf"
	pdfContentTypeHTTPHeader     = "application/pdf"
	csvContentTypeHTTPHeader     = "application/csv"
	defaultContentTypeHTTPHeader = "application/octet-stream"
	inlineContentDisposition     = "inline"
	fileNameInPathRegex          = `[^\/:*?"<>|\r\n]+$`

	// Screen action labels
	acknowledgeFileActionLabel = "Acknowledge File"
	enterForexRateActionLabel  = "Enter Forex Rate"
	downloadFileLabel          = "Download File"
	reDownloadFileLabel        = "Re-download File"
	computeLrsCheckLabel       = "Compute LRS Check"
	uploadFileLabel            = "Upload File"

	// Inward fund transfer nav bar labels
	inwardRemittanceParentNavBarLabel = "Inward Remittance"
	inwardFundTransferNavBarLabel     = "1. Inward TTUM Reporting"
	inwardGstReportingNavBarLabel     = "2. GST Reporting (IN)"
	inwardGstFundTransferNavBarLabel  = "3. Inward GST TTUM Reporting"

	// Outward fund transfer nav bar labels
	outwardRemittanceParentNavBarLabel = "Outward Remittance"
	lrsLimitCheckNavBarLabel           = "1. LRS Limit Check"
	swiftTransferNavBarLabel           = "2(a). SWIFT Transfer Initiation"
	mt199MessageAttachmentNavBarLabel  = "2(b). MT-199 Message Attachment"
	refundTransferNavBarLabel          = "3. Refund Transfer"
	swiftTaxTransferNavBarLabel        = "4. Tax TTM"
	reportingFilesNavBarParentLabel    = "Reporting Files"
	lrsReportingNavBarLabel            = "5. LRS Reporting"
	outwardGstReportingNavBarLabel     = "6. GST Reporting (OUT)"
	tcsReportingNavBarLabel            = "7. TCS Reporting"
	preLaunchLrsCheckNavBarLabel       = "Pre launch LRS Check"

	// File info labels
	generatedFileUrlLabel    = "Generated File URL"
	acknowledgedFileUrlLabel = "Acknowledged File URL"

	// Filter params
	fileProcessingStatusParam = "processingStatus"
	fromDateParam             = "fromDate"
	toDateParam               = "toDate"

	// Filter value labels
	createdLabel            = "Created"
	acknowledgedLabel       = "Acknowledged"
	readyToAcknowledgeLabel = "ReadyToAcknowledge"

	// Table column values
	// Note: These names should match the JSON names
	// of the corresponding TableDataEntry type protobuf fields.
	// E.g., "number_of_pans" is the JSON name of the corresponding protobuf field in LrsCheckTableDataEntry message
	fileNameCol             = "file_name"
	fileIdCol               = "file_id"
	fileStatusCol           = "file_status"
	createdAtCol            = "created_at"
	numberOfPansCol         = "number_of_pans"
	lrsCheckStatusCol       = "lrs_check_status"
	numberOfEntriesCol      = "number_of_entries"
	numberOfTransactionsCol = "number_of_transactions"
	forexRateApplicableCol  = "forex_rate"
	totalAmountCol          = "total_amount"
	dealIdCol               = "deal_id"
	swiftTxnId              = "swift_transaction_id"
)

func getFileProcessingStatusForDisplay(fgaStatus iftFileGenPb.FileStatus) FileProcessingStatus {
	switch fgaStatus {
	case iftFileGenPb.FileStatus_FILE_STATUS_CREATION_FAILED,
		iftFileGenPb.FileStatus_FILE_STATUS_UPLOAD_FAILED:
		return Failed
	case iftFileGenPb.FileStatus_FILE_STATUS_UPLOAD_SUCCESSFUL:
		return Created
	case iftFileGenPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED:
		return Acknowledged
	case iftFileGenPb.FileStatus_FILE_STATUS_INVALID:
		return Invalidated
	default:
		return Unspecified
	}
}
