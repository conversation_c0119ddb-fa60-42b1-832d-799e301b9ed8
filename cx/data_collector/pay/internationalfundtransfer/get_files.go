// nolint: dupl
package internationalfundtransfer

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/cx"
	cxDcIftPb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	dbState "github.com/epifi/gamma/api/cx/developer/db_state"
	iftFileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

func (s *Service) GetFileEntries(ctx context.Context, req *cxDcIftPb.GetFileEntriesRequest) (*cxDcIftPb.GetFileEntriesResponse, error) {
	fileStatuses, startTime, endTime := parseFileEntriesFilter(req.GetFilters(), req.GetFileType())
	resp, err := s.fileGeneratorClient.GetFileGenerationAttempts(ctx, &iftFileGenPb.GetFileGenerationAttemptsRequest{
		PageContext: req.GetPageContextRequest(),
		FileType:    getFileGeneratorFileType(req.GetFileType()),
		FileStatus:  fileStatuses,
		StartTime:   startTime,
		EndTime:     endTime,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		cxLogger.Error(ctx, "error getting file gen attempts", zap.Error(err))
		return &cxDcIftPb.GetFileEntriesResponse{Status: rpc.StatusInternalWithDebugMsg("error getting file gen attempts")}, nil
	}
	fileEntriesTable, err := s.getFileEntriesTableFromFileGenerationAttempts(ctx, resp.GetFileGenerationAttempts(), req.GetHeader())
	if err != nil {
		cxLogger.Error(ctx, "error converting file gen attempts to file entries", zap.Error(err))
		return &cxDcIftPb.GetFileEntriesResponse{Status: rpc.StatusInternalWithDebugMsg("error converting file gen attempts to file entries")}, nil
	}
	return &cxDcIftPb.GetFileEntriesResponse{
		Status:              rpc.StatusOk(),
		FileEntriesTable:    fileEntriesTable,
		PageContextResponse: resp.GetPageContext(),
	}, nil
}

func parseFileEntriesFilter(filters []*dbState.Filter, fileType cxDcIftPb.FileType) ([]iftFileGenPb.FileStatus, *timestampPb.Timestamp, *timestampPb.Timestamp) {
	var (
		selectedFileStatuses []string
		startTime, endTime   *timestampPb.Timestamp
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case fileProcessingStatusParam:
			selectedFileStatuses = filter.GetMultiSelectDropdownFilter().GetDropdownValues()
		case fromDateParam:
			startTime = filter.GetTimestamp()
		case toDateParam:
			endTime = filter.GetTimestamp()
		}
	}
	// if there are no filters selected, then show all possible file statuses
	var fileStatuses []iftFileGenPb.FileStatus
	if len(selectedFileStatuses) > 0 {
		fileStatuses = getFileStatusesForFilter(fileType, selectedFileStatuses)
	} else {
		fileStatuses = getDefaultFileStatuses(fileType)
	}
	return fileStatuses, startTime, endTime
}

func getFileStatusesForFilter(fileType cxDcIftPb.FileType, selectedFileStatuses []string) []iftFileGenPb.FileStatus {
	fileStatuses := make([]iftFileGenPb.FileStatus, 0)
	for _, selectedFileStatus := range selectedFileStatuses {
		if fileType == cxDcIftPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER || fileType == cxDcIftPb.FileType_FILE_TYPE_INWARD_GST_TTUM {
			// selectedFileStatus for inward are different
			switch selectedFileStatus {
			case createdLabel:
				fileStatuses = append(fileStatuses, iftFileGenPb.FileStatus_FILE_STATUS_CREATION_INITIATED)
			case acknowledgedLabel:
				fileStatuses = append(fileStatuses, iftFileGenPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED)
			case readyToAcknowledgeLabel:
				fileStatuses = append(fileStatuses, iftFileGenPb.FileStatus_FILE_STATUS_UPLOAD_SUCCESSFUL)
			}
		} else {
			switch selectedFileStatus {
			case createdLabel:
				fileStatuses = append(fileStatuses, iftFileGenPb.FileStatus_FILE_STATUS_UPLOAD_SUCCESSFUL)
			case acknowledgedLabel:
				fileStatuses = append(fileStatuses, iftFileGenPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED)
			}
		}
	}
	return fileStatuses
}

func getDefaultFileStatuses(fileType cxDcIftPb.FileType) []iftFileGenPb.FileStatus {
	if fileType == cxDcIftPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER || fileType == cxDcIftPb.FileType_FILE_TYPE_INWARD_GST_TTUM {
		return []iftFileGenPb.FileStatus{
			iftFileGenPb.FileStatus_FILE_STATUS_CREATION_INITIATED,
			iftFileGenPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED,
			iftFileGenPb.FileStatus_FILE_STATUS_UPLOAD_SUCCESSFUL,
			iftFileGenPb.FileStatus_FILE_STATUS_INVALID,
		}
	}
	return []iftFileGenPb.FileStatus{
		iftFileGenPb.FileStatus_FILE_STATUS_UPLOAD_SUCCESSFUL,
		iftFileGenPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED,
		iftFileGenPb.FileStatus_FILE_STATUS_INVALID,
	}
}

func (s *Service) getFileEntriesTableFromFileGenerationAttempts(ctx context.Context, fgAttempts []*iftFileGenPb.FileGenerationAttempt, reqHeader *cx.Header) (*cxDcIftPb.FileEntriesTable, error) {
	if len(fgAttempts) == 0 {
		cxLogger.Info(ctx, "no file generation attempts available for file type")
		return nil, nil
	}
	tableColumns := s.getTableColumnsBasedOnFileType(ctx, fgAttempts[0].FileType)
	tableData, err := s.getTableDataBasedOnFileType(ctx, fgAttempts, reqHeader)
	if err != nil {
		return nil, err
	}
	fileEntries := &cxDcIftPb.FileEntriesTable{
		TableColumns: tableColumns,
		TableData:    tableData,
		// sending table actions as nil since actions are defined on per file entry basis
		Actions: nil,
	}
	return fileEntries, nil
}

// nolint: funlen
func (s *Service) getTableColumnsBasedOnFileType(ctx context.Context, fileType iftFileGenPb.FileType) []*cxDcIftPb.TableColumn {
	tableColumns := []*cxDcIftPb.TableColumn{
		{
			Label:     "File Id",
			Value:     fileIdCol,
			IsVisible: false,
		},
		{
			Label:     "File Name",
			Value:     fileNameCol,
			IsVisible: true,
		},
		{
			Label:     "Created At",
			Value:     createdAtCol,
			IsVisible: true,
		},
		{
			Label:     "Status",
			Value:     fileStatusCol,
			IsVisible: true,
		},
	}
	// nolint: exhaustive
	switch fileType {
	case iftFileGenPb.FileType_FILE_TYPE_LRS_CHECK, iftFileGenPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK:
		tableColumns = append(tableColumns, &cxDcIftPb.TableColumn{
			Label:     "Number Of Pans",
			Value:     numberOfPansCol,
			IsVisible: true,
		})
		if s.conf.InternationalFundTransfer().EnableLRSCheckFromVendor() {
			logger.Info(ctx, "Adding lrs check status column")
			tableColumns = append(tableColumns, &cxDcIftPb.TableColumn{
				Label:     "LRS Check Status",
				Value:     lrsCheckStatusCol,
				IsVisible: true,
			})
		}

	case iftFileGenPb.FileType_FILE_TYPE_SOF_CHECK:
		tableColumns = append(tableColumns, &cxDcIftPb.TableColumn{
			Label:     "Number Of Entries",
			Value:     numberOfEntriesCol,
			IsVisible: true,
		})
	case iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER:
		tableColumns = append(tableColumns,
			&cxDcIftPb.TableColumn{
				Label:     "Number Of Transactions",
				Value:     numberOfTransactionsCol,
				IsVisible: true,
			},
			&cxDcIftPb.TableColumn{
				Label:     "Total Amount($)",
				Value:     totalAmountCol,
				IsVisible: true,
			},
		)
	case iftFileGenPb.FileType_FILE_TYPE_INWARD_GST_TTUM:
		tableColumns = append(tableColumns,
			&cxDcIftPb.TableColumn{
				Label:     "Number Of Transactions",
				Value:     numberOfTransactionsCol,
				IsVisible: true,
			},
		)
	case iftFileGenPb.FileType_FILE_TYPE_REFUND_TRANSFER:
		tableColumns = append(tableColumns, &cxDcIftPb.TableColumn{
			Label:     "Number Of Transactions",
			Value:     numberOfTransactionsCol,
			IsVisible: true,
		})
	case iftFileGenPb.FileType_FILE_TYPE_SWIFT_TRANSFER:
		tableColumns = append(tableColumns,
			&cxDcIftPb.TableColumn{
				Label:     "Number Of Transactions",
				Value:     numberOfTransactionsCol,
				IsVisible: true,
			},
			&cxDcIftPb.TableColumn{
				Label:     "Forex Rate Applicable",
				Value:     forexRateApplicableCol,
				IsVisible: true,
			},
			&cxDcIftPb.TableColumn{
				Label:     "Deal Id",
				Value:     dealIdCol,
				IsVisible: true,
			},
		)
	case iftFileGenPb.FileType_FILE_TYPE_MT199_MESSAGE_ATTACHMENT:
		tableColumns = append(tableColumns, &cxDcIftPb.TableColumn{
			Label:     "Swift Txn ID",
			Value:     swiftTxnId,
			IsVisible: true,
		})
	}
	cxLogger.Debug(ctx, "column type based on file type", zap.String(logger.FILE_TYPE, fileType.String()), zap.Any("tableColumns", tableColumns))
	return tableColumns
}

// nolint: exhaustive, funlen
func (s *Service) getTableDataBasedOnFileType(ctx context.Context, fgAttempts []*iftFileGenPb.FileGenerationAttempt, reqHeader *cx.Header) (*cxDcIftPb.TableData, error) {
	fileType := fgAttempts[0].FileType
	var tableData *cxDcIftPb.TableData
	switch fileType {
	case iftFileGenPb.FileType_FILE_TYPE_LRS_CHECK, iftFileGenPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_LrsTableData{
				LrsTableData: &cxDcIftPb.LrsCheckTableData{
					Entries: []*cxDcIftPb.LrsCheckTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			entry := &cxDcIftPb.LrsCheckTableDataEntry{
				FileId:     fga.GetClientRequestId(),
				FileName:   fga.GetFileName(),
				CreatedAt:  fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus: fileProcessingStatus.String(),
				Actions:    getScreenActionsForLrsReportingFile(reqHeader, fileProcessingStatus),
			}
			// nolint: exhaustive
			switch fileProcessingStatus {
			case Created, Acknowledged:
				entry.NumberOfPans = strconv.Itoa(int(fga.GetFileProcessingInfo().GetLrsFileProcessingInfo().GetNumberOfPans()))
			case Failed, Unspecified:
				continue
			}

			entry.LrsCheckStatus = "-"
			if s.conf.InternationalFundTransfer().EnableLRSCheckFromVendor() {
				switch {
				case fga.GetFileProcessingInfo().GetLrsFileProcessingInfo().GetLrsCheckResultProcessedAt() != nil:
					entry.LrsCheckStatus = "COMPLETED"
				case fga.GetFileStatus() == iftFileGenPb.FileStatus_FILE_STATUS_VENDOR_PROCESSED:
					entry.LrsCheckStatus = "PENDING"
					entry.Actions = append(entry.Actions, &cxDcIftPb.ScreenActions{
						Label:          computeLrsCheckLabel,
						NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_LRS_CHECK,
						ScreenData: &cxDcIftPb.ScreenData{
							ActionData: &cxDcIftPb.ScreenData_AcknowledgeScreen{
								AcknowledgeScreen: &cxDcIftPb.AcknowledgeScreenActionData{
									Label:   "Compute LRS Check",
									Message: "Please click on button below to compute LRS check",
								},
							},
						},
					})
				default:
					entry.LrsCheckStatus = "-"
				}
			}
			tableData.GetLrsTableData().Entries = append(tableData.GetLrsTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_SOF_CHECK:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_SofTableData{
				SofTableData: &cxDcIftPb.SofCheckTableData{
					Entries: []*cxDcIftPb.SofCheckTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			entry := &cxDcIftPb.SofCheckTableDataEntry{
				FileId:     fga.GetClientRequestId(),
				FileName:   fga.GetFileName(),
				CreatedAt:  fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus: fileProcessingStatus.String(),
				// TODO: add a function for SOF File Processing if any
				Actions: nil,
			}
			// nolint: exhaustive
			switch fileProcessingStatus {
			case Created, Acknowledged:
				entry.NumberOfEntries = strconv.Itoa(int(fga.GetFileProcessingInfo().GetSofFileProcessingInfo().GetNumberOfEntries()))
			case Failed, Unspecified:
				continue
			}
			tableData.GetSofTableData().Entries = append(tableData.GetSofTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_SWIFT_TRANSFER:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_SwiftTransferTableData{
				SwiftTransferTableData: &cxDcIftPb.SwiftTransferTableData{
					Entries: []*cxDcIftPb.SwiftTransferTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			entry := &cxDcIftPb.SwiftTransferTableDataEntry{
				FileId:             fga.GetClientRequestId(),
				FileName:           fga.GetFileName(),
				CreatedAt:          fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus:         fileProcessingStatus.String(),
				ForexRate:          toDisplayMoney(ctx, fga.GetFileProcessingInfo().GetSwiftTransferFileProcessingInfo().GetFxRate()),
				Actions:            getScreenActionsForSwiftTransferFile(reqHeader, fileProcessingStatus),
				DealId:             fga.GetFileProcessingInfo().GetSwiftTransferFileProcessingInfo().GetDealId(),
				SwiftTransactionId: fga.GetFileProcessingInfo().GetSwiftTransferFileProcessingInfo().GetSwiftTransactionId(),
			}
			// nolint: exhaustive
			switch fileProcessingStatus {
			case Created, Acknowledged, Invalidated:
				entry.NumberOfTransactions = strconv.Itoa(int(fga.GetFileProcessingInfo().GetSwiftTransferFileProcessingInfo().GetNumberOfTransactions()))
			case Failed, Unspecified:
				continue
			}
			tableData.GetSwiftTransferTableData().Entries = append(tableData.GetSwiftTransferTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER, iftFileGenPb.FileType_FILE_TYPE_INWARD_GST_TTUM:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_InwardFundTransferTableData{
				InwardFundTransferTableData: &cxDcIftPb.InwardFundTransferTableData{
					Entries: []*cxDcIftPb.InwardFundTransferTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			switch fga.GetFileStatus() {
			// represent file is created by system
			case iftFileGenPb.FileStatus_FILE_STATUS_UPLOAD_SUCCESSFUL:
				fileProcessingStatus = ReadyToAcknowledge
				// represent attempt is created by system
			case iftFileGenPb.FileStatus_FILE_STATUS_CREATION_INITIATED:
				fileProcessingStatus = Created
			}
			entry := &cxDcIftPb.InwardFundTransferTableDataEntry{
				FileId:      fga.GetClientRequestId(),
				FileName:    fga.GetFileName(),
				CreatedAt:   fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus:  fileProcessingStatus.String(),
				Actions:     getScreenActionsForInwardTransferFile(reqHeader, fileProcessingStatus),
				TotalAmount: toDisplayMoney(ctx, fga.GetFileProcessingInfo().GetInwardFundTransferFileProcessingInfo().GetTotalAmount()),
			}
			// nolint: exhaustive
			switch fileProcessingStatus {
			case Created, Invalidated:
				entry.NumberOfTransactions = "-"
			case ReadyToAcknowledge, Acknowledged:
				entry.NumberOfTransactions = strconv.Itoa(int(fga.GetFileProcessingInfo().GetInwardFundTransferFileProcessingInfo().GetNumberOfTransactions()))
			case Failed, Unspecified:
				continue
			}
			tableData.GetInwardFundTransferTableData().Entries = append(tableData.GetInwardFundTransferTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_REFUND_TRANSFER:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_RefundTransferTableData{
				RefundTransferTableData: &cxDcIftPb.RefundTransferTableData{
					Entries: []*cxDcIftPb.RefundTransferTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			entry := &cxDcIftPb.RefundTransferTableDataEntry{
				FileId:     fga.GetClientRequestId(),
				FileName:   fga.GetFileName(),
				CreatedAt:  fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus: fileProcessingStatus.String(),
				Actions:    getScreenActionsForRefundTransferFile(reqHeader, fileProcessingStatus),
			}
			switch fileProcessingStatus {
			case Created, Acknowledged:
				entry.NumberOfTransactions = strconv.Itoa(int(fga.GetFileProcessingInfo().GetRefundTransferFileProcessingInfo().GetNumberOfTransactions()))
			case Failed, Unspecified:
				continue
			}
			tableData.GetRefundTransferTableData().Entries = append(tableData.GetRefundTransferTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_LRS_REPORTING:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_LrsReportingTableData{
				LrsReportingTableData: &cxDcIftPb.LRSReportingTableData{
					Entries: []*cxDcIftPb.LRSReportingTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			entry := &cxDcIftPb.LRSReportingTableDataEntry{
				FileId:     fga.GetClientRequestId(),
				FileName:   fga.GetFileName(),
				CreatedAt:  fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus: fileProcessingStatus.String(),
				Actions:    getUserActionsForReportingFiles(reqHeader, fileProcessingStatus),
			}
			switch fileProcessingStatus {
			case Failed, Unspecified:
				continue
			}
			tableData.GetLrsReportingTableData().Entries = append(tableData.GetLrsReportingTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_MT199_MESSAGE_ATTACHMENT:
		var tableEntries []*cxDcIftPb.Mt199MessageAttachmentTableDataEntry
		tableEntryActions := []*cxDcIftPb.ScreenActions{
			{
				Label:          downloadFileLabel,
				NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
			},
		}
		for _, fga := range fgAttempts {
			entry := &cxDcIftPb.Mt199MessageAttachmentTableDataEntry{
				FileId:             fga.GetClientRequestId(),
				FileName:           fga.GetFileName(),
				FileStatus:         getFileProcessingStatusForDisplay(fga.GetFileStatus()).String(),
				CreatedAt:          fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				SwiftTransactionId: fga.GetClientRequestId(),
				Actions:            tableEntryActions,
			}
			tableEntries = append(tableEntries, entry)
		}
		return &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_Mt199MessageAttachmentTableData{
				Mt199MessageAttachmentTableData: &cxDcIftPb.Mt199MessageAttachmentTableData{
					Entries: tableEntries,
				},
			},
		}, nil
	case iftFileGenPb.FileType_FILE_TYPE_TAX_TTM:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_TaxTtmTableData{
				TaxTtmTableData: &cxDcIftPb.TaxTTMTableData{
					Entries: []*cxDcIftPb.TaxTTMTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			entry := &cxDcIftPb.TaxTTMTableDataEntry{
				FileId:     fga.GetClientRequestId(),
				FileName:   fga.GetFileName(),
				CreatedAt:  fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus: fileProcessingStatus.String(),
				Actions:    getUserActionsForReportingFiles(reqHeader, fileProcessingStatus),
			}
			switch fileProcessingStatus {
			case Failed, Unspecified:
				continue
			}
			tableData.GetTaxTtmTableData().Entries = append(tableData.GetTaxTtmTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_GstReportingTableData{
				GstReportingTableData: &cxDcIftPb.GSTReportingTableData{
					Entries: []*cxDcIftPb.GSTReportingTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			entry := &cxDcIftPb.GSTReportingTableDataEntry{
				FileId:     fga.GetClientRequestId(),
				FileName:   fga.GetFileName(),
				CreatedAt:  fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus: fileProcessingStatus.String(),
				Actions:    getUserActionsForReportingFiles(reqHeader, fileProcessingStatus),
			}
			switch fileProcessingStatus {
			case Failed, Unspecified:
				continue
			}
			tableData.GetGstReportingTableData().Entries = append(tableData.GetGstReportingTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_TCS_REPORTING:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_TcsReportingTableData{
				TcsReportingTableData: &cxDcIftPb.TCSReportingTableData{
					Entries: []*cxDcIftPb.TCSReportingTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			entry := &cxDcIftPb.TCSReportingTableDataEntry{
				FileId:     fga.GetClientRequestId(),
				FileName:   fga.GetFileName(),
				CreatedAt:  fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
				FileStatus: fileProcessingStatus.String(),
				Actions:    getUserActionsForReportingFiles(reqHeader, fileProcessingStatus),
			}
			switch fileProcessingStatus {
			case Failed, Unspecified:
				continue
			}
			tableData.GetTcsReportingTableData().Entries = append(tableData.GetTcsReportingTableData().Entries, entry)
		}
	case iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING_INWARD:
		tableData = &cxDcIftPb.TableData{
			Table: &cxDcIftPb.TableData_GstReportingInwardTableData{
				GstReportingInwardTableData: &cxDcIftPb.GSTReportingInwardTableData{
					Entries: []*cxDcIftPb.GSTReportingInwardTableDataEntry{},
				},
			},
		}
		for _, fga := range fgAttempts {
			fileProcessingStatus := getFileProcessingStatusForDisplay(fga.GetFileStatus())
			if fileProcessingStatus != Failed && fileProcessingStatus != Unspecified {
				entry := &cxDcIftPb.GSTReportingInwardTableDataEntry{
					FileId:     fga.GetClientRequestId(),
					FileName:   fga.GetFileName(),
					CreatedAt:  fga.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
					FileStatus: fileProcessingStatus.String(),
					Actions:    getUserActionsForReportingFiles(reqHeader, fileProcessingStatus),
				}
				tableData.GetGstReportingInwardTableData().Entries = append(tableData.GetGstReportingInwardTableData().Entries, entry)
			}
		}
	default:
		return nil, fmt.Errorf("invalid fileType to insert rows: %s", fileType.String())
	}
	cxLogger.Debug(ctx, "table data on file type", zap.String(logger.FILE_TYPE, fileType.String()), zap.Any("tableData", tableData))
	return tableData, nil
}

func getScreenActionsForLrsReportingFile(reqHeader *cx.Header, fileProcessingStatus FileProcessingStatus) []*cxDcIftPb.ScreenActions {
	// no file entry actions on failed files
	if fileProcessingStatus == Failed {
		return nil
	}
	tableEntryActions := []*cxDcIftPb.ScreenActions{
		{
			Label:          reDownloadFileLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
			ScreenData:     &cxDcIftPb.ScreenData{},
		},
	}

	// if remittance ops role then we should not allow user to upload lrs response
	if isRemittanceOpsRole(reqHeader) {
		return tableEntryActions
	}

	// adding acknowledge dev action on created file
	if fileProcessingStatus == Created {
		tableEntryActions = append(tableEntryActions, &cxDcIftPb.ScreenActions{
			Label:          uploadFileLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_INPUT_SCREEN,
			ScreenData: &cxDcIftPb.ScreenData{
				ActionData: &cxDcIftPb.ScreenData_InputScreen{
					InputScreen: &cxDcIftPb.InputScreenActionData{
						DevActionNames: []string{
							// DEV ACTION: INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE
							"INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE",
						},
					},
				},
			},
		})
	}
	return tableEntryActions
}

func toDisplayMoney(ctx context.Context, amt *moneyPb.Money) string {
	str, err := money.ToString(amt, 2)
	if err != nil {
		cxLogger.Error(ctx, "error while parsing forex rate", zap.Error(err))
		return "-error-parsing-"
	}
	return str
}

func getScreenActionsForSwiftTransferFile(reqHeader *cx.Header, fileProcessingStatus FileProcessingStatus) []*cxDcIftPb.ScreenActions {
	// no file entry actions on failed files
	if fileProcessingStatus == Failed {
		return nil
	}
	tableEntryActions := []*cxDcIftPb.ScreenActions{
		{
			Label:          reDownloadFileLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
			ScreenData:     &cxDcIftPb.ScreenData{},
		},
	}
	// if remittance ops role then we should not allow acknowledgement of swift transfer
	if isRemittanceOpsRole(reqHeader) {
		return tableEntryActions
	}
	// adding acknowledge dev action on swift created file
	if fileProcessingStatus == Created {
		ackSwiftTransferAction := &cxDcIftPb.ScreenActions{
			Label:          acknowledgeFileActionLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_INPUT_SCREEN,
			ScreenData: &cxDcIftPb.ScreenData{
				ActionData: &cxDcIftPb.ScreenData_InputScreen{
					InputScreen: &cxDcIftPb.InputScreenActionData{
						DevActionNames: []string{
							"INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_SWIFT_TRANSFER",
						},
					},
				},
			},
		}
		rejectTxns := &cxDcIftPb.ScreenActions{
			Label:          "Reject transactions",
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_INPUT_SCREEN,
			ScreenData: &cxDcIftPb.ScreenData{
				ActionData: &cxDcIftPb.ScreenData_InputScreen{
					InputScreen: &cxDcIftPb.InputScreenActionData{
						DevActionNames: []string{
							"IFT_REJECT_TRANSACTIONS_IN_OUTWARD_SWIFT_FILE",
						},
					},
				},
			},
		}
		tableEntryActions = append(tableEntryActions, ackSwiftTransferAction, rejectTxns)
	}
	return tableEntryActions
}

func getScreenActionsForInwardTransferFile(reqHeader *cx.Header, fileProcessingStatus FileProcessingStatus) []*cxDcIftPb.ScreenActions {
	// no file entry actions on failed files
	if fileProcessingStatus == Failed || fileProcessingStatus == Invalidated {
		return nil
	}
	tableEntryActions := make([]*cxDcIftPb.ScreenActions, 0)
	// adding acknowledge dev action on  created file
	if fileProcessingStatus == Created {

		// if remittance role then we should not allow entering forex rate while acknowledgement
		if isRemittanceOpsRole(reqHeader) {
			return tableEntryActions
		}

		tableEntryActions = append(tableEntryActions, &cxDcIftPb.ScreenActions{
			Label:          enterForexRateActionLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_INPUT_SCREEN,
			ScreenData: &cxDcIftPb.ScreenData{
				ActionData: &cxDcIftPb.ScreenData_InputScreen{
					InputScreen: &cxDcIftPb.InputScreenActionData{
						DevActionNames: []string{
							// DEV ACTION: INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER
							"INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER",
						},
					},
				},
			},
		})
	} else {
		tableEntryActions = append(tableEntryActions,
			&cxDcIftPb.ScreenActions{
				Label:          reDownloadFileLabel,
				NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
				ScreenData:     &cxDcIftPb.ScreenData{},
			})

		// if remittance role then we should not allow acknowledgement of reporting file
		if isRemittanceOpsRole(reqHeader) {
			return tableEntryActions
		}

		if fileProcessingStatus != Acknowledged {
			tableEntryActions = append(tableEntryActions, &cxDcIftPb.ScreenActions{
				Label:          acknowledgeFileActionLabel,
				NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_ACKNOWLEDGE,
				ScreenData: &cxDcIftPb.ScreenData{
					ActionData: &cxDcIftPb.ScreenData_AcknowledgeScreen{
						AcknowledgeScreen: &cxDcIftPb.AcknowledgeScreenActionData{
							Label:   "Acknowledge Reporting File",
							Message: "Please click on button below to acknowledge",
						},
					},
				},
			})
		}

	}
	return tableEntryActions
}

func getScreenActionsForRefundTransferFile(reqHeader *cx.Header, fileProcessingStatus FileProcessingStatus) []*cxDcIftPb.ScreenActions {
	// no file entry actions on failed files
	if fileProcessingStatus == Failed {
		return nil
	}
	tableEntryActions := []*cxDcIftPb.ScreenActions{
		{
			Label:          downloadFileLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
			ScreenData:     &cxDcIftPb.ScreenData{},
		},
	}
	// If remittance role, then we shouldn't permit swift file acknowledgment.
	if isRemittanceOpsRole(reqHeader) {
		return tableEntryActions
	}

	// adding acknowledge dev action on swift created file
	if fileProcessingStatus == Created {
		tableEntryActions = append(tableEntryActions, &cxDcIftPb.ScreenActions{
			Label:          acknowledgeFileActionLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_ACKNOWLEDGE,
			ScreenData: &cxDcIftPb.ScreenData{
				ActionData: &cxDcIftPb.ScreenData_AcknowledgeScreen{
					AcknowledgeScreen: &cxDcIftPb.AcknowledgeScreenActionData{
						Label:   "Acknowledge Refund Transfer File",
						Message: "Please click on button below to acknowledge",
					},
				},
			},
		})
	}
	return tableEntryActions
}

func getUserActionsForReportingFiles(reqHeader *cx.Header, fileProcessingStatus FileProcessingStatus) []*cxDcIftPb.ScreenActions {
	// no file entry actions on failed files
	if fileProcessingStatus == Failed {
		return nil
	}
	tableEntryActions := []*cxDcIftPb.ScreenActions{
		{
			Label:          downloadFileLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_FILE_ENTRY,
			// TODO(Brijesh): Discuss with web team to handle nil screen data values and then remove below default values
			ScreenData: &cxDcIftPb.ScreenData{
				ActionData: &cxDcIftPb.ScreenData_FileEntryScreen{
					FileEntryScreen: &cxDcIftPb.FileEntryScreenActionData{
						Label:   "",
						Message: "",
					},
				},
			},
		},
	}

	// If remittance role, then we shouldn't permit file generation and acknowledgment for refund file
	if isRemittanceOpsRole(reqHeader) {
		return tableEntryActions
	}

	// adding acknowledge dev action for reporting files
	if fileProcessingStatus == Created {
		tableEntryActions = append(tableEntryActions, &cxDcIftPb.ScreenActions{
			Label:          acknowledgeFileActionLabel,
			NextScreenType: cxDcIftPb.ScreenType_SCREEN_TYPE_ACKNOWLEDGE,
			ScreenData: &cxDcIftPb.ScreenData{
				ActionData: &cxDcIftPb.ScreenData_AcknowledgeScreen{
					AcknowledgeScreen: &cxDcIftPb.AcknowledgeScreenActionData{
						Label:   "Acknowledge Reporting File",
						Message: "Please click on button below to acknowledge",
					},
				},
			},
		})
	}
	return tableEntryActions
}

func getFileGeneratorFileType(fileType cxDcIftPb.FileType) iftFileGenPb.FileType {
	// TODO: move these mappings to a map
	switch fileType {
	case cxDcIftPb.FileType_FILE_TYPE_LRS_CHECK:
		return iftFileGenPb.FileType_FILE_TYPE_LRS_CHECK
	case cxDcIftPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK:
		return iftFileGenPb.FileType_FILE_TYPE_PRO_ACTIVE_LRS_CHECK
	case cxDcIftPb.FileType_FILE_TYPE_SOF_CHECK:
		return iftFileGenPb.FileType_FILE_TYPE_SOF_CHECK
	case cxDcIftPb.FileType_FILE_TYPE_SWIFT_TRANSFER:
		return iftFileGenPb.FileType_FILE_TYPE_SWIFT_TRANSFER
	case cxDcIftPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER:
		return iftFileGenPb.FileType_FILE_TYPE_INWARD_FUND_TRANSFER
	case cxDcIftPb.FileType_FILE_TYPE_REFUND_TRANSFER:
		return iftFileGenPb.FileType_FILE_TYPE_REFUND_TRANSFER
	case cxDcIftPb.FileType_FILE_TYPE_LRS_REPORTING:
		return iftFileGenPb.FileType_FILE_TYPE_LRS_REPORTING
	case cxDcIftPb.FileType_FILE_TYPE_TAX_TTM:
		return iftFileGenPb.FileType_FILE_TYPE_TAX_TTM
	case cxDcIftPb.FileType_FILE_TYPE_GST_REPORTING:
		return iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING
	case cxDcIftPb.FileType_FILE_TYPE_TCS_REPORTING:
		return iftFileGenPb.FileType_FILE_TYPE_TCS_REPORTING
	case cxDcIftPb.FileType_FILE_TYPE_GST_REPORTING_INWARD:
		return iftFileGenPb.FileType_FILE_TYPE_GST_REPORTING_INWARD
	case cxDcIftPb.FileType_FILE_TYPE_MT199_MESSAGE_ATTACHMENT:
		return iftFileGenPb.FileType_FILE_TYPE_MT199_MESSAGE_ATTACHMENT
	case cxDcIftPb.FileType_FILE_TYPE_INWARD_GST_TTUM:
		return iftFileGenPb.FileType_FILE_TYPE_INWARD_GST_TTUM
	default:
		return iftFileGenPb.FileType_FILE_TYPE_UNSPECIFIED
	}
}
