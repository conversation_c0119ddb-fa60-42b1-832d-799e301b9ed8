package p2pinvestment

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/p2pinvestment/common"
	"github.com/epifi/be-common/pkg/logger"
	p2pPkg "github.com/epifi/gamma/pkg/p2pinvestment"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxP2pPb "github.com/epifi/gamma/api/cx/data_collector/p2pinvestment"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	p2pCxPb "github.com/epifi/gamma/api/p2pinvestment/cx"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	"go.uber.org/zap"
)

type Service struct {
	authEngine            auth_engine.IAuthEngine
	p2pInvestmentCxClient p2pCxPb.CxClient
}

var _ cxP2pPb.P2PInvestmentServer = &Service{}

var (
	schemeStringToSchemeNameMap = map[string]common.DisplaySchemeName{
		"10% scheme (Booster)":   common.DisplaySchemeName_DISPLAY_SCHEME_NAME_10_BOOSTER_LOCK_IN,
		"9% scheme (Long term)":  common.DisplaySchemeName_DISPLAY_SCHEME_NAME_9_LONG_TERM_PENALTY,
		"8% scheme (Short term)": common.DisplaySchemeName_DISPLAY_SCHEME_NAME_8_SHORT_TERM_LOCK_IN,
		"7% scheme (Flexi)":      common.DisplaySchemeName_DISPLAY_SCHEME_NAME_7_FLEXI_LIQUID,
	}
	txnStringToTxnMap = map[string][]p2pPb.InvestmentTransactionType{
		"Investment": {p2pPb.InvestmentTransactionType_INVESTMENT_TRANSACTION_TYPE_INVESTMENT},
		"Withdrawal": {p2pPb.InvestmentTransactionType_INVESTMENT_TRANSACTION_TYPE_WITHDRAWAL},
		"Maturity": {p2pPb.InvestmentTransactionType_INVESTMENT_TRANSACTION_TYPE_MATURITY_REINVESTMENT_SAME_SCHEME,
			p2pPb.InvestmentTransactionType_INVESTMENT_TRANSACTION_TYPE_MATURITY_REINVESTMENT,
			p2pPb.InvestmentTransactionType_INVESTMENT_TRANSACTION_TYPE_MATURITY_WITHDRAWAL,
			p2pPb.InvestmentTransactionType_INVESTMENT_TRANSACTION_TYPE_MATURITY_PAYOUT,
			p2pPb.InvestmentTransactionType_INVESTMENT_TRANSACTION_TYPE_MATURITY_INTEREST_PAYOUT},
	}
)

func NewService(authEngine auth_engine.IAuthEngine, p2pInvestmentCxClient p2pCxPb.CxClient) *Service {
	return &Service{
		authEngine:            authEngine,
		p2pInvestmentCxClient: p2pInvestmentCxClient,
	}
}

// GetInvestmentSummary RPC to be used to get details of all the investment done by an actor
// The RPC can be used to fetch all investments/withdrawal transactions for an actor.
// nolint:funlen,dupl
func (s Service) GetInvestmentSummary(ctx context.Context, req *cxP2pPb.GetInvestmentSummaryRequest) (*cxP2pPb.GetInvestmentSummaryResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxP2pPb.GetInvestmentSummaryResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	var creationFilterDateOption *p2pCxPb.CreationDateFilterOption
	var schemeFilterOption *p2pCxPb.SchemeFilterOption
	var txnFilterOption *p2pCxPb.TransactionTypeFilterOption
	if req.GetCreationDateFilterOption() != nil {
		creationFilterDateOption = &p2pCxPb.CreationDateFilterOption{
			FromDate: req.GetCreationDateFilterOption().FromDate,
			ToDate:   req.GetCreationDateFilterOption().ToDate,
		}
	}
	if req.GetSchemeFilterOption() != nil {
		var schemeNames []p2pPb.SchemeName
		for _, sName := range req.GetSchemeFilterOption().GetSchemeNames() {
			name, ok := schemeStringToSchemeNameMap[sName]
			if !ok {
				logger.Error(ctx, "provided scheme name not supported at CX", zap.String("schemeName", sName))
				continue
			}
			schemeNames = append(schemeNames, p2pPkg.DisplaySchemeToBeSchemes[name]...)
		}
		schemeFilterOption = &p2pCxPb.SchemeFilterOption{
			SchemeNames: schemeNames,
		}
	}
	if req.GetTxnTypeFilterOption() != nil {
		var txnTypesList []p2pPb.InvestmentTransactionType
		for _, tName := range req.GetTxnTypeFilterOption().GetTransactionTypes() {
			txnTypes, ok := txnStringToTxnMap[tName]
			if !ok {
				logger.Error(ctx, "provided txn type not supported at cx data collector", zap.String("txnType", tName))
				continue
			}
			txnTypesList = append(txnTypesList, txnTypes...)
		}
		txnFilterOption = &p2pCxPb.TransactionTypeFilterOption{
			TransactionTypes: txnTypesList,
		}
	}
	p2pRes, err := s.p2pInvestmentCxClient.GetInvestmentSummary(ctx, &p2pCxPb.GetInvestmentSummaryRequest{
		ActorId:                  req.GetHeader().GetActor().GetId(),
		CreationDateFilterOption: creationFilterDateOption,
		SchemeFilterOption:       schemeFilterOption,
		TxnTypeFilterOption:      txnFilterOption,
	})
	if te := epifigrpc.RPCError(p2pRes, err); te != nil {
		if p2pRes.GetStatus().IsRecordNotFound() {
			return &cxP2pPb.GetInvestmentSummaryResponse{Status: rpcPb.StatusInternalWithDebugMsg("no p2p summary details found")}, nil
		}
		cxLogger.Error(ctx, "error while fetching p2p investment summary", zap.Error(te))
		return &cxP2pPb.GetInvestmentSummaryResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch p2p summary details")}, nil
	}

	res := &cxP2pPb.GetInvestmentSummaryResponse{Status: rpcPb.StatusOk()}
	res.InvestmentSummary = make([]*cxP2pPb.InvestmentDetails, 0)
	for _, investmentSummary := range p2pRes.GetTransactionDetails() {
		res.InvestmentSummary = append(res.InvestmentSummary, &cxP2pPb.InvestmentDetails{
			Timestamp:       investmentSummary.GetTimestamp(),
			TransactionType: investmentSummary.GetTransactionType().String(),
			Status:          investmentSummary.GetStatus().String(),
			OrderId:         investmentSummary.GetOrderId(),
			Utr:             investmentSummary.GetUtr(),
			SchemeId:        investmentSummary.GetSchemeId(),
			CreatedAt:       investmentSummary.GetCreatedAt(),
			UpdatedAt:       investmentSummary.GetUpdatedAt(),
			Eta:             investmentSummary.GetEta(),
			SubStatus:       investmentSummary.GetSubStatus().String(),
			VendorRefId:     investmentSummary.GetVendorRefId(),
		})
	}
	return res, nil
}

func (s Service) GetInvestor(ctx context.Context, req *cxP2pPb.GetInvestorRequest) (*cxP2pPb.GetInvestorResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxP2pPb.GetInvestorResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	p2pRes, err := s.p2pInvestmentCxClient.GetInvestor(ctx, &p2pCxPb.GetInvestorRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(p2pRes, err); te != nil {
		cxLogger.Error(ctx, "error while fetching p2p investor", zap.Error(te))
		return &cxP2pPb.GetInvestorResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch p2p investor details")}, nil
	}
	availableAmountLimitSchemeLevel := make([]string, 0)
	investmentEligibiltySchemeLevel := make([]string, 0)
	for schemeName, details := range p2pRes.GetInvestor().GetSchemeLevelDetails() {
		availableAmountLimitSchemeLevel = append(availableAmountLimitSchemeLevel, getAmountLimitAvailableOnSchemeLevel(schemeName, details))
		investmentEligibiltySchemeLevel = append(investmentEligibiltySchemeLevel, getInvestmentEligibilitySchemeLevel(schemeName, details))
	}
	var schemeLevelDetails []*cxP2pPb.Investor_SchemeLevelDetail

	for scheme, details := range p2pRes.GetInvestor().GetSchemeLevelDetails() {
		schemeLevelDetail := &cxP2pPb.Investor_SchemeLevelDetail{
			PlanName:                getSchemeNameToDisplay(scheme),
			IsUserEligible:          isEligible(details.GetIsUserEligible()),
			ReasonsForIneligibility: details.GetReasonsForIneligibility(),
			LimitLeft:               money.ToDisplayString(details.GetAmountLimitAvailableForInvestment()),
			UnlockedLimit:           money.ToDisplayString(details.GetUnlockedLimit()),
		}
		schemeLevelDetails = append(schemeLevelDetails, schemeLevelDetail)
	}

	res := &cxP2pPb.GetInvestorResponse{
		Status: rpcPb.StatusOk(),
		Investor: &cxP2pPb.Investor{
			HasUserCheckedEligibility: p2pRes.GetInvestor().GetHasUserCheckedEligibility(),
			IsEligible:                p2pRes.GetInvestor().GetIsEligible(),
			IneligibilityReasons:      getIneligibilityReasons(p2pRes.GetInvestor().GetIneligibilityReasons()),
			NumSlotsConsumed:          p2pRes.GetInvestor().GetNumSlotsConsumed(),
			NumSlotsAvailable:         p2pRes.GetInvestor().GetNumSlotsAvailable(),
			InvestorId:                p2pRes.GetInvestor().GetInvestorId(),
			OverallLimitAvailable:     money.ToDisplayString(p2pRes.GetInvestor().GetOverallLimitAvailable()),
			SchemeLevelDetails:        schemeLevelDetails,
		},
	}
	logger.Debug(ctx, "cx_investor_resp", zap.Any("cx_investor_resp", res))
	return res, nil
}

func (s Service) GetFilterValuesForJumpInvestmentSummary(ctx context.Context,
	req *cxP2pPb.GetFilterValuesForJumpInvestmentSummaryRequest) (*cxP2pPb.GetFilterValuesForJumpInvestmentSummaryResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &cxP2pPb.GetFilterValuesForJumpInvestmentSummaryResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	var schemeNamesForDisplay []string
	var transactionTypesForDisplay []string
	for schemeString := range schemeStringToSchemeNameMap {
		schemeNamesForDisplay = append(schemeNamesForDisplay, schemeString)
	}
	for txnString := range txnStringToTxnMap {
		transactionTypesForDisplay = append(transactionTypesForDisplay, txnString)
	}

	return &cxP2pPb.GetFilterValuesForJumpInvestmentSummaryResponse{
		Status:           rpcPb.StatusOk(),
		SchemeNames:      schemeNamesForDisplay,
		TransactionTypes: transactionTypesForDisplay,
	}, nil
}

func getIneligibilityReasons(reasons []p2pPb.IneligibilityReason) []string {
	res := make([]string, 0)
	for _, reason := range reasons {
		res = append(res, reason.String())
	}
	return res
}

func getAmountLimitAvailableOnSchemeLevel(schemeName string, details *p2pCxPb.GetInvestorResponse_InvestorForCX_SchemeLevelDetails) string {
	return fmt.Sprintf("scheme name: %v, amount limit available for investment: %v", schemeName, details.GetAmountLimitAvailableForInvestment())
}

func getInvestmentEligibilitySchemeLevel(schemeName string, details *p2pCxPb.GetInvestorResponse_InvestorForCX_SchemeLevelDetails) string {
	return fmt.Sprintf("scheme name: %v, investment eligibility: %v", schemeName, details.GetInvestmentEligibility())
}

func isEligible(isUserEligilble bool) string {
	switch isUserEligilble {
	case true:
		return "Yes"
	case false:
		return "No"
	}
	return ""
}

func getSchemeNameToDisplay(schemeName string) string {
	switch schemeName {
	case p2pPb.SchemeName_SCHEME_NAME_LL_FLEXI.String():
		return "Flexi(7%)"
	case p2pPb.SchemeName_SCHEME_NAME_LL_8_FLEXI.String():
		return "Flexi(8%)"
	case p2pPb.SchemeName_SCHEME_NAME_LL_8_25_FLEXI.String():
		return "Flexi(8.25%)"
	case p2pPb.SchemeName_SCHEME_NAME_LL_8_75_FLEXI.String():
		return "Flexi(8.75%)"
	case p2pPb.SchemeName_SCHEME_NAME_LL_SHORT_TERM.String():
		return "Short term (8%)"
	case p2pPb.SchemeName_SCHEME_NAME_LL_LONG_TERM.String():
		return "Long term(9%)"
	}
	return ""
}
