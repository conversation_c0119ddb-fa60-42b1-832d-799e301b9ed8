package risk_ops_wealth

import (
	"context"
	"errors"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	riskOpsWobPb "github.com/epifi/gamma/api/cx/data_collector/risk_ops_wealth"
	"github.com/epifi/gamma/api/persistentqueue"
	woCxPb "github.com/epifi/gamma/api/wealthonboarding/cx"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type Service struct {
	wOnbCxClient woCxPb.WealthCxServiceClient
}

func NewService(wOnbClient woCxPb.WealthCxServiceClient) *Service {
	return &Service{
		wOnbCxClient: wOnbClient,
	}
}

func (s *Service) GetWealthDataQueue(ctx context.Context, req *riskOpsWobPb.GetWealthDataQueueRequest) (*riskOpsWobPb.GetWealthDataQueueResponse, error) {
	payloadType := convertToWealthPayload(req.GetPayloadType())
	if payloadType == persistentqueue.PayloadType_PAYLOAD_TYPE_UNSPECIFIED {
		return &riskOpsWobPb.GetWealthDataQueueResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	var kycDocketFilters *woCxPb.GetQueueElementsRequest_KycDocketFilters
	if req.GetKycDocketFilters() != nil {
		kycDocketFilters = &woCxPb.GetQueueElementsRequest_KycDocketFilters{
			KycDocketFilters: &woCxPb.KycDocketFilters{
				KraStatus: req.GetKycDocketFilters().GetKraStatus(),
				Pan:       req.GetKycDocketFilters().GetPan(),
			},
		}
	}
	res, err := s.wOnbCxClient.GetQueueElements(ctx, &woCxPb.GetQueueElementsRequest{
		PayloadType: payloadType,
		Limit:       req.GetLimit(),
		PageNo:      req.GetPageNum(),
		Filters:     kycDocketFilters,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		cxLogger.Error(ctx, "error while fetching queue elements", zap.Error(te))
		return &riskOpsWobPb.GetWealthDataQueueResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if len(res.GetElements()) == 0 {
		cxLogger.Info(ctx, "record not found in the queue")
		return &riskOpsWobPb.GetWealthDataQueueResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	var queueElements []*riskOpsWobPb.QueueElement
	for _, element := range res.GetElements() {
		wealthElement, cErr := convertToWealthQueueElement(element, payloadType)
		if cErr != nil {
			cxLogger.Error(ctx, "error in converting queue element", zap.Error(cErr))
			return &riskOpsWobPb.GetWealthDataQueueResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		queueElements = append(queueElements, wealthElement)
	}
	return &riskOpsWobPb.GetWealthDataQueueResponse{
		Status:   rpcPb.StatusOk(),
		Elements: queueElements,
	}, nil
}

func (s *Service) DeleteWealthDataQueueElement(ctx context.Context, req *riskOpsWobPb.DeleteWealthDataQueueElementRequest) (*riskOpsWobPb.DeleteWealthDataQueueElementResponse, error) {
	if req.GetId() == "" || req.GetActorId() == "" {
		cxLogger.Error(ctx, "record id or actor id cannot be empty")
		return &riskOpsWobPb.DeleteWealthDataQueueElementResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("record id or actor id cannot be empty"),
		}, nil
	}
	payloadType := convertToWealthPayload(req.GetPayloadType())
	if payloadType == persistentqueue.PayloadType_PAYLOAD_TYPE_UNSPECIFIED {
		return &riskOpsWobPb.DeleteWealthDataQueueElementResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	res, err := s.wOnbCxClient.DeleteQueueElement(ctx, &woCxPb.DeleteQueueElementRequest{
		Id:          req.GetId(),
		ActorId:     req.GetActorId(),
		PayloadType: payloadType,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		cxLogger.Error(ctx, "error while deleting queue elements", zap.Error(te))
		return &riskOpsWobPb.DeleteWealthDataQueueElementResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &riskOpsWobPb.DeleteWealthDataQueueElementResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) CountWealthDataQueueElements(ctx context.Context, req *riskOpsWobPb.CountWealthDataQueueElementsRequest) (*riskOpsWobPb.CountWealthDataQueueElementsResponse, error) {
	if req.GetPayloadType() == riskOpsWobPb.PayloadType_PAYLOAD_TYPE_UNSPECIFIED {
		cxLogger.Error(ctx, "payload type unspecified")
		return &riskOpsWobPb.CountWealthDataQueueElementsResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	res, err := s.wOnbCxClient.CountQueueElements(ctx, &woCxPb.CountQueueElementsRequest{
		PayloadType: convertToWealthPayload(req.GetPayloadType()),
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		cxLogger.Error(ctx, "error while getting count of queue elements", zap.Error(te))
		return &riskOpsWobPb.CountWealthDataQueueElementsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &riskOpsWobPb.CountWealthDataQueueElementsResponse{
		Status: rpcPb.StatusOk(),
		Count:  res.GetCount(),
	}, nil
}

// convertToWealthPayload is used to convert cx payload type to BE specific payload type
func convertToWealthPayload(payloadType riskOpsWobPb.PayloadType) persistentqueue.PayloadType {
	switch payloadType {
	case riskOpsWobPb.PayloadType_PAYLOAD_TYPE_WEALTH_DATA:
		return persistentqueue.PayloadType_PAYLOAD_TYPE_WEALTH_DATA
	case riskOpsWobPb.PayloadType_PAYLOAD_TYPE_KYC_DOCKET:
		return persistentqueue.PayloadType_PAYLOAD_TYPE_KYC_DOCKET
	default:
		return persistentqueue.PayloadType_PAYLOAD_TYPE_UNSPECIFIED
	}
}

// nolint: funlen
// convertToWealthQueueElement is used to convert cx queue element to BE specific queue element
func convertToWealthQueueElement(element *woCxPb.QueueElement, payloadType persistentqueue.PayloadType) (*riskOpsWobPb.QueueElement, error) {
	switch payloadType {
	case persistentqueue.PayloadType_PAYLOAD_TYPE_WEALTH_DATA:
		lvPayload := element.GetWealthDataReview().GetLivenessPayload()
		redactPayload := element.GetWealthDataReview().GetRedactionPayload()
		expiryPayload := element.GetWealthDataReview().GetExpiryPayload()
		var itemTypes []riskOpsWobPb.ItemType
		res := &riskOpsWobPb.QueueElement{
			Id:         element.GetId(),
			WealthData: &riskOpsWobPb.WealthData{},
		}
		if lvPayload != nil {
			res.GetWealthData().LivenessData = &riskOpsWobPb.LivenessData{
				VideoLocation:  lvPayload.GetVideoLocation(),
				AttemptId:      lvPayload.GetAttemptId(),
				Otp:            lvPayload.GetOtp(),
				OtpScore:       lvPayload.GetOtpScore(),
				LivenessScore:  lvPayload.GetLivenessScore(),
				CreatedAt:      lvPayload.GetCreatedAt(),
				RequestId:      lvPayload.GetRequestId(),
				ManualReviewId: lvPayload.GetManualReviewId(),
			}
			itemTypes = append(itemTypes, riskOpsWobPb.ItemType_ITEM_TYPE_LIVENESS)
		}
		if redactPayload != nil {
			res.GetWealthData().OcrData = &riskOpsWobPb.OcrData{
				OriginalDocument: redactPayload.GetRawOriginalDocument(),
				ProcessedDocument: &riskOpsWobPb.OcrDocumentProof{
					DocumentProof:    redactPayload.GetRawProcessedDocument().GetDocumentProof(),
					ConfidenceScore:  redactPayload.GetRawProcessedDocument().GetConfidenceScore(),
					ThresholdScore:   redactPayload.GetRawProcessedDocument().GetThresholdScore(),
					VendorReviewFlag: redactPayload.GetRawProcessedDocument().GetVendorReviewFlag(),
				},
				ManualReviewId: redactPayload.GetManualReviewId(),
			}
			itemTypes = append(itemTypes, riskOpsWobPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION)
		}
		if expiryPayload != nil {
			res.GetWealthData().OcrData = &riskOpsWobPb.OcrData{
				OriginalDocument: expiryPayload.GetRawOriginalDocument(),
				ProcessedDocument: &riskOpsWobPb.OcrDocumentProof{
					DocumentProof:    expiryPayload.GetRawProcessedDocument().GetDocumentProof(),
					ConfidenceScore:  expiryPayload.GetRawProcessedDocument().GetConfidenceScore(),
					ThresholdScore:   expiryPayload.GetRawProcessedDocument().GetThresholdScore(),
					VendorReviewFlag: expiryPayload.GetRawProcessedDocument().GetVendorReviewFlag(),
				},
				ManualReviewId: expiryPayload.GetManualReviewId(),
			}
			itemTypes = append(itemTypes, riskOpsWobPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY)
		}
		res.GetWealthData().ItemTypes = itemTypes
		res.GetWealthData().ActorId = element.GetWealthDataReview().GetActorId()
		return res, nil
	case persistentqueue.PayloadType_PAYLOAD_TYPE_KYC_DOCKET:
		res := &riskOpsWobPb.QueueElement{
			Id: element.GetId(),
			KycDocketData: &riskOpsWobPb.KycDocketData{
				ActorId: element.GetKycDocketReview().GetActorId(),
				KraFormPayload: &riskOpsWobPb.KycDocketData_KycDocketPayload{
					KycForm:                 element.GetKycDocketReview().GetKraFormPayload().GetKycForm(),
					UploadDate:              element.GetKycDocketReview().GetKraFormPayload().GetUploadDate(),
					KraStatus:               element.GetKycDocketReview().GetKraFormPayload().GetKraStatus(),
					KraRejectReason:         convertToWealthKraRejectReason(element.GetKycDocketReview().GetKraFormPayload().GetKraRejectReason()),
					HoldOrDeactivateRemarks: element.GetKycDocketReview().GetKraFormPayload().GetHoldOrDeactivateRemarks(),
					KraUpdateRemarks:        element.GetKycDocketReview().GetKraFormPayload().GetKraUpdateRemarks(),
					PendingUserData:         element.GetKycDocketReview().GetKraFormPayload().GetUserInputPendingData(),
				},
				Pan: element.GetKycDocketReview().GetPan(),
			},
		}
		return res, nil
	default:
		return nil, errors.New("unhandled payload type")
	}
}

func convertToWealthKraRejectReason(reason persistentqueue.KraRejectReason) riskOpsWobPb.KraRejectReason {
	switch reason {
	case persistentqueue.KraRejectReason_KRA_REJECT_REASON_UNKNOWN:
		return riskOpsWobPb.KraRejectReason_KRA_REJECT_REASON_UNKNOWN
	default:
		return riskOpsWobPb.KraRejectReason_KRA_REJECT_REASON_UNSPECIFIED
	}
}
