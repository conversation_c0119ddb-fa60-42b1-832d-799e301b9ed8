package salarydataops

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"time"

	gammanames "github.com/epifi/gamma/pkg/names"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/errgroup"

	"github.com/epifi/be-common/pkg/mask"

	"github.com/epifi/be-common/pkg/pagination"

	"github.com/epifi/be-common/pkg/epifigrpc"

	actorPb "github.com/epifi/gamma/api/actor"
	bcPb "github.com/epifi/gamma/api/bankcust"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/cx/data_collector/salarydataops"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/salaryprogram"
	salarycx "github.com/epifi/gamma/api/salaryprogram/cx"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	employernamecategoriserPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	cxLogger "github.com/epifi/gamma/cx/logger"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

const GetRemitterInfoFromVgForTxnsBeforeTime = "2022-07-20T23:59:59+05:30"

var (
	SalaryTxnVerificationEmployerChannelCXToBEMapping = map[salarydataops.EmployerSalaryProgramChannel]employmentPb.EmployerSalaryProgramChannel{
		salarydataops.EmployerSalaryProgramChannel_B2B: employmentPb.EmployerSalaryProgramChannel_B2B,
		salarydataops.EmployerSalaryProgramChannel_B2C: employmentPb.EmployerSalaryProgramChannel_B2C,
	}
	SalaryTxnVerificationEmployerChannelBEToCXMapping = map[employmentPb.EmployerSalaryProgramChannel]salarydataops.EmployerSalaryProgramChannel{
		employmentPb.EmployerSalaryProgramChannel_B2B: salarydataops.EmployerSalaryProgramChannel_B2B,
		employmentPb.EmployerSalaryProgramChannel_B2C: salarydataops.EmployerSalaryProgramChannel_B2C,
	}
	SalaryOnbStageDetailsBEtoBoolMapping = map[salaryprogram.SalaryProgramRegistrationStageStatus]bool{
		salaryprogram.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_UNSPECIFIED:   false,
		salaryprogram.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_NOT_INITIATED: false,
		salaryprogram.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_INITIATED:     false,
		salaryprogram.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_COMPLETED:     true,
	}
	// SalaryTxnVerificationStatusCXToBEMapping is used to get BE statuses for salary txn from CX status
	SalaryTxnVerificationStatusCXToBEMapping = map[salarydataops.SalaryTxnVerificationStatus]salaryTxnStatuses{
		salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_PENDING_VERIFICATION: {
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_OPS_VERIFICATION,
		},
		salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_ACCEPTED: {
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED,
		},
		salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_REJECTED: {
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED,
		},
		salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_ESCALATED_TO_ENGG_TEAM: {
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_STATUS_ESCALATED_TO_ENGG_TEAM,
		},
		salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_AWAITING_EMPLOYMENT_UPDATE: {
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE,
		},
	}
	// SalaryTxnVerificationStatusBEtoCXMapping is used to get CX status for salary txn from BE statuses
	SalaryTxnVerificationStatusBEtoCXMapping = map[salaryTxnStatuses]salarydataops.SalaryTxnVerificationStatus{
		{
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_OPS_VERIFICATION,
		}: salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_PENDING_VERIFICATION,
		{
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED,
		}: salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_ACCEPTED,
		{
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED,
		}: salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_REJECTED,
		{
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_STATUS_ESCALATED_TO_ENGG_TEAM,
		}: salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_ESCALATED_TO_ENGG_TEAM,
		{
			salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
			salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE,
		}: salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_AWAITING_EMPLOYMENT_UPDATE,
	}
	// VkycSummaryStatusToSalaryOpsKycStatusMapping maps vkyc summary status in vkyc service
	// to simpler statuses to show in sherlock dashboards
	VkycSummaryStatusToSalaryOpsKycStatusMapping = map[vkyc.VKYCSummaryStatus]salarydataops.KycStatus{
		vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNSPECIFIED:  salarydataops.KycStatus_KYC_STATUS_UNSPECIFIED,
		vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNREGISTERED: salarydataops.KycStatus_KYC_STATUS_VKYC_IN_REVIEW,
		vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED:   salarydataops.KycStatus_KYC_STATUS_VKYC_IN_REVIEW,
		vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_PROGRESS:  salarydataops.KycStatus_KYC_STATUS_VKYC_IN_REVIEW,
		vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_RE_REGISTER:  salarydataops.KycStatus_KYC_STATUS_VKYC_IN_REVIEW,
		vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW:    salarydataops.KycStatus_KYC_STATUS_VKYC_IN_REVIEW,
		vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED:     salarydataops.KycStatus_KYC_STATUS_FULL_KYC,
		vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED:     salarydataops.KycStatus_KYC_STATUS_VKYC_REJECTED,
	}

	SalaryVerificationEligibilityStatusBEToCXMapping = map[salarycx.UserSalaryVerificationEligibilityStatus]salarydataops.UserSalaryVerificationEligibilityStatus{
		salarycx.UserSalaryVerificationEligibilityStatus_ELIGIBILITY_STATUS_UNSPECIFIED: salarydataops.UserSalaryVerificationEligibilityStatus_USER_SALARY_VERIFICATION_ELIGIBILITY_STATUS_UNSPECIFIED,
		salarycx.UserSalaryVerificationEligibilityStatus_NEW:                            salarydataops.UserSalaryVerificationEligibilityStatus_USER_SALARY_VERIFICATION_ELIGIBILITY_STATUS_NEW,
		salarycx.UserSalaryVerificationEligibilityStatus_PENDING:                        salarydataops.UserSalaryVerificationEligibilityStatus_USER_SALARY_VERIFICATION_ELIGIBILITY_STATUS_PENDING,
		salarycx.UserSalaryVerificationEligibilityStatus_COMPLETE:                       salarydataops.UserSalaryVerificationEligibilityStatus_USER_SALARY_VERIFICATION_ELIGIBILITY_STATUS_COMPLETE,
	}

	SalaryProgramStageBEToCXMapping = map[salarydataops.SalaryProgramStage]salarycx.SalaryProgramStage{
		salarydataops.SalaryProgramStage_SALARY_PROGRAM_STAGE_UNSPECIFIED:                       salarycx.SalaryProgramStage_SALARY_PROGRAM_STAGE_UNSPECIFIED,
		salarydataops.SalaryProgramStage_SALARY_PROGRAM_STAGE_REGISTERED_AND_ACTIVATION_PENDING: salarycx.SalaryProgramStage_REGISTERED_AND_ACTIVATION_PENDING,
		salarydataops.SalaryProgramStage_SALARY_PROGRAM_STAGE_SALARY_ACTIVE:                     salarycx.SalaryProgramStage_SALARY_ACTIVE,
	}

	SalaryActivationTypeBEToCxMapping = map[salaryprogram.SalaryActivationType]salarydataops.SalaryActivationType{
		salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION: salarydataops.SalaryActivationType_FULL_SALARY_ACTIVATION,
		salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION: salarydataops.SalaryActivationType_SALARY_LITE_ACTIVATION,
	}

	// gstinRegex is regex format for a valid gstin number
	gstinRegex = regexp.MustCompile(`(^\d{2}[A-Z]{5}\d{4}[A-Z][A-Z\d][A-Z\d]{2}$)`)
)

type salaryTxnStatuses struct {
	status    salaryprogram.SalaryTxnVerificationRequestStatus
	subStatus salaryprogram.SalaryTxnVerificationRequestSubStatus
}

type userEmploymentDetails struct {
	employmentType       string
	employerId           string
	employerName         string
	updatedAt            *timestampPb.Timestamp
	salaryProgramChannel employmentPb.EmployerSalaryProgramChannel
}

type salaryAccountDetails struct {
	employmentInfo    *salarydataops.EmploymentInfo
	kycInfo           *salarydataops.KycInfo
	salaryProgramInfo *salarydataops.SalaryProgramInfo
}

type updateVerificationStatusInput struct {
	employerId string
	status     salaryprogram.SalaryTxnVerificationRequestStatus
	subStatus  salaryprogram.SalaryTxnVerificationRequestSubStatus
	err        error
}

// nolint:dupl
func (s *Service) getUserAndActorFromEmailId(ctx context.Context, emailId string) (*userPb.User, *types.Actor, error) {
	getUserResp, err := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_EmailId{
			EmailId: emailId,
		},
	})
	if grpcErr := epifigrpc.RPCError(getUserResp, err); grpcErr != nil {
		if getUserResp.GetStatus().IsRecordNotFound() {
			return nil, nil, epifierrors.ErrRecordNotFound
		}
		return nil, nil, errors.Wrap(grpcErr, "error getting user from email")
	}
	getActorResp, err := s.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: getUserResp.GetUser().GetId(),
	})
	if grpcErr := epifigrpc.RPCError(getActorResp, err); grpcErr != nil {
		if getActorResp.GetStatus().IsRecordNotFound() {
			return nil, nil, epifierrors.ErrRecordNotFound
		}
		return nil, nil, errors.Wrap(grpcErr, "error getting actor from user id")
	}
	return getUserResp.GetUser(), getActorResp.GetActor(), nil
}

// nolint:dupl
func (s *Service) getUserAndActorFromPhoneNumber(ctx context.Context, phoneNumber *commontypes.PhoneNumber) (*userPb.User, *types.Actor, error) {
	getUserResp, err := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_PhoneNumber{
			PhoneNumber: phoneNumber,
		},
	})
	if grpcErr := epifigrpc.RPCError(getUserResp, err); grpcErr != nil {
		if getUserResp.GetStatus().IsRecordNotFound() {
			return nil, nil, epifierrors.ErrRecordNotFound
		}
		return nil, nil, errors.Wrap(grpcErr, "error getting user from phone number")
	}
	getActorResp, err := s.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: getUserResp.GetUser().GetId(),
	})
	if grpcErr := epifigrpc.RPCError(getActorResp, err); grpcErr != nil {
		if getActorResp.GetStatus().IsRecordNotFound() {
			return nil, nil, epifierrors.ErrRecordNotFound
		}
		return nil, nil, errors.Wrap(grpcErr, "error getting actor from user id")
	}
	return getUserResp.GetUser(), getActorResp.GetActor(), nil
}

func (s *Service) getActorIdFromExtOrderId(ctx context.Context, extOrderId string) (string, error) {
	getOrderResp, getOrderErr := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ExternalId{
			ExternalId: extOrderId,
		},
	})
	if grpcErr := epifigrpc.RPCError(getOrderResp, getOrderErr); grpcErr != nil {
		return "", errors.Wrap(grpcErr, "error getting order from external order id")
	}
	return getOrderResp.GetOrder().GetToActorId(), nil
}

func (s *Service) getUserFromActorId(ctx context.Context, actorId string) (*userPb.User, error) {
	getActorResp, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getActorResp, err); grpcErr != nil {
		if getActorResp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(grpcErr, "failed to get actor by id")
	}
	userId := getActorResp.GetActor().GetEntityId()
	getUserResp, err := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: userId,
		},
	})
	if grpcErr := epifigrpc.RPCError(getActorResp, err); grpcErr != nil {
		if getUserResp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(grpcErr, "failed to get user by id")
	}
	return getUserResp.GetUser(), nil
}

func (s *Service) getEmployerDetailsForActor(ctx context.Context, actorId string) (*userEmploymentDetails, error) {
	employmentInfo, err := s.empClient.GetEmploymentInfo(ctx, &employmentPb.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(employmentInfo, err); grpcErr != nil {
		return nil, errors.Wrap(grpcErr, "error fetching employment info of user")
	}
	employer, err := s.empClient.GetEmployerOfUser(ctx, &employmentPb.GetEmployerOfUserRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(employer, err); grpcErr != nil {
		if employer.GetStatus().IsRecordNotFound() {
			cxLogger.Error(ctx, "no employer found for user", zap.Error(grpcErr))
			return &userEmploymentDetails{
				employmentType: employmentInfo.GetEmploymentData().GetEmploymentType().String(),
				employerId:     employmentInfo.GetEmploymentData().GetEmployerId(),
				employerName:   "",
				updatedAt:      employmentInfo.GetEmploymentData().GetUpdatedAt(),
			}, nil
		}
		return nil, errors.Wrap(grpcErr, "error fetching employer of user")
	}
	var employerName string
	if employmentInfo.GetEmploymentData().GetEmploymentInfo().GetIsCompanyNameManualInput() {
		employerName = employer.GetCustomEmployerInfo().GetName()
	} else {
		employerName = employer.GetEmployerInfo().GetNameBySource()
	}
	return &userEmploymentDetails{
		employmentType:       employmentInfo.GetEmploymentData().GetEmploymentType().String(),
		employerId:           employmentInfo.GetEmploymentData().GetEmployerId(),
		employerName:         employerName,
		updatedAt:            employmentInfo.GetEmploymentData().GetUpdatedAt(),
		salaryProgramChannel: employer.GetEmployerInfo().GetSalaryProgramChannel(),
	}, nil
}

func (s *Service) getEmploymentAndUserDetails(ctx context.Context, actorId string, user *userPb.User) (*salarydataops.EmploymentInfo, error) {
	errGrp, gCtx := errgroup.WithContext(ctx)

	var employmentData *userEmploymentDetails
	errGrp.Go(func() error {
		var getEmploymentDataErr error
		employmentData, getEmploymentDataErr = s.getEmployerDetailsForActor(gCtx, actorId)
		if getEmploymentDataErr != nil {
			return getEmploymentDataErr
		}
		return nil
	})

	var userGroup string
	var name *commontypes.Name
	errGrp.Go(func() error {
		var getUserErr error
		if user == nil {
			user, getUserErr = s.getUserFromActorId(gCtx, actorId)
			if getUserErr != nil {
				return getUserErr
			}
		}
		name = gammanames.BestNameFromProfile(gCtx, user.GetProfile())
		mappingRes, errResp := s.userGroupClient.CheckMapping(gCtx, &userGroupPb.CheckMappingRequest{
			UserGroup: commontypes.UserGroup_B2B_SALARY_PROGRAM,
			IdentifierValue: &userGroupPb.IdentifierValue{
				Identifier: &userGroupPb.IdentifierValue_PhoneNumber{PhoneNumber: user.GetProfile().GetPhoneNumber()},
			},
		})

		if rpcErr := epifigrpc.RPCError(mappingRes, errResp); rpcErr != nil && !mappingRes.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("error checking for user-group mapping of salary b2b: %w", rpcErr)
		}

		isUserInB2BSalaryGroup := mappingRes.GetStatus().IsSuccess()

		switch {
		case isUserInB2BSalaryGroup:
			userGroup = employmentPb.EmployerSalaryProgramChannel_B2B.String()
		default:
			userGroup = employmentPb.EmployerSalaryProgramChannel_B2C.String()
		}
		return nil
	})

	var curRegDetails *salaryprogram.CurrentRegStatusAndNextRegStageResponse
	var empConfirmationComplete bool
	errGrp.Go(func() error {
		// fetching current salary program enrollment stage
		var getCurRegDetailsErr error
		curRegDetails, getCurRegDetailsErr = s.salaryClient.GetCurrentRegStatusAndNextRegStage(gCtx, &salaryprogram.CurrentRegStatusAndNextRegStageRequest{
			ActorId:  actorId,
			FlowType: salaryprogram.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
		})
		if grpcErr := epifigrpc.RPCError(curRegDetails, getCurRegDetailsErr); grpcErr != nil {
			return errors.Wrap(grpcErr, "error fetching current salary program registration status")
		}
		// fetching employment confirmation stage details from salary program enrollment
		if curRegDetails.GetRegistrationStatus() == salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED ||
			curRegDetails.GetRegistrationStatus() == salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_UNSPECIFIED {
			empConfirmationComplete = false
			return nil
		}
		empConfStageDetails, err := s.salaryClient.GetRegistrationStageDetails(gCtx, &salaryprogram.GetRegistrationStageDetailsRequest{
			RegistrationId: curRegDetails.GetRegistrationId(),
			StageName:      salaryprogram.SalaryProgramRegistrationStage_REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION,
		})
		if grpcErr := epifigrpc.RPCError(empConfStageDetails, err); grpcErr != nil {
			return errors.Wrap(grpcErr, "error fetching salary program employment confirmation stage details")
		}
		var getEmpConfirmationCompleteErr bool
		empConfirmationComplete, getEmpConfirmationCompleteErr = SalaryOnbStageDetailsBEtoBoolMapping[empConfStageDetails.GetStageStatus()]
		if !getEmpConfirmationCompleteErr {
			cxLogger.Error(ctx, "error mapping onboarding stage status to bool")
		}
		return nil
	})

	err := errGrp.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch data in err group, %w", err)
	}

	return &salarydataops.EmploymentInfo{
		Name:                           name,
		PhoneNumber:                    user.GetProfile().GetPhoneNumber(),
		EmailId:                        user.GetProfile().GetEmail(),
		EmploymentType:                 employmentData.employmentType,
		EmployerId:                     employmentData.employerId,
		EmployerName:                   employmentData.employerName,
		EmploymentDataUpdatedAt:        employmentData.updatedAt,
		EmploymentConfirmationComplete: empConfirmationComplete,
		UserGroup:                      userGroup,
		EmployerSalaryProgramChannel:   employmentData.salaryProgramChannel.String(),
	}, nil
}

func (s *Service) getKycData(ctx context.Context, actorId string) (*salarydataops.KycInfo, error) {
	/*
	   full kyc -> full kyc + check if user in VKYC user group -> if yes, then VKYC(GetVKYCSummary) status should be approved
	   min kyc -> user min kyc
	   review/reject kyc -> GetVKYCSummary
	*/
	var err error
	custInfo, errResp := s.bcCLient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if err := epifigrpc.RPCError(custInfo, errResp); err != nil {
		return nil, errors.Wrap(err, "error getting vendor customer info")
	}

	kycUpdatedAt := custInfo.GetBankCustomer().GetFiCreationSucceededAt()
	var kycStatus salarydataops.KycStatus
	switch custInfo.GetBankCustomer().GetDedupeInfo().GetKycLevel() {
	case kyc.KYCLevel_MIN_KYC:
		kycStatus = salarydataops.KycStatus_KYC_STATUS_MIN_KYC
	case kyc.KYCLevel_FULL_KYC:
		kycStatus = salarydataops.KycStatus_KYC_STATUS_FULL_KYC
	default:
		kycStatus, kycUpdatedAt, err = s.getVKYCStatus(ctx, actorId)
		if err != nil {
			return nil, errors.Wrap(err, "error getting vkyc status")
		}
	}

	return &salarydataops.KycInfo{
		KycStatus:          kycStatus,
		KycStatusUpdatedAt: kycUpdatedAt,
	}, nil
}

func (s *Service) getVKYCStatus(ctx context.Context, actorId string) (salarydataops.KycStatus, *timestampPb.Timestamp, error) {
	var kycStatus salarydataops.KycStatus
	vkycSummary, err := s.vkycClient.GetVKYCSummary(ctx, &vkyc.GetVKYCSummaryRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(vkycSummary, err); grpcErr != nil {
		return kycStatus, nil, errors.Wrap(grpcErr, "error fetching vkyc summary for user")
	}
	kycStatus, ok := VkycSummaryStatusToSalaryOpsKycStatusMapping[vkycSummary.GetVkycRecord().GetVkycSummary().GetStatus()]
	if !ok {
		return kycStatus, nil, fmt.Errorf("unmapped vkyc summary status: %s", vkycSummary.GetVkycRecord().GetVkycSummary().GetStatus())
	}
	return kycStatus, vkycSummary.GetVkycRecord().GetVkycSummary().GetUpdatedAt(), nil
}

func (s *Service) getSalaryProgramInfoForActor(ctx context.Context, actorId string) (*salarydataops.SalaryProgramInfo, error) {
	salaryRegDetails, err := s.salaryClient.GetRegistrationDetails(ctx, &salaryprogram.GetRegistrationDetailsRequest{
		ActorId:  actorId,
		FlowType: salaryprogram.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if grpcErr := epifigrpc.RPCError(salaryRegDetails, err); grpcErr != nil {
		return nil, errors.Wrap(grpcErr, "error fetching current salary program registration details")
	}
	if salaryRegDetails.GetRegistrationStatus() == salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED {
		return &salarydataops.SalaryProgramInfo{
			RegistrationStatus:  salaryRegDetails.GetRegistrationStatus().String(),
			SalaryAccountStatus: salarydataops.SalaryAccountStatus_SALARY_ACCOUNT_STATUS_INACTIVE,
		}, nil
	}

	salaryActivationHistory, rpcError := s.salaryClient.GetSalaryProgramActivationHistories(ctx, &salaryprogram.GetSalaryProgramActivationHistoriesRequest{
		RegistrationId: salaryRegDetails.GetRegistrationId(),
		SortOrder:      salaryprogram.SortOrder_ASC,
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})
	if rpcErr := epifigrpc.RPCError(salaryActivationHistory, rpcError); rpcErr != nil {
		cxLogger.Error(ctx, "error fetching salary program activation histories", zap.Error(rpcErr))
		// not returning error here since we still want registration status to be sent
	}
	var (
		firstSalaryActivationDate *timestampPb.Timestamp
		firstSalaryActivationType salarydataops.SalaryActivationType
	)
	if len(salaryActivationHistory.GetActivationHistories()) != 0 {
		firstSalaryActivationDate = salaryActivationHistory.GetActivationHistories()[0].GetActiveFrom()
		var exist bool
		firstSalaryActivationType, exist = SalaryActivationTypeBEToCxMapping[salaryActivationHistory.GetActivationHistories()[0].GetActivationType()]
		if !exist {
			return nil, fmt.Errorf("salary activation type BE to CX mapping not present, beActivationType: %s", salaryActivationHistory.GetActivationHistories()[0].GetActivationType().String())
		}
	}

	salaryActivationDetails, err := s.salaryClient.GetLatestActivationDetailsActiveAtTime(ctx, &salaryprogram.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: salaryRegDetails.GetRegistrationId(),
		ActiveAtTime:   timestampPb.Now(),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user and its activation type.
		ActivationKind: salaryprogram.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	grpcErr := epifigrpc.RPCError(salaryActivationDetails, err)

	var (
		salaryActivationStatus salarydataops.SalaryAccountStatus
		salaryActivationType   salarydataops.SalaryActivationType
	)
	switch {
	case salaryActivationDetails.GetStatus().IsRecordNotFound():
		salaryActivationStatus = salarydataops.SalaryAccountStatus_SALARY_ACCOUNT_STATUS_INACTIVE
	case grpcErr != nil:
		cxLogger.Error(ctx, "error fetching current salary program activation details", zap.Error(grpcErr))
		// not returning error here since we still want registration status to be sent
	default:
		salaryActivationStatus = salarydataops.SalaryAccountStatus_SALARY_ACCOUNT_STATUS_ACTIVE
		var exist bool
		salaryActivationType, exist = SalaryActivationTypeBEToCxMapping[salaryActivationDetails.GetActivationType()]
		if !exist {
			return nil, fmt.Errorf("salary activation type BE to CX mapping not present, beActivationType: %s", salaryActivationDetails.GetActivationType())
		}
	}
	return &salarydataops.SalaryProgramInfo{
		RegistrationStatus:         salaryRegDetails.GetRegistrationStatus().String(),
		SalaryAccountStatus:        salaryActivationStatus,
		RegistrationCompletionDate: salaryRegDetails.GetRegistrationCompletionTime(),
		FirstSalaryActivationDate:  firstSalaryActivationDate,
		FirstSalaryActivationType:  firstSalaryActivationType,
		SalaryActivationType:       salaryActivationType,
	}, nil
}

// nolint: funlen
func (s *Service) getTxnDetails(ctx context.Context, salaryTxnVeriReq *salaryprogram.SalaryTxnVerificationRequest, getRemitterInfo bool, getPercentageDiffFromLastSalaryString bool, getProbabilityOfRemitterBeingAMerchant bool, areAdditionalDetailsRequired bool) (*salarydataops.TxnDetails, error) {
	var (
		verificationFailureReasonCategory    = s.salaryOpsConfig.VerificationFailureReasonsCategoryToDisplayString[salaryTxnVeriReq.GetVerificationFailureReasonCategory().String()]
		verificationFailureSubReasonCategory = s.salaryOpsConfig.VerificationFailureReasonsSubCategoryToDisplayString[salaryTxnVeriReq.GetVerificationFailureReasonSubCategory().String()]
	)
	getOrderResp, getOrderErr := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ExternalId{
			ExternalId: salaryTxnVeriReq.GetTxnId(),
		},
	})
	if grpcErr := epifigrpc.RPCError(getOrderResp, getOrderErr); grpcErr != nil {
		return nil, errors.Wrap(grpcErr, "error getting order from external order id")
	}
	orderWithTxn, getOrderWithTxnErr := s.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{
		OrderId: getOrderResp.GetOrder().GetId(),
	})
	if grpcErr := epifigrpc.RPCError(orderWithTxn, getOrderWithTxnErr); grpcErr != nil {
		return nil, errors.Wrap(grpcErr, "error getting order with txn for id")
	}
	if len(orderWithTxn.GetOrderWithTransactions().GetTransactions()) != 1 {
		cxLogger.Error(ctx, fmt.Sprintf("expected only one transaction to be linked to the order, got %v", len(orderWithTxn.GetOrderWithTransactions().GetTransactions())))
	}
	if len(orderWithTxn.GetOrderWithTransactions().GetTransactions()) < 1 {
		cxLogger.Error(ctx, "no transactions linked to order")
		return nil, fmt.Errorf("no transactions linked to order")
	}
	txn := orderWithTxn.GetOrderWithTransactions().GetTransactions()[0]
	// fetch remitter name only if getRemitterInfo boolean is true
	var piVerifiedName, piAccountName, remitterName string
	var remitterErr error
	if getRemitterInfo {
		piVerifiedName, piAccountName, remitterName, remitterErr = s.getTxnRemitterInfo(ctx, txn, getOrderResp.GetOrder())
		if remitterErr != nil {
			cxLogger.Error(ctx, "error getting remitter info for txn", zap.Error(remitterErr), zap.String(logger.TXN_ID, txn.GetId()))
			return nil, errors.Wrap(remitterErr, "error getting remitter name")
		}
	}
	salaryTxnVerificationStatus, ok := SalaryTxnVerificationStatusBEtoCXMapping[salaryTxnStatuses{
		status:    salaryTxnVeriReq.GetVerificationStatus(),
		subStatus: salaryTxnVeriReq.GetVerificationSubStatus(),
	}]
	if !ok {
		cxLogger.Error(ctx, fmt.Sprintf("unable to map BE txn verification statuses to CX: \nstatus: %s, sub-status: %s",
			salaryTxnVeriReq.GetVerificationStatus(), salaryTxnVeriReq.GetVerificationSubStatus()))
		salaryTxnVerificationStatus = salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_UNSPECIFIED
	}
	getActorByIdRes, getActorByIdErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: orderWithTxn.GetOrderWithTransactions().GetOrder().GetFromActorId(),
	})
	if rpcErr := epifigrpc.RPCError(getActorByIdRes, getActorByIdErr); rpcErr != nil {
		cxLogger.Error(ctx, "error in GetActorById rpc", zap.Error(rpcErr))
		return nil, fmt.Errorf("error in GetActorById rpc")
	}

	// getting verified employer name for given request if the transaction was verified adn employer name exists
	var verifiedEmployerName string
	if salaryTxnVeriReq.GetTxnEmployerId() != "" {
		getEmployerRes, err := s.empClient.GetEmployer(ctx, &employmentPb.GetEmployerRequest{
			Identifier: &employmentPb.GetEmployerRequest_EmployerId{
				EmployerId: salaryTxnVeriReq.GetTxnEmployerId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(getEmployerRes, err); rpcErr != nil {
			cxLogger.Error(ctx, "error in empClient.GetEmployer rpc", zap.Error(rpcErr), zap.String(logger.EMPLOYER_ID, salaryTxnVeriReq.GetTxnEmployerId()))
			return nil, fmt.Errorf("error in empClient.GetEmployer rpc")
		}
		verifiedEmployerName = getEmployerRes.GetEmployerInfo().GetNameBySource()
	}

	var (
		percentageDiffOfTxnAmtFromLastSalaryAmtString      string
		err                                                error
		probabilityOfRemitterNameBeingAMerchantString      string
		probabilityOfPiRemitterNameBeingAMerchantString    string
		probabilityOfRemitterActorNameBeingAMerchantString string
		probabilityOfPiAccountNameBeingAMerchantString     string
	)

	if getPercentageDiffFromLastSalaryString {
		percentageDiffOfTxnAmtFromLastSalaryAmtString, err = s.percentageDiffOfTxnAmtFromLastSalaryAmtString(ctx, orderWithTxn.GetOrderWithTransactions(), salaryTxnVeriReq.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in getting percentageDiffOfTxnAmtFromLastSalaryAmtString", zap.Error(err))
			return nil, fmt.Errorf("error in getting percentageDiffOfTxnAmtFromLastSalaryAmtString")
		}
	}

	if getProbabilityOfRemitterBeingAMerchant {
		probabilityOfRemitterNameBeingAMerchantString, err = s.getProbabilityOfRemitterBeingAMerchantString(ctx, remitterName)
		if err != nil {
			logger.Error(ctx, "error in getting probabilityOfRemitterBeingAMerchant", zap.Error(err))
			return nil, fmt.Errorf("error in getting probabilityOfRemitterBeingAMerchant")
		}

		probabilityOfPiRemitterNameBeingAMerchantString, err = s.getProbabilityOfRemitterBeingAMerchantString(ctx, piVerifiedName)
		if err != nil {
			logger.Error(ctx, "error in getting probabilityOfRemitterBeingAMerchant", zap.Error(err))
			return nil, fmt.Errorf("error in getting probabilityOfRemitterBeingAMerchant")
		}

		probabilityOfRemitterActorNameBeingAMerchantString, err = s.getProbabilityOfRemitterBeingAMerchantString(ctx, getActorByIdRes.GetActor().GetName())
		if err != nil {
			logger.Error(ctx, "error in getting probabilityOfRemitterBeingAMerchant", zap.Error(err))
			return nil, fmt.Errorf("error in getting probabilityOfRemitterBeingAMerchant")
		}

		probabilityOfPiAccountNameBeingAMerchantString, err = s.getProbabilityOfRemitterBeingAMerchantString(ctx, piAccountName)
		if err != nil {
			logger.Error(ctx, "error in getting probabilityOfRemitterBeingAMerchant", zap.Error(err))
			return nil, fmt.Errorf("error in getting probabilityOfRemitterBeingAMerchant")
		}
	}

	isSalaryAmountDivisibleBy1000 := orderWithTxn.GetOrderWithTransactions().GetOrder().GetAmount().GetUnits()%1000 == 0

	var actorDetails *salarydataops.ActorDetails
	var employerDetails *salarydataops.EmployerDetails
	var actorDetailsErr error
	var employerDetailsErr error
	if areAdditionalDetailsRequired {
		actorDetails, actorDetailsErr = s.getActorDetailsById(ctx, salaryTxnVeriReq.GetActorId())
		if actorDetailsErr != nil {
			return nil, fmt.Errorf("error getting actor details, err: %w", actorDetailsErr)
		}
		employerDetails, employerDetailsErr = s.getEmployerDetailsById(ctx, salaryTxnVeriReq.GetActorId())
		if employerDetailsErr != nil {
			if errors.Is(employerDetailsErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "employer not found for actor",
					zap.String("actorId", salaryTxnVeriReq.GetActorId()), zap.Error(employerDetailsErr))
			}
		}
	}

	return &salarydataops.TxnDetails{
		SalaryTxnVerReqId:                    salaryTxnVeriReq.GetId(),
		TxnId:                                salaryTxnVeriReq.GetTxnId(),
		RemitterName:                         remitterName,
		PiRemitterName:                       piVerifiedName,
		PaymentProtocol:                      txn.GetPaymentProtocol().String(),
		Particulars:                          txn.GetRawNotificationDetails()["CREDIT"].GetParticulars(),
		Utr:                                  txn.GetUtr(),
		TxnTimestamp:                         salaryTxnVeriReq.GetTxnTimestamp(),
		SalaryTxnVerificationStatus:          salaryTxnVerificationStatus,
		UpdatedAt:                            salaryTxnVeriReq.GetUpdatedAt(),
		TxnVerificationRequestCreatedAt:      salaryTxnVeriReq.GetCreatedAt(),
		VerificationFailureReasonCategory:    verificationFailureReasonCategory,
		VerificationFailureReasonSubCategory: verificationFailureSubReasonCategory,
		VerificationRemark:                   salaryTxnVeriReq.GetVerificationRemark(),
		RemitterPiId:                         txn.GetPiFrom(),
		RemitterActorName:                    getActorByIdRes.GetActor().GetName(),
		PiAccountName:                        piAccountName,
		VerifiedEmployerName:                 verifiedEmployerName,
		PercentageDiffFromLastSalaryString:   percentageDiffOfTxnAmtFromLastSalaryAmtString,
		IsAmountDivisibleBy1000:              isSalaryAmountDivisibleBy1000,
		ProbabilityOfRemitterBeingAMerchant: &salarydataops.ProbabilityOfRemitterBeingAMerchant{
			ProbabilityOfRemitterNameBeingAMerchantString:      probabilityOfRemitterNameBeingAMerchantString,
			ProbabilityOfPiRemitterNameBeingAMerchantString:    probabilityOfPiRemitterNameBeingAMerchantString,
			ProbabilityOfRemitterActorNameBeingAMerchantString: probabilityOfRemitterActorNameBeingAMerchantString,
			ProbabilityOfPiAccountNameBeingAMerchantString:     probabilityOfPiAccountNameBeingAMerchantString,
		},
		ActorDetails:    actorDetails,
		EmployerDetails: employerDetails,
	}, nil
}

func (s *Service) getEmployerDetailsById(ctx context.Context, actorId string) (*salarydataops.EmployerDetails, error) {
	employerOfUserResp, employerOfUserErr := s.empClient.GetEmployerOfUser(ctx, &employmentPb.GetEmployerOfUserRequest{
		ActorId: actorId,
	})
	if employerOfUserResp.GetStatus().IsRecordNotFound() {
		return nil, epifierrors.ErrRecordNotFound
	}
	if rpcErr := epifigrpc.RPCError(employerOfUserResp, employerOfUserErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to get user declared employer, err: %w", rpcErr)
	}
	employer := employerOfUserResp.GetEmployerInfo()
	cxEmployerChannel, ok := SalaryTxnVerificationEmployerChannelBEToCXMapping[employer.GetSalaryProgramChannel()]
	if !ok {
		return nil, fmt.Errorf("unable to map BE employer channel to CX")
	}

	return &salarydataops.EmployerDetails{
		EmployerId:      employer.GetEmployerId(),
		NameBySource:    employer.GetNameBySource(),
		EmployerChannel: cxEmployerChannel,
	}, nil
}

// this method gets actor details by id
// it masks the first name, last name, and phone number
func (s *Service) getActorDetailsById(ctx context.Context, actorId string) (*salarydataops.ActorDetails, error) {
	user, err := s.getUserFromActorId(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("error in getting user from actor id, err: %w", err)
	}
	bestNameFromProfile := gammanames.BestNameFromProfile(ctx, user.GetProfile())
	maskedFirstName := mask.MaskLastNDigits(bestNameFromProfile.GetFirstName(), 3, "*")
	maskedLastName := mask.MaskLastNDigits(bestNameFromProfile.GetLastName(), 3, "*")

	var actorDetails salarydataops.ActorDetails
	actorDetails.Name = maskedFirstName + " " + maskedLastName
	actorDetails.PhoneNumber = mask.GetMaskedPhoneNumber(user.GetProfile().GetPhoneNumber(), "*")
	isRepeatSalaryProgramUser, isRepeatSalaryProgramUserErr := s.isRepeatSalaryProgramUser(ctx, actorId)
	if isRepeatSalaryProgramUserErr != nil {
		return nil, fmt.Errorf("error in check if repeat salary program user, err: %w", isRepeatSalaryProgramUserErr)
	}
	actorDetails.IsRepeatSalaryProgramUser = isRepeatSalaryProgramUser

	return &actorDetails, nil
}

func (s *Service) isRepeatSalaryProgramUser(ctx context.Context, actorId string) (bool, error) {
	salaryRegDetails, salaryRegDetailsErr := s.salaryClient.GetRegistrationDetails(ctx, &salaryprogram.GetRegistrationDetailsRequest{
		ActorId:  actorId,
		FlowType: salaryprogram.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if rpcErr := epifigrpc.RPCError(salaryRegDetails, salaryRegDetailsErr); rpcErr != nil {
		return false, fmt.Errorf("failed to get registration details for actor, err: %w", rpcErr)
	}
	activationHistoriesResp, activationHistoriesErr := s.salaryClient.GetSalaryProgramActivationHistories(ctx, &salaryprogram.GetSalaryProgramActivationHistoriesRequest{
		RegistrationId: salaryRegDetails.GetRegistrationId(),
		ActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
	})
	if rpcErr := epifigrpc.RPCError(activationHistoriesResp, activationHistoriesErr); rpcErr != nil {
		return false, fmt.Errorf("error getting activation histories, err: %w", activationHistoriesErr)
	}
	isRepeatSalaryProgramUser := activationHistoriesResp.GetStatus().IsRecordNotFound() == false &&
		len(activationHistoriesResp.GetActivationHistories()) > 0
	return isRepeatSalaryProgramUser, nil
}

func (s *Service) getProbabilityOfRemitterBeingAMerchantString(ctx context.Context, remitterName string) (string, error) {
	const minNameLenAllowedToCategorise = 3
	if len(remitterName) < minNameLenAllowedToCategorise {
		return "", nil
	}

	employerNameCategoriser, employerNameCategoriserErr := s.employerNameCategoriserClient.EmployerNameCategoriser(ctx, &employernamecategoriserPb.EmployerNameCategoriserRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_IN_HOUSE,
		},
		RequestId: uuid.NewString(),
		Name:      remitterName,
	})
	if rpcErr := epifigrpc.RPCError(employerNameCategoriser, employerNameCategoriserErr); rpcErr != nil {
		logger.Error(ctx, "error in EmployerNameCategoriser rpc", zap.Error(rpcErr))
		return "", fmt.Errorf("error in EmployerNameCategoriser rpc")
	}
	probabilityOfRemitterBeingAMerchantString := strconv.FormatFloat(float64(employerNameCategoriser.GetProb()*100), 'f', 5, 64)
	probabilityOfRemitterBeingAMerchantString += "%"
	return probabilityOfRemitterBeingAMerchantString, nil
}

// percentageDiffOfTxnAmtFromLastSalaryAmtString gets the percentage difference between this transaction amount and the salary amount which was verified before this transaction
func (s *Service) percentageDiffOfTxnAmtFromLastSalaryAmtString(ctx context.Context, orderWithTransactions *orderPb.OrderWithTransactions, actorId string) (string, error) {

	var percentageDiffFromLastSalaryString string
	previousSalaryTxnVerificationRequests, getSalaryTxnVerificationRequestsErr := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
		Filters: &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
			ActorId:  actorId,
			UptoDate: timestampPb.New(orderWithTransactions.GetOrder().GetCreatedAt().AsTime()),
			Status:   salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
		SortOrder: salaryprogram.SortOrder_DESC,
	})
	if rpcErr := epifigrpc.RPCError(previousSalaryTxnVerificationRequests, getSalaryTxnVerificationRequestsErr); rpcErr != nil {
		logger.Error(ctx, "error in GetSalaryTxnVerificationRequests rpc", zap.Error(rpcErr))
		return "", fmt.Errorf("error in GetSalaryTxnVerificationRequests rpc")
	}

	if len(previousSalaryTxnVerificationRequests.GetSalaryTxnVerificationRequests()) != 0 {
		previousSalaryTxnVerificationRequest := previousSalaryTxnVerificationRequests.GetSalaryTxnVerificationRequests()[0]
		previousSalaryTransactionOrderRes, getOrderErr := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
			Identifier: &orderPb.GetOrderRequest_ExternalId{
				ExternalId: previousSalaryTxnVerificationRequest.GetTxnId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(previousSalaryTransactionOrderRes, getOrderErr); rpcErr != nil {
			logger.Error(ctx, "error in GetOrder rpc", zap.Error(rpcErr), zap.String(logger.EXTERNAL_ID, previousSalaryTxnVerificationRequest.GetTxnId()))
			return "", fmt.Errorf("error in GetOrder rpc")
		}

		currentTxnAmt := orderWithTransactions.GetOrder().GetAmount().GetUnits()
		previousSalaryAmt := previousSalaryTransactionOrderRes.GetOrder().GetAmount().GetUnits()
		percentageDiffFromLastSalary := ((float64(currentTxnAmt) - float64(previousSalaryAmt)) * float64(100)) / float64(previousSalaryAmt)
		percentageDiffFromLastSalaryString = strconv.FormatFloat(percentageDiffFromLastSalary, 'f', 5, 64)
		percentageDiffFromLastSalaryString += "%"
	}

	return percentageDiffFromLastSalaryString, nil

}

func (s *Service) getTxnRemitterInfo(ctx context.Context, txn *paymentPb.Transaction, order *orderPb.Order) (string, string, string, error) {
	// get remitter details from payment instrument
	var piAccountName string
	pi, getPiErr := s.piClient.GetPiById(ctx, &paymentinstrument.GetPiByIdRequest{
		Id: txn.GetPiFrom(),
	})
	grpcErr := epifigrpc.RPCError(pi, getPiErr)
	switch {
	case grpcErr != nil:
		cxLogger.Error(ctx, "error fetching remitter info from pi", zap.Error(grpcErr), zap.String("p", pi.GetPaymentInstrument().GetId()))
		// dont return as we might have vg name to send to user
	default:
		piAccountName = pi.GetPaymentInstrument().GetAccount().GetName()
	}
	// TODO (Eswar) : remove this logic of fetching remitter info from vg on 25th July.
	var piVerifiedName string
	getRemitterInfoFromVgForTxnsBeforeTime, err := time.Parse(time.RFC3339, GetRemitterInfoFromVgForTxnsBeforeTime)
	switch {
	case err != nil:
		cxLogger.Error(ctx, fmt.Sprintf("error parsing GetRemitterInfoFromVgForTxnsBeforeTime string to time object, err : %s", err))
	case txn.GetCreatedAt().AsTime().Before(getRemitterInfoFromVgForTxnsBeforeTime) &&
		(txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_NEFT || txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_RTGS) &&
		order.GetWorkflow() == orderPb.OrderWorkflow_NO_OP:
		// get remitter details of txn using vg rpc
		piVerifiedName, err = s.getTxnRemitterInfoFromVg(ctx, txn)
		if err != nil {
			cxLogger.Error(ctx, "error fetching remitter info from vg")
			// dont return as we might have pi name to send to user
		}
	default:
		piVerifiedName = pi.GetPaymentInstrument().GetVerifiedName()
	}

	var remitterName string
	// VG api only supports INTRA_BANK and IMPS protocols
	if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_INTRA_BANK || txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_IMPS {
		remitterName, err = s.getTxnRemitterNameFromVgV1(ctx, txn)
		if err != nil {
			cxLogger.Error(ctx, "error in getting remitter name using GetRemitterDetailsV1", zap.Error(err))
		}
	}

	// if both names are empty, then send an error
	if piAccountName == "" && piVerifiedName == "" && remitterName == "" {
		return "", "", "", errors.New("error fetching remitter info from PI and VG")
	}
	return piVerifiedName, piAccountName, remitterName, nil
}

// fetch remitter name from vg federal api, currently supported only for INTRA_BANK and IMPS protocol.
func (s *Service) getTxnRemitterNameFromVgV1(ctx context.Context, txn *paymentPb.Transaction) (string, error) {
	var cbsTransactionId, utr string
	switch txn.GetPaymentProtocol() {
	case paymentPb.PaymentProtocol_INTRA_BANK:
		cbsTransactionId = txn.GetDedupeId().GetCbsId()
	case paymentPb.PaymentProtocol_IMPS:
		utr = txn.GetUtr()
	default:
		return "", fmt.Errorf("payment protocol not supported for fetching remitter info from vg")
	}
	remitterInfoRes, err := s.vgPaymentClient.GetRemitterDetailsV1(ctx, &vgPaymentPb.GetRemitterDetailsV1Request{
		Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		CbsTranId:       cbsTransactionId,
		Utr:             utr,
		TxnDatetime:     txn.GetDedupeId().GetTxnTime(),
		PaymentProtocol: txn.GetPaymentProtocol(),
	})
	if err != nil || !remitterInfoRes.GetStatus().IsSuccess() && !remitterInfoRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error fetching remitter info from VG", zap.String(logger.TXN_ID, txn.GetId()), zap.Any(logger.RPC_STATUS, remitterInfoRes.GetStatus()), zap.Error(err))
		return "", fmt.Errorf("error fetching remitter info from VG")
	}
	if remitterInfoRes.GetStatus().IsRecordNotFound() {
		logger.WarnWithCtx(ctx, "remitter info not found for the txn", zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.PAYMENT_PROTOCOL, txn.GetPaymentProtocol().String()))
		return "", epifierrors.ErrRecordNotFound
	}
	return remitterInfoRes.GetRemitterAccountInfo().GetRemitterName(), nil
}

func (s *Service) getTxnRemitterInfoFromVg(ctx context.Context, txn *paymentPb.Transaction) (string, error) {
	remitterInfoRes, err := s.vgPaymentClient.GetRemitterDetails(ctx, &vgPaymentPb.GetRemitterDetailsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		CbsTranId:   txn.GetDedupeId().GetCbsId(),
		TxnDatetime: txn.GetDedupeId().GetTxnTime(),
	})
	if grpcErr := epifigrpc.RPCError(remitterInfoRes, err); grpcErr != nil {
		cxLogger.Error(ctx, "error fetching remitter info from vg", zap.String(logger.TXN_ID, txn.GetId()),
			zap.Any(logger.RPC_STATUS, remitterInfoRes.GetStatus()), zap.Error(grpcErr))
		return "", fmt.Errorf("error fetching remitter info from vg")
	}
	return remitterInfoRes.GetRemitterName(), nil
}

func (s *Service) getSalaryAccountDetailsForUserAndActor(ctx context.Context, user *userPb.User, actor *types.Actor) (*salaryAccountDetails, error) {
	// fetching user profile and employment related details
	employmentDetails, err := s.getEmploymentAndUserDetails(ctx, actor.GetId(), user)
	if err != nil {
		return nil, errors.Wrap(err, "error getting user employment details")
	}
	resp := &salaryAccountDetails{
		employmentInfo: employmentDetails,
	}

	// fetching kyc details for the user
	kycData, err := s.getKycData(ctx, actor.GetId())
	if err != nil {
		cxLogger.Error(ctx, "error getting user kyc details", zap.Error(err))
		// not returning error since we want to allow partial data to be sent as well
	}
	resp.kycInfo = kycData
	if kycData == nil {
		resp.kycInfo = &salarydataops.KycInfo{
			KycStatus: salarydataops.KycStatus_KYC_STATUS_UNSPECIFIED,
		}
	}

	// fetching salary program details
	salaryProgramInfo, err := s.getSalaryProgramInfoForActor(ctx, actor.GetId())
	if err != nil {
		cxLogger.Error(ctx, "error getting user salary program info", zap.Error(err))
		// not returning error since we want to allow partial data to be sent as well
	}
	resp.salaryProgramInfo = salaryProgramInfo
	return resp, nil
}

// getVerificationFailureReasonCategoryToSubCategoriesMapping get supported salary txn verification reasons category to subcategory display string map
func (s *Service) getVerificationFailureReasonCategoryToSubCategoriesMapping(ctx context.Context, failureReasons map[string]*salaryprogram.VerificationFailureReasonSubCategories) (*salarydataops.SupportedSalaryVerificationFailureReasons, error) {
	var (
		failureReasonsCategoryToDisplayString    = s.salaryOpsConfig.VerificationFailureReasonsCategoryToDisplayString
		failureReasonsSubCategoryToDisplayString = s.salaryOpsConfig.VerificationFailureReasonsSubCategoryToDisplayString
		failureReasonsDisplayStrings             = make(map[string]*salarydataops.SupportedSalaryVerificationFailureReasons_FailureReasonSubCategories)
	)

	for category, subcategories := range failureReasons {
		subcategoriesDisplayStrings := make([]string, 0, len(subcategories.SubCategories))
		for _, subcategory := range subcategories.GetSubCategories() {
			subcategoryDisplayString, exist := failureReasonsSubCategoryToDisplayString[subcategory]
			if !exist {
				cxLogger.Error(ctx, "failed to get verification failure reasons category to subcategories mapping, display name for verification failure reason sub-category not found", zap.String("sub_category", subcategory))
				return nil, fmt.Errorf("display name for verification failure reason sub-category not found")
			}
			subcategoriesDisplayStrings = append(subcategoriesDisplayStrings, subcategoryDisplayString)
		}
		categoryDisplayString, exist := failureReasonsCategoryToDisplayString[category]
		if !exist {
			cxLogger.Error(ctx, "failed to get verification failure reasons category to subcategories mapping, display name for verification failure reason category not found", zap.String("category", category))
			return nil, fmt.Errorf("display name for verification failure reason category not found")
		}
		failureReasonsDisplayStrings[categoryDisplayString] = &salarydataops.SupportedSalaryVerificationFailureReasons_FailureReasonSubCategories{
			SubCategories: subcategoriesDisplayStrings,
		}
	}

	return &salarydataops.SupportedSalaryVerificationFailureReasons{
		CategoryToSubcategoriesFailureReasonsMap: failureReasonsDisplayStrings,
	}, nil
}

// getVerificationStatusFieldsFromInput is used to fill input fields for UpdateManualSalaryTxnVerificationStatus
// function from the data given by the ops agent
func getVerificationStatusFieldsFromInput(employerId string, usersEmployerMatch bool, nameMatchLevel salarydataops.NameMatchLevel) *updateVerificationStatusInput {
	switch {
	case usersEmployerMatch:
		if employerId == "" {
			return &updateVerificationStatusInput{err: epifierrors.ErrInvalidArgument}
		}
		return &updateVerificationStatusInput{
			employerId: employerId,
			status:     salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
		}
	case nameMatchLevel == salarydataops.NameMatchLevel_NAME_MATCH_LEVEL_NO_MATCH:
		return &updateVerificationStatusInput{
			status: salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED,
		}
	case nameMatchLevel == salarydataops.NameMatchLevel_NAME_MATCH_LEVEL_PARTIAL_MATCH:
		return &updateVerificationStatusInput{
			status:    salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
			subStatus: salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_STATUS_ESCALATED_TO_ENGG_TEAM,
		}
	case nameMatchLevel == salarydataops.NameMatchLevel_NAME_MATCH_LEVEL_PERFECT_MATCH:
		if employerId == "" {
			return &updateVerificationStatusInput{err: epifierrors.ErrInvalidArgument}
		}
		return &updateVerificationStatusInput{
			employerId: employerId,
			status:     salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
			subStatus:  salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE,
		}
	default:
		cxLogger.ErrorNoCtx("invalid combination of inputs to update salary transaction verification status")
		return &updateVerificationStatusInput{
			err: epifierrors.ErrInvalidArgument,
		}
	}
}

func (s *Service) isGivenTxnNotCategorizedAsIncome(ctx context.Context, beneficiaryActorId string, txn *paymentPb.Transaction) (bool, error) {
	txnCategoriesRes, err := s.txnCatClient.GetTxnCategoryDetails(ctx, &categorizerPb.GetTxnCategoryDetailsRequest{
		ActorId: beneficiaryActorId,
		ActivityId: &categorizerPb.ActivityId{
			Id: &categorizerPb.ActivityId_TxnId{
				TxnId: txn.GetId(),
			},
		},
		Provenance:   categorizerPb.Provenance_DS,
		DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
	})
	if grpcErr := epifigrpc.RPCError(txnCategoriesRes, err); grpcErr != nil {
		return false, fmt.Errorf("error while fetching txn category details, err: %w", grpcErr)
	}
	txnCategories := txnCategoriesRes.GetTxnCategories()
	// set on ontology ids linked to the txn
	txnCategoryOntologyIdsSet := make(map[string]struct{}, len(txnCategories.GetOntologies()))
	for _, ontology := range txnCategories.GetOntologies() {
		txnCategoryOntologyIdsSet[ontology.GetOntologyId()] = struct{}{}
	}

	// check if the txn was tagged with a non income related ontology id.
	for _, nonIncomeOntologyId := range s.salaryOpsConfig.NonIncomeRelatedTxnCategoryOntologyIds {
		if _, ok := txnCategoryOntologyIdsSet[nonIncomeOntologyId]; ok {
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) getBESortOrder(sortOrder salarydataops.SortOrder) salaryprogram.SortOrder {
	switch sortOrder {
	case salarydataops.SortOrder_ASC:
		return salaryprogram.SortOrder_ASC
	default:
		return salaryprogram.SortOrder_DESC
	}
}

// creates page context response from pageSize, offset and fetched rows count.
// it does not use timestamp for pagination.
func getCustomPageContextResponse(offset uint32, pageSize uint32, fetchedRowCount uint32) (*rpc.PageContextResponse, error) {
	pageCtxResp := &rpc.PageContextResponse{
		BeforeToken: "",
		HasBefore:   false,
		AfterToken:  "",
		HasAfter:    false,
	}
	var err error
	if pageSize < fetchedRowCount {
		afterToken := &pagination.PageToken{
			Offset: offset + pageSize,
		}
		pageCtxResp.AfterToken, err = afterToken.Marshal()
		if err != nil {
			return nil, fmt.Errorf("error marshalling after token, err: %w", err)
		}
		pageCtxResp.HasAfter = true
	}

	if offset > 0 {
		newOffset := offset - pageSize
		if newOffset < 0 {
			newOffset = 0
		}
		beforeToken := &pagination.PageToken{
			Offset: newOffset,
		}
		pageCtxResp.BeforeToken, err = beforeToken.Marshal()
		if err != nil {
			return nil, fmt.Errorf("error marshalling before token, err: %w", err)
		}
		pageCtxResp.HasBefore = true
	}
	return pageCtxResp, nil
}
