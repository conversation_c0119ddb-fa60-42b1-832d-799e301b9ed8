package salarydataops

import (
	"context"
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	bankCustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	categorizerMocks "github.com/epifi/gamma/api/categorizer/mocks"
	"github.com/epifi/gamma/api/cx/data_collector/salarydataops"
	"github.com/epifi/gamma/api/employment"
	employmentMocks "github.com/epifi/gamma/api/employment/mocks"
	vkycMocks "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	"github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	recurringPaymentMocks "github.com/epifi/gamma/api/recurringpayment/mocks"
	"github.com/epifi/gamma/api/salaryprogram"
	cxMocks "github.com/epifi/gamma/api/salaryprogram/cx/mocks"
	healthInsuranceMocks "github.com/epifi/gamma/api/salaryprogram/healthinsurance/mocks"
	salaryProgramMocks "github.com/epifi/gamma/api/salaryprogram/mocks"
	"github.com/epifi/gamma/api/user"
	groupMocks "github.com/epifi/gamma/api/user/group/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	employerNameCategoriserMocks "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser/mocks"
	paymentMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	mockAuthEngine "github.com/epifi/gamma/cx/customer_auth/auth_engine/mocks"
)

type MockedDependencies struct {
	usersClient                   *userMocks.MockUsersClient
	actorClient                   *actorMocks.MockActorClient
	empClient                     *employmentMocks.MockEmploymentClient
	salaryProgramClient           *salaryProgramMocks.MockSalaryProgramClient
	orderServiceClient            *orderMocks.MockOrderServiceClient
	piClient                      *piMocks.MockPiClient
	vkycClient                    *vkycMocks.MockVKYCClient
	groupClient                   *groupMocks.MockGroupClient
	paymentClient                 *paymentMocks.MockPaymentClient
	cxClient                      *cxMocks.MockCxClient
	txnCategorizerClient          *categorizerMocks.MockTxnCategorizerClient
	bankCustomerServiceClient     *bankCustMocks.MockBankCustomerServiceClient
	healthInsuranceClient         *healthInsuranceMocks.MockHealthInsuranceClient
	employerNameCategoriserClient *employerNameCategoriserMocks.MockEmployerNameCategoriserClient
	recurringPaymentServiceClient *recurringPaymentMocks.MockRecurringPaymentServiceClient
	salaryProgramBucketS3Client   *mocks.MockS3Client
	authEngine                    *mockAuthEngine.MockIAuthEngine
}

func NewServiceWithMocks(t *testing.T) (*Service, *MockedDependencies) {
	ctrl := gomock.NewController(t)

	md := &MockedDependencies{
		usersClient:                   userMocks.NewMockUsersClient(ctrl),
		actorClient:                   actorMocks.NewMockActorClient(ctrl),
		empClient:                     employmentMocks.NewMockEmploymentClient(ctrl),
		salaryProgramClient:           salaryProgramMocks.NewMockSalaryProgramClient(ctrl),
		orderServiceClient:            orderMocks.NewMockOrderServiceClient(ctrl),
		piClient:                      piMocks.NewMockPiClient(ctrl),
		vkycClient:                    vkycMocks.NewMockVKYCClient(ctrl),
		groupClient:                   groupMocks.NewMockGroupClient(ctrl),
		paymentClient:                 paymentMocks.NewMockPaymentClient(ctrl),
		cxClient:                      cxMocks.NewMockCxClient(ctrl),
		txnCategorizerClient:          categorizerMocks.NewMockTxnCategorizerClient(ctrl),
		bankCustomerServiceClient:     bankCustMocks.NewMockBankCustomerServiceClient(ctrl),
		healthInsuranceClient:         healthInsuranceMocks.NewMockHealthInsuranceClient(ctrl),
		employerNameCategoriserClient: employerNameCategoriserMocks.NewMockEmployerNameCategoriserClient(ctrl),
		recurringPaymentServiceClient: recurringPaymentMocks.NewMockRecurringPaymentServiceClient(ctrl),
		salaryProgramBucketS3Client:   mocks.NewMockS3Client(ctrl),
		authEngine:                    mockAuthEngine.NewMockIAuthEngine(ctrl),
	}

	svc := &Service{
		UnimplementedSalaryDataOpsServer: salarydataops.UnimplementedSalaryDataOpsServer{},
		salaryOpsDynConf:                 gConf.SalaryOpsConfig(),
		salaryOpsConfig:                  conf.SalaryOpsConfig,
		usersClient:                      md.usersClient,
		actorClient:                      md.actorClient,
		empClient:                        md.empClient,
		salaryClient:                     md.salaryProgramClient,
		orderClient:                      md.orderServiceClient,
		piClient:                         md.piClient,
		vkycClient:                       md.vkycClient,
		userGroupClient:                  md.groupClient,
		vgPaymentClient:                  md.paymentClient,
		salaryCxClient:                   md.cxClient,
		txnCatClient:                     md.txnCategorizerClient,
		bcCLient:                         md.bankCustomerServiceClient,
		healthInsuranceClient:            md.healthInsuranceClient,
		employerNameCategoriserClient:    md.employerNameCategoriserClient,
		recurringPaymentClient:           md.recurringPaymentServiceClient,
		salaryProgramBucketS3Client:      md.salaryProgramBucketS3Client,
		authEngine:                       md.authEngine,
	}

	return svc, md
}

func TestGetDetailedSalaryTxnVerificationRequestsByFilter(t *testing.T) {

	emp123 := "emp_123"
	emp456 := "emp_456"
	emp789 := "emp_789"
	actor1 := "actor_1"
	actor2 := "actor_2"
	actor3 := "actor_3"
	actor4 := "actor_4"
	actor5 := "actor_5"
	actor6 := "actor_6"
	actor7 := "actor_7"
	actor8 := "actor_8"
	actor9 := "actor_9"
	actor10 := "actor_10"
	actor11 := "actor_11"

	actorToEmployerMap := map[string]string{
		actor1:  emp123,
		actor2:  emp456,
		actor3:  emp789,
		actor4:  emp123,
		actor5:  emp456,
		actor6:  emp789,
		actor7:  emp123,
		actor8:  emp456,
		actor9:  emp789,
		actor10: emp123,
		actor11: emp456,
	}

	employerData := map[string]*employment.EmployerInfo{
		emp123: newEmployerInfo(emp123, employment.EmployerSalaryProgramChannel_B2C),
		emp456: newEmployerInfo(emp456, employment.EmployerSalaryProgramChannel_B2C),
		emp789: newEmployerInfo(emp789, employment.EmployerSalaryProgramChannel_B2B),
	}

	req1 := newSalaryTxnVerificationRequest(1, actor1, actorToEmployerMap[actor1])
	req2 := newSalaryTxnVerificationRequest(2, actor2, actorToEmployerMap[actor2])
	req3 := newSalaryTxnVerificationRequest(3, actor3, actorToEmployerMap[actor3])
	req4 := newSalaryTxnVerificationRequest(4, actor4, actorToEmployerMap[actor4])
	req5 := newSalaryTxnVerificationRequest(5, actor5, actorToEmployerMap[actor5])
	req6 := newSalaryTxnVerificationRequest(6, actor6, actorToEmployerMap[actor6])
	req7 := newSalaryTxnVerificationRequest(7, actor7, actorToEmployerMap[actor7])
	req8 := newSalaryTxnVerificationRequest(8, actor8, actorToEmployerMap[actor8])
	req9 := newSalaryTxnVerificationRequest(9, actor9, actorToEmployerMap[actor9])
	req10 := newSalaryTxnVerificationRequest(10, actor10, actorToEmployerMap[actor10])
	req11 := newSalaryTxnVerificationRequest(11, actor11, actorToEmployerMap[actor11])

	reqs := []*salaryprogram.SalaryTxnVerificationRequest{
		req1, req2, req3, req4, req5, req6, req7, req8, req9, req10, req11,
	}

	type args struct {
		ctx context.Context
		req *salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterRequest
	}
	tests := []struct {
		name     string
		args     args
		want     int
		wantErr  bool
		wantMock func(md *MockedDependencies, a *require.Assertions)
	}{
		{
			name: "multiple-page-employer-channel-filter",
			args: args{
				ctx: context.Background(),
				req: &salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterRequest{
					PageContextRequest: &rpc.PageContextRequest{
						Token:    nil,
						PageSize: 5,
					},
					Filter: &salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterRequest_Filter{
						SalaryTxnVerificationStatus: salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_PENDING_VERIFICATION,
						EmployerChannel:             salarydataops.EmployerSalaryProgramChannel_B2C,
					},
					SortOrder: salarydataops.SortOrder_DESC,
				},
			},
			want: 7,
			wantMock: func(md *MockedDependencies, a *require.Assertions) {

				md.salaryProgramClient.EXPECT().
					GetSalaryTxnVerificationRequests(gomock.Any(), gomock.Any()).
					DoAndReturn(func(_ context.Context, req *salaryprogram.GetSalaryTxnVerificationRequestsRequest, x ...interface{}) (*salaryprogram.GetSalaryTxnVerificationRequestsResponse, error) {
						{

							token := pagination.PageToken{}
							err := token.Unmarshal(req.GetPageContext().GetAfterToken())
							a.NoError(err, "couldn't unmarshall page token")
							st := int(token.GetOffset())
							end := st + int(req.GetPageContext().GetPageSize())
							if end > len(reqs) {
								end = len(reqs)
							}
							res := reqs[st:end]
							nextToken := &pagination.PageToken{
								Timestamp: timestamppb.Now(),
								Offset:    uint32(end),
								IsReverse: false,
							}
							nextTokenStr, err := nextToken.Marshal()
							a.NoError(err, "couldn't marshall page token")
							return &salaryprogram.GetSalaryTxnVerificationRequestsResponse{
								Status:                        rpc.StatusOk(),
								SalaryTxnVerificationRequests: res,
								PageContext:                   &rpc.PageContextResponse{AfterToken: nextTokenStr, HasAfter: end < len(reqs)},
							}, nil
						}
					}).AnyTimes()
				md.empClient.EXPECT().GetEmployerOfUser(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, req *employment.GetEmployerOfUserRequest, x ...interface{}) (*employment.GetEmployerOfUserResponse, error) {
					return &employment.GetEmployerOfUserResponse{
						Status: rpc.StatusOk(),
						EmploymentInfo: &employment.GetEmployerOfUserResponse_EmployerInfo{
							EmployerInfo: employerData[actorToEmployerMap[req.GetActorId()]],
						},
					}, nil

				}).AnyTimes()

				md.empClient.EXPECT().GetEmployer(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, req *employment.GetEmployerRequest, x ...interface{}) (*employment.GetEmployerResponse, error) {
					return &employment.GetEmployerResponse{
						Status:       rpc.StatusOk(),
						EmployerInfo: employerData[req.GetEmployerId()],
					}, nil

				}).AnyTimes()
				md.orderServiceClient.EXPECT().GetOrder(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, req *order.GetOrderRequest, x ...interface{}) (*order.GetOrderResponse, error) {
					return &order.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &order.Order{
							Id: req.GetOrderId(),
						},
					}, nil
				}).AnyTimes()

				md.orderServiceClient.EXPECT().GetOrderWithTransactions(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, req *order.GetOrderWithTransactionsRequest, x ...interface{}) (*order.GetOrderWithTransactionsResponse, error) {
					return &order.GetOrderWithTransactionsResponse{
						Status: rpc.StatusOk(),
						OrderWithTransactions: &order.OrderWithTransactions{
							Order: &order.Order{
								Id: req.GetOrderId(),
							},
							Transactions: []*payment.Transaction{{
								Id: req.GetOrderId(),
							}},
						},
					}, nil
				}).AnyTimes()

				md.piClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&paymentinstrument.GetPiByIdResponse{
					Status: rpc.StatusOk(),
				}, nil).AnyTimes()
				md.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
				}, nil).AnyTimes()
				md.salaryProgramClient.EXPECT().GetRegistrationDetails(gomock.Any(), gomock.Any()).Return(&salaryprogram.GetRegistrationDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil).AnyTimes()
				md.salaryProgramClient.EXPECT().GetSalaryProgramActivationHistories(gomock.Any(), gomock.Any()).Return(&salaryprogram.GetSalaryProgramActivationHistoriesResponse{
					Status:              rpc.StatusOk(),
					ActivationHistories: nil,
					PageContext:         nil,
				}, nil).AnyTimes()
				md.usersClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &user.User{
						Profile: &user.Profile{
							PanName: &common.Name{
								FirstName:  "FirstName",
								MiddleName: "MiddleName",
								LastName:   "LastName",
							},
							PhoneNumber: &common.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
						},
					},
				}, nil).AnyTimes()
				return
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := require.New(t)
			srv, md := NewServiceWithMocks(t)

			tt.wantMock(md, a)

			got, err := srv.GetDetailedSalaryTxnVerificationRequestsByFilter(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDetailedSalaryTxnVerificationRequestsByFilter() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(len(got.GetTxnDetails()), tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("Payments are unequal - got = %v, want %v \n diff: %v", got, tt.want, diff)
			}

		})
	}
}

func newSalaryTxnVerificationRequest(id int, actorId, empId string) *salaryprogram.SalaryTxnVerificationRequest {
	idStr := strconv.Itoa(id)
	return &salaryprogram.SalaryTxnVerificationRequest{
		Id:                    "ver_req_" + idStr,
		ActorId:               actorId,
		TxnId:                 "txn_" + idStr,
		RequestSource:         salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST,
		VerificationStatus:    salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
		VerificationSubStatus: salaryprogram.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_OPS_VERIFICATION,
		VerificationVersion:   salaryprogram.SalaryTxnVerificationVersion_VERIFICATION_VERSION_V1,
		TxnEmployerId:         empId,
		TxnTimestamp:          timestamppb.Now(),
		UserAckStatus:         salaryprogram.AcknowledgmentStatus_ACKNOWLEDGED,
		AutoVerifierMeta: &salaryprogram.SalaryAutoVerifierMeta{
			VerificationSuccessMeta: &salaryprogram.SalaryAutoVerifierMeta_VerificationSuccessMeta{
				FieldUsedForTxnRemitterName:    salaryprogram.SalaryAutoVerifierMeta_PAYMENT_INSTRUMENT_VERIFIED_NAME,
				EmployerMatchedWithTxnRemitter: salaryprogram.SalaryAutoVerifierMeta_USER_DECLARED_EMPLOYER,
				RemitterToEmployerMatchLogic:   salaryprogram.SalaryAutoVerifierMeta_DS_NAME_MATCH,
			},
		},
	}
}

func newEmployerInfo(id string, employerChannel employment.EmployerSalaryProgramChannel) *employment.EmployerInfo {
	return &employment.EmployerInfo{
		NameBySource:                   "Employer " + id,
		TradeName:                      "Employer " + id,
		EmployerId:                     id,
		IsEpfRegistered:                false,
		IsVerified:                     true,
		PossibleRemitterNames:          nil,
		Metadata:                       nil,
		SalaryProgramChannel:           employerChannel,
		SalaryProgramEligibilityStatus: employment.EmployerSalaryProgramEligibilityStatus_ELIGIBLE,
	}
}
