package salarydataops

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/cx/data_collector/salarydataops"
	"github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	"github.com/epifi/gamma/api/typesv2/webui"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

func (s *Service) GetHealthInsuranceUserPolicyDetailsForAgents(ctx context.Context, req *salarydataops.GetHealthInsuranceUserPolicyDetailsForAgentsRequest) (*salarydataops.GetHealthInsuranceUserPolicyDetailsForAgentsResponse, error) {
	actorId := req.GetHeader().GetActor().GetId()

	getIssuedPoliciesForActorResponse, err := s.healthInsuranceClient.GetIssuedPoliciesForActor(ctx, &healthinsurance.GetIssuedPoliciesForActorRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getIssuedPoliciesForActorResponse, err); grpcErr != nil {
		cxLogger.Error(ctx, "GetIssuedPoliciesForActor call failed", zap.Error(err))
		return &salarydataops.GetHealthInsuranceUserPolicyDetailsForAgentsResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetIssuedPoliciesForActor call failed"),
		}, nil
	}

	policies := getIssuedPoliciesForActorResponse.GetPolicies()

	// creating policy table for user
	policyDetailsTable := &webui.Table{}
	serialNumberKey := "sNo"
	policyNumberKey := "policyNumber"
	policyActivatedOnDateKey := "policyActivatedOnDate"
	policyActiveTillDateKey := "policyActiveTillDate"
	policyAutoRenewStatusKey := "policyAutoRenewStatus"

	var tableHeaders []*webui.TableHeader
	tableHeaders = append(tableHeaders,
		&webui.TableHeader{
			Label:     "S.No",
			HeaderKey: serialNumberKey,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     "Policy Number",
			HeaderKey: policyNumberKey,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     "Policy Activated On Date",
			HeaderKey: policyActivatedOnDateKey,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     "Policy Active Till Date",
			HeaderKey: policyActiveTillDateKey,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     "Policy Auto - Renew Status",
			HeaderKey: policyAutoRenewStatusKey,
			IsVisible: true,
		},
	)
	policyDetailsTable.TableHeaders = tableHeaders

	var tableRows []*webui.TableRow
	for indx, policy := range policies {
		headerKeyToCellValueMap := make(map[string]string)
		headerKeyToCellValueMap[serialNumberKey] = fmt.Sprintf("%d", indx+1)
		headerKeyToCellValueMap[policyNumberKey] = policy.GetVendorPolicyId()
		headerKeyToCellValueMap[policyActivatedOnDateKey] = policy.GetPolicyActiveFrom().AsTime().In(datetime.IST).Format("January 02, 2006 15:04:05")
		if policy.GetPolicyActiveTill() != nil {
			headerKeyToCellValueMap[policyActiveTillDateKey] = policy.GetPolicyActiveTill().AsTime().In(datetime.IST).Format("January 02, 2006 15:04:05")
		} else {
			headerKeyToCellValueMap[policyActiveTillDateKey] = ""
		}
		if policy.GetPolicyActiveTill() == nil {
			headerKeyToCellValueMap[policyAutoRenewStatusKey] = "ON"
		} else {
			headerKeyToCellValueMap[policyAutoRenewStatusKey] = "OFF"
		}

		tableRow := &webui.TableRow{
			HeaderKeyToCellValueMap: headerKeyToCellValueMap,
		}
		tableRows = append(tableRows, tableRow)
	}
	policyDetailsTable.TableRows = tableRows
	policyDetailsTable.TableName = "User Policy Details"

	return &salarydataops.GetHealthInsuranceUserPolicyDetailsForAgentsResponse{
		Status:        rpc.StatusOk(),
		PolicyDetails: policyDetailsTable,
		NoteInfo:      "Note: When policy auto-renew status is OFF, that means that specific policy will get deactivated on the “Policy Active till Date” ",
	}, nil
}

func (s *Service) GetHealthInsuranceGeneralPolicyInfo(ctx context.Context, req *salarydataops.GetHealthInsuranceGeneralPolicyInfoRequest) (*salarydataops.GetHealthInsuranceGeneralPolicyInfoResponse, error) {
	healthInsurancePolicyFAQDocUrl, err := s.getHealthInsurancePolicyFAQDocUrl(ctx)
	if err != nil {
		cxLogger.Error(ctx, "error in getting healthInsurancePolicyFAQDocUrl", zap.Error(err))
		return &salarydataops.GetHealthInsuranceGeneralPolicyInfoResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in getting healthInsurancePolicyFAQDocUrl"),
		}, nil
	}

	healthInsurancePolicyClaimProcessDocUrl, err := s.getHealthInsurancePolicyClaimProcessDocUrl(ctx)
	if err != nil {
		cxLogger.Error(ctx, "error in getting healthInsurancePolicyClaimProcessDocUrl", zap.Error(err))
		return &salarydataops.GetHealthInsuranceGeneralPolicyInfoResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in getting healthInsurancePolicyClaimProcessDocUrl"),
		}, nil
	}

	healthInsuranceInclusionExclusionAndHowItWorksDocUrl, err := s.getHealthInsuranceInclusionExclusionAndHowItWorksDocUrl(ctx)
	if err != nil {
		cxLogger.Error(ctx, "error in getting healthInsuranceInclusionExclusionAndHowItWorksDocUrl", zap.Error(err))
		return &salarydataops.GetHealthInsuranceGeneralPolicyInfoResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in getting healthInsuranceInclusionExclusionAndHowItWorksDocUrl"),
		}, nil
	}

	healthInsuranceTncsDocUrl, err := s.getHealthInsuranceTncsDocUrl(ctx)
	if err != nil {
		cxLogger.Error(ctx, "error in getting healthInsuranceTncsDocUrl", zap.Error(err))
		return &salarydataops.GetHealthInsuranceGeneralPolicyInfoResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in getting healthInsuranceTncsDocUrl"),
		}, nil
	}

	var infoSections []*webui.LabelValue
	policyRelatedInfoSection := &webui.LabelValue{
		Label: "For policy related information, if needed please direct users to contact:",
		Value: []string{
			"<p><b>Riskcovry:</b></p>",
			"<p>Vimlesh Yadav - **********</p>",
			"<p>Archana Soni - **********</p>",
			"<p><b>Insurer contact details:</b></p>",
			"<p>Toll-Free: 1800-102-4462</p>",
			"<p>Email: <EMAIL></p>",
			"<p>You may also contact:</p>",
			"<p>Head of Customer Service,</p>",
			"<p>Manipal Cigna Health Insurance Company Limited,</p>",
			"<p>401/402, Raheja Titanium, Western Express Highway,</p>",
			"<p>Goregaon (East), Mumbai – 400063</p>",
			"<p>Email: <EMAIL></p>",
		},
	}

	claimsRelatedInfoSection := &webui.LabelValue{
		Label: "For claims related information, if needed please direct users to contact:",
		Value: []string{
			"<p><b>Insurer contact details:</b></p>",
			"<p>Medi Assist Insurance TPA Pvt. Ltd.</p>",
			"<p>Tower D, 4th Floor, IBC Knowledge Park, 4/1, Bannerghatta Road,</p>",
			"<p>Bengaluru, Bengaluru, Karnataka – 560020</p>",
			"<p><li>Toll-Free Healthline No: 18004259449</li></p>",
			"<p><li>Fax Number: 1800-419-1159</li></p>",
			"<p><li>Email: <EMAIL></li></p>",
			"<p><li>Web: www.mediassistindia.com</li></p>",
			"<p>You may also <NAME_EMAIL>, and for claim-</p>",
			"<p>related queries, call the toll-free Health Line No: 1800-102-4462.</p>",
		},
	}

	otherHelpfulLinksInfoSection := &webui.LabelValue{
		Label: "Other helpful links:",
		Value: []string{
			"<p><b>You can find:</b></p>",
			fmt.Sprintf("<p><li>Policy Details, Inclusions/Exclusions and how it works <a href=\"%s\">here</a></li></p>", healthInsuranceInclusionExclusionAndHowItWorksDocUrl),
			fmt.Sprintf("<p><li>Policy Terms and Conditions <a href=\"%s\">here</a></li></p>", healthInsuranceTncsDocUrl),
			fmt.Sprintf("<p><li>Policy FAQs <a href=\"%s\">here</a></li></p>", healthInsurancePolicyFAQDocUrl),
			fmt.Sprintf("<p><li>Policy Claims Process <a href=\"%s\">here</a></li></p>", healthInsurancePolicyClaimProcessDocUrl),
		},
	}

	infoSections = append(infoSections,
		policyRelatedInfoSection,
		claimsRelatedInfoSection,
		otherHelpfulLinksInfoSection,
	)
	noteInfo := "Please be aware that the policy is offered by Manipal Cigna Health Insurance Company (the “Insurer”) and Epifi Technologies only provides in-app access to user in partnership with digital insuretech company, Riskcovry"

	return &salarydataops.GetHealthInsuranceGeneralPolicyInfoResponse{
		Status:       rpc.StatusOk(),
		NoteInfo:     noteInfo,
		InfoSections: infoSections,
	}, nil
}

func (s *Service) getHealthInsurancePolicyFAQDocUrl(ctx context.Context) (string, error) {
	policyFAQDocS3Path := s.salaryOpsDynConf.SalaryProgramHealthInsuranceConfig().PolicyFAQsDocS3Path()
	if policyFAQDocS3Path == "" {
		return "", fmt.Errorf("empty s3 file path for policy faqs doc")
	}
	policyFAQDocSignedUrl, err := s.salaryProgramBucketS3Client.GetPreSignedUrl(ctx, policyFAQDocS3Path, 30*time.Minute)
	if err != nil {
		return "", fmt.Errorf("error generating s3 signed url for policy FAQ doc, err : %w", err)
	}
	return policyFAQDocSignedUrl, nil
}

func (s *Service) getHealthInsurancePolicyClaimProcessDocUrl(ctx context.Context) (string, error) {
	policyClaimProcessDocS3Path := s.salaryOpsDynConf.SalaryProgramHealthInsuranceConfig().PolicyClaimProcessDocS3Path()
	if policyClaimProcessDocS3Path == "" {
		return "", fmt.Errorf("empty s3 file path for policy claim process doc")
	}
	policyClaimProcessDocSignedUrl, err := s.salaryProgramBucketS3Client.GetPreSignedUrl(ctx, policyClaimProcessDocS3Path, 30*time.Minute)
	if err != nil {
		return "", fmt.Errorf("error generating s3 signed url for policy claim process doc, err : %w", err)
	}
	return policyClaimProcessDocSignedUrl, nil
}

func (s *Service) getHealthInsuranceInclusionExclusionAndHowItWorksDocUrl(ctx context.Context) (string, error) {
	cashlessHospitalListDocS3Path := s.salaryOpsDynConf.SalaryProgramHealthInsuranceConfig().InclusionExclusionAndHowItWorksDocS3Path()
	if cashlessHospitalListDocS3Path == "" {
		return "", fmt.Errorf("empty s3 file path for cashless hospital list doc")
	}
	cashlessHospitalListDocSignedUrl, err := s.salaryProgramBucketS3Client.GetPreSignedUrl(ctx, cashlessHospitalListDocS3Path, 30*time.Minute)
	if err != nil {
		return "", fmt.Errorf("error generating s3 signed url for cashless hospital list doc, err : %w", err)
	}
	return cashlessHospitalListDocSignedUrl, nil
}

func (s *Service) getHealthInsuranceTncsDocUrl(ctx context.Context) (string, error) {
	tncsDocS3Path := s.salaryOpsDynConf.SalaryProgramHealthInsuranceConfig().TncsDocS3Path()
	if tncsDocS3Path == "" {
		return "", fmt.Errorf("empty s3 file path for healthinsurance tncs doc")
	}
	tncsDocSignedUrl, err := s.salaryProgramBucketS3Client.GetPreSignedUrl(ctx, tncsDocS3Path, 30*time.Minute)
	if err != nil {
		return "", fmt.Errorf("error generating s3 signed url for healthinsurance tncs doc, err : %w", err)
	}
	return tncsDocSignedUrl, nil
}
