package salarydataops

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/cx/data_collector/salarydataops"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/salaryprogram"
	salarycx "github.com/epifi/gamma/api/salaryprogram/cx"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

func (s *Service) GetUserDetailsNeededForRaisingSalaryVerification(ctx context.Context, req *salarydataops.GetUserDetailsNeededForRaisingSalaryVerificationRequest) (*salarydataops.GetUserDetailsNeededForRaisingSalaryVerificationResponse, error) {
	var (
		employmentInfo                       *salarydataops.EmploymentInfo
		lastVerifiedSalaryTxnVerificationReq *salaryprogram.SalaryTxnVerificationRequest
		actorId                              = req.GetActorId()
	)

	errGroup, gctx := errgroup.WithContext(ctx)
	// fetching employment and user details
	errGroup.Go(func() error {
		res, err := s.getEmploymentAndUserDetails(gctx, actorId, nil)
		if err != nil {
			cxLogger.Error(gctx, "error getting users employment details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return fmt.Errorf("error getting user's employment details")
		}
		employmentInfo = res
		return nil
	})
	// fetching salary txn verification requests
	errGroup.Go(func() error {
		res, err := s.salaryClient.GetSalaryTxnVerificationRequests(gctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
			Filters: &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
				ActorId: actorId,
				Status:  salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
			},
			SortOrder: salaryprogram.SortOrder_DESC,
			PageContext: &rpc.PageContextRequest{
				PageSize: 1,
			},
		})
		if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil {
			cxLogger.Error(gctx, "error getting salary txn verification requests", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(grpcErr))
			return fmt.Errorf("error getting salary txn verification requests")
		}

		if len(res.GetSalaryTxnVerificationRequests()) != 0 {
			lastVerifiedSalaryTxnVerificationReq = res.GetSalaryTxnVerificationRequests()[0]
		}
		return nil
	})

	if errGrpErr := errGroup.Wait(); errGrpErr != nil {
		return &salarydataops.GetUserDetailsNeededForRaisingSalaryVerificationResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting details needed for raise salary verification req"),
		}, nil
	}

	var txnDetails *salarydataops.TxnDetails
	if lastVerifiedSalaryTxnVerificationReq != nil {
		var err error
		txnDetails, err = s.getTxnDetails(ctx, lastVerifiedSalaryTxnVerificationReq, true, false, false, false)
		if err != nil {
			cxLogger.Error(ctx, "error getting last verified salary txn details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return &salarydataops.GetUserDetailsNeededForRaisingSalaryVerificationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error getting last verified salary txn details"),
			}, nil
		}
	}

	logger.Info(ctx, "opened user details section in raise-a-request dashboard", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("salaryStage", req.GetSalaryProgramStage().String()))

	return &salarydataops.GetUserDetailsNeededForRaisingSalaryVerificationResponse{
		Status:                 rpc.StatusOk(),
		EmploymentInfo:         employmentInfo,
		LastVerifiedTxnDetails: txnDetails,
	}, nil
}

func (s *Service) GetPossibleActorsForSalaryVerification(ctx context.Context, req *salarydataops.GetPossibleActorsForSalaryVerificationRequest) (*salarydataops.GetPossibleActorsForSalaryVerificationResponse, error) {
	var (
		actorId        string
		sortOrder      = salarycx.SortOrder_DESC
		userIdentifier = req.GetFilters().GetUserIdentifier()
	)

	// get actor id from user identifier.
	switch userIdentifier.GetIdentifier().(type) {
	case *salarydataops.GetPossibleActorsForSalaryVerificationRequest_UserIdentifier_ActorId:
		actorId = userIdentifier.GetActorId()
	case *salarydataops.GetPossibleActorsForSalaryVerificationRequest_UserIdentifier_PhoneNumber:
		phoneNumber := userIdentifier.GetPhoneNumber()
		_, actor, err := s.getUserAndActorFromPhoneNumber(ctx, phoneNumber)
		if err != nil {
			return nil, err
		}
		actorId = actor.GetId()
	case *salarydataops.GetPossibleActorsForSalaryVerificationRequest_UserIdentifier_EmailId:
		emailId := userIdentifier.GetEmailId()
		_, actor, err := s.getUserAndActorFromEmailId(ctx, emailId)
		if err != nil {
			return nil, err
		}
		actorId = actor.GetId()
	}

	// map cx sort order to salary sort order enum
	if req.GetSortOrder() == salarydataops.SortOrder_ASC {
		sortOrder = salarycx.SortOrder_ASC
	}
	// get possible actors for manual salary verification

	possibleActorsRes, possibleActorsErr := s.salaryCxClient.GetPossibleActorsForManualSalaryVerification(ctx, &salarycx.GetPossibleActorsForManualSalaryVerificationRequest{
		Filters: &salarycx.GetPossibleActorsForManualSalaryVerificationRequest_Filters{
			ActorId:                                 actorId,
			UserSalaryVerificationEligibilityStatus: s.getBESalaryVerificationEligibilityStatusFromCXStatus(req.GetFilters().GetUserSalaryVerificationEligibilityStatus()),
			SalaryProgramStage:                      SalaryProgramStageBEToCXMapping[req.GetFilters().GetSalaryProgramStage()],
		},
		SortOrder:          sortOrder,
		PageContextRequest: req.GetPageContextRequest(),
	})
	if grpcErr := epifigrpc.RPCError(possibleActorsRes, possibleActorsErr); grpcErr != nil {
		cxLogger.Error(ctx, "error fetching possible actors for manual salary verification", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("eligibility_status", req.GetFilters().GetUserSalaryVerificationEligibilityStatus().String()), zap.Error(grpcErr))
		return &salarydataops.GetPossibleActorsForSalaryVerificationResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching possible actors for manual salary verification"),
		}, nil
	}
	if len(possibleActorsRes.GetActorsInfo()) == 0 {
		cxLogger.Info(ctx, "no possible actors found for salary verification", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("eligibility_status", req.GetFilters().GetUserSalaryVerificationEligibilityStatus().String()))
		return &salarydataops.GetPossibleActorsForSalaryVerificationResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	// extracting possible actors' actor-ids to get actor entity details.
	possibleActorsIds := make([]string, 0, len(possibleActorsRes.GetActorsInfo()))
	for _, actor := range possibleActorsRes.GetActorsInfo() {
		possibleActorsIds = append(possibleActorsIds, actor.GetActorId())
	}

	possibleActorsEntityDetailsRes, err := s.actorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{
		ActorIds: possibleActorsIds,
	})
	if grpcErr := epifigrpc.RPCError(possibleActorsEntityDetailsRes, err); grpcErr != nil {
		cxLogger.Error(ctx, "error fetching possible actors for manual salary verification", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(grpcErr))
		return &salarydataops.GetPossibleActorsForSalaryVerificationResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching possible actors for manual salary verification"),
		}, nil
	}

	// mapping actor id to actor's entity details.
	possibleActorIdToEntityDetailsMap := make(map[string]*actorPb.GetEntityDetailsResponse_EntityDetail)
	for _, possibleActorsEntityDetails := range possibleActorsEntityDetailsRes.GetEntityDetails() {
		possibleActorIdToEntityDetailsMap[possibleActorsEntityDetails.GetActorId()] = possibleActorsEntityDetails
	}

	// creating actor info required for checking eligibility for raising salary verification request.
	possibleActorsSalaryVerificationEligibiltyInfo := make([]*salarydataops.GetPossibleActorsForSalaryVerificationResponse_ActorInfo, 0, len(possibleActorsRes.GetActorsInfo()))
	for _, actor := range possibleActorsRes.GetActorsInfo() {
		actorEntityDetails := possibleActorIdToEntityDetailsMap[actor.GetActorId()]
		possibleActorsSalaryVerificationEligibiltyInfo = append(possibleActorsSalaryVerificationEligibiltyInfo, &salarydataops.GetPossibleActorsForSalaryVerificationResponse_ActorInfo{
			ActorId:                                 actor.GetActorId(),
			ActorName:                               actorEntityDetails.GetName(),
			ActorPhoneNumber:                        actorEntityDetails.GetMobileNumber(),
			LastVerifiedSalaryTxnTimestamp:          actor.GetLastVerifiedSalaryTxnTimestamp(),
			EmployerName:                            actor.GetEmployerName(),
			UserSalaryVerificationEligibilityStatus: SalaryVerificationEligibilityStatusBEToCXMapping[actor.GetUserSalaryVerificationEligibilityStatus()],
			SalaryProgramRegistrationCompletionTime: actor.GetRegistrationCompletionTime(),
		})
		logger.Info(ctx, "actor loaded in raise-a-request queue", zap.String(logger.ACTOR_ID_V2, actor.GetActorId()), zap.String("salaryStage", req.GetFilters().GetSalaryProgramStage().String()))
	}
	return &salarydataops.GetPossibleActorsForSalaryVerificationResponse{
		Status:              rpc.StatusOk(),
		PageContextResponse: possibleActorsRes.GetPageContextResponse(),
		ActorsInfo:          possibleActorsSalaryVerificationEligibiltyInfo,
	}, nil
}

func (s *Service) UpdateSalaryVerificationEligibilityStatus(ctx context.Context, req *salarydataops.UpdateSalaryVerificationEligibilityStatusRequest) (*salarydataops.UpdateSalaryVerificationEligibilityStatusResponse, error) {
	var (
		orderId string
	)
	if req.GetUpdatedVerificationEligibilityStatus() == salarydataops.UserSalaryVerificationEligibilityStatus_USER_SALARY_VERIFICATION_ELIGIBILITY_STATUS_COMPLETE {
		orderRes, orderErr := s.orderClient.GetOrder(ctx, &order.GetOrderRequest{
			Identifier: &order.GetOrderRequest_ExternalId{
				ExternalId: req.GetTxnId(),
			},
		})
		if grpcErr := epifigrpc.RPCError(orderRes, orderErr); grpcErr != nil {
			cxLogger.Error(ctx, "error fetching order by external id", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.EXTERNAL_ORDER_ID, req.GetTxnId()), zap.Error(grpcErr))
			return &salarydataops.UpdateSalaryVerificationEligibilityStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("error fetching order by external id"),
			}, nil
		}
		orderId = orderRes.GetOrder().GetId()
	}
	updateRes, updateErr := s.salaryCxClient.UpdateUserSalaryVerificationEligibilityStatus(ctx, &salarycx.UpdateUserSalaryVerificationEligibilityStatusRequest{
		ActorId: req.GetActorId(),
		// required for updating the status to COMPLETE.
		// todo(yuvraj) change TxnId name to orderId and update contract.
		TxnId: orderId,
		UpdatedUserSalaryVerificationEligibilityStatus: s.getBESalaryVerificationEligibilityStatusFromCXStatus(req.GetUpdatedVerificationEligibilityStatus()),
	})
	if grpcErr := epifigrpc.RPCError(updateRes, updateErr); grpcErr != nil {
		cxLogger.Error(ctx, "error updating user salary verification eligibility status", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("eligibility_status", req.GetUpdatedVerificationEligibilityStatus().String()), zap.Error(grpcErr))
		return &salarydataops.UpdateSalaryVerificationEligibilityStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg("error updating user salary verification eligibility status"),
		}, nil
	}

	// info log for metrics
	switch req.GetUpdatedVerificationEligibilityStatus() {
	case salarydataops.UserSalaryVerificationEligibilityStatus_USER_SALARY_VERIFICATION_ELIGIBILITY_STATUS_COMPLETE:
		logger.Info(ctx, "raised salary txn ver req from raise-a-request queue", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("salaryStage", req.GetSalaryProgramStage().String()))
	case salarydataops.UserSalaryVerificationEligibilityStatus_USER_SALARY_VERIFICATION_ELIGIBILITY_STATUS_PENDING:
		logger.Info(ctx, "update eligibility status of raise-a-request req to pending", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("salaryStage", req.GetSalaryProgramStage().String()))
	}

	return &salarydataops.UpdateSalaryVerificationEligibilityStatusResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) getBESalaryVerificationEligibilityStatusFromCXStatus(status salarydataops.UserSalaryVerificationEligibilityStatus) salarycx.UserSalaryVerificationEligibilityStatus {
	var beUserSalaryVerificationEligibilityStatus salarycx.UserSalaryVerificationEligibilityStatus
	for beStatus, cxStatus := range SalaryVerificationEligibilityStatusBEToCXMapping {
		if status == cxStatus {
			beUserSalaryVerificationEligibilityStatus = beStatus
			break
		}
	}
	return beUserSalaryVerificationEligibilityStatus
}
