package salarydataops

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/cx/data_collector/salarydataops"
	paymentInstrumentPb "github.com/epifi/gamma/api/paymentinstrument"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

func (s *Service) GetSalaryLiteMandateDetailsForAgents(ctx context.Context, req *salarydataops.GetSalaryLiteMandateDetailsForAgentsRequest) (*salarydataops.GetSalaryLiteMandateDetailsForAgentsResponse, error) {
	isActionRequired, sherlockDeeplink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &salarydataops.GetSalaryLiteMandateDetailsForAgentsResponse{
			Status:           rpc.StatusOk(),
			SherlockDeeplink: sherlockDeeplink,
		}, nil
	}

	salaryLiteMandateRes, err := s.salaryClient.GetSalaryLiteMandateRequests(ctx, &salaryPb.GetSalaryLiteMandateRequestsRequest{
		Filters: &salaryPb.GetSalaryLiteMandateRequestsRequest_Filters{
			ActorId: req.GetHeader().GetActor().GetId(),
		},
		SortOrder:          s.getBESortOrder(req.GetSortOrder()),
		PageContextRequest: req.GetPageContextRequest(),
	})
	if rpcErr := epifigrpc.RPCError(salaryLiteMandateRes, err); rpcErr != nil {
		if salaryLiteMandateRes.GetStatus().IsRecordNotFound() {
			return &salarydataops.GetSalaryLiteMandateDetailsForAgentsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "salaryClient.GetSalaryLiteMandateRequests call failed", zap.Error(rpcErr))
		return &salarydataops.GetSalaryLiteMandateDetailsForAgentsResponse{
			Status: rpc.StatusInternalWithDebugMsg("salaryClient.GetSalaryLiteMandateRequests call failed"),
		}, nil
	}

	mandateDetailsTable, err := s.getMandateDetailsTable(ctx, salaryLiteMandateRes.GetSalaryLiteMandateRequests())
	if err != nil {
		cxLogger.Error(ctx, "error while getting mandate details table", zap.Error(err))
		return &salarydataops.GetSalaryLiteMandateDetailsForAgentsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting mandate details table"),
		}, nil
	}

	return &salarydataops.GetSalaryLiteMandateDetailsForAgentsResponse{
		MandatesTable:       mandateDetailsTable,
		PageContextResponse: salaryLiteMandateRes.GetPageContextResponse(),
		Status:              rpc.StatusOk(),
	}, nil
}

// nolint: funlen
func (s *Service) getMandateDetailsTable(ctx context.Context, mandates []*salaryPb.SalaryLiteMandateRequest) (*webui.Table, error) {
	const (
		MandateId             = "Mandate Id"
		MandateStatus         = "Mandate Status"
		StartDate             = "Start Date"
		EndDate               = "End Date"
		SourceBankAccNo       = "Source Bank Account No."
		SourceBankAccName     = "Source Bank Account Name"
		SourceBankIfscCode    = "Source Bank IFSC Code"
		PreferredTransferDate = "Preferred Transfer Date"
		RegistrationInitDate  = "Registration Initiation Date"
		LastUpdateDate        = "Last Updated Date"
	)

	mandateDetailsTable := &webui.Table{}
	mandateDetailsTable.TableName = "Salary lite mandates"
	mandateDetailsTable.TableHeaders = []*webui.TableHeader{}
	mandateDetailsTable.TableRows = []*webui.TableRow{}
	mandateDetailsTable.TableHeaders = append(mandateDetailsTable.TableHeaders,
		&webui.TableHeader{
			Label:     MandateId,
			HeaderKey: MandateId,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     MandateStatus,
			HeaderKey: MandateStatus,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     StartDate,
			HeaderKey: StartDate,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     EndDate,
			HeaderKey: EndDate,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     SourceBankAccNo,
			HeaderKey: SourceBankAccNo,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     SourceBankAccName,
			HeaderKey: SourceBankAccName,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     SourceBankIfscCode,
			HeaderKey: SourceBankIfscCode,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     PreferredTransferDate,
			HeaderKey: PreferredTransferDate,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     RegistrationInitDate,
			HeaderKey: RegistrationInitDate,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     LastUpdateDate,
			HeaderKey: LastUpdateDate,
			IsVisible: true,
		},
	)
	for _, mandate := range mandates {
		remitterPi, activationInterval, err := s.getSalaryLiteMandateRemitterPiAndActivationInterval(ctx, mandate.GetRecurringPaymentId())
		if err != nil {
			return nil, err
		}
		headerKeyToTableCellMap := make(map[string]*webui.TableCell)
		headerKeyToTableCellMap[MandateId] = &webui.TableCell{
			Value: mandate.GetId(),
		}
		headerKeyToTableCellMap[MandateStatus] = &webui.TableCell{
			Value: mandate.GetRequestStatus().String(),
		}
		headerKeyToTableCellMap[StartDate] = &webui.TableCell{
			Value: activationInterval.GetStartTime().AsTime().In(datetime.IST).Format(time.RFC822),
		}
		headerKeyToTableCellMap[EndDate] = &webui.TableCell{
			Value: activationInterval.GetEndTime().AsTime().In(datetime.IST).Format(time.RFC822),
		}
		headerKeyToTableCellMap[SourceBankAccNo] = &webui.TableCell{
			Value: remitterPi.GetAccount().GetSecureAccountNumber(),
		}
		headerKeyToTableCellMap[SourceBankIfscCode] = &webui.TableCell{
			Value: remitterPi.GetAccount().GetIfscCode(),
		}
		headerKeyToTableCellMap[SourceBankAccName] = &webui.TableCell{
			Value: remitterPi.GetAccount().GetName(),
		}
		headerKeyToTableCellMap[PreferredTransferDate] = &webui.TableCell{
			Value: strconv.FormatUint(uint64(mandate.GetPreferredExecutionDayOfMonth()), 10),
		}
		headerKeyToTableCellMap[RegistrationInitDate] = &webui.TableCell{
			Value: mandate.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
		}
		headerKeyToTableCellMap[LastUpdateDate] = &webui.TableCell{
			Value: mandate.GetUpdatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
		}

		mandateDetailsTable.TableRows = append(mandateDetailsTable.TableRows, &webui.TableRow{
			HeaderKeyCellMap: headerKeyToTableCellMap,
		})
	}
	return mandateDetailsTable, nil
}

// nolint:funlen
func (s *Service) GetSalaryLiteMandateExecutionDetailsForAgents(ctx context.Context, req *salarydataops.GetSalaryLiteMandateExecutionDetailsForAgentsRequest) (*salarydataops.GetSalaryLiteMandateExecutionDetailsForAgentsResponse, error) {
	isActionRequired, sherlockDeeplink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		return &salarydataops.GetSalaryLiteMandateExecutionDetailsForAgentsResponse{
			Status:           rpc.StatusOk(),
			SherlockDeeplink: sherlockDeeplink,
		}, nil
	}

	salaryLiteMandateRes, err := s.salaryClient.GetSalaryLiteMandateRequests(ctx, &salaryPb.GetSalaryLiteMandateRequestsRequest{
		Filters: &salaryPb.GetSalaryLiteMandateRequestsRequest_Filters{
			Id: req.GetFilters().GetSalaryLiteMandateId(),
		},
		PageContextRequest: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})
	if rpcErr := epifigrpc.RPCError(salaryLiteMandateRes, err); rpcErr != nil {
		if salaryLiteMandateRes.GetStatus().IsRecordNotFound() {
			return &salarydataops.GetSalaryLiteMandateExecutionDetailsForAgentsResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg("no salary lite mandate present"),
			}, nil
		}
		cxLogger.Error(ctx, "salaryClient.GetSalaryLiteMandateRequests call failed", zap.Error(rpcErr))
		return &salarydataops.GetSalaryLiteMandateExecutionDetailsForAgentsResponse{
			Status: rpc.StatusInternalWithDebugMsg("salaryClient.GetSalaryLiteMandateRequests call failed"),
		}, nil
	}

	salaryLiteMandateExecutionRes, err := s.salaryClient.GetSalaryLiteMandateExecutionRequests(ctx, &salaryPb.GetSalaryLiteMandateExecutionRequestsRequest{
		Filters: &salaryPb.GetSalaryLiteMandateExecutionRequestsRequest_Filters{
			RecurringPaymentId: salaryLiteMandateRes.GetSalaryLiteMandateRequests()[0].GetRecurringPaymentId(),
			CreatedAtFromTime:  req.GetFilters().GetCreatedFrom(),
			CreatedAtTillTime:  req.GetFilters().GetCreatedTill(),
			SortOrder:          s.getBESortOrder(req.GetSortOrder()),
		},
		PageContextRequest: req.GetPageContextRequest(),
	})
	if rpcErr := epifigrpc.RPCError(salaryLiteMandateExecutionRes, err); rpcErr != nil {
		if salaryLiteMandateExecutionRes.GetStatus().IsRecordNotFound() {
			return &salarydataops.GetSalaryLiteMandateExecutionDetailsForAgentsResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg("no salary lite mandate executions present"),
			}, nil
		}
		cxLogger.Error(ctx, "salaryClient.GetSalaryLiteMandateExecutionRequests call failed", zap.Error(rpcErr))
		return &salarydataops.GetSalaryLiteMandateExecutionDetailsForAgentsResponse{
			Status: rpc.StatusInternalWithDebugMsg("salaryClient.GetSalaryLiteMandateExecutionRequests call failed"),
		}, nil
	}

	mandateExecutionsTable := s.getSalaryLiteMandateExecutionsTable(salaryLiteMandateExecutionRes.GetSalaryLiteMandateExecutionRequests())

	return &salarydataops.GetSalaryLiteMandateExecutionDetailsForAgentsResponse{
		Status:                 rpc.StatusOk(),
		MandateExecutionsTable: mandateExecutionsTable,
		PageContextResponse:    salaryLiteMandateExecutionRes.GetPageContextResponse(),
	}, nil
}

func (s *Service) getSalaryLiteMandateRemitterPiAndActivationInterval(ctx context.Context, recurringPaymentId string) (*paymentInstrumentPb.PaymentInstrument, *types.Interval, error) {
	recurringPaymentRes, err := s.recurringPaymentClient.GetRecurringPaymentById(ctx, &recurringPaymentPb.GetRecurringPaymentByIdRequest{
		Id: recurringPaymentId,
	})
	if rpcErr := epifigrpc.RPCError(recurringPaymentRes, err); rpcErr != nil {
		return nil, nil, fmt.Errorf("recurringPaymentClient.GetRecurringPaymentById call failed, rpId: %s, err: %w", recurringPaymentId, rpcErr)
	}
	piRes, err := s.piClient.GetPiById(ctx, &paymentInstrumentPb.GetPiByIdRequest{
		Id: recurringPaymentRes.GetRecurringPayment().GetPiFrom(),
	})
	if rpcErr := epifigrpc.RPCError(piRes, err); rpcErr != nil {
		return nil, nil, fmt.Errorf("piClient.GetPiById call failed, piId: %s, err: %w", recurringPaymentRes.GetRecurringPayment().GetPiFrom(), rpcErr)
	}

	return piRes.GetPaymentInstrument(), recurringPaymentRes.GetRecurringPayment().GetInterval(), nil
}

// nolint: funlen
func (s *Service) getSalaryLiteMandateExecutionsTable(mandateExecutions []*salaryPb.SalaryLiteMandateExecutionRequest) *webui.Table {
	const (
		ExecutionId       = "Id"
		ExecutionStatus   = "Status"
		ExecutionInitDate = "Transfer Initiation Date"
		LastUpdateDate    = "Last Updated Date"
	)

	mandateDetailsTable := &webui.Table{}
	mandateDetailsTable.TableName = "Salary lite mandate transfers"
	mandateDetailsTable.TableHeaders = []*webui.TableHeader{}
	mandateDetailsTable.TableRows = []*webui.TableRow{}
	mandateDetailsTable.TableHeaders = append(mandateDetailsTable.TableHeaders,
		&webui.TableHeader{
			Label:     ExecutionId,
			HeaderKey: ExecutionId,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     ExecutionStatus,
			HeaderKey: ExecutionStatus,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     ExecutionInitDate,
			HeaderKey: ExecutionInitDate,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     LastUpdateDate,
			HeaderKey: LastUpdateDate,
			IsVisible: true,
		},
	)
	for _, mandateExecution := range mandateExecutions {
		headerKeyToTableCellMap := make(map[string]*webui.TableCell)
		headerKeyToTableCellMap[ExecutionId] = &webui.TableCell{
			Value: mandateExecution.GetId(),
		}
		headerKeyToTableCellMap[ExecutionStatus] = &webui.TableCell{
			Value: mandateExecution.GetRequestStatus().String(),
		}
		headerKeyToTableCellMap[ExecutionInitDate] = &webui.TableCell{
			Value: mandateExecution.GetCreatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
		}
		headerKeyToTableCellMap[LastUpdateDate] = &webui.TableCell{
			Value: mandateExecution.GetUpdatedAt().AsTime().In(datetime.IST).Format(time.RFC822),
		}

		mandateDetailsTable.TableRows = append(mandateDetailsTable.TableRows, &webui.TableRow{
			HeaderKeyCellMap: headerKeyToTableCellMap,
		})
	}
	return mandateDetailsTable
}
