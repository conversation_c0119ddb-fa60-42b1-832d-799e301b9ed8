package salarydataops

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	actorPb "github.com/epifi/gamma/api/actor"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/cx/data_collector/salarydataops"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/order"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/salaryprogram"
	salarycx "github.com/epifi/gamma/api/salaryprogram/cx"
	"github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroup "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	"github.com/epifi/gamma/cx/config"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxLogger "github.com/epifi/gamma/cx/logger"
	wireTypes "github.com/epifi/gamma/cx/wire/types"
)

type Service struct {
	salarydataops.UnimplementedSalaryDataOpsServer
	salaryOpsDynConf              *cxGenConf.SalaryOpsConfig
	salaryOpsConfig               *config.SalaryOpsConfig
	usersClient                   userPb.UsersClient
	actorClient                   actorPb.ActorClient
	empClient                     employmentPb.EmploymentClient
	salaryClient                  salaryprogram.SalaryProgramClient
	orderClient                   order.OrderServiceClient
	piClient                      paymentinstrument.PiClient
	vkycClient                    vkyc.VKYCClient
	userGroupClient               userGroup.GroupClient
	vgPaymentClient               vgPaymentPb.PaymentClient
	authEngine                    auth_engine.IAuthEngine
	salaryCxClient                salarycx.CxClient
	txnCatClient                  categorizerPb.TxnCategorizerClient
	bcCLient                      bankCustomerPb.BankCustomerServiceClient
	healthInsuranceClient         healthinsurance.HealthInsuranceClient
	salaryProgramBucketS3Client   wireTypes.SalaryProgramS3Client
	employerNameCategoriserClient employernamecategoriser.EmployerNameCategoriserClient
	recurringPaymentClient        recurringPaymentPb.RecurringPaymentServiceClient
}

func NewSalaryDataOpsService(salaryOpsDynConf *cxGenConf.SalaryOpsConfig, salaryOpsConfig *config.SalaryOpsConfig, usersClient userPb.UsersClient, actorClient actorPb.ActorClient, empClient employmentPb.EmploymentClient,
	salaryClient salaryprogram.SalaryProgramClient, orderClient order.OrderServiceClient, piClient paymentinstrument.PiClient, vkycClient vkyc.VKYCClient,
	userGroupClient userGroup.GroupClient, vgPaymentClient vgPaymentPb.PaymentClient, authEngine auth_engine.IAuthEngine, salaryCxClient salarycx.CxClient,
	txnCatClient categorizerPb.TxnCategorizerClient, bcClient bankCustomerPb.BankCustomerServiceClient, healthInsuranceClient healthinsurance.HealthInsuranceClient, salaryProgramBucketS3Client wireTypes.SalaryProgramS3Client,
	employerNameCategoriserClient employernamecategoriser.EmployerNameCategoriserClient, recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient) *Service {
	return &Service{
		salaryOpsDynConf:              salaryOpsDynConf,
		salaryOpsConfig:               salaryOpsConfig,
		usersClient:                   usersClient,
		actorClient:                   actorClient,
		empClient:                     empClient,
		salaryClient:                  salaryClient,
		orderClient:                   orderClient,
		piClient:                      piClient,
		vkycClient:                    vkycClient,
		userGroupClient:               userGroupClient,
		vgPaymentClient:               vgPaymentClient,
		authEngine:                    authEngine,
		salaryCxClient:                salaryCxClient,
		txnCatClient:                  txnCatClient,
		bcCLient:                      bcClient,
		healthInsuranceClient:         healthInsuranceClient,
		salaryProgramBucketS3Client:   salaryProgramBucketS3Client,
		employerNameCategoriserClient: employerNameCategoriserClient,
		recurringPaymentClient:        recurringPaymentClient,
	}
}

// compile time check to ensure Service implements salarydataops.SalaryDataOpsServer
var _ salarydataops.SalaryDataOpsServer = &Service{}

func (s *Service) GetSalaryTxnVerificationRequestsCount(ctx context.Context,
	req *salarydataops.GetSalaryTxnVerificationRequestsCountRequest) (*salarydataops.GetSalaryTxnVerificationRequestsCountResponse, error) {

	requestSourceList := make([]salaryprogram.SalaryTxnVerificationRequestSource, 0)
	requestSourceList = append(requestSourceList, salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST,
		salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_SALARY_OPS_REQUEST)

	beVerificationStatus, ok := SalaryTxnVerificationStatusCXToBEMapping[req.GetVerificationStatus()]
	if !ok {
		cxLogger.Error(ctx, fmt.Sprintf("unable to map CX txn verification status to BE: %s", req.GetVerificationStatus()))
		return &salarydataops.GetSalaryTxnVerificationRequestsCountResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid salary txn verification request status"),
		}, nil
	}

	salaryTxnVerificationRequestsCountRequest := &salaryprogram.GetSalaryTxnVerificationRequestsCountRequest{
		RequestSourceIn:       requestSourceList,
		VerificationStatus:    beVerificationStatus.status,
		VerificationSubStatus: beVerificationStatus.subStatus,
	}
	salaryTxnVerificationRequestsCountResp, salaryTxnVerificationRequestsCountErr := s.salaryClient.GetSalaryTxnVerificationRequestsCount(ctx, salaryTxnVerificationRequestsCountRequest)
	if grpcErr := epifigrpc.RPCError(salaryTxnVerificationRequestsCountResp, salaryTxnVerificationRequestsCountErr); grpcErr != nil {
		cxLogger.Error(ctx, "error getting salary txn verification requests count", zap.Error(grpcErr))
		return &salarydataops.GetSalaryTxnVerificationRequestsCountResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("error getting salary txn verification requests count"),
		}, nil
	}
	return &salarydataops.GetSalaryTxnVerificationRequestsCountResponse{
		Status:                                rpc.StatusOk(),
		NumberOfSalaryTxnVerificationRequests: salaryTxnVerificationRequestsCountResp.GetNumberOfSalaryTxnVerificationRequests(),
	}, nil
}

func (s *Service) SearchUsingGSTINOrName(ctx context.Context, req *salarydataops.SearchUsingGSTINOrNameRequest) (*salarydataops.SearchUsingGSTINOrNameResponse, error) {
	// search by gstin number if pattern matches
	if gstinRegex.MatchString(strings.ToUpper(req.GetSearchString())) {
		gstinResponse, err := s.empClient.GetEmployerDetailsByGSTIN(ctx, &employmentPb.GetEmployerDetailsByGSTINRequest{
			Gstin: strings.ToUpper(req.GetSearchString()),
		})
		grpcErr := epifigrpc.RPCError(gstinResponse, err)
		switch {
		// return status ok in case of employer being found or not.
		// employer details will be empty array if record is not found
		case gstinResponse.GetStatus().IsSuccess():
			return &salarydataops.SearchUsingGSTINOrNameResponse{
				Status: rpc.StatusOk(),
				EmployerDetails: []*salarydataops.EmployerDetails{
					{
						EmployerId:   gstinResponse.GetEmployerInfo().GetEmployerId(),
						NameBySource: gstinResponse.GetEmployerInfo().GetNameBySource(),
						TradeName:    gstinResponse.GetEmployerInfo().GetTradeName(),
						Gstin:        strings.ToUpper(req.GetSearchString()),
					},
				},
			}, nil
		case gstinResponse.GetStatus().IsRecordNotFound():
			return &salarydataops.SearchUsingGSTINOrNameResponse{
				Status:          rpc.StatusOk(),
				EmployerDetails: []*salarydataops.EmployerDetails{},
			}, nil
		default:
			cxLogger.Error(ctx, "error while fetching employer with given gstin", zap.Error(grpcErr))
			return &salarydataops.SearchUsingGSTINOrNameResponse{
				Status: rpc.StatusInternalWithDebugMsg("error searching employer using gstin or name"),
			}, nil
		}
	}
	// search by name if search string does not resemble gstin number
	searchResp, err := s.empClient.SearchCompanyV2(ctx, &employmentPb.SearchCompanyRequestV2{
		SearchString: req.GetSearchString(),
		Provenance:   employmentPb.SearchCompanyRequestV2_SALARY_PROGRAMME,
	})
	if grpcErr := epifigrpc.RPCError(searchResp, err); grpcErr != nil {
		cxLogger.Error(ctx, "error searching employer using gstin or name", zap.Error(grpcErr))
		return &salarydataops.SearchUsingGSTINOrNameResponse{
			Status: rpc.StatusInternalWithDebugMsg("error searching employer using gstin or name"),
		}, nil
	}
	var empDetails []*salarydataops.EmployerDetails
	// TODO: GSTIN info to be added after BE search rpc is updated
	for _, company := range searchResp.GetCompanies() {
		empDetails = append(empDetails, &salarydataops.EmployerDetails{
			EmployerId:   company.GetEmployerId(),
			NameBySource: company.GetNameBySource(),
			TradeName:    company.GetTradeName(),
			Gstin:        "",
		})
	}
	return &salarydataops.SearchUsingGSTINOrNameResponse{
		Status:          rpc.StatusOk(),
		EmployerDetails: empDetails,
	}, nil
}

func (s *Service) GetSalaryAccountDetailsForSalaryDataOps(ctx context.Context, req *salarydataops.GetSalaryAccountDetailsForSalaryDataOpsRequest) (*salarydataops.GetSalaryAccountDetailsForSalaryDataOpsResponse, error) {
	var (
		user  *userPb.User
		actor *types.Actor
		err   error
	)
	// getting user and actor using provided identifier
	switch req.GetUserIdentifier().(type) {
	case *salarydataops.GetSalaryAccountDetailsForSalaryDataOpsRequest_PhoneNumber:
		user, actor, err = s.getUserAndActorFromPhoneNumber(ctx, req.GetPhoneNumber())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &salarydataops.GetSalaryAccountDetailsForSalaryDataOpsResponse{
					Status: rpc.StatusRecordNotFoundWithDebugMsg("no user/actor found for phone number"),
				}, nil
			}
			cxLogger.Error(ctx, "error getting user and actor from phone number", zap.Error(err))
			return &salarydataops.GetSalaryAccountDetailsForSalaryDataOpsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error getting user and actor from phone number"),
			}, nil
		}
	case *salarydataops.GetSalaryAccountDetailsForSalaryDataOpsRequest_EmailId:
		user, actor, err = s.getUserAndActorFromEmailId(ctx, req.GetEmailId())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &salarydataops.GetSalaryAccountDetailsForSalaryDataOpsResponse{
					Status: rpc.StatusRecordNotFoundWithDebugMsg("no user/actor found for email id"),
				}, nil
			}
			cxLogger.Error(ctx, "error getting user and actor from email id", zap.Error(err))
			return &salarydataops.GetSalaryAccountDetailsForSalaryDataOpsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error getting user and actor from email id"),
			}, nil
		}
	default:
		cxLogger.Error(ctx, "invalid user identifier")
		return &salarydataops.GetSalaryAccountDetailsForSalaryDataOpsResponse{
			Status: rpc.StatusInternalWithDebugMsg("invalid user identifier"),
		}, nil
	}

	resp, err := s.getSalaryAccountDetailsForUserAndActor(ctx, user, actor)
	if err != nil {
		cxLogger.Error(ctx, "error getting salary account details", zap.Error(err))
		return &salarydataops.GetSalaryAccountDetailsForSalaryDataOpsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting salary account details"),
		}, nil
	}
	// TODO: last active duration details to be added after a new BE rpc is added
	return &salarydataops.GetSalaryAccountDetailsForSalaryDataOpsResponse{
		Status:            rpc.StatusOk(),
		EmploymentInfo:    resp.employmentInfo,
		KycInfo:           resp.kycInfo,
		SalaryProgramInfo: resp.salaryProgramInfo,
	}, nil
}

func (s *Service) GetSalaryAccountDetailsForAgents(ctx context.Context, req *salarydataops.GetSalaryAccountDetailsForAgentsRequest) (*salarydataops.GetSalaryAccountDetailsForAgentsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &salarydataops.GetSalaryAccountDetailsForAgentsResponse{
			Status:           rpc.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	resp, err := s.getSalaryAccountDetailsForUserAndActor(ctx, req.GetHeader().GetUser(), req.GetHeader().GetActor())
	if err != nil {
		cxLogger.Error(ctx, "error getting salary account details", zap.Error(err))
		return &salarydataops.GetSalaryAccountDetailsForAgentsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting salary account details"),
		}, nil
	}
	// TODO: last active duration details to be added after a new BE rpc is added
	return &salarydataops.GetSalaryAccountDetailsForAgentsResponse{
		Status:            rpc.StatusOk(),
		EmploymentInfo:    resp.employmentInfo,
		KycInfo:           resp.kycInfo,
		SalaryProgramInfo: resp.salaryProgramInfo,
	}, nil
}

func (s *Service) UpdateUsersEmploymentDetails(ctx context.Context, req *salarydataops.UpdateUsersEmploymentDetailsRequest) (*salarydataops.UpdateUsersEmploymentDetailsResponse, error) {
	_, actor, err := s.getUserAndActorFromEmailId(ctx, req.GetEmailId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &salarydataops.UpdateUsersEmploymentDetailsResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg("error getting user and actor from email id"),
			}, nil
		}
		cxLogger.Error(ctx, "error getting user and actor from email id", zap.Error(err))
		return &salarydataops.UpdateUsersEmploymentDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting user from email id"),
		}, nil
	}
	empUpdateRes, err := s.empClient.UpdateNewEmploymentData(ctx, &employmentPb.UpdateNewEmploymentDataRequest{
		ActorId:        actor.GetId(),
		EmploymentType: employmentPb.EmploymentType_SALARIED,
		EmploymentInfoOptions: &employmentPb.UpdateNewEmploymentDataRequest_EmployerInfo{
			EmployerInfo: &employmentPb.EmployerInfoOption{
				EmployerId: req.GetEmployerId(),
			},
		},
		UpdateSource: employmentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_DATA_OPS,
	})
	if grpcErr := epifigrpc.RPCError(empUpdateRes, err); err != nil {
		cxLogger.Error(ctx, "error updating employer of the user", zap.Error(grpcErr),
			zap.Any("rpc status", empUpdateRes.GetStatus()), zap.String("employerId", req.GetEmployerId()),
		)
		return &salarydataops.UpdateUsersEmploymentDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error updating employer of the user"),
		}, nil
	}
	cxLogger.Info(ctx, "updated employment info for user from sherlock", zap.String("actor id", actor.GetId()), zap.String("new employer", req.GetEmployerId()))
	return &salarydataops.UpdateUsersEmploymentDetailsResponse{
		Status: rpc.StatusOk(),
	}, nil
}

//nolint:funlen
func (s *Service) GetDetailedSalaryTxnVerificationRequestsByFilter(ctx context.Context,
	req *salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterRequest) (*salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterResponse, error) {
	filter := req.GetFilter()
	sortOrder := salaryprogram.SortOrder_ASC
	if req.GetSortOrder() == salarydataops.SortOrder_DESC {
		sortOrder = salaryprogram.SortOrder_DESC
	}

	// variable setting
	numberOfBeRpcCalls := 0

	hasAfter := true
	hasBefore := false
	beforePageToken := ""
	afterPageToken := ""
	txnDetailsRespList := make([]*salarydataops.TxnDetails, 0)
	var actorId string

	// get actor id if user identifier is given in request filter
	if filter.GetUserIdentifier() != nil {
		var getActorIdErr error
		actorId, getActorIdErr = s.getActorIdForUserIdentifier(ctx, filter.GetUserIdentifier())
		if getActorIdErr != nil {
			cxLogger.Error(ctx, "failed to get actor id for user identifier", zap.Error(getActorIdErr))
			return &salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to get actor id for user identifier"),
			}, nil
		}
	}
	searchStatus, ok := SalaryTxnVerificationStatusCXToBEMapping[filter.GetSalaryTxnVerificationStatus()]
	if !ok {
		cxLogger.Error(ctx, fmt.Sprintf("unable to map CX txn verification status to BE: %s", filter.GetSalaryTxnVerificationStatus()))
		return &salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid search status"),
		}, nil
	}

	firstBatchProcessed := false
	pageContextRequest := req.GetPageContextRequest()

	pageSize := req.GetPageContextRequest().GetPageSize()
	// Get salary txn verification requests and filter them - this will be done in batches based on below condition
	for (numberOfBeRpcCalls < s.salaryOpsDynConf.MaxBEPaginatedCallsForFiltering()) && (len(txnDetailsRespList) < int(pageSize)) && hasAfter {

		pageCtxResp, txnDetails, err2 := s.getDetailedSalaryTxnVerificationRequestsByFilter(ctx, actorId, searchStatus, pageContextRequest, sortOrder, filter)
		if err2 != nil {
			logger.Error(ctx, "error getting salary txn verification requests", zap.Error(err2))
			return &salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterResponse{
				Status: rpc.StatusInternalWithDebugMsg("error getting salary txn verification requests"),
			}, nil
		}

		// final appending of txn details to the response list
		txnDetailsRespList = append(txnDetailsRespList, txnDetails...)
		logger.Debug(ctx, "Number of records for txnDetailsRespList", zap.Any("txnDetailsRespListSize", len(txnDetailsRespList)), zap.Any("hasAfter", pageCtxResp.GetHasAfter()))

		hasAfter = pageCtxResp.GetHasAfter()
		afterPageToken = pageCtxResp.GetAfterToken()
		if hasAfter {
			// setting next page size to remaining records is the ideal solution.
			// But this can lead to too many calls to backend if the page size is too small and filtered result is a small subset and scattered in the queue.
			// So we are setting the next page size to the original page size and the final result can be ~2x of pagesize.
			// nextPageSize := pageSize - uint32(len(txnDetailsRespList))
			nextPageSize := pageSize
			pageContextRequest = &rpc.PageContextRequest{
				PageSize: nextPageSize,
				Token: &rpc.PageContextRequest_AfterToken{
					AfterToken: afterPageToken,
				},
			}
		}
		if !firstBatchProcessed {
			beforePageToken = pageCtxResp.GetBeforeToken()
			hasBefore = pageCtxResp.GetHasBefore()
			firstBatchProcessed = true
		}
		numberOfBeRpcCalls++
	}

	return &salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterResponse{
		Status:     rpc.StatusOk(),
		TxnDetails: txnDetailsRespList,
		PageContextResponse: &rpc.PageContextResponse{
			HasBefore:   hasBefore,
			BeforeToken: beforePageToken,
			HasAfter:    hasAfter,
			AfterToken:  afterPageToken,
		},
	}, nil

}

func (s *Service) getDetailedSalaryTxnVerificationRequestsByFilter(ctx context.Context, actorId string, searchStatus salaryTxnStatuses, pageContextRequest *rpc.PageContextRequest, sortOrder salaryprogram.SortOrder,
	filter *salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterRequest_Filter) (*rpc.PageContextResponse, []*salarydataops.TxnDetails, error) {
	// fetch the salary transaction verification requests by verification status
	salaryTxnVeriReqs, err := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
		Filters: &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
			ActorId: actorId,
			Status:  searchStatus.status,
			SubStatuses: []salaryprogram.SalaryTxnVerificationRequestSubStatus{
				searchStatus.subStatus,
			},
			ReqSources: []salaryprogram.SalaryTxnVerificationRequestSource{salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST,
				salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_SALARY_OPS_REQUEST},
		},
		PageContext: pageContextRequest,
		// sort order based on salary txn verification request creation time
		SortOrder: sortOrder,
	})
	if grpcErr := epifigrpc.RPCError(salaryTxnVeriReqs, err); grpcErr != nil {
		cxLogger.Error(ctx, "error getting salary txn verification requests", zap.Error(grpcErr))
		return nil, nil, grpcErr
	}
	logger.Debug(ctx, "Number of records for salaryTxnVeriReqs", zap.Any("salaryTxnVeriReqsSize", len(salaryTxnVeriReqs.GetSalaryTxnVerificationRequests())))
	if len(salaryTxnVeriReqs.GetSalaryTxnVerificationRequests()) == 0 {
		return nil, nil, nil
	}
	// filter salary txn verification requests based on given filters in request
	filteredSalaryVerificationRequests, filteredSalaryVerificationRequestsErr :=
		s.getFilteredSalaryVerificationRequests(ctx, salaryTxnVeriReqs.GetSalaryTxnVerificationRequests(), filter)
	if filteredSalaryVerificationRequestsErr != nil {
		logger.Error(ctx, "failed to get filtered salary verification requests", zap.Error(filteredSalaryVerificationRequestsErr))
		return nil, nil, filteredSalaryVerificationRequestsErr
	}
	logger.Debug(ctx, "Number of records for filteredSalaryVerificationRequests", zap.Any("filteredSalaryVerificationRequestsSize",
		len(filteredSalaryVerificationRequests)))

	// fetch transaction details using the transaction id
	var txnDetails []*salarydataops.TxnDetails
	for _, salaryTxnVeriReq := range filteredSalaryVerificationRequests {
		txnDetail, getTxnErr := s.getTxnDetails(ctx, salaryTxnVeriReq, false, false, false, true)
		if getTxnErr != nil {
			cxLogger.Error(ctx, "error getting transaction details", zap.Error(getTxnErr))
			// we do not want to return here just because one transaction details could not be fetched
			continue
		}
		txnDetails = append(txnDetails, txnDetail)
	}
	logger.Debug(ctx, "Number of records for txnDetail", zap.Any("txnDetailSize", len(txnDetails)))
	return salaryTxnVeriReqs.GetPageContext(), txnDetails, nil
}

func (s *Service) getActorIdForUserIdentifier(ctx context.Context, userIdentifier *salarydataops.UserIdentifier) (string, error) {
	switch userIdentifier.GetIdentifier().(type) {
	case *salarydataops.UserIdentifier_EmailId:
		_, actor, err := s.getUserAndActorFromEmailId(ctx, userIdentifier.GetEmailId())
		if err != nil {
			return "", fmt.Errorf("error fetching actor details using emailId, err: %w", err)
		}
		return actor.GetId(), nil
	case *salarydataops.UserIdentifier_PhoneNumber:
		_, actor, err := s.getUserAndActorFromPhoneNumber(ctx, userIdentifier.GetPhoneNumber())
		if err != nil {
			return "", fmt.Errorf("error fetching actor details using phoneNumber, err: %w", err)
		}
		return actor.GetId(), nil
	case *salarydataops.UserIdentifier_ActorId:
		return userIdentifier.GetActorId(), nil
	default:
		return "", fmt.Errorf("unhandled user identifier")
	}
}

func (s *Service) getFilteredSalaryVerificationRequests(ctx context.Context, salaryTxnVerificationRequests []*salaryprogram.SalaryTxnVerificationRequest,
	filter *salarydataops.GetDetailedSalaryTxnVerificationRequestsByFilterRequest_Filter) ([]*salaryprogram.SalaryTxnVerificationRequest, error) {
	filteredSalaryVerificationRequests := make([]*salaryprogram.SalaryTxnVerificationRequest, 0)

	for _, salaryTxnVerificationRequest := range salaryTxnVerificationRequests {
		candidateSalaryTxnVerificationRequest := salaryTxnVerificationRequest
		var employerDetails *salarydataops.EmployerDetails
		var employerDetailsErr error
		if filter.GetEmployerId() != "" || filter.GetEmployerChannel() != salarydataops.EmployerSalaryProgramChannel_SALARY_PROGRAM_CHANNEL_UNSPECIFIED {
			employerDetails, employerDetailsErr = s.getEmployerDetailsById(ctx, salaryTxnVerificationRequest.GetActorId())
			if employerDetailsErr != nil {
				if errors.Is(employerDetailsErr, epifierrors.ErrRecordNotFound) {
					logger.Error(ctx, "employer not found for actor",
						zap.String("actorId", salaryTxnVerificationRequest.GetActorId()), zap.Error(employerDetailsErr))
					continue
				}
				return nil, fmt.Errorf("error getting employer details, err: %w", employerDetailsErr)
			}
		}
		// check if employer id filter is present
		if filter.GetEmployerId() != "" && filter.GetEmployerId() != employerDetails.GetEmployerId() {
			candidateSalaryTxnVerificationRequest = nil
		}
		// check if employer channel filter is present
		if candidateSalaryTxnVerificationRequest != nil &&
			filter.GetEmployerChannel() != salarydataops.EmployerSalaryProgramChannel_SALARY_PROGRAM_CHANNEL_UNSPECIFIED {
			if filter.GetEmployerChannel() != employerDetails.GetEmployerChannel() {
				candidateSalaryTxnVerificationRequest = nil
			}
		}
		if candidateSalaryTxnVerificationRequest != nil {
			filteredSalaryVerificationRequests = append(filteredSalaryVerificationRequests, candidateSalaryTxnVerificationRequest)
		}
	}
	return filteredSalaryVerificationRequests, nil
}

func (s *Service) GetSalaryTxnVerificationRequestsByStatus(ctx context.Context, req *salarydataops.GetSalaryTxnVerificationRequestsByStatusRequest) (*salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse, error) {
	searchStatus, ok := SalaryTxnVerificationStatusCXToBEMapping[req.GetSalaryTxnVerificationStatus()]
	if !ok {
		cxLogger.Error(ctx, fmt.Sprintf("unable to map CX txn verification status to BE: %s", req.GetSalaryTxnVerificationStatus()))
		return &salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid search status"),
		}, nil
	}

	reqFilters := req.GetFilter()

	var actorId string
	// if user identifier filter is present, get actorId using that identifier
	if reqFilters.GetUserIdentifier() != nil {
		switch req.GetFilter().GetUserIdentifier().GetIdentifier().(type) {
		case *salarydataops.GetSalaryTxnVerificationRequestsByStatusRequest_UserIdentifier_EmailId:
			_, actor, err := s.getUserAndActorFromEmailId(ctx, reqFilters.GetUserIdentifier().GetEmailId())
			if err != nil {
				cxLogger.Error(ctx, "error fetching actor details using emailId", zap.Error(err))
				return &salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse{
					Status: rpc.StatusInternalWithDebugMsg("error fetching actor details using emailId"),
				}, nil
			}
			actorId = actor.GetId()

		case *salarydataops.GetSalaryTxnVerificationRequestsByStatusRequest_UserIdentifier_PhoneNumber:
			_, actor, err := s.getUserAndActorFromPhoneNumber(ctx, reqFilters.GetUserIdentifier().GetPhoneNumber())
			if err != nil {
				cxLogger.Error(ctx, "error fetching actor details using phoneNumber", zap.Error(err))
				return &salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse{
					Status: rpc.StatusInternalWithDebugMsg("error fetching actor details using phoneNumber"),
				}, nil
			}
			actorId = actor.GetId()

		case *salarydataops.GetSalaryTxnVerificationRequestsByStatusRequest_UserIdentifier_ActorId:
			actorId = reqFilters.GetUserIdentifier().GetActorId()

		default:
			cxLogger.Error(ctx, "unhandled user identifier")
			return &salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("unhandled user identifier"),
			}, nil
		}
	}

	sortOrder := salaryprogram.SortOrder_ASC
	if req.GetSortOrder() == salarydataops.SortOrder_DESC {
		sortOrder = salaryprogram.SortOrder_DESC
	}

	// fetch the salary transaction verification requests by verification status
	salaryTxnVeriReqs, err := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
		Filters: &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
			ActorId: actorId,
			Status:  searchStatus.status,
			SubStatuses: []salaryprogram.SalaryTxnVerificationRequestSubStatus{
				searchStatus.subStatus,
			},
			// todo (utkarsh) : remove reqSource filter after the requestSources filter goes live
			ReqSource:  salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST,
			ReqSources: []salaryprogram.SalaryTxnVerificationRequestSource{salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST, salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_SALARY_OPS_REQUEST},
		},
		PageContext: req.GetPageContextRequest(),
		// sort order based on salary txn verification request creation time
		SortOrder: sortOrder,
	})
	if grpcErr := epifigrpc.RPCError(salaryTxnVeriReqs, err); grpcErr != nil {
		cxLogger.Error(ctx, "error getting salary txn verification requests", zap.Error(grpcErr))
		return &salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting salary txn verification requests"),
		}, nil
	}
	if len(salaryTxnVeriReqs.GetSalaryTxnVerificationRequests()) == 0 {
		return &salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	// fetch transaction details using the transaction id
	var txnDetails []*salarydataops.TxnDetails
	for _, salaryTxnVeriReq := range salaryTxnVeriReqs.GetSalaryTxnVerificationRequests() {
		txnDetail, getTxnErr := s.getTxnDetails(ctx, salaryTxnVeriReq, false, true, true, req.GetAreAdditionalDetailsRequired())
		if getTxnErr != nil {
			cxLogger.Error(ctx, "error getting transaction details", zap.Error(getTxnErr))
			// we do not want to return here just because one transaction details could not be fetched
			continue
		}
		txnDetails = append(txnDetails, txnDetail)
	}
	if len(txnDetails) == 0 {
		return &salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch txn details for all verification requests"),
		}, nil
	}
	return &salarydataops.GetSalaryTxnVerificationRequestsByStatusResponse{
		Status:              rpc.StatusOk(),
		TxnDetails:          txnDetails,
		PageContextResponse: salaryTxnVeriReqs.GetPageContext(),
	}, nil
}

func (s *Service) GetUserDetailsNeededForVerification(ctx context.Context, req *salarydataops.GetUserDetailsNeededForVerificationRequest) (*salarydataops.GetUserDetailsNeededForVerificationResponse, error) {
	var (
		userDetails                            *salarydataops.EmploymentInfo
		supportedVerificationFailureReasonsRes *salaryprogram.GetSupportedSalaryVerificationFailureReasonsResponse
	)
	actorId, err := s.getActorIdFromExtOrderId(ctx, req.GetTxnId())
	if err != nil {
		return &salarydataops.GetUserDetailsNeededForVerificationResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting actor id from external order id"),
		}, nil
	}

	errGroup, gctx := errgroup.WithContext(ctx)
	// fetching employment details
	errGroup.Go(func() error {
		userDetails, err = s.getEmploymentAndUserDetails(gctx, actorId, nil)
		if err != nil {
			cxLogger.Error(gctx, "error getting users employment details", zap.Error(err))
			return fmt.Errorf("error getting user's employment details")
		}
		return nil
	})

	// fetching supported verification failure reasons
	errGroup.Go(func() error {
		supportedVerificationFailureReasonsRes, err = s.salaryClient.GetSupportedSalaryVerificationFailureReasons(gctx, &salaryprogram.GetSupportedSalaryVerificationFailureReasonsRequest{})
		if grpcErr := epifigrpc.RPCError(supportedVerificationFailureReasonsRes, err); grpcErr != nil {
			cxLogger.Error(gctx, "error getting supported salary txn verification failure reasons", zap.Error(grpcErr))
			return fmt.Errorf("error getting supported salary txn verification failure reasons")
		}
		return nil
	})

	if errGrpErr := errGroup.Wait(); errGrpErr != nil {
		return &salarydataops.GetUserDetailsNeededForVerificationResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting details needed for verification"),
		}, nil
	}

	supportedVerificationFailureReasons, err := s.getVerificationFailureReasonCategoryToSubCategoriesMapping(ctx, supportedVerificationFailureReasonsRes.GetCategoryToSubcategoriesFailureReasonsMap())
	if err != nil {
		cxLogger.Error(ctx, "error getting verification failure reason category to sub-category mapping", zap.Error(err))
		return &salarydataops.GetUserDetailsNeededForVerificationResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting verification failure reason category to sub-category mapping"),
		}, nil
	}

	// fetching salary txn details
	salaryTxnVeriReqs, err := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
		Filters: &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
			Id: req.GetSalaryTxnVerReqId(),
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 10,
		},
	})
	if grpcErr := epifigrpc.RPCError(salaryTxnVeriReqs, err); grpcErr != nil {
		cxLogger.Error(ctx, "error getting salary txn verification requests", zap.Error(grpcErr))
		// we do not throw error here and send partial data
		return &salarydataops.GetUserDetailsNeededForVerificationResponse{
			Status:                              rpc.StatusOk(),
			EmploymentInfo:                      userDetails,
			SupportedVerificationFailureReasons: supportedVerificationFailureReasons,
		}, nil
	}
	if len(salaryTxnVeriReqs.GetSalaryTxnVerificationRequests()) < 1 {
		cxLogger.Error(ctx, "error getting salary txn verification requests")
		// we do not throw error here and send partial data
		return &salarydataops.GetUserDetailsNeededForVerificationResponse{
			Status:                              rpc.StatusOk(),
			EmploymentInfo:                      userDetails,
			SupportedVerificationFailureReasons: supportedVerificationFailureReasons,
		}, nil
	}
	txnDetails, txnErr := s.getTxnDetails(ctx, salaryTxnVeriReqs.GetSalaryTxnVerificationRequests()[0], true, false, true, false)
	if txnErr != nil {
		cxLogger.Error(ctx, "error getting transaction details", zap.Error(txnErr))
		// we do not throw error here to send partial data
	}

	return &salarydataops.GetUserDetailsNeededForVerificationResponse{
		Status:                              rpc.StatusOk(),
		EmploymentInfo:                      userDetails,
		TxnDetails:                          txnDetails,
		SupportedVerificationFailureReasons: supportedVerificationFailureReasons,
	}, nil
}

func (s *Service) UpdateVerificationRequestStatus(ctx context.Context, req *salarydataops.UpdateVerificationRequestStatusRequest) (*salarydataops.UpdateVerificationRequestStatusResponse, error) {
	var (
		failureReasonsCategoryToDisplayString    = s.salaryOpsConfig.VerificationFailureReasonsCategoryToDisplayString
		failureReasonsSubCategoryToDisplayString = s.salaryOpsConfig.VerificationFailureReasonsSubCategoryToDisplayString
		failureReasonCategoryEnum                salaryprogram.SalaryTxnVerificationFailureReasonCategory
		failureReasonSubCategoryEnum             salaryprogram.SalaryTxnVerificationFailureReasonSubCategory
	)

	updateStatusInput := getVerificationStatusFieldsFromInput(req.GetEmployerId(), req.GetUserEmployerMatch(), req.GetNameMatchLevel())
	if updateStatusInput.err != nil {
		return &salarydataops.UpdateVerificationRequestStatusResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid combination of inputs to update salary transaction verification status"),
		}, nil
	}

	if updateStatusInput.status == salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED &&
		req.GetIsRemitterNameCompletelyMatchingEmployer() {

		piFromId, rpcErr := s.getPiFromIdFromTxnRequestId(ctx, req.GetSalaryTxnVerReqId())
		if rpcErr != nil {
			cxLogger.Error(ctx, "error getting pi id from txn id", zap.Error(rpcErr), zap.String("salaryTxnVerReqId", req.GetSalaryTxnVerReqId()))
			return &salarydataops.UpdateVerificationRequestStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("error getting pi id from txn id"),
			}, nil
		}

		paymentInstrumentResp, paymentInstrumentErr := s.piClient.GetPiById(ctx, &paymentinstrument.GetPiByIdRequest{
			Id: piFromId,
		})
		if rpcError := epifigrpc.RPCError(paymentInstrumentResp, paymentInstrumentErr); rpcError != nil {
			cxLogger.Error(ctx, "error getting payment instrument from pi id", zap.Error(rpcErr), zap.String("piId", piFromId))
			return &salarydataops.UpdateVerificationRequestStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("error getting payment instrument from pi id"),
			}, nil
		}

		cxLogger.Info(ctx, "successfully got payment instrument for piId with id", zap.String("piId", piFromId), zap.Any("paymentInstrumentId", paymentInstrumentResp.GetPaymentInstrument().GetId()))
		if paymentInstrumentResp.GetPaymentInstrument().GetType() != paymentinstrument.PaymentInstrumentType_GENERIC {
			createEmployerPiMappingRequest := &employmentPb.CreateEmployerPiMappingRequest{
				PiId:       piFromId,
				EmployerId: updateStatusInput.employerId,
				Source:     employmentPb.EmployerPiMappingSource_EMPLOYER_PI_MAPPING_SOURCE_SALARY_OPS,
			}
			createEmpPiMappingResp, err := s.empClient.CreateEmployerPiMapping(ctx, createEmployerPiMappingRequest)
			if rpcErr := epifigrpc.RPCError(createEmpPiMappingResp, err); rpcErr != nil {
				cxLogger.Error(ctx, "error creating employer id to pi id mapping", zap.Error(rpcErr))
				return &salarydataops.UpdateVerificationRequestStatusResponse{
					Status: rpc.StatusInternalWithDebugMsg("error creating employer id to pi id mapping"),
				}, nil
			}
			cxLogger.Info(ctx, "successfully created employer pi mapping", zap.String("piId", piFromId), zap.String("employerId", updateStatusInput.employerId))
		}
	}

	if req.GetVerificationFailureReasonCategory() != "" {
		for category, categoryDisplayString := range failureReasonsCategoryToDisplayString {
			if req.GetVerificationFailureReasonCategory() == categoryDisplayString {
				failureReasonCategoryEnum = salaryprogram.SalaryTxnVerificationFailureReasonCategory(salaryprogram.SalaryTxnVerificationFailureReasonCategory_value[category])
				break
			}
		}
		if failureReasonCategoryEnum == salaryprogram.SalaryTxnVerificationFailureReasonCategory_FAILURE_REASON_CATEGORY_UNSPECIFIED {
			cxLogger.Error(ctx, "error updating salary transaction verification status, enum not found for failure reason category display string", zap.String("category", req.GetVerificationFailureReasonCategory()))
			return &salarydataops.UpdateVerificationRequestStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("error updating salary transaction verification status, failure reason category not found"),
			}, nil
		}
	}

	if req.GetVerificationFailureReasonSubCategory() != "" {
		for subCategory, subCategoryDisplayString := range failureReasonsSubCategoryToDisplayString {
			if req.GetVerificationFailureReasonSubCategory() == subCategoryDisplayString {
				failureReasonSubCategoryEnum = salaryprogram.SalaryTxnVerificationFailureReasonSubCategory(salaryprogram.SalaryTxnVerificationFailureReasonSubCategory_value[subCategory])
				break
			}
		}
		if failureReasonSubCategoryEnum == salaryprogram.SalaryTxnVerificationFailureReasonSubCategory_FAILURE_REASON_SUB_CATEGORY_UNSPECIFIED {
			cxLogger.Error(ctx, "error updating salary transaction verification status, enum not found for failure reason sub category display string", zap.String("sub_category", req.GetVerificationFailureReasonSubCategory()))
			return &salarydataops.UpdateVerificationRequestStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("error updating salary transaction verification status, failure reason sub-category not found"),
			}, nil
		}
	}

	salaryTxnStatus, err := s.salaryClient.UpdateManualSalaryTxnVerificationStatus(ctx, &salaryprogram.UpdateManualSalaryTxnVerificationStatusRequest{
		SalaryTxnVerRequestId:                req.GetSalaryTxnVerReqId(),
		UpdateStatusTo:                       updateStatusInput.status,
		UpdateSubStatusTo:                    updateStatusInput.subStatus,
		SalaryTxnEmployerId:                  updateStatusInput.employerId,
		VerifiedBy:                           salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
		VerificationFailureReasonCategory:    failureReasonCategoryEnum,
		VerificationFailureReasonSubCategory: failureReasonSubCategoryEnum,
		VerificationRemark:                   req.GetVerificationRemark(),
	})
	if grpcErr := epifigrpc.RPCError(salaryTxnStatus, err); grpcErr != nil {
		cxLogger.Error(ctx, "error updating salary transaction verification status", zap.Error(grpcErr))
		return &salarydataops.UpdateVerificationRequestStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg("error updating salary transaction verification status"),
		}, nil
	}

	// commenting this because of irrelevant names getting populated in the DB
	// if len(req.GetRemitterName()) > 0 && (req.GetUserEmployerMatch() || req.GetNameMatchLevel() == salarydataops.NameMatchLevel_NAME_MATCH_LEVEL_PERFECT_MATCH) {
	//	addRemitterRes, err1 := s.empClient.AddEmployerRemitterNames(ctx, &employmentPb.AddEmployerRemitterNamesRequest{
	//		EmployerId:    req.GetEmployerId(),
	//		RemitterNames: []string{req.GetRemitterName()},
	//	})
	//	if grpcErr := epifigrpc.RPCError(addRemitterRes, err1); grpcErr != nil {
	//		cxLogger.Error(ctx, "error updating remitter name of employer", zap.Error(grpcErr))
	//		return &salarydataops.UpdateVerificationRequestStatusResponse{
	//			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprint("error updating remitter name of employer", zap.Error(grpcErr))),
	//		}, nil
	//	}
	// }

	return &salarydataops.UpdateVerificationRequestStatusResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) getPiById(ctx context.Context, piId string) (*paymentinstrument.PaymentInstrument, error) {
	getPiRes, getPiErr := s.piClient.GetPiById(ctx, &paymentinstrument.GetPiByIdRequest{
		Id: piId,
	})
	if rpcError := epifigrpc.RPCError(getPiRes, getPiErr); rpcError != nil {
		return nil, fmt.Errorf("GetPiById rpc failed: %w", rpcError)
	}
	return getPiRes.GetPaymentInstrument(), nil
}

func (s *Service) getPiFromIdFromTxnRequestId(ctx context.Context, txnRequestId string) (string, error) {
	txnRequests, txnRequestsErr := s.getTxnRequestsFromTxnId(ctx, txnRequestId)
	if txnRequestsErr != nil {
		return "", fmt.Errorf("error getting txn requests for txnId, err %w", txnRequestsErr)
	}
	if len(txnRequests) == 0 {
		return "", fmt.Errorf("No txn requests found for txnRequestId, txnId %v", txnRequestId)
	}

	externalId := txnRequests[0].GetTxnId()
	getOrderResp, getOrderErr := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ExternalId{
			ExternalId: externalId,
		},
	})
	if grpcErr := epifigrpc.RPCError(getOrderResp, getOrderErr); grpcErr != nil {
		return "", fmt.Errorf("error getting order for salary verification req id, err %w", grpcErr)
	}
	orderWithTxn, getOrderWithTxnErr := s.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{
		OrderId: getOrderResp.GetOrder().GetId(),
	})
	if grpcErr := epifigrpc.RPCError(orderWithTxn, getOrderWithTxnErr); grpcErr != nil {
		return "", fmt.Errorf("error getting order with txn for id, err %w", grpcErr)
	}
	txn := orderWithTxn.GetOrderWithTransactions().GetTransactions()
	if len(txn) == 0 {
		return "", fmt.Errorf("no txn found for salary txn request id, txnId %v", txnRequestId)
	}
	return txn[0].GetPiFrom(), nil
}

func (s *Service) getTxnRequestsFromTxnId(ctx context.Context, txnId string) ([]*salaryprogram.SalaryTxnVerificationRequest, error) {
	salaryTxnVerificationReqResp, salaryTxnVerificationReqErr := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
		Filters: &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
			Id: txnId,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})
	if rpcResp := epifigrpc.RPCError(salaryTxnVerificationReqResp, salaryTxnVerificationReqErr); rpcResp != nil {
		return nil, fmt.Errorf("failed to get salary transaction verification request for txnId %v, err %w", txnId, rpcResp)
	}
	return salaryTxnVerificationReqResp.GetSalaryTxnVerificationRequests(), nil
}

func (s *Service) RaiseAndVerifySalaryTxnVerificationReq(ctx context.Context, req *salarydataops.RaiseAndVerifySalaryTxnVerificationReqRequest) (*salarydataops.RaiseAndVerifySalaryTxnVerificationReqResponse, error) {
	var (
		actorId = req.GetActorId()
	)

	orderRes, orderErr := s.orderClient.GetOrder(ctx, &order.GetOrderRequest{
		Identifier: &order.GetOrderRequest_ExternalId{
			ExternalId: req.GetTxnId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(orderRes, orderErr); rpcErr != nil {
		cxLogger.Error(ctx, "error fetching order by external id", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.EXTERNAL_ORDER_ID, req.GetTxnId()), zap.Error(rpcErr))
		return &salarydataops.RaiseAndVerifySalaryTxnVerificationReqResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching order by external id"),
		}, nil
	}

	raiseReqRes, raiseReqErr := s.salaryClient.RaiseManualSalaryVerification(ctx, &salaryprogram.RaiseManualSalaryVerificationRequest{
		ActorId:   actorId,
		OrderId:   orderRes.GetOrder().GetId(),
		ReqSource: salaryprogram.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_SALARY_OPS_REQUEST,
	})
	if rpcErr := epifigrpc.RPCError(raiseReqRes, raiseReqErr); rpcErr != nil {
		cxLogger.Error(ctx, "error while raising manual salary verification request", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.ORDER_ID, req.GetTxnId()), zap.Error(rpcErr))
		return &salarydataops.RaiseAndVerifySalaryTxnVerificationReqResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error while raising manual salary verification request, err: %s", rpcErr.Error())),
		}, nil
	}

	// mark the raised verification request to VERIFIED status
	updateStatusRes, updateStatusErr := s.salaryClient.UpdateManualSalaryTxnVerificationStatus(ctx, &salaryprogram.UpdateManualSalaryTxnVerificationStatusRequest{
		SalaryTxnVerRequestId: raiseReqRes.GetSalaryVerificationReq().GetId(),
		SalaryTxnEmployerId:   req.GetEmployerId(),
		UpdateStatusTo:        salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
		VerifiedBy:            salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
	})
	if rpcErr := epifigrpc.RPCError(updateStatusRes, updateStatusErr); rpcErr != nil {
		cxLogger.Error(ctx, "error updating salary txn verification request status", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(rpcErr))
		return &salarydataops.RaiseAndVerifySalaryTxnVerificationReqResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error updating salary txn verification request status, err: %s", rpcErr.Error())),
		}, nil
	}

	logger.Info(ctx, "activated salary benefits from raise-a-request queue", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("salaryStage", req.GetSalaryProgramStage().String()))

	return &salarydataops.RaiseAndVerifySalaryTxnVerificationReqResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) GetSalaryTxnVerificationRequestsForUser(ctx context.Context, req *salarydataops.GetSalaryTxnVerificationRequestsForUserRequest) (*salarydataops.GetSalaryTxnVerificationRequestsForUserResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &salarydataops.GetSalaryTxnVerificationRequestsForUserResponse{
			Status:           rpc.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	filters := &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
		ActorId: req.GetHeader().GetActor().GetId(),
	}

	if req.GetFilters().GetSalaryTxnVerificationStatus() != salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_UNSPECIFIED {
		beSalaryTxnVerificationStatus, ok := SalaryTxnVerificationStatusCXToBEMapping[req.GetFilters().GetSalaryTxnVerificationStatus()]
		if !ok {
			cxLogger.Error(ctx, "unable to map CX txn verification status to BE", zap.String("SalaryTxnVerificationStatus", req.GetFilters().GetSalaryTxnVerificationStatus().String()))
			return &salarydataops.GetSalaryTxnVerificationRequestsForUserResponse{
				Status: rpc.StatusInternalWithDebugMsg("unable to map CX txn verification status to BE"),
			}, nil
		}

		filters.Status = beSalaryTxnVerificationStatus.status
		filters.SubStatuses = []salaryprogram.SalaryTxnVerificationRequestSubStatus{
			beSalaryTxnVerificationStatus.subStatus,
		}
	}

	// fetch the salary transaction verification requests by verification status
	salaryTxnVeriReqs, err := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
		Filters:     filters,
		PageContext: req.GetPageContextRequest(),
		// we want to show the requests in desc order of creation time
		SortOrder: salaryprogram.SortOrder_DESC,
	})
	if grpcErr := epifigrpc.RPCError(salaryTxnVeriReqs, err); grpcErr != nil {
		cxLogger.Error(ctx, "error getting salary txn verification requests for actor", zap.Error(grpcErr))
		return &salarydataops.GetSalaryTxnVerificationRequestsForUserResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting salary txn verification requests for actor"),
		}, nil
	}
	if len(salaryTxnVeriReqs.GetSalaryTxnVerificationRequests()) == 0 {
		return &salarydataops.GetSalaryTxnVerificationRequestsForUserResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	// fetch transaction details using the transaction id
	var txnDetails []*salarydataops.TxnDetails
	for _, salaryTxnVeriReq := range salaryTxnVeriReqs.GetSalaryTxnVerificationRequests() {
		txnDetail, getTxnErr := s.getTxnDetails(ctx, salaryTxnVeriReq, false, false, false, false)
		if getTxnErr != nil {
			cxLogger.Error(ctx, "error getting transaction details", zap.Error(getTxnErr))
			// we do not want to return here just because one transaction details could not be fetched
			continue
		}
		txnDetails = append(txnDetails, txnDetail)
	}
	if len(txnDetails) == 0 {
		return &salarydataops.GetSalaryTxnVerificationRequestsForUserResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch txn details for all verification requests"),
		}, nil
	}
	return &salarydataops.GetSalaryTxnVerificationRequestsForUserResponse{
		Status:              rpc.StatusOk(),
		TxnDetails:          txnDetails,
		PageContextResponse: salaryTxnVeriReqs.GetPageContext(),
	}, nil
}

func (s *Service) GetSalaryTxnVerificationRequestsForSalaryDataOps(ctx context.Context, req *salarydataops.GetSalaryTxnVerificationRequestsForSalaryDataOpsRequest) (*salarydataops.GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse, error) {

	_, actor, err := s.getUserAndActorFromPhoneNumber(ctx, req.GetPhoneNumber())
	if err != nil {
		cxLogger.Error(ctx, "error in getting actor from phone number", zap.Error(err))
		return &salarydataops.GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in getting actor from phone number"),
		}, nil
	}

	filters := &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
		ActorId: actor.GetId(),
	}

	if req.GetFilters().GetSalaryTxnVerificationStatus() != salarydataops.SalaryTxnVerificationStatus_SALARY_TXN_VERIFICATION_STATUS_UNSPECIFIED {
		beSalaryTxnVerificationStatus, ok := SalaryTxnVerificationStatusCXToBEMapping[req.GetFilters().GetSalaryTxnVerificationStatus()]
		if !ok {
			cxLogger.Error(ctx, "unable to map CX txn verification status to BE", zap.String("SalaryTxnVerificationStatus", req.GetFilters().GetSalaryTxnVerificationStatus().String()))
			return &salarydataops.GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse{
				Status: rpc.StatusInternalWithDebugMsg("unable to map CX txn verification status to BE"),
			}, nil
		}

		filters.Status = beSalaryTxnVerificationStatus.status
		filters.SubStatuses = []salaryprogram.SalaryTxnVerificationRequestSubStatus{
			beSalaryTxnVerificationStatus.subStatus,
		}
	}

	// fetch the salary transaction verification requests by verification status
	salaryTxnVerificationRequests, err := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
		Filters:     filters,
		PageContext: req.GetPageContextRequest(),
		// we want to show the requests in desc order of creation time
		SortOrder: salaryprogram.SortOrder_DESC,
	})
	if grpcErr := epifigrpc.RPCError(salaryTxnVerificationRequests, err); grpcErr != nil {
		cxLogger.Error(ctx, "error getting salary txn verification requests for actor", zap.Error(grpcErr))
		return &salarydataops.GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting salary txn verification requests for actor"),
		}, nil
	}
	if len(salaryTxnVerificationRequests.GetSalaryTxnVerificationRequests()) == 0 {
		return &salarydataops.GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	// fetch transaction details using the transaction id
	txnDetails, err := s.getTransactionDetailsForSalaryTxnVerificationRequestList(ctx, salaryTxnVerificationRequests.GetSalaryTxnVerificationRequests())
	if err != nil {
		return &salarydataops.GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &salarydataops.GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse{
		Status:              rpc.StatusOk(),
		TxnDetails:          txnDetails,
		PageContextResponse: salaryTxnVerificationRequests.GetPageContext(),
	}, nil
}

func (s *Service) getTransactionDetailsForSalaryTxnVerificationRequestList(ctx context.Context, salaryTxnVerificationRequests []*salaryprogram.SalaryTxnVerificationRequest) ([]*salarydataops.TxnDetails, error) {
	var txnDetails []*salarydataops.TxnDetails
	for _, salaryTxnVerificationRequest := range salaryTxnVerificationRequests {
		txnDetail, getTxnErr := s.getTxnDetails(ctx, salaryTxnVerificationRequest, false, false, false, false)
		if getTxnErr != nil {
			cxLogger.Error(ctx, "error getting transaction details", zap.Error(getTxnErr))
			// we do not want to return here just because one transaction details could not be fetched
			continue
		}
		txnDetails = append(txnDetails, txnDetail)
	}
	if len(txnDetails) == 0 {
		return nil, fmt.Errorf("failed to fetch txn details for all verification requests")
	}
	return txnDetails, nil
}

func (s *Service) GetPossibleSalaryTxnsForActor(ctx context.Context, req *salarydataops.GetPossibleSalaryTxnsForActorRequest) (*salarydataops.GetPossibleSalaryTxnsForActorResponse, error) {
	var (
		actorId      = req.GetActorId()
		pageSize     = req.GetPageContextRequest().GetPageSize()
		pageOffset   uint32
		txnsFromTime time.Time
		txnsTillTime time.Time
	)

	pageToken, err := pagination.GetPageToken(req.GetPageContextRequest())
	if err != nil {
		cxLogger.Error(ctx, "error getting page token from page context request", zap.Error(err), zap.Any("page_ctx_req", req.GetPageContextRequest()))
		return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting page token from page context request"),
		}, nil
	}
	pageOffset = pageToken.GetOffset()

	salaryTxnVerReqsRes, salaryTxnVerReqsErr := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryprogram.GetSalaryTxnVerificationRequestsRequest{
		Filters: &salaryprogram.GetSalaryTxnVerificationRequestsRequest_Filters{
			ActorId: actorId,
			Status:  salaryprogram.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
		},
		SortOrder: salaryprogram.SortOrder_DESC,
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})
	if grpcErr := epifigrpc.RPCError(salaryTxnVerReqsRes, salaryTxnVerReqsErr); grpcErr != nil {
		cxLogger.Error(ctx, "error getting salary txn verification requests for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(grpcErr))
		return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting salary txn verification requests for actor"),
		}, nil
	}

	if len(salaryTxnVerReqsRes.GetSalaryTxnVerificationRequests()) == 0 {

		registrationDetails, getRegistrationDetailsErr := s.salaryClient.GetRegistrationDetails(ctx, &salaryprogram.GetRegistrationDetailsRequest{
			ActorId:  req.GetActorId(),
			FlowType: salaryprogram.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
		})
		if grpcErr := epifigrpc.RPCError(registrationDetails, getRegistrationDetailsErr); grpcErr != nil {
			cxLogger.Error(ctx, "GetRegistrationDetails rpc call failed", zap.Error(getRegistrationDetailsErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("GetRegistrationDetails rpc call failed, err: %s", getRegistrationDetailsErr.Error())),
			}, nil
		}

		// if no verified salary txns then get txns from reg completion date to end of next month
		registrationCompletionTime := registrationDetails.GetRegistrationCompletionTime().AsTime()
		txnsFromTime = datetime.StartOfMonth(registrationCompletionTime)
		nxtMonthTime := *datetime.AddNMonths(&txnsFromTime, 1)
		txnsTillTime = datetime.EndOfMonth(nxtMonthTime)
	} else {
		lastVerifiedTxnTime := salaryTxnVerReqsRes.GetSalaryTxnVerificationRequests()[0].GetTxnTimestamp().AsTime()
		// get possible txns from and till time from last verified salary txn time.
		txnsFromTime = lastVerifiedTxnTime.Add(s.salaryOpsDynConf.SalaryTransactionFilters().MinReqDurationFromLastVerification())
		txnsTillTime = lastVerifiedTxnTime.Add(s.salaryOpsDynConf.SalaryTransactionFilters().MaxAllowedDurationFromLastVerification())
	}

	minSalaryAmtRes, minSalaryAmtErr := s.salaryClient.GetMinRequiredAmountForSalaryTxnDetection(ctx, &salaryprogram.MinRequiredAmountForSalaryTxnDetectionRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(minSalaryAmtRes, minSalaryAmtErr); grpcErr != nil {
		cxLogger.Error(ctx, "error getting min required amount for salary detection for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(grpcErr))
		return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting min required amount for salary detection for actor"),
		}, nil
	}

	var allowedPaymentProtocol []paymentPb.PaymentProtocol
	for _, protocol := range s.salaryOpsDynConf.SalaryTransactionFilters().AllowedTransactionProtocols().ToStringArray() {
		allowedPaymentProtocol = append(allowedPaymentProtocol, paymentPb.PaymentProtocol(paymentPb.PaymentProtocol_value[protocol]))
	}

	successOrdersWithTxnsReq := &order.GetSuccessOrdersWithTransactionsForActorRequest{
		ActorId:         actorId,
		StartTimestamp:  timestamp.New(txnsFromTime),
		EndTime:         timestamp.New(txnsTillTime),
		FromAmount:      minSalaryAmtRes.GetAmount(),
		PageSize:        int32(pageSize),
		Offset:          int32(pageOffset),
		TransactionType: order.GetSuccessOrdersWithTransactionsForActorRequest_CREDIT,
		PaymentProtocol: allowedPaymentProtocol,
	}

	successOrdersWithTxnsRes, successOrdersWithTxnsErr := s.orderClient.GetSuccessOrdersWithTransactionsForActor(ctx, successOrdersWithTxnsReq)
	if successOrdersWithTxnsErr != nil || !successOrdersWithTxnsRes.GetStatus().IsRecordNotFound() && !successOrdersWithTxnsRes.GetStatus().IsSuccess() {
		cxLogger.Error(ctx, "error getting success orders with transactions for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("response", successOrdersWithTxnsRes), zap.Error(successOrdersWithTxnsErr))
		return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting success orders with transactions for actor"),
		}, nil
	}
	if successOrdersWithTxnsRes.GetStatus().IsRecordNotFound() {
		cxLogger.Info(ctx, "no possible salary txn found for actor", zap.String(logger.ACTOR_ID_V2, actorId))
		return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("no possible salary txn found for actor"),
		}, nil
	}

	var (
		possibleSalaryOrdersWithTxns []*order.OrderWithTransactions
		actorTxnRemitterPis          []*paymentinstrument.PaymentInstrument
		actorTxnRemitterPiIds        []string
		possibleSalaryOrderIds       []string
	)
	for _, orderWithTxns := range successOrdersWithTxnsRes.GetOrdersWithTxns() {
		// Only orders with NO_OP workflow and only one txn are allowed for salary txn,
		// above GetSuccessOrdersWithTransactionsForActor rpc only return one txn as of now but adding this check in case any changes made in the rpc
		if orderWithTxns.GetOrder().GetWorkflow() != order.OrderWorkflow_NO_OP || len(orderWithTxns.GetTransactions()) != 1 {
			continue
		}
		// Todo (yuvraj): make bulk rpc call for getting txn categories once we get bulk rpc for this.
		isNonIncomeTxn, txnCatErr := s.isGivenTxnNotCategorizedAsIncome(ctx, actorId, orderWithTxns.GetTransactions()[0])
		if txnCatErr == nil && isNonIncomeTxn {
			cxLogger.Info(ctx, "txn is categorised as non-income", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, orderWithTxns.GetOrder().GetId()))
			continue
		}
		// if there is error while getting category of a txn then we are considering that txn as possible salary txn so that ops can verify it further.
		if txnCatErr != nil {
			cxLogger.Error(ctx, "error while checking transaction category", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, orderWithTxns.GetOrder().GetId()), zap.Error(txnCatErr))
		}
		actorTxnRemitterPiIds = append(actorTxnRemitterPiIds, orderWithTxns.GetTransactions()[0].GetPiFrom())
		possibleSalaryOrderIds = append(possibleSalaryOrderIds, orderWithTxns.GetOrder().GetId())
	}
	if len(possibleSalaryOrderIds) == 0 {
		cxLogger.Info(ctx, "no possible salary txn found for actor", zap.String(logger.ACTOR_ID_V2, actorId))
		return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	errGroup, gctx := errgroup.WithContext(ctx)
	// fetching orders and payment instruments for actor
	// fetching orders with txns again because GetSuccessOrdersWithTransactionsForActor rpc doesn't return raw_notification_details in response.
	errGroup.Go(func() error {
		var orderIdentifiers []*order.OrderIdentifier
		for _, orderId := range possibleSalaryOrderIds {
			orderIdentifiers = append(orderIdentifiers, &order.OrderIdentifier{
				Identifier: &order.OrderIdentifier_OrderId{
					OrderId: orderId,
				},
			})
		}
		res, gerr := s.orderClient.GetOrdersWithTransactions(gctx, &order.GetOrdersWithTransactionsRequest{
			OrderIdentifiers: orderIdentifiers,
		})
		if grpcErr := epifigrpc.RPCError(res, gerr); grpcErr != nil {
			cxLogger.Error(gctx, "orderClient.GetOrdersWithTransactions rpc call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("orderIds", possibleSalaryOrderIds), zap.Error(grpcErr))
			return fmt.Errorf("orderClient.GetOrdersWithTransactions rpc call failed")
		}
		possibleSalaryOrdersWithTxns = res.GetOrderWithTransactions()
		return nil
	})
	// fetching salary txn verification requests
	errGroup.Go(func() error {
		res, gerr := s.piClient.GetPIsByIds(gctx, &paymentinstrument.GetPIsByIdsRequest{
			Ids: actorTxnRemitterPiIds,
		})
		if grpcErr := epifigrpc.RPCError(res, gerr); grpcErr != nil {
			cxLogger.Error(gctx, "piClient.GetPIsByIds rpc call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("piIds", actorTxnRemitterPiIds), zap.Error(grpcErr))
			return fmt.Errorf("piClient.GetPIsByIds rpc call failed")
		}
		actorTxnRemitterPis = res.GetPaymentinstruments()
		return nil
	})

	if errGrpErr := errGroup.Wait(); errGrpErr != nil {
		return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting orders and payment instruments for actor"),
		}, nil
	}

	// salary orders should have only one txn.
	possibleSalaryOrdersWithTxns = lo.Filter(possibleSalaryOrdersWithTxns, func(orderWithTxns *order.OrderWithTransactions, idx int) bool {
		return len(orderWithTxns.GetTransactions()) == 1
	})

	// map actor txn remitter payment instrument id to payment instrument details
	actorTxnRemitterPiIdToPiDetailsMap := make(map[string]*paymentinstrument.PaymentInstrument)
	for _, piDetails := range actorTxnRemitterPis {
		actorTxnRemitterPiIdToPiDetailsMap[piDetails.GetId()] = piDetails
	}

	var possibleSalaryTxns []*salarydataops.TxnDetails
	for _, orderWithTxns := range possibleSalaryOrdersWithTxns {
		txn := orderWithTxns.GetTransactions()[0]
		piDetails := actorTxnRemitterPiIdToPiDetailsMap[txn.GetPiFrom()]

		percentageDiffOfTxnAmtFromLastSalaryAmtString, percentageDiffOfTxnAmtFromLastSalaryAmtStringErr := s.percentageDiffOfTxnAmtFromLastSalaryAmtString(ctx, orderWithTxns, actorId)
		if percentageDiffOfTxnAmtFromLastSalaryAmtStringErr != nil {
			logger.Error(ctx, "error in getting percentageDiffOfTxnAmtFromLastSalaryAmtString", zap.Error(percentageDiffOfTxnAmtFromLastSalaryAmtStringErr))
			return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in getting percentageDiffOfTxnAmtFromLastSalaryAmtString"),
			}, nil
		}

		probabilityOfRemitterNameBeingAMerchant, getProbabilityOfRemitterNameBeingAMerchantErr := s.getProbabilityOfRemitterBeingAMerchantString(ctx, piDetails.GetVerifiedName())
		if getProbabilityOfRemitterNameBeingAMerchantErr != nil {
			logger.Error(ctx, "error in getting probabilityOfRemitterBeingAMerchant", zap.Error(getProbabilityOfRemitterNameBeingAMerchantErr))
		}

		probabilityOfPiRemitterNameBeingAMerchant, getProbabilityOfPiRemitterNameBeingAMerchantErr := s.getProbabilityOfRemitterBeingAMerchantString(ctx, piDetails.GetAccount().GetName())
		if getProbabilityOfPiRemitterNameBeingAMerchantErr != nil {
			logger.Error(ctx, "error in getting probabilityOfRemitterBeingAMerchant", zap.Error(getProbabilityOfPiRemitterNameBeingAMerchantErr))
		}

		possibleSalaryTxns = append(possibleSalaryTxns, &salarydataops.TxnDetails{
			RemitterName:                       piDetails.GetVerifiedName(),
			PiRemitterName:                     piDetails.GetAccount().GetName(),
			TxnId:                              orderWithTxns.GetOrder().GetExternalId(),
			TxnTimestamp:                       txn.GetCreatedAt(),
			PaymentProtocol:                    txn.GetPaymentProtocol().String(),
			Particulars:                        txn.GetRawNotificationDetails()["CREDIT"].GetParticulars(),
			RemitterPiId:                       piDetails.GetId(),
			PercentageDiffFromLastSalaryString: percentageDiffOfTxnAmtFromLastSalaryAmtString,
			ProbabilityOfRemitterBeingAMerchant: &salarydataops.ProbabilityOfRemitterBeingAMerchant{
				ProbabilityOfRemitterNameBeingAMerchantString:   probabilityOfRemitterNameBeingAMerchant,
				ProbabilityOfPiRemitterNameBeingAMerchantString: probabilityOfPiRemitterNameBeingAMerchant,
			},
		})
	}

	pageCtxResp, err := getCustomPageContextResponse(pageOffset, pageSize, uint32(len(successOrdersWithTxnsRes.GetOrdersWithTxns())))
	if err != nil {
		cxLogger.Error(ctx, "error getting page context response from page token", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting page context response from page token"),
		}, nil
	}

	return &salarydataops.GetPossibleSalaryTxnsForActorResponse{
		Status:              rpc.StatusOk(),
		Txns:                possibleSalaryTxns,
		PageContextResponse: pageCtxResp,
	}, nil
}

func (s *Service) GetSalaryProgramActivationStatusAtTimeForAgents(ctx context.Context, req *salarydataops.GetSalaryProgramActivationStatusAtTimeForAgentsRequest) (*salarydataops.GetSalaryProgramActivationStatusAtTimeForAgentsResponse, error) {
	actorId := req.GetHeader().GetActor().GetId()

	currentRegStatusAndNextRegStageResponse, err := s.salaryClient.GetCurrentRegStatusAndNextRegStage(ctx, &salaryprogram.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: salaryprogram.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if grpcErr := epifigrpc.RPCError(currentRegStatusAndNextRegStageResponse, err); grpcErr != nil {
		cxLogger.Error(ctx, "GetCurrentRegStatusAndNextRegStage call failed", zap.Error(err))
		return &salarydataops.GetSalaryProgramActivationStatusAtTimeForAgentsResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetCurrentRegStatusAndNextRegStage call failed"),
		}, nil
	}

	if currentRegStatusAndNextRegStageResponse.GetRegistrationStatus() != salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		return &salarydataops.GetSalaryProgramActivationStatusAtTimeForAgentsResponse{
			Status:                 rpc.StatusOk(),
			SalaryActivationStatus: salarydataops.SalaryActivationStatus_SALARY_ACTIVATION_STATUS_INACTIVE,
		}, nil
	}

	latestActivationDetailsActiveAtTimeResponse, err := s.salaryClient.GetLatestActivationDetailsActiveAtTime(ctx, &salaryprogram.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: currentRegStatusAndNextRegStageResponse.GetRegistrationId(),
		ActiveAtTime:   req.GetTimestamp(),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user.
		ActivationKind: salaryprogram.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	if err != nil || !latestActivationDetailsActiveAtTimeResponse.GetStatus().IsRecordNotFound() && !latestActivationDetailsActiveAtTimeResponse.GetStatus().IsSuccess() {
		cxLogger.Error(ctx, "GetLatestActivationDetailsActiveAtTime call failed", zap.Error(err))
		return &salarydataops.GetSalaryProgramActivationStatusAtTimeForAgentsResponse{
			Status: rpc.StatusInternalWithDebugMsg("GetLatestActivationDetailsActiveAtTime call failed"),
		}, nil
	}
	// if no activation record found or activation type is not full salary activation then show INACTIVE status to the agent
	if latestActivationDetailsActiveAtTimeResponse.GetStatus().IsRecordNotFound() || latestActivationDetailsActiveAtTimeResponse.GetActivationType() != salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION {
		return &salarydataops.GetSalaryProgramActivationStatusAtTimeForAgentsResponse{
			Status:                 rpc.StatusOk(),
			SalaryActivationStatus: salarydataops.SalaryActivationStatus_SALARY_ACTIVATION_STATUS_INACTIVE,
		}, nil
	}

	return &salarydataops.GetSalaryProgramActivationStatusAtTimeForAgentsResponse{
		Status:                 rpc.StatusOk(),
		SalaryActivationStatus: salarydataops.SalaryActivationStatus_SALARY_ACTIVATION_STATUS_ACTIVE,
	}, nil
}
