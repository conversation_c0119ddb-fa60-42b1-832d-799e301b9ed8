package onboarding

import (
	"context"
	"regexp"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth/afu"
	empPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/salaryprogram"
	userspb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/cx/config/genconf"

	authPb "github.com/epifi/gamma/api/auth"

	"github.com/epifi/gamma/cx/config"

	"github.com/epifi/gamma/api/user/onboarding"

	livenessPb "github.com/epifi/gamma/api/auth/liveness"

	"github.com/epifi/be-common/pkg/epifigrpc"

	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"

	"go.uber.org/zap"

	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/api/rpc"
	obPb "github.com/epifi/gamma/api/cx/data_collector/onboarding"
)

type Service struct {
	obPb.UnimplementedOnboardingServer
	kycClient                     kycPb.KycClient
	authEngine                    auth_engine.IAuthEngine
	livenessClient                livenessPb.LivenessClient
	onbClient                     onboarding.OnboardingClient
	onboardingStageDetailsMapping map[string]*config.OnboardingStageDetails
	authClient                    authPb.AuthClient
	userGroupClient               userGroupPb.GroupClient
	actorClient                   actorPb.ActorClient
	usersClient                   userspb.UsersClient
	emailRegexp                   *regexp.Regexp
	productClient                 product.ProductClient
	salaryClient                  salaryprogram.SalaryProgramClient
	employmentClient              empPb.EmploymentClient
}

func NewService(kycClient kycPb.KycClient, authEngine auth_engine.IAuthEngine, livenessClient livenessPb.LivenessClient,
	onbClient onboarding.OnboardingClient, onboardingStageDetailsMapping map[string]*config.OnboardingStageDetails,
	authClient authPb.AuthClient, userGroupClient userGroupPb.GroupClient, usersClient userspb.UsersClient, actorClient actorPb.ActorClient,
	conf *genconf.Config, productClient product.ProductClient, salaryClient salaryprogram.SalaryProgramClient, employmentClient empPb.EmploymentClient) *Service {
	return &Service{
		kycClient:                     kycClient,
		authEngine:                    authEngine,
		livenessClient:                livenessClient,
		onbClient:                     onbClient,
		onboardingStageDetailsMapping: onboardingStageDetailsMapping,
		authClient:                    authClient,
		userGroupClient:               userGroupClient,
		actorClient:                   actorClient,
		usersClient:                   usersClient,
		emailRegexp:                   regexp.MustCompile(conf.EmailValidationRegex()),
		productClient:                 productClient,
		salaryClient:                  salaryClient,
		employmentClient:              employmentClient,
	}
}

const (
	afuActionPendingStatusStr       = "PENDING"
	afuActionCompletedStatusStr     = "COMPLETED"
	afuStatusFailedStatusStr        = "FAILED"
	afuReregEnquiryFailedStatusStr  = "ENQUIRY_REQUEST_FAILED"
	afuReregFailedStatusStr         = "REREGISTRATION_FAILED"
	afuActionNotRequiredStatusStr   = "NOT_REQUIRED"
	LivenessCheckStage              = "LIVENESS_CHECK"
	ATMPinValidationStage           = "ATM_PIN_VALIDATION"
	UpdateConfirmationStage         = "UPDATE_CONFIRMATION"
	DeviceRegistrationStage         = "DEVICE_REGISTRATION"
	DeviceDeactivation              = "DEVICE_DEACTIVATION"
	DeviceReregistrationRequest     = "DEVICE_REREGISTRATION_REQUESTED"
	DeviceReregistrationEnquiry     = "DEVICE_REREGISTRATION_ENQUIRY"
	DeviceReactivation              = "DEVICE_REACTIVATION"
	EpifiDeviceUpdate               = "EPIFI_DEVICE_UPDATE"
	EpifiPhoneNoAndEmailUpdateStage = "EPIFI_PHONE_NO_AND_EMAIL_UPDATE"
	PublishReoobeEventStage         = "PUBLISH_REOOBE_EVENT"
	FiOnbStatusReachedHome          = "Reached Home"
)

var (
	ReOOBEStageList = []string{
		UpdateConfirmationStage,
		DeviceRegistrationStage,
		DeviceDeactivation,
		DeviceReregistrationRequest,
		DeviceReregistrationEnquiry,
		DeviceReactivation,
		EpifiDeviceUpdate,
		EpifiPhoneNoAndEmailUpdateStage,
		PublishReoobeEventStage,
	}
	ReoobeVendorUpdateStageOrder = map[afu.UpdateVendorState]int{
		afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED: 0,
		afu.UpdateVendorState_INITIATED:                       1,
		afu.UpdateVendorState_DEACTIVATED:                     2,
		afu.UpdateVendorState_REREGISTRATION_REQUESTED:        3,
		afu.UpdateVendorState_REREGISTERED:                    4,
		afu.UpdateVendorState_COMPLETED:                       5,
	}
	ReoobeVendorUpdateFailureOrder = map[afu.UpdateVendorFailureType]int{
		afu.UpdateVendorFailureType_UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED: 0,
		afu.UpdateVendorFailureType_DEACTIVATION_FAILED:                    1,
		afu.UpdateVendorFailureType_REREG_ENQUIRY_FAILED:                   2,
		afu.UpdateVendorFailureType_REREGISTRATION_FAILED:                  3,
		afu.UpdateVendorFailureType_REACTIVATION_FAILED:                    4,
	}
	IsScreenerStageMapping = map[string]bool{
		onboarding.OnboardingStage_REFERRAL_FINITE_CODE.String():           true,
		onboarding.OnboardingStage_TNC_CONSENT.String():                    true,
		onboarding.OnboardingStage_CHECK_CREDIT_REPORT_PRESENCE.String():   true,
		onboarding.OnboardingStage_CONSENT_CREDIT_REPORT_DOWNLOAD.String(): true,
		onboarding.OnboardingStage_CREDIT_REPORT_VERIFICATION.String():     true,
		onboarding.OnboardingStage_EMPLOYMENT_VERIFICATION.String():        true,
		onboarding.OnboardingStage_MANDATE_CONSENT_CREDIT_REPORT.String():  true,
		onboarding.OnboardingStage_MANUAL_SCREENING.String():               true,
		onboarding.OnboardingStage_APP_SCREENING.String():                  true,
		onboarding.OnboardingStage_WORK_EMAIL_VERIFICATION.String():        true,
		onboarding.OnboardingStage_LINKEDIN_VERIFICATION.String():          true,
		onboarding.OnboardingStage_GMAIL_VERIFICATION.String():             true,
	}
)

var _ obPb.OnboardingServer = &Service{}

func (s *Service) GetOnboardingScores(ctx context.Context, req *obPb.GetOnboardingScoresRequest) (*obPb.GetOnboardingScoresResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &obPb.GetOnboardingScoresResponse{Status: rpc.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	// call kyc service first to get attempt ids for liveness and face match
	kycResp, err := s.kycClient.CheckKYCStatus(ctx, &kycPb.CheckKYCStatusRequest{ActorId: req.Header.GetActor().GetId(), IgnoreLiveness: true})
	if te := epifigrpc.RPCError(kycResp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch attempt id's for user", zap.Error(te))
		return &obPb.GetOnboardingScoresResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch attempt id's for user"),
		}, nil
	}
	// get liveness status and scores
	livenessResp, err := s.FetchLivenessScore(ctx, actorId, kycResp.LivenessRequestId)
	if err != nil {
		return &obPb.GetOnboardingScoresResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch liveness score for user"),
		}, nil
	}
	// get face match status and scores for each attempt
	facematchResponseList, err := s.FetchFacematchScores(ctx, actorId, kycResp.FmRequestIds)
	if err != nil {
		return &obPb.GetOnboardingScoresResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch facematch scores for user"),
		}, nil
	}
	return &obPb.GetOnboardingScoresResponse{
		Status:                 rpc.StatusOk(),
		LivenessStatus:         obPb.LivenessStatus(livenessResp.GetLivenessStatus()),
		LivenessStatusString:   livenessResp.GetLivenessStatus().String(),
		LivenessScore:          livenessResp.LivenessScore,
		LivenessScoreThreshold: livenessResp.LivenessScoreThreshold,
		OtpScore:               livenessResp.OtpScore,
		OtpScoreThreshold:      livenessResp.OtpScoreThreshold,
		FaceMatchScores:        facematchResponseList,
	}, nil
}

func (s *Service) GetOnboardingDetails(ctx context.Context, req *obPb.GetOnboardingDetailsRequest) (*obPb.GetOnboardingDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &obPb.GetOnboardingDetailsResponse{Status: rpc.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if actorId == "" {
		cxLogger.Error(ctx, "header does not have actor information")
		return &obPb.GetOnboardingDetailsResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("customer not found in system"),
		}, nil
	}
	troubleShootingResp, err := s.onbClient.GetTroubleshootingDetails(ctx, &onboarding.GetTroubleshootingDetailsRequest{
		ActorIds: []string{req.GetHeader().GetActor().GetId()},
	})
	if te := epifigrpc.RPCError(troubleShootingResp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch onboarding troubleshooting details", zap.Error(te))
		return &obPb.GetOnboardingDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch onboarding details"),
		}, nil
	}

	featResp, err := s.onbClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(featResp, err); te != nil {
		if featResp.GetStatus().IsRecordNotFound() {
			return &obPb.GetOnboardingDetailsResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		cxLogger.Error(ctx, "failed to fetch feature details", zap.Error(te))
		return &obPb.GetOnboardingDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch feature details"),
		}, nil
	}
	// if number of onboarding entries is equal to 0, then return record not found error
	if len(troubleShootingResp.GetDetails()) == 0 {
		return &obPb.GetOnboardingDetailsResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("onboarding details not found"),
		}, nil
	}
	troubleShootingDetails := troubleShootingResp.GetDetails()[0]

	userResp, err := s.usersClient.GetUser(ctx, &userspb.GetUserRequest{
		Identifier: &userspb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err != nil {
		logger.Error(ctx, "error in get user", zap.Error(err))
		return nil, err
	}
	getActiveProductsResp, errActiveProducts := s.productClient.GetActiveProductsByPAN(ctx, &product.GetActiveProductsByPANRequest{
		ExcludedActorId: actorId,
		Pan:             userResp.GetUser().GetProfile().GetPAN(),
	})
	if errActiveProducts != nil {
		logger.Error(ctx, "failed to get active products by PAN", zap.Error(errActiveProducts))
		return nil, err
	}

	return s.BuildOnboardingResponse(ctx, troubleShootingDetails, featResp.IsFiLiteUser, getActiveProductsResp), nil
}

// nolint:funlen
func (s *Service) BuildOnboardingResponse(ctx context.Context, resp *onboarding.StageTroubleshootingDetails, isFiLite bool, activeProducts *product.GetActiveProductsByPANResponse) *obPb.GetOnboardingDetailsResponse {
	fiOnbStatus := resp.GetState().String()
	var currentProduct string
	if isFiLite {
		fiOnbStatus = FiOnbStatusReachedHome
	}
	currProduct := resp.GetOnb().GetFeature()
	if currProduct == onboarding.Feature_FEATURE_UNSPECIFIED || currProduct == onboarding.Feature_FEATURE_FI_LITE {
		currProduct = onboarding.Feature_FEATURE_SA
	}
	switch currProduct {
	case onboarding.Feature_FEATURE_SA:
		currentProduct = "Savings Account"
	case onboarding.Feature_FEATURE_CC:
		currentProduct = "Credit Card"
	case onboarding.Feature_FEATURE_PL:
		currentProduct = "Personal Loan"
	}

	onboardingDetails := &obPb.OnboardingDetails{
		StageDetails: &obPb.OnboardingStageDetails{
			StageMapping:        map[string]string{},
			StageDetailsMapping: map[string]*obPb.StageDetails{},
		},
		CurrentStageTroubleshootingDetails: &obPb.CurrentStageTroubleShootingDetails{
			Stage:          resp.GetStage().String(),
			State:          resp.GetState().String(),
			L1:             resp.GetL1(),
			L2:             resp.GetL2(),
			L3:             resp.GetL3(),
			Advice:         resp.GetAdvice(),
			StageDebugInfo: getStageDebugInfo(ctx, resp),
		},
		FiOnboardingStatus: fiOnbStatus,
		CurrentProduct:     currentProduct,
		OnboardedProducts:  activeProducts.GetActiveProducts(),
	}
	stageDetails := resp.GetOnb().GetStageDetails()
	if stageDetails == nil {
		cxLogger.Error(ctx, "error while building onboarding response, stage details not present",
			zap.String(logger.ACTOR_ID, resp.GetOnb().GetActorId()))
		return &obPb.GetOnboardingDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while building onboarding response"),
		}
	}
	// populate current stage
	onboardingDetails.StageDetails.CurrentStage = resp.GetOnb().GetCurrentOnboardingStage().String()
	// populate screener status
	onboardingDetails.StageDetails.AppScreenerStatus = getAppScreenerStatus(resp)
	// populate status values for all stages
	for key, val := range stageDetails.GetStageMapping() {
		onboardingDetails.StageDetails.StageMapping[key] = val.GetState().String()
		// populate status and other stage details from config
		stageDetailsResp := &obPb.StageDetails{
			Status:    val.GetState().String(),
			UpdatedAt: val.GetLastUpdatedAt(),
		}
		onboardingStageDetailsConf, ok := s.onboardingStageDetailsMapping[key]
		if ok {
			stageDetailsResp.Order = onboardingStageDetailsConf.Order
			stageDetailsResp.Description = onboardingStageDetailsConf.Description
		}
		stageDetailsResp.IsPartOfScreenerFlow = isStagePartOfScreenerFlow(key)
		// set meta data if available for stage
		setMetaDataInfo(key, stageDetailsResp, resp.GetOnb())
		onboardingDetails.GetStageDetails().StageDetailsMapping[key] = stageDetailsResp
	}

	return &obPb.GetOnboardingDetailsResponse{
		Status:            rpc.StatusOk(),
		OnboardingDetails: onboardingDetails,
	}
}

func getStageDebugInfo(ctx context.Context, resp *onboarding.StageTroubleshootingDetails) string {
	stageDebugInfo, ok := resp.GetStageDebugInfo().(onboarding.StageDebugInfo)
	if !ok {
		cxLogger.Error(ctx, "stage debug info type doesn't implement StageDebugInfo interface")
		return ""
	}
	jsonBytes, err := stageDebugInfo.ToJson()

	if err != nil {
		cxLogger.Error(ctx, "error while marshaling stage debug info for onboarding response", zap.Error(err))
	}
	return string(jsonBytes)
}

func getAppScreenerStatus(resp *onboarding.StageTroubleshootingDetails) obPb.AppScreenerStatus {
	currentStage := resp.GetOnb().GetCurrentOnboardingStage()
	screenerStageDetails := resp.GetOnb().GetStageDetails().StageMapping[onboarding.OnboardingStage_APP_SCREENING.String()]
	switch {
	case screenerStageDetails.GetState() == onboarding.OnboardingState_SKIPPED:
		return obPb.AppScreenerStatus_NOT_REQUIRED
	case screenerStageDetails.GetState() == onboarding.OnboardingState_SUCCESS:
		return obPb.AppScreenerStatus_SUCCESS
	case screenerStageDetails.GetState() == onboarding.OnboardingState_FAILURE:
		return obPb.AppScreenerStatus_FAILED
	case isStagePartOfScreenerFlow(currentStage.String()):
		return obPb.AppScreenerStatus_IN_PROGRESS
	default:
		return obPb.AppScreenerStatus_NOT_STARTED
	}
}

func setMetaDataInfo(stage string, resp *obPb.StageDetails, details *onboarding.OnboardingDetails) {
	switch stage {
	case onboarding.OnboardingStage_DEDUPE_CHECK.String():
		resp.MetaData = &obPb.StageDetails_DedupeStatus{
			DedupeStatus: details.GetStageMetadata().GetDedupeStatus().String(),
		}
		return
	case onboarding.OnboardingStage_KYC_DEDUPE_CHECK.String():
		resp.MetaData = &obPb.StageDetails_KycDedupeStatus{
			KycDedupeStatus: details.GetStageMetadata().GetKycDedupeStatus().String(),
		}
		return
	}
	// populate the default in app screener meta if the stage is part of screener flow
	if isStagePartOfScreenerFlow(stage) {
		resp.MetaData = &obPb.StageDetails_InAppScreeningMetadata{
			InAppScreeningMetadata: GetInAppScreenerMeta(details),
		}
	}
}

func GetInAppScreenerMeta(details *onboarding.OnboardingDetails) *obPb.InAppScreeningMetaData {
	return &obPb.InAppScreeningMetaData{
		CreditReportFound: &obPb.MetaField{
			Label: "Was credit report found",
			Value: &obPb.MetaField_BoolValue{
				BoolValue: details.GetStageMetadata().GetAppScreeningData().GetCreditReportFound(),
			},
		},
		ConsentCreditReportDownload: &obPb.MetaField{
			Label: "did user give consent to download report",
			Value: &obPb.MetaField_BoolValue{
				BoolValue: details.GetStageMetadata().GetAppScreeningData().GetConsentCreditReportDownload(),
			},
		},
		CreditReportVerificationPassed: &obPb.MetaField{
			Label: "was credit report verification successful",
			Value: &obPb.MetaField_BoolValue{
				BoolValue: details.GetStageMetadata().GetAppScreeningData().GetCreditReportVerificationPassed(),
			},
		},
		EmploymentVerificationPassed: &obPb.MetaField{
			Label: "was employment verification successful",
			Value: &obPb.MetaField_BoolValue{
				BoolValue: details.GetStageMetadata().GetAppScreeningData().GetEmploymentVerificationPassed(),
			},
		},
		ManualScreeningReason: &obPb.MetaField{
			Label: "reason for manual screening",
			Value: &obPb.MetaField_StringValue{
				StringValue: details.GetStageMetadata().GetAppScreeningData().GetManualScreeningReason(),
			},
		},
	}
}

func (s *Service) FetchLivenessScore(ctx context.Context, actorId string, attemptId string) (*livenessPb.GetLivenessStatusResponse, error) {
	resp, err := s.livenessClient.GetLivenessStatus(ctx, &livenessPb.GetLivenessStatusRequest{
		ActorId:   actorId,
		AttemptId: attemptId,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to fetch liveness score for user", zap.String(logger.ACTOR_ID,
			actorId), zap.String("attemptId", attemptId), zap.Error(te))
		return nil, te
	}
	return resp, nil
}

func (s *Service) FetchFacematchScores(ctx context.Context, actorId string, attempetIds []string) ([]*obPb.FaceMatchScoreResponse, error) {
	var faceMatchResponseList []*obPb.FaceMatchScoreResponse
	for _, attempetId := range attempetIds {
		resp, err := s.livenessClient.GetFaceMatchStatus(ctx, &livenessPb.GetFaceMatchStatusRequest{
			ActorId:   actorId,
			AttemptId: attempetId,
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			logger.Error(ctx, "failed to fetch facematch score for user", zap.String(logger.ACTOR_ID,
				actorId), zap.String("attemptId", attempetId), zap.Error(te))
			return nil, te
		}
		faceMatchResponseList = append(faceMatchResponseList, &obPb.FaceMatchScoreResponse{
			AttemptId:             attempetId,
			FaceMatchStatus:       obPb.FaceMatchStatus(resp.GetFaceMatchStatus()),
			FaceMatchStatusString: resp.GetFaceMatchStatus().String(),
			FaceMatchScore:        resp.FaceMatchScore,
			FmScoreThreshold:      resp.FmScoreThreshold,
		})
	}
	return faceMatchResponseList, nil
}

func (s *Service) GetReOOBEDetails(ctx context.Context, req *obPb.GetReOOBEDetailsRequest) (*obPb.GetReOOBEDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &obPb.GetReOOBEDetailsResponse{Status: rpc.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	afuResp, err := s.authClient.GetAuthFactorUpdatesForActor(ctx, &authPb.GetAuthFactorUpdatesForActorRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if te := epifigrpc.RPCError(afuResp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch onboarding details", zap.Error(te))
		if afuResp.GetStatus().IsRecordNotFound() {
			return &obPb.GetReOOBEDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &obPb.GetReOOBEDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching reoobe details"),
		}, nil
	}
	// fetching the troubleshooting details for the given actor
	tsResp, err := s.authClient.GetTroubleshootingDetails(ctx, &authPb.GetTroubleshootingDetailsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
	})
	if tsErr := epifigrpc.RPCError(tsResp, err); tsErr != nil {
		cxLogger.Error(ctx, "failed to fetch troubleshooting details", zap.Error(tsErr))
		return &obPb.GetReOOBEDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching troubleshooting details"),
		}, nil
	}

	return s.BuildReOOBEResponse(ctx, afuResp, tsResp), nil
}

func (s *Service) BuildReOOBEResponse(ctx context.Context, afuResp *authPb.GetAuthFactorUpdatesForActorResponse, tsResp *authPb.GetTroubleshootingDetailsResponse) *obPb.GetReOOBEDetailsResponse {
	var reoobeSummaryRecords []*obPb.AuthFactorUpdateSummary
	for _, record := range afuResp.GetAuthFactorUpdates() {
		reoobeSummaryRecords = append(reoobeSummaryRecords, s.getAFUSummary(record))
	}
	tsD := tsResp.GetDetails()
	troubleshootingDetails := &obPb.AfuTroubleshootingDetails{
		Stage:           tsD.GetStage().String(),
		StageStatus:     tsD.GetStageStatus().String(),
		DiagnosisCode:   tsD.GetDiagnosisCode().String(),
		DiagnosisReport: tsD.GetDiagnosisReport(),
		CardForm:        tsD.GetCardForm().String(),
		Remedy: &obPb.Remedy{
			Advice: tsD.GetRemedy().GetAdvice(),
		},
	}
	return &obPb.GetReOOBEDetailsResponse{
		Status:                 rpc.StatusOk(),
		ReoobeSummaryRecords:   reoobeSummaryRecords,
		TroubleshootingDetails: troubleshootingDetails,
	}
}

func (s *Service) getAFUSummary(afuRecord *afu.AuthFactorUpdate) *obPb.AuthFactorUpdateSummary {
	res := &obPb.AuthFactorUpdateSummary{
		AfuId:            afuRecord.Id,
		ActorId:          afuRecord.ActorId,
		AuthFactors:      convertAuthFactorsArrToStr(afuRecord.Context.AuthFactors),
		ProcessStatus:    afuRecord.OverallStatus.String(),
		CreatedAt:        afuRecord.CreatedAt,
		DeletedAt:        afuRecord.DeletedAt,
		StageWiseDetails: s.getStageWiseDetails(afuRecord),
		FailureReason:    afuRecord.GetFailureReason().String(),
	}
	return res
}

func (s *Service) getStageWiseDetails(afuRecord *afu.AuthFactorUpdate) []*obPb.AFUStageSummary {
	afuCtx := afuRecord.Context
	stageWiseDetails := make([]*obPb.AFUStageSummary, 0)
	for _, credential := range afuCtx.CredentialStatuses {
		stageDetail := &obPb.AFUStageSummary{}
		switch credential.Credential {
		case afu.Credential_LIVENESS_FM_VALIDATION:
			stageDetail.StageName = LivenessCheckStage
		case afu.Credential_ATM_PIN_VALIDATION:
			stageDetail.StageName = ATMPinValidationStage
		default:
			continue
		}
		if credential.Status == afu.VerificationStatus_VERIFICATION_STATUS_UNSPECIFIED {
			stageDetail.Status = afuActionPendingStatusStr
		} else {
			stageDetail.Status = credential.Status.String()
		}
		stageWiseDetails = append(stageWiseDetails, stageDetail)
	}
	for _, stage := range ReOOBEStageList {
		stageDetails, ok := s.getStageDetail(stage, afuRecord)
		if ok {
			stageWiseDetails = append(stageWiseDetails, stageDetails)
		}
	}
	return stageWiseDetails
}

func (s *Service) getStageDetail(stage string, afuRecord *afu.AuthFactorUpdate) (*obPb.AFUStageSummary, bool) {
	afuCtx := afuRecord.Context
	stageDetail := &obPb.AFUStageSummary{
		StageName: stage,
		Status:    afuActionNotRequiredStatusStr,
	}
	switch stage {
	case UpdateConfirmationStage:
		afuConfirmationStatus := afuCtx.UserConfirmationStatus
		if afuConfirmationStatus == afu.UserConfirmationStatus_USER_CONFIRMATION_STATUS_UNSPECIFIED {
			stageDetail.Status = afuActionPendingStatusStr
		} else {
			stageDetail.Status = afuConfirmationStatus.String()
		}
	case DeviceRegistrationStage:
		if s.isDeviceRegistrationRequired(afuCtx.GetAuthFactors()) {
			if afuCtx.EncryptedPayload == "" {
				stageDetail.Status = afuActionPendingStatusStr
			} else {
				stageDetail.Status = afuActionCompletedStatusStr
			}
		}
	case DeviceDeactivation:
		stageDetail.Status = getStatusForVendorUpdateStage(afuRecord,
			afu.UpdateVendorState_DEACTIVATED, afu.UpdateVendorFailureType_DEACTIVATION_FAILED)
	case DeviceReregistrationRequest:
		status := getStatusForVendorUpdateStage(afuRecord, afu.UpdateVendorState_REREGISTRATION_REQUESTED, afu.UpdateVendorFailureType_REREG_ENQUIRY_FAILED)
		if status == afuStatusFailedStatusStr {
			// if enquiry has failed, that means re-registration request was successfully made
			status = afuActionCompletedStatusStr
		}
		stageDetail.Status = status
	case DeviceReregistrationEnquiry:
		status := getStatusForVendorUpdateStage(afuRecord, afu.UpdateVendorState_REREGISTERED, afu.UpdateVendorFailureType_REREG_ENQUIRY_FAILED)
		if status == afuStatusFailedStatusStr {
			stageDetail.Status = afuReregEnquiryFailedStatusStr
			break
		}
		status = getStatusForVendorUpdateStage(afuRecord, afu.UpdateVendorState_REREGISTERED, afu.UpdateVendorFailureType_REREGISTRATION_FAILED)
		if status == afuStatusFailedStatusStr {
			stageDetail.Status = afuReregFailedStatusStr
		} else {
			stageDetail.Status = status
		}
	case DeviceReactivation:
		stageDetail.Status = getStatusForVendorUpdateStage(afuRecord,
			afu.UpdateVendorState_COMPLETED, afu.UpdateVendorFailureType_REACTIVATION_FAILED)
	case EpifiDeviceUpdate:
		stageDetail.Status = convertRequestStatusToStageStatus(afuCtx.GetEpifiDeviceUpdate())
	case EpifiPhoneNoAndEmailUpdateStage:
		if s.isUserProfileUpdateRequired(afuCtx.AuthFactors) {
			stageDetail.Status = convertRequestStatusToStageStatus(afuCtx.GetEpifiEmailPhoneNumUpdate())
		}
	case PublishReoobeEventStage:
		if afuCtx.GetAfuCompletionEventPublished() {
			stageDetail.Status = afuActionCompletedStatusStr
		}
	}
	return stageDetail, true
}

func getStatusForVendorUpdateStage(afuRecord *afu.AuthFactorUpdate, stage afu.UpdateVendorState,
	stageFailureType afu.UpdateVendorFailureType) string {
	vendorUpdateStage := afuRecord.GetVendorContext().GetState()
	vendorFailureStage := afuRecord.GetVendorContext().GetFailureType()
	if vendorUpdateStage == afu.UpdateVendorState_FAILED {
		switch {
		case ReoobeVendorUpdateFailureOrder[vendorFailureStage] > ReoobeVendorUpdateFailureOrder[stageFailureType]:
			return afuActionCompletedStatusStr
		case ReoobeVendorUpdateFailureOrder[vendorFailureStage] < ReoobeVendorUpdateFailureOrder[stageFailureType]:
			return afuActionPendingStatusStr
		default:
			return afuStatusFailedStatusStr
		}
	} else {
		if ReoobeVendorUpdateStageOrder[vendorUpdateStage] >= ReoobeVendorUpdateStageOrder[stage] {
			return afuActionCompletedStatusStr
		} else {
			return afuActionPendingStatusStr
		}
	}
}

func convertAuthFactorsArrToStr(authFactors []afu.AuthFactor) []string {
	outputArr := make([]string, 0)
	for _, af := range authFactors {
		outputArr = append(outputArr, af.String())
	}
	return outputArr
}

func getVendorUpdateState(afuRecord *afu.AuthFactorUpdate) string {
	updateState := afuRecord.VendorContext.GetState()
	failureType := afuRecord.VendorContext.GetFailureType()
	if updateState == afu.UpdateVendorState_FAILED {
		return failureType.String()
	} else if updateState == afu.UpdateVendorState_UPDATE_VENDOR_STATE_UNSPECIFIED {
		return "PENDING"
	} else {
		return updateState.String()
	}
}

func convertRequestStatusToStageStatus(reqStatus afu.RequestStatus) string {
	switch reqStatus {
	case afu.RequestStatus_REQUEST_STATUS_SUCCESS:
		return afuActionCompletedStatusStr
	case afu.RequestStatus_REQUEST_STATUS_UNSPECIFIED:
		return afuActionPendingStatusStr
	default:
		return reqStatus.String()
	}
}

// isDeviceRegistrationRequired returns whether device registration
// is required or not. It's not required when user is updating
// only email. It's required when either of phone number or device are changed.
func (s *Service) isDeviceRegistrationRequired(authFactors []afu.AuthFactor) bool {
	for _, factor := range authFactors {
		if factor == afu.AuthFactor_DEVICE || factor == afu.AuthFactor_PHONE_NUM || factor == afu.AuthFactor_SIM {
			return true
		}
	}
	return false
}

func (s *Service) isUserProfileUpdateRequired(authFactors []afu.AuthFactor) bool {
	for _, factor := range authFactors {
		if factor == afu.AuthFactor_EMAIL || factor == afu.AuthFactor_PHONE_NUM {
			return true
		}
	}
	return false
}

func isStagePartOfScreenerFlow(stage string) bool {
	isPartOfScreening := IsScreenerStageMapping[stage]
	return isPartOfScreening
}
