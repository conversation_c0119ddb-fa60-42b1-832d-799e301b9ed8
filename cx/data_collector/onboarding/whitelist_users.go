package onboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gocarina/gocsv"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/rpc/code"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/cx/data_collector/onboarding"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/salaryprogram"
	usersPb "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
)

const (
	limit = 200
)

// format of the csv file with phone numbers
type rowWithPhoneNumber struct {
	PhoneNumber      string `csv:"PhoneNumber"`
	EmployerId       string `csv:"EmployerId"`
	TartanEmployerId string `csv:"TartanEmployerId"`
	TartanUserId     string `csv:"TartanUserId"`
}

// format of the csv file with emails
type rowWithEmail struct {
	Email string `csv:"Email"`
}

func (s *Service) WhitelistUsersForOnboarding(ctx context.Context, req *onboarding.WhitelistUsersForOnboardingRequest) (*onboarding.WhitelistUsersForOnboardingResponse, error) {
	if req.GetHeader().GetAgentEmail() == "" {
		logger.Info(ctx, "empty email")
		return &onboarding.WhitelistUsersForOnboardingResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	identifiers, err := s.getIdentifiers(ctx, req)
	if err != nil {
		logger.Error(ctx, "error in fetching identifiers", zap.Error(err))
		return &onboarding.WhitelistUsersForOnboardingResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}
	if len(identifiers) == 0 {
		logger.Info(ctx, "no identifiers")
		return &onboarding.WhitelistUsersForOnboardingResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	if len(identifiers) > limit {
		logger.Info(ctx, fmt.Sprintf("limit exceeded %v", len(identifiers)))
		status := rpc.StatusInternal()
		status.ShortMessage = fmt.Sprintf("limit exceeded, only a max of %v values can be added at once", limit)
		return &onboarding.WhitelistUsersForOnboardingResponse{
			Status: status,
		}, nil
	}

	logIdentifiers(ctx, identifiers)
	res, err := s.userGroupClient.AddMappings(ctx, &usergrouppb.AddMappingsRequest{
		IdentifierValues: identifiers,
		UserGroup:        commontypes.UserGroup_B2B_SALARY_PROGRAM,
		Agent: &usergrouppb.Agent{
			Email: req.GetHeader().GetAgentEmail(),
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in adding user group mappings", zap.Error(rpcErr))
		return &onboarding.WhitelistUsersForOnboardingResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}
	if req.GetEnableBiometricKyc().ToBool() {
		if err = s.enableBKYC(ctx, identifiers, req.GetHeader().GetAgentEmail()); err != nil {
			return &onboarding.WhitelistUsersForOnboardingResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}

	err = s.whitelistSalaryProgramB2BUsersInSalaryDb(ctx, req)
	if err != nil {
		logger.Error(ctx, "error whitelisting salary program b2b user in salary db", zap.Error(err))
		return &onboarding.WhitelistUsersForOnboardingResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	status := rpc.StatusOk()
	status.ShortMessage = getSuccessMessage(req, identifiers)
	logger.Info(ctx, fmt.Sprintf("success message %v", status.ShortMessage))
	s.resetStages(ctx, identifiers)
	return &onboarding.WhitelistUsersForOnboardingResponse{
		Status: status,
	}, nil
}

func (s *Service) enableBKYC(ctx context.Context, identifiers []*usergrouppb.IdentifierValue, agentEmail string) error {
	res, err := s.userGroupClient.AddMappings(ctx, &usergrouppb.AddMappingsRequest{
		IdentifierValues: identifiers,
		UserGroup:        commontypes.UserGroup_BKYC,
		Agent: &usergrouppb.Agent{
			Email:     agentEmail,
			Reason:    "Allow Biometric KYC via Whitelisting tool",
			UpdatedAt: timestampPb.New(time.Now()),
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in adding BKYC user group mappings", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (s *Service) getIdentifiers(ctx context.Context, req *onboarding.WhitelistUsersForOnboardingRequest) ([]*usergrouppb.IdentifierValue, error) {
	var (
		phoneNumbers = make([]string, 0)
		emails       = make([]string, 0)
		identifiers  = make([]*usergrouppb.IdentifierValue, 0)
	)
	switch req.GetValue().(type) {
	case *onboarding.WhitelistUsersForOnboardingRequest_Content:
		if len(req.GetContent()) == 0 {
			logger.Info(ctx, "empty file")
			return nil, rpc.StatusAsError(rpc.NewStatus(uint32(onboarding.WhitelistUsersForOnboardingResponse_INVALID_FILE_FORMAT), "Empty CSV file received", ""))
		}

		// check if file has phone numbers
		var rowsWithPhoneNumber []*rowWithPhoneNumber
		phErr := gocsv.Unmarshal(bytes.NewReader(req.GetContent()), &rowsWithPhoneNumber)
		if phErr == nil {
			for _, row := range rowsWithPhoneNumber {
				logger.Debug(ctx, fmt.Sprintf("phoneNumber are %v", row.PhoneNumber))
				if row.PhoneNumber != "" {
					phoneNumbers = append(phoneNumbers, row.PhoneNumber)
				}
			}
		}

		// check if file has emails
		var rowsWithEmail []*rowWithEmail
		emErr := gocsv.Unmarshal(bytes.NewReader(req.GetContent()), &rowsWithEmail)
		if emErr == nil {
			for _, row := range rowsWithEmail {
				logger.Debug(ctx, fmt.Sprintf("email are %v", row.Email))
				if row.Email != "" {
					emails = append(emails, row.Email)
				}
			}
		}
		logger.Debug(ctx, fmt.Sprintf("values are %v %v", phoneNumbers, emails))
		logger.Debug(ctx, fmt.Sprintf("values are %v %v", len(phoneNumbers), len(emails)))
		if phErr != nil && emErr != nil {
			logger.Error(ctx, "Error in parsing file", zap.Errors(logger.ERROR_LIST, []error{phErr, emErr}))
			return nil, rpc.StatusAsError(rpc.NewStatus(uint32(onboarding.WhitelistUsersForOnboardingResponse_INVALID_FILE_FORMAT), "Only csv format is allowed, please check the CSV header, it must be PhoneNumber or Email", ""))
		}
	case *onboarding.WhitelistUsersForOnboardingRequest_Emails:
		emails = strings.Split(req.GetEmails(), ",")
	case *onboarding.WhitelistUsersForOnboardingRequest_PhoneNumbers:
		phoneNumbers = strings.Split(req.GetPhoneNumbers(), ",")
	default:
		logger.Error(ctx, "unhandled type")
		return nil, fmt.Errorf("unhandled type")
	}

	logger.Debug(ctx, fmt.Sprintf("values are %v %v", phoneNumbers, emails))
	logger.Info(ctx, fmt.Sprintf("length of phone numbers %v", len(phoneNumbers)))
	logger.Info(ctx, fmt.Sprintf("length of emails %v", len(emails)))
	for _, phoneNo := range phoneNumbers {
		if len(phoneNo) > 10 {
			return nil, rpc.StatusAsError(rpc.NewStatus(uint32(onboarding.WhitelistUsersForOnboardingResponse_INVALID_PHONE_NUMBER), fmt.Sprintf("Length of phone number: %v must be 10", phoneNo), ""))
		}
		parsedPhoneNo, err := commontypes.ParsePhoneNumber(phoneNo)
		if err != nil {
			logger.Error(ctx, "invalid phone number format", zap.Error(err))
			return nil, rpc.StatusAsError(rpc.NewStatus(uint32(onboarding.WhitelistUsersForOnboardingResponse_INVALID_PHONE_NUMBER), fmt.Sprintf("Invalid Phone Number %v", phoneNo), ""))
		}
		identifier, err := getIdentifier(parsedPhoneNo)
		if err != nil {
			logger.Error(ctx, "error in getting phone identifier", zap.Error(err))
			return nil, fmt.Errorf("error in getting phone identifier %w", err)
		}
		identifiers = append(identifiers, identifier)
	}

	for _, email := range emails {
		if !s.emailRegexp.MatchString(email) {
			logger.Error(ctx, "invalid email id")
			status := rpc.StatusInvalidArgument()
			status.ShortMessage = fmt.Sprintf("Invalid email ID %v", email)
			return nil, nil
		}
		identifier, err := getIdentifier(email)
		if err != nil {
			logger.Error(ctx, "error in getting email identifier", zap.Error(err))
			return nil, fmt.Errorf("error in getting email identifier %w", err)
		}
		identifiers = append(identifiers, identifier)
	}

	return identifiers, nil
}

func (s *Service) whitelistSalaryProgramB2BUsersInSalaryDb(ctx context.Context, req *onboarding.WhitelistUsersForOnboardingRequest) error {
	whitelistedB2BUsers, err := s.getSalaryProgramWhitelistedB2BUsersFromReq(ctx, req)
	if err != nil {
		return err
	}
	if len(whitelistedB2BUsers) == 0 {
		logger.Debug(ctx, "no users to whitelist for salary program b2b")
		return nil
	}

	logger.Debug(ctx, "list of salary program B2B users to be whitelisted", zap.Any("whitelistedUsers", whitelistedB2BUsers))

	createWhitelistedB2BUsersRes, createErr := s.salaryClient.CreateWhitelistedB2BUsersInBulk(ctx, &salaryprogram.CreateWhitelistedB2BUsersInBulkRequest{
		WhitelistedB2BUsers: whitelistedB2BUsers,
	})
	if rpcErr := epifigrpc.RPCError(createWhitelistedB2BUsersRes, createErr); rpcErr != nil {
		logger.Error(ctx, "salaryClient.CreateWhitelistedB2BUsersInBulk rpc call failed", zap.Error(rpcErr))
		return rpc.StatusAsError(rpc.NewStatusWithoutDebug(uint32(code.Code_INTERNAL), "salaryClient.CreateWhitelistedB2BUsersInBulk rpc call failed"))
	}

	return nil
}

func (s *Service) getSalaryProgramWhitelistedB2BUsersFromReq(ctx context.Context, req *onboarding.WhitelistUsersForOnboardingRequest) ([]*salaryprogram.WhitelistedB2BUser, error) {
	switch req.GetValue().(type) {
	case *onboarding.WhitelistUsersForOnboardingRequest_Content:
		return s.getSalaryProgramWhitelistedB2BUsersFromCsv(ctx, req.GetContent())

	case *onboarding.WhitelistUsersForOnboardingRequest_Emails:
		// no supported for emails
		return nil, nil

	case *onboarding.WhitelistUsersForOnboardingRequest_PhoneNumbers:
		return s.getSalaryProgramWhitelistedB2BUsersFromPhoneNumbers(ctx, req)
	default:
		logger.Error(ctx, fmt.Sprintf("unhandled type: %T", req.GetValue()))
		return nil, fmt.Errorf("unhandled type: %T", req.GetValue())
	}
}

func (s *Service) getSalaryProgramWhitelistedB2BUsersFromPhoneNumbers(ctx context.Context, req *onboarding.WhitelistUsersForOnboardingRequest) ([]*salaryprogram.WhitelistedB2BUser, error) {
	phoneNumbers := strings.Split(req.GetPhoneNumbers(), ",")
	employerId := strings.TrimSpace(req.GetSalaryProgramB2BWhitelistingData().GetEmployerId())
	if employerId == "" {
		return nil, nil
	}

	if err := s.validateEmployerId(ctx, employerId); err != nil {
		return nil, err
	}

	var whitelistedB2BUsers []*salaryprogram.WhitelistedB2BUser
	for _, phoneNo := range phoneNumbers {
		phoneNo = strings.TrimSpace(phoneNo)
		if len(phoneNo) != 10 {
			return nil, rpc.StatusAsError(rpc.NewStatus(uint32(onboarding.WhitelistUsersForOnboardingResponse_INVALID_PHONE_NUMBER), fmt.Sprintf("Length of phone number: %v must be 10", phoneNo), ""))
		}
		phoneNo = strconv.Itoa(types.IndiaCountryCode) + phoneNo

		whitelistedB2BUsers = append(whitelistedB2BUsers, &salaryprogram.WhitelistedB2BUser{
			PhoneNumber: phoneNo,
			EmployerId:  employerId,
		})
	}

	return whitelistedB2BUsers, nil
}

func (s *Service) getSalaryProgramWhitelistedB2BUsersFromCsv(ctx context.Context, csvContent []byte) ([]*salaryprogram.WhitelistedB2BUser, error) {
	if len(csvContent) == 0 {
		logger.Info(ctx, "empty file received")
		return nil, rpc.StatusAsError(rpc.NewStatus(uint32(onboarding.WhitelistUsersForOnboardingResponse_INVALID_FILE_FORMAT), "Empty CSV file received", ""))
	}

	// check if file has phone numbers
	var rowsWithPhoneNumber []*rowWithPhoneNumber
	err := gocsv.Unmarshal(bytes.NewReader(csvContent), &rowsWithPhoneNumber)
	if err != nil {
		logger.Error(ctx, "Error in parsing file", zap.Error(err))
		return nil, rpc.StatusAsError(rpc.NewStatus(uint32(onboarding.WhitelistUsersForOnboardingResponse_INVALID_FILE_FORMAT), "Only csv format is allowed, please check the CSV header", ""))
	}

	logger.Debug(ctx, "whitelisted b2b user csv rows", zap.Any("rows", rowsWithPhoneNumber))

	var whitelistedB2BUsers []*salaryprogram.WhitelistedB2BUser
	employerId := ""
	for _, row := range rowsWithPhoneNumber {
		whitelistedB2BUser, getErr := s.getSalaryProgramWhitelistedB2BUserFromCsvRowWithPhoneNumber(row)
		if getErr != nil {
			return nil, getErr
		}

		// validate employer id, same employer id should be present in the all rows
		if employerId == "" {
			employerId = whitelistedB2BUser.GetEmployerId()
		} else if employerId != whitelistedB2BUser.GetEmployerId() {
			return nil, rpc.StatusAsError(rpc.NewStatusWithoutDebug(uint32(code.Code_INVALID_ARGUMENT), "only users with same employer can be whitelisted at a time, multiple distinct employer ids are passed in the input"))
		}

		whitelistedB2BUsers = append(whitelistedB2BUsers, whitelistedB2BUser)
	}

	if err = s.validateEmployerId(ctx, employerId); err != nil {
		return nil, err
	}

	return whitelistedB2BUsers, nil
}

func (s *Service) getSalaryProgramWhitelistedB2BUserFromCsvRowWithPhoneNumber(row *rowWithPhoneNumber) (*salaryprogram.WhitelistedB2BUser, error) {
	var (
		phoneNo          = strings.TrimSpace(row.PhoneNumber)
		empId            = strings.TrimSpace(row.EmployerId)
		tartanUserId     = strings.TrimSpace(row.TartanUserId)
		tartanEmployerId = strings.TrimSpace(row.TartanEmployerId)
		hrmsMgmtVendor   = commonvgpb.Vendor_VENDOR_UNSPECIFIED
	)
	// validate phone number
	if len(phoneNo) != 10 {
		return nil, rpc.StatusAsError(rpc.NewStatus(uint32(onboarding.WhitelistUsersForOnboardingResponse_INVALID_PHONE_NUMBER), fmt.Sprintf("Length of phone number: %v must be 10", phoneNo), ""))
	}
	phoneNo = strconv.Itoa(types.IndiaCountryCode) + phoneNo

	if empId == "" {
		return nil, rpc.StatusAsError(rpc.NewStatusWithoutDebug(uint32(code.Code_INVALID_ARGUMENT), "EmployerId cannot be empty"))
	}

	if (tartanUserId == "") != (tartanEmployerId == "") {
		return nil, rpc.StatusAsError(rpc.NewStatusWithoutDebug(uint32(code.Code_INVALID_ARGUMENT), "TartanUserId and TartanEmployerId should be present together or should be empty together"))
	}
	if tartanUserId != "" && tartanEmployerId != "" {
		hrmsMgmtVendor = commonvgpb.Vendor_TARTAN
	}

	return &salaryprogram.WhitelistedB2BUser{
		PhoneNumber:          phoneNo,
		EmployerId:           empId,
		HrmsManagementVendor: hrmsMgmtVendor,
		VendorEmployerId:     tartanEmployerId,
		VendorUserId:         tartanUserId,
	}, nil
}

func (s *Service) validateEmployerId(ctx context.Context, employerId string) error {
	empRes, getErr := s.employmentClient.GetEmployer(ctx, &employmentPb.GetEmployerRequest{
		Identifier: &employmentPb.GetEmployerRequest_EmployerId{
			EmployerId: employerId,
		},
	})
	if rpcErr := epifigrpc.RPCError(empRes, getErr); rpcErr != nil {
		logger.Error(ctx, "employmentClient.GetEmployer rpc call failed", zap.String(logger.EMPLOYER_ID, employerId), zap.Error(rpcErr))
		if empRes.GetStatus().IsRecordNotFound() {
			return rpc.StatusAsError(rpc.NewStatusWithoutDebug(uint32(code.Code_INVALID_ARGUMENT), fmt.Sprintf("employer not present for the passed employerId: %s", employerId)))
		}
		return rpc.StatusAsError(rpc.NewStatusWithoutDebug(uint32(code.Code_INTERNAL), "employmentClient.GetEmployer rpc call failed"))
	}

	return nil
}

func getIdentifier(i interface{}) (*usergrouppb.IdentifierValue, error) {
	switch typeVar := i.(type) {
	case *commontypes.PhoneNumber:
		typeVar.CountryCode = 91
		return &usergrouppb.IdentifierValue{
			Identifier: &usergrouppb.IdentifierValue_PhoneNumber{
				PhoneNumber: typeVar,
			},
		}, nil
	case string:
		return &usergrouppb.IdentifierValue{
			Identifier: &usergrouppb.IdentifierValue_Email{
				Email: typeVar,
			},
		}, nil
	default:
		return nil, fmt.Errorf("type not handled")
	}
}

func getSuccessMessage(req *onboarding.WhitelistUsersForOnboardingRequest, identifiers []*usergrouppb.IdentifierValue) string {
	switch req.GetValue().(type) {
	case *onboarding.WhitelistUsersForOnboardingRequest_Content:
		switch identifiers[0].GetIdentifier().(type) {
		case *usergrouppb.IdentifierValue_PhoneNumber:
			return fmt.Sprintf("CSV uploaded succesfully.%v phone numbers have been whitelisted successfully", len(identifiers))
		case *usergrouppb.IdentifierValue_Email:
			return fmt.Sprintf("CSV uploaded succesfully.%v emails have been whitelisted successfully", len(identifiers))
		default:
			return "Success"
		}
	case *onboarding.WhitelistUsersForOnboardingRequest_PhoneNumbers:
		return fmt.Sprintf("%v phone numbers have been whitelisted successfully", len(identifiers))
	case *onboarding.WhitelistUsersForOnboardingRequest_Emails:
		return fmt.Sprintf("%v emails have been whitelisted successfully", len(identifiers))
	default:
		return "Success"
	}
}

func (s *Service) resetStages(ctx context.Context, values []*usergrouppb.IdentifierValue) {
	for _, value := range values {
		// using localValue to avoid value change in the middle of goroutine execution
		localValue := value
		goroutine.Run(ctx, 120*time.Second, func(ctx context.Context) {
			if err := s.resetStagesForB2BSalaryProgramUser(ctx, localValue); err != nil {
				if !storageV2.IsRecordNotFoundError(err) {
					logger.Error(ctx, "error in reset", zap.Error(err))
				}
			}
		})
	}
}

// resetStagesForB2BSalaryProgramUser resets some stages in onboarding where we skip some checks using salary program whitelisting
// This method is used to reset these stages so that agents can whitelist users even if the user has already started onboarding
func (s *Service) resetStagesForB2BSalaryProgramUser(ctx context.Context, value *usergrouppb.IdentifierValue) error {
	actorId, err := s.getActorIdFromIdentifier(ctx, value)
	if err != nil {
		return err
	}
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	onbDetails, err := s.getOnbDetails(ctx, actorId)
	if err != nil {
		return err
	}

	// Currently, we use salary program whitelisting at credit report verification, risk screening and add funds.
	// More stages may be added later as required
	stagesToReset := []onbPb.OnboardingStage{
		onbPb.OnboardingStage_CREDIT_REPORT_VERIFICATION,
		onbPb.OnboardingStage_RISK_SCREENING,
		onbPb.OnboardingStage_ADD_MONEY,
	}
	stagesReset := make([]onbPb.OnboardingStage, 0)
	for _, stage := range stagesToReset {
		if stageInfo, ok := onbDetails.GetStageDetails().GetStageMapping()[stage.String()]; ok && !skipStageReset(stage, stageInfo.GetState()) {
			if err = s.updateOnboardingStage(ctx, actorId, stage); err == nil {
				logger.Info(ctx, fmt.Sprintf("reset %v success", stage))
				stagesReset = append(stagesReset, stage)
			}
		}
	}

	// assert states after sync
	s.assertStates(ctx, actorId, stagesReset)
	return nil
}

func (s *Service) assertStates(ctx context.Context, actorId string, stagesReset []onbPb.OnboardingStage) {
	onbDetails, err := s.getOnbDetails(ctx, actorId)
	if err != nil {
		return
	}
	for _, stage := range stagesReset {
		if err = assertStageState(stage, onbDetails); err != nil {
			logger.Error(ctx, fmt.Sprintf("error in %v assert state", stage.String()), zap.Error(err))
		}
	}
}

func assertStageState(stage onbPb.OnboardingStage, onbDetails *onbPb.OnboardingDetails) error {
	switch stage {
	case onbPb.OnboardingStage_CREDIT_REPORT_VERIFICATION:
		if onbDetails.GetStageMetadata().GetAppScreeningData().GetSkipReason() == onbPb.ScreeningSkipReason_B2B_SALARY_PROGRAM_USER {
			break
		}
		return fmt.Errorf("invalid credit report skip reason %v", onbDetails.GetStageMetadata().GetAppScreeningData().GetSkipReason().String())
	}
	state := onbDetails.GetStageDetails().GetStageMapping()[stage.String()].GetState()
	if !state.IsSuccessOrSkipped() {
		return fmt.Errorf("invalid state %v for %v", state.String(), stage.String())
	}
	return nil
}

func (s *Service) updateOnboardingStage(ctx context.Context, actorId string, stage onbPb.OnboardingStage) error {
	res, err := s.onbClient.UpdateStage(ctx, &onbPb.UpdateStageRequest{
		Stage:    stage,
		ActorId:  actorId,
		NewState: onbPb.OnboardingState_RESET,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		if !res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in updating onboarding details", zap.Error(rpcErr))
		}
		return rpcErr
	}
	return nil
}

func (s *Service) getActorIdFromIdentifier(ctx context.Context, value *usergrouppb.IdentifierValue) (string, error) {
	switch value.GetIdentifier().(type) {
	case *usergrouppb.IdentifierValue_PhoneNumber:
		actor, err := s.getActorFromPhone(ctx, value.GetPhoneNumber())
		if err != nil {
			return "", err
		}
		return actor.GetId(), nil
	case *usergrouppb.IdentifierValue_Email:
		actor, err := s.getActorFromEmail(ctx, value.GetEmail())
		if err != nil {
			return "", err
		}
		return actor.GetId(), nil
	default:
		return "", fmt.Errorf("invalid type")
	}
}

func (s *Service) getActorFromPhone(ctx context.Context, phoneNumber *commontypes.PhoneNumber) (*types.Actor, error) {
	res, err := s.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_PhoneNumber{
			PhoneNumber: phoneNumber,
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		if !res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in getting user by phone number", zap.Error(rpcErr))
		}
		return nil, rpcErr
	}
	return s.getActorByUserId(ctx, res.GetUser().GetId())
}

func (s *Service) getActorFromEmail(ctx context.Context, email string) (*types.Actor, error) {
	res, err := s.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_EmailId{
			EmailId: email,
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		if !res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in getting user by phone number", zap.Error(rpcErr))
		}
		return nil, rpcErr
	}
	return s.getActorByUserId(ctx, res.GetUser().GetId())
}

func (s *Service) getActorByUserId(ctx context.Context, userId string) (*types.Actor, error) {
	actorRes, err := s.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		EntityId: userId,
		Type:     types.Actor_USER,
	})
	if rpcErr := epifigrpc.RPCError(actorRes, err); rpcErr != nil {
		if !actorRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in getting actor by entity ID", zap.Error(rpcErr))
		}
		return nil, rpcErr
	}
	return actorRes.GetActor(), nil
}

func (s *Service) getOnbDetails(ctx context.Context, actorId string) (*onbPb.OnboardingDetails, error) {
	res, err := s.onbClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId: actorId,
		Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		if !res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in getting onboarding details", zap.Error(rpcErr))
		}
		return nil, rpcErr
	}
	return res.GetDetails(), nil
}

func skipStageReset(stage onbPb.OnboardingStage, state onbPb.OnboardingState) bool {
	switch stage {
	case onbPb.OnboardingStage_CREDIT_REPORT_VERIFICATION:
		// not skipping reset for onboarding state SKIPPED for credit report verification,as app screening may not be successful for the user
		return state.IsSuccess()
	default:
		return state.IsSuccessOrSkipped()
	}
}

func logIdentifiers(ctx context.Context, identifiers []*usergrouppb.IdentifierValue) {
	for _, identifier := range identifiers {
		switch identifier.GetIdentifier().(type) {
		case *usergrouppb.IdentifierValue_PhoneNumber:
			logger.Info(ctx, fmt.Sprintf("Obfuscated phone number %v", getMaskedNumber(identifier.GetPhoneNumber().GetNationalNumber())))
		case *usergrouppb.IdentifierValue_Email:
			logger.Info(ctx, fmt.Sprintf("Obfuscated email %v", getMaskedEmail(identifier.GetEmail())))
		}
	}
}

func getMaskedNumber(phone uint64) string {
	phoneStr := fmt.Sprintf("%v", phone)
	return strings.Join([]string{strings.Repeat("x", 6), phoneStr[6:]}, "")
}

func getMaskedEmail(email string) string {
	parts := strings.Split(email, "@")
	firstPart := parts[0]
	masked := firstPart[0:1] + strings.Repeat("x", len(firstPart)-2) + firstPart[len(firstPart)-1:]
	return strings.Join([]string{masked, "@", parts[1]}, "")
}
