package alfred

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/accounts/operstatus"
	alfredPb "github.com/epifi/gamma/api/alfred"
	cxAlfredPb "github.com/epifi/gamma/api/cx/data_collector/alfred"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/data_collector/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type Service struct {
	alfredClient            alfredPb.AlfredClient
	savingsClient           savingsPb.SavingsClient
	authEngine              auth_engine.IAuthEngine
	operationalStatusClient operstatus.OperationalStatusServiceClient
}

func NewService(alfredClient alfredPb.AlfredClient, authEngine auth_engine.IAuthEngine, savingsClient savingsPb.SavingsClient,
	operationalStatusClient operstatus.OperationalStatusServiceClient) *Service {
	return &Service{
		alfredClient:            alfredClient,
		authEngine:              authEngine,
		savingsClient:           savingsClient,
		operationalStatusClient: operationalStatusClient,
	}
}

func (s *Service) GetRequestDetails(ctx context.Context, req *cxAlfredPb.GetRequestDetailsRequest) (*cxAlfredPb.GetRequestDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxAlfredPb.GetRequestDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	if req.GetPageContext().GetPageSize() == 0 {
		// we will be showing only 2 request in one page
		req.PageContext.PageSize = 2
	}

	savingsResp, err := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     req.GetHeader().GetActor().GetId(),
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})

	if rpcErr := epifigrpc.RPCError(savingsResp, err); rpcErr != nil {
		cxLogger.Error(ctx, "error while fetching request details", zap.Error(rpcErr))
	}

	rpcCtxWithTimeOut, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	opResp, opErr := s.operationalStatusClient.GetOperationalStatus(rpcCtxWithTimeOut, &operstatus.GetOperationalStatusRequest{
		DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
		AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: savingsResp.GetAccount().GetId(),
		},
	})
	if opErr = epifigrpc.RPCError(opResp, opErr); opErr != nil {
		cxLogger.Error(rpcCtxWithTimeOut, "cannot fetch sign status from bank", zap.Error(opErr))
	}

	resp, err := s.alfredClient.GetAllRequestStatusDetails(ctx, &alfredPb.GetAllRequestStatusDetailsRequest{
		Filters: &alfredPb.Filters{
			ActorId:      req.GetHeader().GetActor().GetId(),
			RequestTypes: req.GetRequestTypes(),
			StatusList: []alfredPb.Status{
				alfredPb.Status_STATUS_IN_PROGRESS,
				alfredPb.Status_STATUS_SUCCESS,
				alfredPb.Status_STATUS_FAILED,
				alfredPb.Status_STATUS_STUCK,
				alfredPb.Status_STATUS_CREATED,
			},
		},
		PageContext: helper.ConvertToRpcPageContext(req.GetPageContext()),
		SortOrder:   alfredPb.SortOrder_SORT_ORDER_DESC,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		if rpcPb.StatusFromError(rpcErr).IsRecordNotFound() {
			return &cxAlfredPb.GetRequestDetailsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching request details", zap.Error(rpcErr))
	}
	cxLogger.Info(ctx, "received service requests details from be", zap.Any(logger.PAGE_CONTEXT, resp.GetPageContext()))
	return s.buildRequestDetailsResponse(ctx, resp, opResp.GetOperationalStatusInfo().GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetSignCount())
}

func (s *Service) buildRequestDetailsResponse(ctx context.Context, resp *alfredPb.GetAllRequestStatusDetailsResponse, signCount int32) (*cxAlfredPb.GetRequestDetailsResponse, error) {
	var signStatus string
	if signCount == 0 {
		signStatus = "Not present at Bank"
	} else {
		signStatus = "Present at Bank"
	}
	cxResp := &cxAlfredPb.GetRequestDetailsResponse{
		Status:      resp.GetStatus(),
		PageContext: helper.ConvertToCxPageContext(resp.GetPageContext()),
		SignStatus:  signStatus,
	}

	// populating request details in cxResp
	for _, res := range resp.GetServiceRequestList() {
		switch res.GetDetails().GetMetadata().(type) {
		case *alfredPb.Details_ChequebookMetadata:
			cxResp.ServiceRequestList = append(cxResp.GetServiceRequestList(), &cxAlfredPb.ServiceRequest{
				RequestDetails: &cxAlfredPb.ServiceRequest_ChequebookDetails{
					ChequebookDetails: &cxAlfredPb.ChequebookDetails{
						OrderedAt:              getDateInString(res.GetDetails().GetChequebookMetadata().GetOrderedAt()),
						CourierServiceProvider: getCourierServiceProvider(res.GetDetails().GetChequebookMetadata().GetCourieredVia()),
						TrackingId:             getCourierServiceProvider(res.GetDetails().GetChequebookMetadata().GetTrackingId()),
						Charges:                getPriceInString(res.GetDetails().GetChequebookMetadata().GetCharges()),
						ChequeLeavesCount:      10,
						Status:                 res.GetStatus().String(),
						TrackingUrl:            res.GetDetails().GetChequebookMetadata().GetTrackingUrl(),
					},
				},
			})
		default:
			cxLogger.Info(ctx, "unexpected service request type")
		}
	}
	return cxResp, nil
}

func getDateInString(time *timestamp.Timestamp) string {
	if time == nil {
		return "Yet to place a request"
	}
	return datetime.DateToString(datetime.TimestampToDateInLoc(time, datetime.IST), "02 Jan", datetime.IST)
}

func getCourierServiceProvider(word string) string {
	if word == "" {
		return "Yet to be shared by bank."
	}
	return word
}

func getPriceInString(charges *types.Money) string {
	if charges == nil {
		return "Yet to place a request"
	}
	return fmt.Sprintf("%v %v", charges.GetUnits(), charges.GetCurrencyCode())
}
