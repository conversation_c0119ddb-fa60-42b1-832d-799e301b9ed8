package helper

import beTieringExtPb "github.com/epifi/gamma/api/tiering/external"

var (
	regularDisplayName       = "REGULAR"
	basicDisplayName         = "STANDARD"
	plusDisplayName          = "PLUS"
	infiniteDisplayName      = "INFINITE"
	salaryLiteDisplayName    = "SALARY LITE"
	aaSalaryDisplayName      = "AA SALARY"
	salaryDisplayName        = "SALARY"
	aaSalaryBand1DisplayName = "Prime 1%"
	aaSalaryBand2DisplayName = "Prime 2%"
	aaSalaryBand3DisplayName = "Prime 3%"
	salaryBasicDisplayName   = "SALARY BASIC"
)

// ConvertTierNameToDisplayName converts tier name to display string to show in sherlock
func ConvertTierNameToDisplayName(tier beTieringExtPb.Tier) string {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return regularDisplayName
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return basicDisplayName
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return plusDisplayName
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return infiniteDisplayName
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return salaryLiteDisplayName
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return salaryDisplayName
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1:
		return aaSalaryBand1DisplayName
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2:
		return aaSalaryBand2DisplayName
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return aaSalaryBand3DisplayName
	case beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return salaryBasicDisplayName
	default:
		if tier.IsAaSalaryTier() {
			return aaSalaryDisplayName
		}
		return tier.String()
	}
}
