package tiering

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cxTieringPb "github.com/epifi/gamma/api/cx/data_collector/tiering"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	cxTieringHelper "github.com/epifi/gamma/cx/data_collector/tiering/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/frontend/tiering/helper"
)

var (
	currentPlanDisplayText = "CURRENT PLAN"

	criteriaOptionTypeInternalToExternalMap = map[enums.CriteriaOptionType]string{
		enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED: "Unspecifiied Crtieria",
		enums.CriteriaOptionType_BALANCE_AND_KYC:                  "Balance Criteria Satisfied",
		enums.CriteriaOptionType_BALANCE_V2_AND_KYC:               "Balance V2 Criteria Satisfied",
		enums.CriteriaOptionType_SALARY_AND_KYC:                   "Salary and KYC Criteria Satisfied",
		enums.CriteriaOptionType_US_STOCKS_SIP_AND_KYC:            "US Stocks SIP and KYC Criteria Satisfied",
		enums.CriteriaOptionType_DEPOSITS_AND_KYC:                 "Deposits and KYC Criteria Satisfied",
		enums.CriteriaOptionType_BASE_TIER:                        "Base Tier Criteria Satisfied",
		enums.CriteriaOptionType_AA_SALARY_AND_KYC:                "AA Salary and KYC Criteria Satisfied",
	}
)

type Service struct {
	cxTieringPb.UnimplementedTieringServer
	tieringClient beTieringPb.TieringClient
	authEngine    auth_engine.IAuthEngine
}

func NewService(
	tieringClient beTieringPb.TieringClient,
	authEngine auth_engine.IAuthEngine,
) *Service {
	return &Service{
		tieringClient: tieringClient,
		authEngine:    authEngine,
	}
}

func (s *Service) GetTieringDetails(ctx context.Context, req *cxTieringPb.GetTieringDetailsRequest) (*cxTieringPb.GetTieringDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxTieringPb.GetTieringDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Error(ctx, "actor information not present in header")
		return &cxTieringPb.GetTieringDetailsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("actor information not present in header"),
		}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	tieringDetailsResp, err := s.tieringClient.GetDetailsForCx(ctx, &beTieringPb.GetDetailsForCxRequest{
		ActorId: actorId,
	})
	if getTieringDetailsErr := epifigrpc.RPCError(tieringDetailsResp, err); getTieringDetailsErr != nil {
		// if tiering is disabled gracefully ignore error and return OK status
		if tieringDetailsResp.GetStatus().GetCode() == uint32(beTieringPb.GetDetailsForCxResponse_DISABLED) {
			cxLogger.Info(ctx, "tiering is disabled for actor", zap.Any(logger.ACTOR_ID_V2, actorId))
			return &cxTieringPb.GetTieringDetailsResponse{
				Status: rpcPb.StatusOkWithDebugMsg("tiering is disabled for actor"),
			}, nil
		}
		cxLogger.Error(ctx, "error fetching tiering details", zap.Error(getTieringDetailsErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &cxTieringPb.GetTieringDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error fetching tiering details"),
		}, nil
	}
	mntHistories, conversionErr := convertToCxMovementHistories(tieringDetailsResp.GetMovementHistories())
	if conversionErr != nil {
		cxLogger.Error(ctx, "error converting to cx movement histories", zap.Error(conversionErr),
			zap.Any(logger.ACTOR_ID_V2, actorId))
		return &cxTieringPb.GetTieringDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error converting to cx movement histories"),
		}, nil
	}
	tierMovementCriterias := convertToCxTierMovementCriterias(ctx, tieringDetailsResp.GetTierMovementCriterias())

	// Convert AMB history if present
	var ambHistory []*cxTieringPb.AmbDetails
	for _, ambDetail := range tieringDetailsResp.GetAmbHistory() {
		ambHistory = append(ambHistory, &cxTieringPb.AmbDetails{
			Dates:         ambDetail.GetDates(),
			Plan:          ambDetail.GetPlan(),
			AmbMaintained: ambDetail.GetAmbMaintained(),
		})
	}

	return &cxTieringPb.GetTieringDetailsResponse{
		Status:                rpcPb.StatusOk(),
		TierPlanName:          cxTieringHelper.ConvertTierNameToDisplayName(tieringDetailsResp.GetCurrentTier()),
		IsUserInGrace:         helper.ConvertBoolToBooleanEnum(tieringDetailsResp.GetIsUserInGrace()),
		GracePeriodTill:       tieringDetailsResp.GetGracePeriodTill(),
		IsUserInCoolOff:       helper.ConvertBoolToBooleanEnum(tieringDetailsResp.GetIsUserInCoolOff()),
		CoolOffPeriodTill:     tieringDetailsResp.GetCoolOffPeriodTill(),
		MovementHistories:     mntHistories,
		IsARewardsAbuserUser:  helper.ConvertBoolToBooleanEnum(tieringDetailsResp.GetIsARewardsAbuserUser()),
		TierMovementCriterias: tierMovementCriterias,
		CurrentAmb:            tieringDetailsResp.GetCurrentAmb(),
		RequiredAmb:           tieringDetailsResp.GetRequiredAmb(),
		Shortfall:             tieringDetailsResp.GetShortfall(),
		AmbHistory:            ambHistory,
	}, nil
}

// convertToCxMovementHistories converts tiering external TMH to cx TMH
func convertToCxMovementHistories(movementHistoires []*tieringExtPb.TierMovementHistory) ([]*cxTieringPb.TierMovementHistory, error) {
	if len(movementHistoires) == 0 {
		return nil, nil
	}
	var extMovementHistories []*cxTieringPb.TierMovementHistory
	var (
		planStartTime, planEndTime string
		conversionErr              error
	)
	// insert latest movement
	planStartTime, conversionErr = cxTieringHelper.ConvertTimestampToISOString(movementHistoires[0].GetCreatedAt())
	if conversionErr != nil {
		return nil, errors.Wrap(conversionErr, "error converting timestamp to ISO string")
	}
	extMovementHistories = append(extMovementHistories, &cxTieringPb.TierMovementHistory{
		ToTierPlanName:   cxTieringHelper.ConvertTierNameToDisplayName(movementHistoires[0].GetToTier()),
		PlanStart:        planStartTime,
		PlanEnd:          currentPlanDisplayText,
		Id:               movementHistoires[0].GetId(),
		FromTierPlanName: cxTieringHelper.ConvertTierNameToDisplayName(movementHistoires[0].GetFromTier()),
		MovementType:     movementHistoires[0].GetMovementType(),
	})
	// insert rest of the movements
	for i := 1; i < len(movementHistoires); i++ {
		movement := movementHistoires[i]
		lastMovement := movementHistoires[i-1]
		planStartTime, conversionErr = cxTieringHelper.ConvertTimestampToISOString(movement.GetCreatedAt())
		if conversionErr != nil {
			return nil, errors.Wrap(conversionErr, "error converting plan start time in Rest of movements timestamp to ISO string")
		}
		planEndTime, conversionErr = cxTieringHelper.ConvertTimestampToISOString(lastMovement.GetCreatedAt())
		if conversionErr != nil {
			return nil, errors.Wrap(conversionErr, "error converting plan end time in Rest of movements timestamp to ISO string")
		}
		extMovementHistories = append(extMovementHistories, &cxTieringPb.TierMovementHistory{
			ToTierPlanName:   cxTieringHelper.ConvertTierNameToDisplayName(movement.GetToTier()),
			PlanStart:        planStartTime,
			PlanEnd:          planEndTime,
			Id:               movement.GetId(),
			FromTierPlanName: cxTieringHelper.ConvertTierNameToDisplayName(movement.GetFromTier()),
			MovementType:     movement.GetMovementType(),
		})
	}
	return extMovementHistories, nil
}

func convertToCxTierMovementCriterias(ctx context.Context, tierMovementCriteria *tieringExtPb.TierMovementCriterias) *cxTieringPb.TierMovementCriterias {
	if len(tierMovementCriteria.GetCurrentCriteriaOptionTypes()) == 0 {
		return nil
	}
	tierMovementCriterias := &cxTieringPb.TierMovementCriterias{}

	if entryCriteria, ok := criteriaOptionTypeInternalToExternalMap[tierMovementCriteria.GetEntryCriteriaOptionType()]; ok {
		tierMovementCriterias.EntryCriteria = entryCriteria
	} else {
		cxLogger.Info(ctx, "criteria option type not found in map", zap.String("criteria_option_type", tierMovementCriteria.GetEntryCriteriaOptionType().String()))
	}

	for _, tmc := range tierMovementCriteria.GetCurrentCriteriaOptionTypes() {
		if currentCriteria, ok := criteriaOptionTypeInternalToExternalMap[tmc]; ok {
			tierMovementCriterias.CurrentCriterias = append(tierMovementCriterias.CurrentCriterias, currentCriteria)
		} else {
			cxLogger.Info(ctx, "criteria option type not found in map", zap.String("criteria_option_type", tmc.String()))
		}
	}
	return tierMovementCriterias
}

// nolint:dupl
func (s *Service) OverrideGracePeriod(ctx context.Context, req *cxTieringPb.OverrideGracePeriodRequest) (*cxTieringPb.OverrideGracePeriodResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxTieringPb.OverrideGracePeriodResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Error(ctx, "actor information not present in header")
		return &cxTieringPb.OverrideGracePeriodResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("actor information not present in header"),
		}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	overrideGraceResp, err := s.tieringClient.OverrideGracePeriod(ctx, &beTieringPb.OverrideGracePeriodRequest{
		ActorId:           actorId,
		OverrideTimestamp: req.GetOverrideTimestamp(),
	})
	if overrideGraceErr := epifigrpc.RPCError(overrideGraceResp, err); overrideGraceErr != nil {
		cxLogger.Error(ctx, "error in overriding grace period", zap.Error(overrideGraceErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &cxTieringPb.OverrideGracePeriodResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in overriding grace period"),
		}, nil
	}
	return &cxTieringPb.OverrideGracePeriodResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// nolint:dupl
func (s *Service) OverrideCoolOffPeriod(ctx context.Context, req *cxTieringPb.OverrideCoolOffPeriodRequest) (*cxTieringPb.OverrideCoolOffPeriodResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxTieringPb.OverrideCoolOffPeriodResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetHeader().GetActor() == nil {
		cxLogger.Error(ctx, "actor information not present in header")
		return &cxTieringPb.OverrideCoolOffPeriodResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("actor information not present in header"),
		}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()
	overrideCoolOffResp, err := s.tieringClient.OverrideCoolOffPeriod(ctx, &beTieringPb.OverrideCoolOffPeriodRequest{
		ActorId:           actorId,
		OverrideTimestamp: timestampPb.Now(),
	})
	if overrideCoolOffErr := epifigrpc.RPCError(overrideCoolOffResp, err); overrideCoolOffErr != nil {
		cxLogger.Error(ctx, "error in overriding cool off period", zap.Error(overrideCoolOffErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &cxTieringPb.OverrideCoolOffPeriodResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in overriding cool off period"),
		}, nil
	}
	return &cxTieringPb.OverrideCoolOffPeriodResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) GetTieringSensitiveDetails(ctx context.Context, req *cxTieringPb.GetTieringSensitiveDetailsRequest) (*cxTieringPb.GetTieringSensitiveDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxTieringPb.GetTieringSensitiveDetailsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	if req.GetHeader().GetActor() == nil {
		cxLogger.Error(ctx, "actor information not present in header")
		return &cxTieringPb.GetTieringSensitiveDetailsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("actor information not present in header"),
		}, nil
	}
	actorId := req.GetHeader().GetActor().GetId()

	tieringDetailsResp, err := s.tieringClient.GetDetailsForCx(ctx, &beTieringPb.GetDetailsForCxRequest{
		ActorId: actorId,
	})
	if getTieringDetailsErr := epifigrpc.RPCError(tieringDetailsResp, err); getTieringDetailsErr != nil {
		cxLogger.Error(ctx, "error fetching tiering details", zap.Error(getTieringDetailsErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &cxTieringPb.GetTieringSensitiveDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error fetching tiering details"),
		}, nil
	}
	mntHistories, conversionErr := convertToCxMovementHistories(tieringDetailsResp.GetMovementHistories())
	if conversionErr != nil {
		cxLogger.Error(ctx, "error converting to cx movement histories", zap.Error(conversionErr),
			zap.Any(logger.ACTOR_ID_V2, actorId))
		return &cxTieringPb.GetTieringSensitiveDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error converting to cx movement histories"),
		}, nil
	}

	return &cxTieringPb.GetTieringSensitiveDetailsResponse{
		Status:            rpcPb.StatusOk(),
		TierPlanName:      cxTieringHelper.ConvertTierNameToDisplayName(tieringDetailsResp.GetCurrentTier()),
		MovementHistories: mntHistories,
	}, nil
}

func (s *Service) IsUserEligibleForRewards(ctx context.Context, req *cxTieringPb.IsUserEligibleForRewardsRequest) (*cxTieringPb.IsUserEligibleForRewardsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &cxTieringPb.IsUserEligibleForRewardsResponse{
			Status:           rpcPb.StatusOk(),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}

	if req.GetHeader().GetActor() == nil {
		cxLogger.Error(ctx, "actor information not present in header")
		return &cxTieringPb.IsUserEligibleForRewardsResponse{
			Status: rpcPb.StatusRecordNotFoundWithDebugMsg("actor information not present in header"),
		}, nil
	}

	actorId := req.GetHeader().GetActor().GetId()
	eligibilityResp, err := s.tieringClient.IsUserEligibleForRewards(ctx, &beTieringPb.IsUserEligibleForRewardsRequest{
		ActorId: actorId,
		Date:    req.GetDate(),
	})
	if rpcErr := epifigrpc.RPCError(eligibilityResp, err); rpcErr != nil {
		logger.Error(ctx, "error in IsUserEligibleForRewards", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return &cxTieringPb.IsUserEligibleForRewardsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in checking user eligibility for rewards"),
		}, nil
	}

	var tierToEligibilityStatus []*cxTieringPb.IsUserEligibleForRewardsResponse_TierToEligibilityStatus
	for _, tierEligibility := range eligibilityResp.GetTierToEligibilityStatus() {
		tierToEligibilityStatus = append(tierToEligibilityStatus, &cxTieringPb.IsUserEligibleForRewardsResponse_TierToEligibilityStatus{
			TierName:   cxTieringHelper.ConvertTierNameToDisplayName(tierEligibility.GetTierName()),
			IsEligible: helper.ConvertBoolToBooleanEnum(tierEligibility.GetIsEligible()),
		})
	}

	return &cxTieringPb.IsUserEligibleForRewardsResponse{
		Status:                  rpcPb.StatusOk(),
		TierToEligibilityStatus: tierToEligibilityStatus,
	}, nil
}
