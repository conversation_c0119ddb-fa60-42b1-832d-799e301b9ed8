package trigger_processor

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	eventsPb "github.com/epifi/gamma/api/cx/error_activity"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	mocks2 "github.com/epifi/gamma/api/cx/ticket/mocks"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/cx/watson/mocks"
	genConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/error_activity/entity"
	mock_dao "github.com/epifi/gamma/cx/test/mocks/watson/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type CreateIncidentTriggerTestSuite struct {
	genConf *genConf.Config
}

var (
	createIncidentTriggerTS	CreateIncidentTriggerTestSuite
	actorId			= "ACImfwVAMLQz+DstlcwlBf/A230917=="
	issueCategoryId		= "bae5dc35-0d9e-4b80-b043-c47c5f251c94"
	clientReqId		= "123e4567-e89b-12d3-a456-426614174000"
	incident2		= &entity.IncidentDetails{
		ActorId:		actorId,
		EventTrigger:		eventsPb.EventTrigger_EVENT_TRIGGER_CREATE_WATSON_INCIDENT,
		IssueCategoryId:	issueCategoryId,
		ClientRequestId:	clientReqId,
		ReceivedAt:		timestampPb.New(time.Unix(100000000, 0)),
	}
)

func TestCreateIncidentTriggerProcessor_ProcessTrigger(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockWatsonClient := mocks.NewMockWatsonClient(ctr)
	mockIncidentDao := mock_dao.NewMockIIncidentDao(ctr)
	mockTicketClient := mocks2.NewMockTicketClient(ctr)
	type args struct {
		incident *entity.IncidentDetails
	}
	tests := []struct {
		name	string
		args	args
		mocks	[]interface{}
		wantErr	error
	}{
		{
			name:	"error while performing incident cool-off check",
			args: args{
				incident: incident2,
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
			},
			wantErr: epifigrpc.RPCError(&watsonPb.GetIncidentsForClientResponse{
				Status: rpcPb.StatusInternal()}, nil),
		},
		{
			name:	"no ticket for incident and cool-off not yet elapsed",
			args: args{
				incident: incident2,
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).Return(&watsonPb.GetIncidentsForClientResponse{
					Status:		rpcPb.StatusOk(),
					Incidents:	[]*watsonPb.IncidentDetailsForClient{{IdentifiedAt: incident2.ReceivedAt}},
				}, nil),
				mockIncidentDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
			},
			wantErr:	epifierrors.ErrAlreadyExists,
		},
		{
			name:	"ticket exists for incident and cool-off not yet elapsed",
			args: args{
				incident: incident2,
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).Return(&watsonPb.GetIncidentsForClientResponse{
					Status:		rpcPb.StatusOk(),
					Incidents:	[]*watsonPb.IncidentDetailsForClient{{IdentifiedAt: incident2.ReceivedAt, TicketId: 34}},
				}, nil),
				mockIncidentDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				mockTicketClient.EXPECT().AddPrivateNoteAsync(gomock.Any(), gomock.Any()).
					Return(&ticketPb.AddPrivateNoteAsyncResponse{Status: rpcPb.StatusOk()}, nil),
			},
			wantErr:	epifierrors.ErrAlreadyExists,
		},
		{
			name:	"invalid argument actor id not passed",
			args: args{
				incident: incident2,
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).Return(&watsonPb.GetIncidentsForClientResponse{
					Status:		rpcPb.StatusOk(),
					Incidents:	[]*watsonPb.IncidentDetailsForClient{{IdentifiedAt: timestampPb.New(time.Unix(50000, 0))}},
				}, nil),
				mockWatsonClient.EXPECT().IngestEvent(gomock.Any(), gomock.Any()).
					Return(&watsonPb.IngestEventResponse{Status: rpcPb.StatusInvalidArgument()}, nil),
			},
			wantErr:	epifierrors.ErrInvalidArgument,
		},
		{
			name:	"invalid argument client req id not passed",
			args: args{
				incident: &entity.IncidentDetails{
					ActorId:		"ABC",
					IssueCategoryId:	"A",
				},
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{Status: rpcPb.StatusRecordNotFound()}, nil),
				mockWatsonClient.EXPECT().IngestEvent(gomock.Any(), gomock.Any()).
					Return(&watsonPb.IngestEventResponse{Status: rpcPb.StatusInvalidArgument()}, nil),
			},
			wantErr:	epifierrors.ErrInvalidArgument,
		},
		{
			name:	"internal error while creating incident",
			args: args{
				incident: &entity.IncidentDetails{
					ActorId:		"ABC",
					IssueCategoryId:	"A",
				},
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{Status: rpcPb.StatusRecordNotFound()}, nil),
				mockWatsonClient.EXPECT().IngestEvent(gomock.Any(), gomock.Any()).
					Return(&watsonPb.IngestEventResponse{Status: rpcPb.StatusInternal()}, nil),
			},
			wantErr:	rpcPb.StatusAsError(rpcPb.StatusInternal()),
		},
		{
			name:	"success: incident creation happened",
			args: args{
				incident: &entity.IncidentDetails{
					ActorId:		"ABC",
					IssueCategoryId:	"A",
				},
			},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{Status: rpcPb.StatusRecordNotFound()}, nil),
				mockWatsonClient.EXPECT().IngestEvent(gomock.Any(), gomock.Any()).
					Return(&watsonPb.IngestEventResponse{Status: rpcPb.StatusOk()}, nil),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewCreateIncidentTriggerProcessor(mockWatsonClient, mockTicketClient, createIncidentTriggerTS.genConf, mockIncidentDao)
			if err := c.ProcessEventTrigger(context.Background(), tt.args.incident); (err != nil) && !errors.Is(err, tt.wantErr) {
				t.Errorf("ProcessEventTrigger() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
