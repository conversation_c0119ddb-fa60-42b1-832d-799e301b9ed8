package trigger_processor

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, genConf, _, teardown := test.InitTestServer(false)
	createIncidentTriggerTS = CreateIncidentTriggerTestSuite{genConf: genConf}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
