//go:generate mockgen -source=factory.go -destination=../../test/mocks/error_activity/trigger_processor/mock_factory.go
package trigger_processor

import (
	"github.com/pkg/errors"

	consumerPb "github.com/epifi/gamma/api/cx/error_activity"
	"github.com/epifi/be-common/pkg/epifierrors"
)

type TriggerProcessorFactory interface {
	GetTriggerProcessor(trigger consumerPb.EventTrigger) (EventTriggerProcessor, error)
}

type TriggerProcessorFactoryImpl struct {
	createIncidentTriggerProcessor  *CreateIncidentTriggerProcessor
	resolveIncidentTriggerProcessor *ResolveIncidentTriggerProcessor
}

func NewTriggerProcessorFactoryImpl(createIncident *CreateIncidentTriggerProcessor, resolveIncident *ResolveIncidentTriggerProcessor) *TriggerProcessorFactoryImpl {
	return &TriggerProcessorFactoryImpl{
		createIncidentTriggerProcessor:  createIncident,
		resolveIncidentTriggerProcessor: resolveIncident,
	}
}

var _ TriggerProcessorFactory = &TriggerProcessorFactoryImpl{}

func (i *TriggerProcessorFactoryImpl) GetTriggerProcessor(trigger consumerPb.EventTrigger) (EventTriggerProcessor, error) {
	switch trigger {
	case consumerPb.EventTrigger_EVENT_TRIGGER_CREATE_WATSON_INCIDENT:
		return i.createIncidentTriggerProcessor, nil
	case consumerPb.EventTrigger_EVENT_TRIGGER_RESOLVE_WATSON_INCIDENT:
		return i.resolveIncidentTriggerProcessor, nil
	default:
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "no processor found for given trigger")
	}
}
