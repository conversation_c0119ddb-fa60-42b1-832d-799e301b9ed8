package trigger_processor

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	eventsPb "github.com/epifi/gamma/api/cx/error_activity"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/error_activity/entity"
	"github.com/epifi/gamma/cx/watson/dao"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
)

type CreateIncidentTriggerProcessor struct {
	watsonClient watsonPb.WatsonClient
	ticketClient ticketPb.TicketClient
	genConf      *genconf.Config
	incidentDao  dao.IIncidentDao
}

func NewCreateIncidentTriggerProcessor(watsonClient watsonPb.WatsonClient, ticketClient ticketPb.TicketClient,
	genConf *genconf.Config, incidentDao dao.IIncidentDao) *CreateIncidentTriggerProcessor {
	return &CreateIncidentTriggerProcessor{
		watsonClient: watsonClient,
		ticketClient: ticketClient,
		genConf:      genConf,
		incidentDao:  incidentDao,
	}
}

var _ EventTriggerProcessor = &CreateIncidentTriggerProcessor{}

func (c *CreateIncidentTriggerProcessor) ProcessEventTrigger(ctx context.Context, incident *entity.IncidentDetails) error {
	isIncidentCreationCoolOffElapsed, coolOffCheckErr := c.isIncidentCreationCoolOffElapsed(ctx, incident)
	if coolOffCheckErr != nil {
		return errors.Wrap(coolOffCheckErr, "failed to check cool-off period for incident")
	}
	if !isIncidentCreationCoolOffElapsed {
		return errors.Wrap(epifierrors.ErrAlreadyExists, "given incident is within cool-off")
	}

	ingestResp, ingestErr := c.watsonClient.IngestEvent(ctx, &watsonPb.IngestEventRequest{
		EventType:       watsonPb.EventType_EVENT_TYPE_INCIDENT_REPORT,
		Client:          typesPb.ServiceName_ERROR_ACTIVITY_SERVICE,
		ActorId:         incident.ActorId,
		ClientRequestId: incident.ClientRequestId,
		IdentifiedAt:    incident.ReceivedAt,
		IssueCategoryId: incident.IssueCategoryId,
	})
	if te := epifigrpc.RPCError(ingestResp, ingestErr); te != nil {
		if ingestResp.GetStatus().IsInvalidArgument() {
			return errors.Wrap(epifierrors.ErrInvalidArgument, te.Error())
		}
		return errors.Wrap(te, "ingestion to watson failed")
	}
	return nil
}

func (c *CreateIncidentTriggerProcessor) isIncidentCreationCoolOffElapsed(ctx context.Context, incident *entity.IncidentDetails) (bool, error) {
	latestIncident, err := c.fetchLatestSimilarIncident(ctx, incident)
	if err != nil {
		// if there are no similar incident we can move on to creation no cool-off check is required
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return true, nil
		}
		return false, errors.Wrap(err, "failed to fetch similar incidents of user")
	}
	logger.Debug(ctx, "fetched similar incident while creation", zap.Any("incident", latestIncident))
	duration := incident.ReceivedAt.AsTime().Sub(latestIncident.GetIdentifiedAt().AsTime())
	// if duration exceeds cool off period it means cool-off period for last incident has expired
	// for now cool-off period is kept as one day for each issue, based on requirement this can also come from issue config
	if duration > c.genConf.ErrorActivityConfig().DefaultIncidentCreationCoolOffPeriod() {
		return true, nil
	}

	// if the cool-off has not yet elapsed we will add the details of current event inside incident and ticket
	// update incident state with new details and add private note in ticket with time at which incident was triggered
	if latestIncident.GetIncidentData() == nil || latestIncident.GetIncidentData().GetEventHistory() == nil {
		latestIncident.IncidentData = &watsonPb.IncidentData{
			EventHistory: &eventsPb.EventHistory{
				ReceivedAtList: []*timestampPb.Timestamp{incident.ReceivedAt},
			},
		}
	} else {
		// there is already an event history hence appending the current events time-stamp
		latestIncident.GetIncidentData().GetEventHistory().ReceivedAtList = append(latestIncident.GetIncidentData().
			GetEventHistory().GetReceivedAtList(), incident.ReceivedAt)
	}

	updateErr := c.incidentDao.Update(ctx, &watsonPb.Incident{Id: latestIncident.GetIncidentId(),
		IncidentData: latestIncident.GetIncidentData()}, []watsonPb.IncidentMask{watsonPb.IncidentMask_INCIDENT_MASK_INCIDENT_DATA})
	if updateErr != nil {
		return false, errors.Wrap(updateErr, "failed to update incident data")
	}

	// only add private note in ticket if it exists for the incident
	if latestIncident.GetTicketId() != 0 {
		resp, err := c.ticketClient.AddPrivateNoteAsync(ctx, &ticketPb.AddPrivateNoteAsyncRequest{
			TicketId: latestIncident.GetTicketId(),
			Body:     formatEventHistoryToNote(latestIncident),
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			return false, errors.Wrap(te, "failed to add private note in ticket")
		}
	}
	return false, nil
}

func formatEventHistoryToNote(incident *watsonPb.IncidentDetailsForClient) string {
	eventHistory := incident.GetIncidentData().GetEventHistory().GetReceivedAtList()
	note := fmt.Sprintf("User faced this issue %v times, adding details for each instance below: ", len(eventHistory)+1)
	createdAtStr := incident.GetIdentifiedAt().AsTime().In(datetime.IST).Format(time.RFC1123)
	// using '<br>' for now line as freshdesk parses private note in HTML format
	note += "<br>" + createdAtStr

	for _, timeStamp := range eventHistory {
		str := timeStamp.AsTime().In(datetime.IST).Format(time.RFC1123)
		note += "<br>" + str
	}
	return note
}

func (c *CreateIncidentTriggerProcessor) fetchLatestSimilarIncident(ctx context.Context, incident *entity.IncidentDetails) (*watsonPb.IncidentDetailsForClient, error) {
	pageToken := &pagination.PageToken{}
	beforeToken, err := pageToken.Marshal()
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal page-token")
	}

	incidentResp, incidentErr := c.watsonClient.GetIncidentsForClient(ctx, &watsonPb.GetIncidentsForClientRequest{
		IncidentFilter: &watsonPb.IncidentFiltersForClient{
			ActorId:         incident.ActorId,
			Client:          typesPb.ServiceName_ERROR_ACTIVITY_SERVICE,
			IncidentStates:  validIncidentStates,
			IssueCategoryId: incident.IssueCategoryId,
		},
		PageContextRequest: &rpc.PageContextRequest{
			PageSize: 1,
			Token: &rpc.PageContextRequest_BeforeToken{
				BeforeToken: beforeToken,
			},
		},
	})
	if te := epifigrpc.RPCError(incidentResp, incidentErr); te != nil {
		if incidentResp.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, te.Error())
		}
		return nil, errors.Wrap(te, "error while fetching active incidents")
	}
	// handling the case where no incidents are sent with OK status
	if len(incidentResp.GetIncidents()) == 0 {
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no active incidents found")
	}

	// directly returning the first incident, as incidents are queried in descending order by time of creation
	// and record not found case is already handled
	return incidentResp.GetIncidents()[0], nil
}
