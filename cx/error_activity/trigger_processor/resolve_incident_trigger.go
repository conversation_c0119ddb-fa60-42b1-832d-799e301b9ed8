package trigger_processor

import (
	"context"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/cx/error_activity/entity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

var validIncidentStates = []watsonPb.IncidentState{
	watsonPb.IncidentState_INCIDENT_STATE_LOGGED_IN_DB,
	watsonPb.IncidentState_INCIDENT_STATE_INCIDENT_CREATION_COMMS_SENT,
	watsonPb.IncidentState_INCIDENT_STATE_TICKET_CREATED,
}

type ResolveIncidentTriggerProcessor struct {
	watsonClient watsonPb.WatsonClient
}

func NewResolveIncidentTriggerProcessor(watsonClient watsonPb.WatsonClient) *ResolveIncidentTriggerProcessor {
	return &ResolveIncidentTriggerProcessor{watsonClient: watsonClient}
}

var _ EventTriggerProcessor = &ResolveIncidentTriggerProcessor{}

// Watson guarantees that at a given point of time at most one incident can be open for a user, for given issue type
// Hence, we only fetch one open incident and resolve it
func (r *ResolveIncidentTriggerProcessor) ProcessEventTrigger(ctx context.Context, incident *entity.IncidentDetails) error {
	incidentResp, incidentErr := r.watsonClient.GetIncidentsForClient(ctx, &watsonPb.GetIncidentsForClientRequest{
		IncidentFilter: &watsonPb.IncidentFiltersForClient{
			ActorId:         incident.ActorId,
			Client:          typesPb.ServiceName_ERROR_ACTIVITY_SERVICE,
			IncidentStates:  validIncidentStates,
			IssueCategoryId: incident.IssueCategoryId,
		},
		PageContextRequest: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})
	if te := epifigrpc.RPCError(incidentResp, incidentErr); te != nil {
		if incidentResp.GetStatus().IsRecordNotFound() {
			return errors.Wrap(epifierrors.ErrRecordNotFound, te.Error())
		}
		return errors.Wrap(te, "error while fetching active incidents")
	}
	// handling the case where no incidents are sent with OK status
	if len(incidentResp.GetIncidents()) == 0 {
		return errors.Wrap(epifierrors.ErrRecordNotFound, "no active incidents found")
	}

	incidentToResolve := incidentResp.GetIncidents()[0]
	ingestResp, ingestErr := r.watsonClient.IngestEvent(ctx, &watsonPb.IngestEventRequest{
		EventType:       watsonPb.EventType_EVENT_TYPE_INCIDENT_RESOLUTION,
		Client:          incidentToResolve.GetClient(),
		ActorId:         incidentToResolve.GetActorId(),
		ClientRequestId: incidentToResolve.GetClientRequestId(),
		IssueCategoryId: incidentToResolve.GetIssueCategoryId(),
	})
	if te := epifigrpc.RPCError(ingestResp, ingestErr); te != nil {
		return errors.Wrap(te, "error while ingesting incident for resolution")
	}
	return nil
}
