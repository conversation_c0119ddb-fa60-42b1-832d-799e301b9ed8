//go:generate dao_metrics_gen .
//go:generate mockgen -source=dao.go -destination=../../test/mocks/error_activity/dao/mock_dao.go

package dao

import (
	"context"

	eventPb "github.com/epifi/gamma/api/cx/error_activity"
)

// EventConfigDao exposes methods that can be implemented to communicate with rudder_event_details table of Sherlock DB
type EventConfigDao interface {
	// CreateBatch - method to bulk insert event configs record in DB
	// accepts list of EventConfigs objects to be created and returns list of created entries in the db
	// each event in list must have event name, config type and config payload populated
	CreateBatch(ctx context.Context, eventList []*eventPb.EventConfig) ([]*eventPb.EventConfig, error)

	// GetByEventNameAndConfigType method accepts eventName, and configType both are mandatory parameters
	// InvalidArgument will be thrown for invalid request
	// NotFound will be thrown if there is no config for given parameters
	// Any other errors will be thrown with reason
	GetByEventNameAndConfigType(ctx context.Context, eventName string, configType eventPb.ConfigType) (*eventPb.EventConfig, error)

	// UpdateByEventNameAndConfigType method accepts eventConfig, where eventName and configType are mandatory fields
	// it will overwrite the current configPayload with the one provided in eventConfig
	UpdateByEventNameAndConfigType(ctx context.Context, eventConfig *eventPb.EventConfig) error
}
