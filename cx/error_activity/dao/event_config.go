package dao

import (
	"context"
	"time"

	"github.com/pkg/errors"
	gormV2 "gorm.io/gorm"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"

	eventPb "github.com/epifi/gamma/api/cx/error_activity"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/cx/error_activity/dao/model"
)

type EventConfigDaoImpl struct {
	db *gormV2.DB
}

func NewEventConfigsDaoImpl(db pkgTypes.SherlockPGDB) *EventConfigDaoImpl {
	return &EventConfigDaoImpl{db: db}
}

var _ EventConfigDao = &EventConfigDaoImpl{}

func (r *EventConfigDaoImpl) CreateBatch(ctx context.Context, eventDetails []*eventPb.EventConfig) ([]*eventPb.EventConfig, error) {
	defer metric_util.TrackDuration("cx/error_activity/dao", "EventConfigDaoImpl", "CreateBatch", time.Now())

	modelList := model.NewEventDetailsModelList(eventDetails)
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if err := db.Create(modelList).Error; err != nil {
		if storageV2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, errors.Wrap(err, "failed while creating event details record")
	}

	return model.NewEventDetailsProtoList(modelList), nil
}

func (r *EventConfigDaoImpl) GetByEventNameAndConfigType(ctx context.Context, eventName string, configType eventPb.ConfigType) (*eventPb.EventConfig, error) {
	defer metric_util.TrackDuration("cx/error_activity/dao", "EventConfigDaoImpl", "GetByEventNameAndConfigType", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var eventConfig *model.EventConfig
	query := db.Where("event_name = ?", eventName).Where("config_type = ?", configType)
	if err := query.First(&eventConfig).Error; err != nil {
		if errors.Is(err, gormV2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while fetching issue config")
	}
	return eventConfig.ToProtoMessage(), nil
}

func (r *EventConfigDaoImpl) UpdateByEventNameAndConfigType(ctx context.Context, eventConfig *eventPb.EventConfig) error {
	defer metric_util.TrackDuration("cx/error_activity/dao", "EventConfigDaoImpl", "UpdateByEventNameAndConfigType", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	eventConfigModel := model.NewEventDetailsFromProto(eventConfig)
	// updating config_payload for given event_name, config_type record
	updateQuery := db.Model(eventConfigModel).Where("event_name = ?", eventConfig.GetEventName()).
		Where("config_type = ?", eventConfig.GetConfigType()).Select("config_payload").Updates(eventConfigModel)
	if updateQuery.Error != nil {
		return errors.Wrap(updateQuery.Error, "error while updating event config in db")
	}
	if updateQuery.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}
