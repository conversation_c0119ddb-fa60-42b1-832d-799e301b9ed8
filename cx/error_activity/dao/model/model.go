package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	eventsPb "github.com/epifi/gamma/api/cx/error_activity"
)

type EventConfig struct {
	Id            string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	EventName     string
	ConfigType    eventsPb.ConfigType
	ConfigPayload *eventsPb.ConfigPayload
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

func NewEventDetailsFromProto(protoMsg *eventsPb.EventConfig) *EventConfig {
	if protoMsg == nil {
		return nil
	}
	return &EventConfig{
		EventName:     protoMsg.GetEventName(),
		ConfigType:    protoMsg.GetConfigType(),
		ConfigPayload: protoMsg.GetConfigPayload(),
	}
}

func (e *EventConfig) ToProtoMessage() *eventsPb.EventConfig {
	if e == nil {
		return nil
	}
	return &eventsPb.EventConfig{
		Id:            e.Id,
		EventName:     e.EventName,
		ConfigType:    e.ConfigType,
		ConfigPayload: e.ConfigPayload,
		CreatedAt:     timestampPb.New(e.CreatedAt),
		UpdatedAt:     timestampPb.New(e.UpdatedAt),
	}
}

func NewEventDetailsModelList(protoList []*eventsPb.EventConfig) []*EventConfig {
	var modelList []*EventConfig
	for _, eventDetail := range protoList {
		modelList = append(modelList, NewEventDetailsFromProto(eventDetail))
	}
	return modelList
}

func NewEventDetailsProtoList(modelList []*EventConfig) []*eventsPb.EventConfig {
	var protoList []*eventsPb.EventConfig
	for _, eventDetail := range modelList {
		protoList = append(protoList, eventDetail.ToProtoMessage())
	}
	return protoList
}
