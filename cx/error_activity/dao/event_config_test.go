package dao

import (
	"context"
	"testing"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
	gormV2 "gorm.io/gorm"

	eventPb "github.com/epifi/gamma/api/cx/error_activity"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/test"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
)

type EventConfigDaoTestSuite struct {
	db              *gormV2.DB
	conf            *config.Config
	EventConfigsDao *EventConfigDaoImpl
}

var (
	eventConfigDTS    EventConfigDaoTestSuite
	existsEventConfig = &eventPb.EventConfig{
		EventName:  "exists",
		ConfigType: eventPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
		ConfigPayload: &eventPb.ConfigPayload{
			ConfigPayload: &eventPb.ConfigPayload_IssueCategoryId{
				IssueCategoryId: "123",
			},
		},
	}
	existsEventConfig1 = &eventPb.EventConfig{
		EventName:  "exists",
		ConfigType: eventPb.ConfigType_CONFIG_TYPE_ACTIVITY_TYPE_IDENTIFIER,
		ConfigPayload: &eventPb.ConfigPayload{
			ConfigPayload: &eventPb.ConfigPayload_ActivityTypeIdentifier{
				ActivityTypeIdentifier: "123",
			},
		},
	}
	list1 = []*eventPb.EventConfig{
		{
			EventName:  "CreateTest1",
			ConfigType: eventPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER,
			ConfigPayload: &eventPb.ConfigPayload{
				ConfigPayload: &eventPb.ConfigPayload_EventTrigger{
					EventTrigger: eventPb.EventTrigger_EVENT_TRIGGER_CREATE_WATSON_INCIDENT,
				},
			},
		},
		{
			EventName:  "CreateTest2",
			ConfigType: eventPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER,
			ConfigPayload: &eventPb.ConfigPayload{
				ConfigPayload: &eventPb.ConfigPayload_EventTrigger{
					EventTrigger: eventPb.EventTrigger_EVENT_TRIGGER_RESOLVE_WATSON_INCIDENT,
				},
			},
		},
		{
			EventName:  "CreateTest3",
			ConfigType: eventPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
			ConfigPayload: &eventPb.ConfigPayload{
				ConfigPayload: &eventPb.ConfigPayload_IssueCategoryId{
					IssueCategoryId: "ABC",
				},
			},
		},
		{
			EventName:  "CreateTest4",
			ConfigType: eventPb.ConfigType_CONFIG_TYPE_ACTIVITY_TYPE_IDENTIFIER,
			ConfigPayload: &eventPb.ConfigPayload{
				ConfigPayload: &eventPb.ConfigPayload_ActivityTypeIdentifier{
					ActivityTypeIdentifier: "Activity1",
				},
			},
		},
	}
)

func TestEventConfigDaoImpl_CreateBatch(t *testing.T) {
	type args struct {
		ctx          context.Context
		eventDetails []*eventPb.EventConfig
	}
	tests := []struct {
		name    string
		args    args
		want    []*eventPb.EventConfig
		wantErr bool
	}{
		{
			name: "record already exists",
			args: args{
				ctx: context.Background(),
				eventDetails: []*eventPb.EventConfig{
					{
						EventName:  "exists",
						ConfigType: eventPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
						ConfigPayload: &eventPb.ConfigPayload{
							ConfigPayload: &eventPb.ConfigPayload_IssueCategoryId{
								IssueCategoryId: "123",
							},
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "successful creation",
			args: args{
				ctx:          context.Background(),
				eventDetails: list1,
			},
			want: list1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, eventConfigDTS.db, eventConfigDTS.conf.EpifiDb.GetName(),
				test.AffectedTestTables)

			got, err := eventConfigDTS.EventConfigsDao.CreateBatch(tt.args.ctx, tt.args.eventDetails)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if err := isEventConfigsListMatching(got, tt.want); err != nil {
				t.Errorf("CreateBatch() got = %v, want %v | diff %v", got, tt.want, err)
			}
		})
	}
}

func TestEventConfigsDao_GetByEventNameAndConfigType(t *testing.T) {
	type args struct {
		ctx        context.Context
		eventName  string
		configType eventPb.ConfigType
	}
	tests := []struct {
		name    string
		args    args
		want    *eventPb.EventConfig
		wantErr error
	}{
		{
			name: "record not found",
			args: args{
				ctx:        context.Background(),
				eventName:  "ntofound",
				configType: eventPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
			},
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "success: fetch issue category id config",
			args: args{
				ctx:        context.Background(),
				eventName:  "exists",
				configType: eventPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
			},
			want: existsEventConfig,
		},
		{
			name: "success: fetch activity type identifier config",
			args: args{
				ctx:        context.Background(),
				eventName:  "exists",
				configType: eventPb.ConfigType_CONFIG_TYPE_ACTIVITY_TYPE_IDENTIFIER,
			},
			want: existsEventConfig1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, eventConfigDTS.db, eventConfigDTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := eventConfigDTS.EventConfigsDao.GetByEventNameAndConfigType(tt.args.ctx, tt.args.eventName, tt.args.configType)
			if (got == nil || err != nil) && !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByEventNameAndConfigType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				got.UpdatedAt = tt.want.UpdatedAt
				got.CreatedAt = tt.want.CreatedAt
				got.Id = tt.want.Id
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetByEventNameAndConfigType() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestEventConfigsDao_UpdateByEventNameAndConfigType(t *testing.T) {
	type args struct {
		ctx         context.Context
		eventConfig *eventPb.EventConfig
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
				eventConfig: &eventPb.EventConfig{
					EventName:  "test",
					ConfigType: eventPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
					ConfigPayload: &eventPb.ConfigPayload{
						ConfigPayload: &eventPb.ConfigPayload_IssueCategoryId{
							IssueCategoryId: "123",
						},
					},
				},
			},
			wantErr: epifierrors.ErrRowNotUpdated,
		},
		{
			name: "no update required (request payload same as current DB record)",
			args: args{
				ctx: context.Background(),
				eventConfig: &eventPb.EventConfig{
					EventName:  "exists",
					ConfigType: eventPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
					ConfigPayload: &eventPb.ConfigPayload{
						ConfigPayload: &eventPb.ConfigPayload_IssueCategoryId{
							IssueCategoryId: "123",
						},
					},
				},
			},
			wantErr: epifierrors.ErrRowNotUpdated,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				eventConfig: &eventPb.EventConfig{
					EventName:  "exists",
					ConfigType: eventPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
					ConfigPayload: &eventPb.ConfigPayload{
						ConfigPayload: &eventPb.ConfigPayload_IssueCategoryId{
							IssueCategoryId: "1234",
						},
					},
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, eventConfigDTS.db, eventConfigDTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			err := eventConfigDTS.EventConfigsDao.UpdateByEventNameAndConfigType(tt.args.ctx, tt.args.eventConfig)
			if err != nil && !errors.Is(err, tt.wantErr) {
				t.Errorf("GetByEventNameAndConfigType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func isEventConfigsListMatching(got []*eventPb.EventConfig, want []*eventPb.EventConfig) error {
	if len(got) != len(want) {
		return errors.New("length is not equal")
	}
	for i := range got {
		switch {
		case got[i].GetConfigType() != want[i].GetConfigType():
			return errors.New("config type is not matching")
		case got[i].GetConfigPayload().String() != want[i].GetConfigPayload().String():
			return errors.New("config payload is not matching")
		case got[i].GetEventName() != want[i].GetEventName():
			return errors.New("event name is not matching")
		}
	}
	return nil
}
