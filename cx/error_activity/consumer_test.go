package error_activity

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	structPb "google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	consumerPb "github.com/epifi/gamma/api/cx/error_activity"
	"github.com/epifi/gamma/api/event"
	typesPb "github.com/epifi/gamma/api/typesv2"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	mockDao "github.com/epifi/gamma/cx/test/mocks/error_activity/dao"
	mockPro "github.com/epifi/gamma/cx/test/mocks/error_activity/trigger_processor"
)

type ConsumerTestSuite struct {
	genConf *cxGenConf.Config
}

var (
	ConsumerTS	ConsumerTestSuite
	event1		= &event.RudderEvent{
		MessageId:	"123",
		UserId:		"Usr1",
		Event:		"Event1",
		Timestamp:	"2023-09-26T09:42:49.234Z",
	}
	event2	= &event.RudderEvent{
		MessageId:	"123",
		UserId:		"Usr1",
		Event:		"Event1",
		Timestamp:	"invalidtimestamp",
	}
	event3	= &event.RudderEvent{
		MessageId:	"123",
		UserId:		"Usr1",
		Event:		"Event1",
		Timestamp:	"2023-09-26T09:42:49.234Z",
		Properties: &structPb.Struct{
			Fields: map[string]*structPb.Value{
				"is_watson_resolution_event":	{Kind: &structPb.Value_BoolValue{BoolValue: true}},
				"watson_issue_category_id":	{Kind: &structPb.Value_StringValue{StringValue: "ABC"}},
				"watson_client_request_id":	{Kind: &structPb.Value_StringValue{StringValue: "DEF"}},
			},
		},
	}
	triggerConfig	= &consumerPb.EventConfig{
		EventName:	"Event1",
		ConfigType:	consumerPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER,
		ConfigPayload: &consumerPb.ConfigPayload{
			ConfigPayload: &consumerPb.ConfigPayload_EventTrigger{
				EventTrigger: consumerPb.EventTrigger_EVENT_TRIGGER_CREATE_WATSON_INCIDENT,
			},
		},
	}
	issueCategoryIdConfig	= &consumerPb.EventConfig{
		EventName:	"Event1",
		ConfigType:	consumerPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID,
		ConfigPayload: &consumerPb.ConfigPayload{
			ConfigPayload: &consumerPb.ConfigPayload_IssueCategoryId{
				IssueCategoryId: "ABC",
			},
		},
	}
	actor1	= &typesPb.Actor{
		Id:		"123",
		EntityId:	"123",
		Name:		"Actor",
	}
	mockErr	= errors.New("mock error")
)

func TestConsumer_ProcessEvent(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx	context.Context
		event	*event.RudderEvent
	}
	tests := []struct {
		name	string
		args	args
		mocks	func(*mockDao.MockEventConfigDao, *mockPro.MockTriggerProcessorFactory, *mockPro.MockProcessor)
		want	*consumerPb.ProcessEventResponse
		wantErr	error
	}{
		{
			name:	"invalid event payload passed",
			args: args{
				ctx:	context.Background(),
				event:	event2,
			},
			mocks: func(mockEventConfigDao *mockDao.MockEventConfigDao, mockTriggerFac *mockPro.MockTriggerProcessorFactory, mockTriggerProcessor *mockPro.MockProcessor) {
			},
			want:	permanentFailureResp(rpc.StatusInvalidArgument()),
		},
		{
			name:	"event trigger config not found",
			args: args{
				ctx:	context.Background(),
				event:	event1,
			},
			mocks: func(mockEventConfigDao *mockDao.MockEventConfigDao, mockTriggerFac *mockPro.MockTriggerProcessorFactory, mockTriggerProcessor *mockPro.MockProcessor) {
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER).
					Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
			want:	permanentFailureResp(rpc.StatusRecordNotFound()),
		},
		{
			name:	"error while fetching event trigger config",
			args: args{
				ctx:	context.Background(),
				event:	event1,
			},
			mocks: func(mockEventConfigDao *mockDao.MockEventConfigDao, mockTriggerFac *mockPro.MockTriggerProcessorFactory, mockTriggerProcessor *mockPro.MockProcessor) {
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER).
					Return(nil, mockErr).Times(1)
			},
			want:	transientFailureResp(rpc.StatusInternal()),
		},
		{
			name:	"issue category config not found",
			args: args{
				ctx:	context.Background(),
				event:	event1,
			},
			mocks: func(mockEventConfigDao *mockDao.MockEventConfigDao, mockTriggerFac *mockPro.MockTriggerProcessorFactory, mockTriggerProcessor *mockPro.MockProcessor) {
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER).
					Return(triggerConfig, nil).Times(1)
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID).
					Return(nil, epifierrors.ErrRecordNotFound).Times(1)
			},
			want:	permanentFailureResp(rpc.StatusRecordNotFound()),
		},
		{
			name:	"error while fetching issue category config",
			args: args{
				ctx:	context.Background(),
				event:	event1,
			},
			mocks: func(mockEventConfigDao *mockDao.MockEventConfigDao, mockTriggerFac *mockPro.MockTriggerProcessorFactory, mockTriggerProcessor *mockPro.MockProcessor) {
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER).
					Return(triggerConfig, nil).Times(1)
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID).
					Return(nil, mockErr).Times(1)
			},
			want:	transientFailureResp(rpc.StatusInternal()),
		},
		{
			name:	"error while processing trigger",
			args: args{
				ctx:	context.Background(),
				event:	event1,
			},
			mocks: func(mockEventConfigDao *mockDao.MockEventConfigDao, mockTriggerFac *mockPro.MockTriggerProcessorFactory, mockTriggerProcessor *mockPro.MockProcessor) {
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER).
					Return(triggerConfig, nil).Times(1)
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID).
					Return(issueCategoryIdConfig, nil).Times(1)
				mockTriggerFac.EXPECT().GetTriggerProcessor(consumerPb.EventTrigger_EVENT_TRIGGER_CREATE_WATSON_INCIDENT).Return(mockTriggerProcessor, nil).Times(1)
				mockTriggerProcessor.EXPECT().ProcessTrigger(gomock.Any(), gomock.Any()).Return(epifierrors.ErrInvalidArgument).Times(1)
			},
			want:	permanentFailureResp(rpc.StatusInvalidArgument()),
		},
		{
			name:	"success: event trigger processed",
			args: args{
				ctx:	context.Background(),
				event:	event1,
			},
			mocks: func(mockEventConfigDao *mockDao.MockEventConfigDao, mockTriggerFac *mockPro.MockTriggerProcessorFactory, mockTriggerProcessor *mockPro.MockProcessor) {
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER).
					Return(triggerConfig, nil).Times(1)
				mockEventConfigDao.EXPECT().GetByEventNameAndConfigType(gomock.Any(), event1.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID).
					Return(issueCategoryIdConfig, nil).Times(1)
				mockTriggerFac.EXPECT().GetTriggerProcessor(consumerPb.EventTrigger_EVENT_TRIGGER_CREATE_WATSON_INCIDENT).Return(mockTriggerProcessor, nil).Times(1)
				mockTriggerProcessor.EXPECT().ProcessTrigger(gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
			want:	successResp(),
		},
		{
			name:	"success: read details from event payload",
			args: args{
				ctx:	context.Background(),
				event:	event3,
			},
			mocks: func(mockEventConfigDao *mockDao.MockEventConfigDao, mockTriggerFac *mockPro.MockTriggerProcessorFactory, mockTriggerProcessor *mockPro.MockProcessor) {
				mockTriggerFac.EXPECT().GetTriggerProcessor(consumerPb.EventTrigger_EVENT_TRIGGER_RESOLVE_WATSON_INCIDENT).Return(mockTriggerProcessor, nil).Times(1)
				mockTriggerProcessor.EXPECT().ProcessTrigger(gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
			want:	successResp(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockEventConfigDao := mockDao.NewMockEventConfigDao(ctr)
			mockTriggerFac := mockPro.NewMockTriggerProcessorFactory(ctr)
			mockTriggerProcessor := mockPro.NewMockProcessor(ctr)

			// Set up the mocks
			if tt.mocks != nil {
				tt.mocks(mockEventConfigDao, mockTriggerFac, mockTriggerProcessor)
			}

			c := NewConsumer(mockEventConfigDao, ConsumerTS.genConf, mockTriggerFac)
			got, err := c.ProcessEvent(tt.args.ctx, tt.args.event)
			if (err != nil) && !errors.Is(err, tt.wantErr) {
				t.Errorf("ProcessEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}
