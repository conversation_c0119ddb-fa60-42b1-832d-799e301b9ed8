package error_activity

import (
	queuePb "github.com/epifi/be-common/api/queue"
	rpcPb "github.com/epifi/be-common/api/rpc"
	consumerPb "github.com/epifi/gamma/api/cx/error_activity"
)

func successResp() *consumerPb.ProcessEventResponse {
	return &consumerPb.ProcessEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status:         queuePb.MessageConsumptionStatus_SUCCESS,
			GrpcStatusCode: rpcPb.StatusOk(),
		},
	}
}

func permanentFailureResp(status *rpcPb.Status) *consumerPb.ProcessEventResponse {
	return &consumerPb.ProcessEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status:         queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			GrpcStatusCode: status,
		},
	}
}

func transientFailureResp(status *rpcPb.Status) *consumerPb.ProcessEventResponse {
	return &consumerPb.ProcessEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status:         queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			GrpcStatusCode: status,
		},
	}
}
