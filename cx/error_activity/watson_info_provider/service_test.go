package watson_info_provider

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commsPb "github.com/epifi/gamma/api/comms"
	issueConfigPb "github.com/epifi/gamma/api/cx/issue_config"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/mocks"
	mock_dao "github.com/epifi/gamma/cx/test/mocks/issue_config/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var (
	categoryId1		= "123"
	incidentForClient	= &watsonPb.IncidentDetailsForClient{
		Client:			typesPb.ServiceName_CX_SERVICE,
		ActorId:		"AC123",
		ClientRequestId:	"ID1",
		IssueCategoryId:	categoryId1,
		IdentifiedAt: &timestampPb.Timestamp{
			Seconds:	100000000,
			Nanos:		0,
		},
	}
	invalidIncident	= &watsonPb.IncidentDetailsForClient{
		Client:			typesPb.ServiceName_CX_SERVICE,
		ActorId:		"AC123",
		ClientRequestId:	"ID1",
		IdentifiedAt: &timestampPb.Timestamp{
			Seconds:	100000000,
			Nanos:		0,
		},
	}
	creationComms	= []*watsonPb.CommsDetail{
		{
			Detail: &watsonPb.CommsDetail_Email{
				Email: &commsPb.EmailMessage{
					FromEmailId:	"<EMAIL>",
					FromEmailName:	"Tester",
					Subject:	"Ticket: " + dateKey,
				},
			},
		},
	}
	replacedCreationComms	= []*watsonPb.CommsDetail{
		{
			Detail: &watsonPb.CommsDetail_Email{
				Email: &commsPb.EmailMessage{
					FromEmailId:	"<EMAIL>",
					FromEmailName:	"Tester",
					Subject:	"Ticket: 3 March 1973 at 9:46 AM",
				},
			},
		},
	}
	resolutionComms	= []*watsonPb.CommsDetail{
		{
			Detail: &watsonPb.CommsDetail_Notification{
				Notification: &commsPb.NotificationMessage{
					Notification: &fcmPb.Notification{
						NotificationType:	fcmPb.NotificationType_IN_APP,
						NotificationTemplates: &fcmPb.Notification_InAppTemplate{
							InAppTemplate: &fcmPb.InAppTemplate{
								CommonTemplateFields: &fcmPb.CommonTemplateFields{
									Title: "test: " + firstNameKey,
								},
							},
						},
					},
				},
			},
		},
	}
	replacedResolutionComms	= []*watsonPb.CommsDetail{
		{
			Detail: &watsonPb.CommsDetail_Notification{
				Notification: &commsPb.NotificationMessage{
					Notification: &fcmPb.Notification{
						NotificationType:	fcmPb.NotificationType_IN_APP,
						NotificationTemplates: &fcmPb.Notification_InAppTemplate{
							InAppTemplate: &fcmPb.InAppTemplate{
								CommonTemplateFields: &fcmPb.CommonTemplateFields{
									Title: "test: XYZ",
								},
							},
						},
					},
				},
			}},
	}
	issueConfig1	= &issueConfigPb.IssueConfig{
		IssueCategoryId:	categoryId1,
		ConfigType:		issueConfigPb.ConfigType_CONFIG_TYPE_WATSON_TICKET_DETAILS,
		ConfigPayload: &issueConfigPb.ConfigPayload{
			Config: &issueConfigPb.ConfigPayload_WatsonTicketDetails{
				WatsonTicketDetails: &watsonPb.TicketDetails{
					Description: "Test1",
				},
			},
		},
	}
	issueConfig2	= &issueConfigPb.IssueConfig{
		IssueCategoryId:	categoryId1,
		ConfigType:		issueConfigPb.ConfigType_CONFIG_TYPE_INCIDENT_STAGE_BASED_COMMS_DETAILS,
		ConfigPayload: &issueConfigPb.ConfigPayload{
			Config: &issueConfigPb.ConfigPayload_IncidentStageBasedCommsDetails{
				IncidentStageBasedCommsDetails: &issueConfigPb.IncidentStageBasedCommsDetails{
					CreationComms:		creationComms,
					ResolutionComms:	resolutionComms,
				},
			},
		},
	}
	issueConfig3	= &issueConfigPb.IssueConfig{
		IssueCategoryId:	categoryId1,
		ConfigType:		issueConfigPb.ConfigType_CONFIG_TYPE_WATSON,
		ConfigPayload: &issueConfigPb.ConfigPayload{
			Config: &issueConfigPb.ConfigPayload_WatsonConfig{
				WatsonConfig: &issueConfigPb.WatsonConfig{
					AutoClosurePeriod: "24h",
				},
			},
		},
	}
	incidentForClient1	= &watsonPb.IncidentDetailsForClient{
		Client:			typesPb.ServiceName_CX_SERVICE,
		ActorId:		"AC123",
		ClientRequestId:	"ID1",
		IssueCategoryId:	categoryId1,
		IdentifiedAt:		timestampPb.Now(),
	}
	userWithName		= &user.User{Profile: &user.Profile{PanName: &commontypes.Name{FirstName: "XYZ"}}}
	isIncidentValidReq	= &watsonPb.IsIncidentValidRequest{Incident: incidentForClient}
)

func TestWatsonInfoProvider_GetTicketDetails(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockIssueConfigDao := mock_dao.NewMockIssueConfigDao(ctr)

	type args struct {
		request	*watsonPb.GetTicketDetailsRequest
		mocks	[]interface{}
	}
	tests := []struct {
		name	string
		args	args
		want	*watsonPb.GetTicketDetailsResponse
		wantErr	bool
	}{
		{
			name:	"invalid incident provided",
			args: args{
				request: &watsonPb.GetTicketDetailsRequest{Incident: invalidIncident},
			},
			want: &watsonPb.GetTicketDetailsResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
		},
		{
			name:	"invalid action type passed",
			args: args{
				request: &watsonPb.GetTicketDetailsRequest{
					Incident:	incidentForClient,
					ActionType:	watsonPb.GetTicketDetailsActionType_GET_TICKET_DETAILS_ACTION_TYPE_UPDATE_TICKET_FOR_INCIDENT_RESOLUTION,
				},
			},
			want: &watsonPb.GetTicketDetailsResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
		},
		{
			name:	"issue config not found",
			args: args{
				request: &watsonPb.GetTicketDetailsRequest{
					Incident:	incidentForClient,
					ActionType:	watsonPb.GetTicketDetailsActionType_GET_TICKET_DETAILS_ACTION_TYPE_CREATE_TICKET_FOR_INCIDENT,
				},
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(gomock.Any(), incidentForClient.GetIssueCategoryId(),
						issueConfigPb.ConfigType_CONFIG_TYPE_WATSON_TICKET_DETAILS).Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &watsonPb.GetTicketDetailsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name:	"error while fetching config",
			args: args{
				request: &watsonPb.GetTicketDetailsRequest{
					Incident:	incidentForClient,
					ActionType:	watsonPb.GetTicketDetailsActionType_GET_TICKET_DETAILS_ACTION_TYPE_CREATE_TICKET_FOR_INCIDENT,
				},
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(gomock.Any(), incidentForClient.GetIssueCategoryId(),
						issueConfigPb.ConfigType_CONFIG_TYPE_WATSON_TICKET_DETAILS).Return(nil, errors.New("mock error")),
				},
			},
			want: &watsonPb.GetTicketDetailsResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name:	"success",
			args: args{
				request: &watsonPb.GetTicketDetailsRequest{
					Incident:	incidentForClient,
					ActionType:	watsonPb.GetTicketDetailsActionType_GET_TICKET_DETAILS_ACTION_TYPE_CREATE_TICKET_FOR_INCIDENT,
				},
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(gomock.Any(), incidentForClient.GetIssueCategoryId(),
						issueConfigPb.ConfigType_CONFIG_TYPE_WATSON_TICKET_DETAILS).Return(issueConfig1, nil),
				},
			},
			want: &watsonPb.GetTicketDetailsResponse{
				TicketDetails:	issueConfig1.GetConfigPayload().GetWatsonTicketDetails(),
				Status:		rpcPb.StatusOk(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := NewWatsonInfoProvider(mockIssueConfigDao, nil)
			got, err := w.GetTicketDetails(context.Background(), tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTicketDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetTicketDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWatsonInfoProvider_GetCommsDetails(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockIssueConfigDao := mock_dao.NewMockIssueConfigDao(ctr)
	mockUserClient := mocks.NewMockUsersClient(ctr)
	type args struct {
		request	*watsonPb.GetCommsDetailsRequest
		mocks	[]interface{}
	}
	tests := []struct {
		name	string
		args	args
		want	*watsonPb.GetCommsDetailsResponse
		wantErr	bool
	}{
		{
			name:	"invalid incident provided",
			args: args{
				request: &watsonPb.GetCommsDetailsRequest{Incident: invalidIncident},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
		},
		{
			name:	"issue config not found",
			args: args{
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	incidentForClient,
					CommsType:	watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
				},
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(gomock.Any(), categoryId1,
						issueConfigPb.ConfigType_CONFIG_TYPE_INCIDENT_STAGE_BASED_COMMS_DETAILS).Return(nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name:	"error while fetching issue config",
			args: args{
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	incidentForClient,
					CommsType:	watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
				},
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(gomock.Any(), categoryId1,
						issueConfigPb.ConfigType_CONFIG_TYPE_INCIDENT_STAGE_BASED_COMMS_DETAILS).Return(nil, errors.New("mock error")),
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name:	"incident state is not supported for comms",
			args: args{
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	incidentForClient,
					CommsType:	watsonPb.CommsType_COMMS_TYPE_TICKET_STATUS_CHANGE,
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
		},
		{
			name:	"success: comms type creation",
			args: args{
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	incidentForClient,
					CommsType:	watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
				},
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(gomock.Any(), categoryId1,
						issueConfigPb.ConfigType_CONFIG_TYPE_INCIDENT_STAGE_BASED_COMMS_DETAILS).Return(issueConfig2, nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{Status: rpcPb.StatusRecordNotFound()}, nil),
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status:		rpcPb.StatusOk(),
				CommsDetails:	replacedCreationComms,
			},
		},
		{
			name:	"success: comms type resolution",
			args: args{
				request: &watsonPb.GetCommsDetailsRequest{
					Incident:	incidentForClient,
					CommsType:	watsonPb.CommsType_COMMS_TYPE_INCIDENT_RESOLUTION,
				},
				mocks: []interface{}{
					mockIssueConfigDao.EXPECT().Get(gomock.Any(), categoryId1,
						issueConfigPb.ConfigType_CONFIG_TYPE_INCIDENT_STAGE_BASED_COMMS_DETAILS).Return(issueConfig2, nil),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&user.GetUserResponse{Status: rpcPb.StatusOk(), User: userWithName}, nil),
				},
			},
			want: &watsonPb.GetCommsDetailsResponse{
				Status:		rpcPb.StatusOk(),
				CommsDetails:	replacedResolutionComms,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := NewWatsonInfoProvider(mockIssueConfigDao, mockUserClient)
			got, err := w.GetCommsDetails(context.Background(), tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCommsDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetCommsDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWatsonInfoProvider_IsIncidentValid(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockIssueConfigDao := mock_dao.NewMockIssueConfigDao(ctr)
	type args struct {
		request *watsonPb.IsIncidentValidRequest
	}
	tests := []struct {
		name	string
		args	args
		want	*watsonPb.IsIncidentValidResponse
		mocks	[]interface{}
		wantErr	bool
	}{

		{
			name:	"no issue config found",
			args:	args{request: isIncidentValidReq},
			mocks: []interface{}{
				mockIssueConfigDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
			},
			want:	&watsonPb.IsIncidentValidResponse{Status: rpcPb.StatusRecordNotFound()},
		},
		{
			name:	"failed to fetch issue config",
			args:	args{request: isIncidentValidReq},
			mocks: []interface{}{
				mockIssueConfigDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("mock error")),
			},
			want:	&watsonPb.IsIncidentValidResponse{Status: rpcPb.StatusInternal()},
		},
		{
			name:	"success: incident is invalid",
			args: args{
				request: isIncidentValidReq,
			},
			mocks: []interface{}{
				mockIssueConfigDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(issueConfig3, nil),
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status:			rpcPb.StatusOk(),
				IsIncidentValid:	false,
			},
		},
		{
			name:	"success: incident is valid",
			args: args{
				request: &watsonPb.IsIncidentValidRequest{Incident: incidentForClient1},
			},
			mocks: []interface{}{
				mockIssueConfigDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(issueConfig3, nil),
			},
			want: &watsonPb.IsIncidentValidResponse{
				Status:			rpcPb.StatusOk(),
				IsIncidentValid:	true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := NewWatsonInfoProvider(mockIssueConfigDao, nil)
			got, err := w.IsIncidentValid(context.Background(), tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsIncidentValid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("IsIncidentValid() got = %v, want %v", got, tt.want)
			}
		})
	}
}
