package watson_info_provider

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()

	_, _, _, teardown = test.InitTestServer(true) //nolint: dogsled

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
