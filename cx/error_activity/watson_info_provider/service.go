package watson_info_provider

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	servicePb "github.com/epifi/gamma/api/cx/error_activity/watson_info_provider"
	issueConfigPb "github.com/epifi/gamma/api/cx/issue_config"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	usersPb "github.com/epifi/gamma/api/user"
	dao "github.com/epifi/gamma/cx/issue_config/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

type WatsonInfoProvider struct {
	issueConfigDao dao.IssueConfigDao
	userClient     usersPb.UsersClient
}

func NewWatsonInfoProvider(issueConfigDao dao.IssueConfigDao, userClient usersPb.UsersClient) *WatsonInfoProvider {
	return &WatsonInfoProvider{
		issueConfigDao: issueConfigDao,
		userClient:     userClient,
	}
}

var _ servicePb.WatsonInfoProviderServer = &WatsonInfoProvider{}

func (w *WatsonInfoProvider) IsIncidentValid(ctx context.Context, request *watsonPb.IsIncidentValidRequest) (*watsonPb.IsIncidentValidResponse, error) {
	issueConfig, err := w.issueConfigDao.Get(ctx, request.GetIncident().GetIssueCategoryId(), issueConfigPb.ConfigType_CONFIG_TYPE_WATSON)
	if err != nil {
		logger.Error(ctx, "failed to fetch watson config", zap.Error(err), zap.String(logger.ISSUE_CATEGORY_ID, request.GetIncident().GetIssueCategoryId()))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &watsonPb.IsIncidentValidResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &watsonPb.IsIncidentValidResponse{Status: rpcPb.StatusInternal()}, nil
	}

	autoClosurePeriodStr := issueConfig.GetConfigPayload().GetWatsonConfig().GetAutoClosurePeriod()
	if autoClosurePeriodStr == "" {
		return &watsonPb.IsIncidentValidResponse{Status: rpcPb.StatusOk(), IsIncidentValid: true}, nil
	}
	autoCloseDuration, err := time.ParseDuration(autoClosurePeriodStr)
	if err != nil {
		logger.Error(ctx, "failed to parse auto-closure period", zap.Error(err),
			zap.String("auto_closure_period", autoClosurePeriodStr), zap.String(logger.ISSUE_CATEGORY_ID, request.GetIncident().GetIssueCategoryId()))
		return &watsonPb.IsIncidentValidResponse{Status: rpcPb.StatusInternal()}, nil
	}

	isValid := true
	// if the amount of time for which incident is active has exceeded the auto-closure duration we will mark it inactive
	// to calculate the amount of time incident being active we take the difference between current time and time when incident was identified
	if time.Now().Sub(request.GetIncident().GetIdentifiedAt().AsTime()) > autoCloseDuration {
		isValid = false
	}
	return &watsonPb.IsIncidentValidResponse{
		IsIncidentValid: isValid,
		Status:          rpcPb.StatusOk(),
	}, nil
}

func (w *WatsonInfoProvider) GetTicketDetails(ctx context.Context, request *watsonPb.GetTicketDetailsRequest) (*watsonPb.GetTicketDetailsResponse, error) {
	if request.GetIncident().GetIssueCategoryId() == "" || request.GetActionType() != watsonPb.GetTicketDetailsActionType_GET_TICKET_DETAILS_ACTION_TYPE_CREATE_TICKET_FOR_INCIDENT {
		logger.Error(ctx, "issue category id and action type are required, action type can only be creation", zap.String(logger.CLIENT_REQUEST_ID,
			request.GetIncident().GetClientRequestId()), zap.String(logger.ACTION_TYPE, request.GetActionType().String()))
		return &watsonPb.GetTicketDetailsResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	issueConfig, err := w.issueConfigDao.Get(ctx, request.GetIncident().GetIssueCategoryId(), issueConfigPb.ConfigType_CONFIG_TYPE_WATSON_TICKET_DETAILS)
	if err != nil {
		logger.Error(ctx, "failed to fetch ticket details from issue config", zap.Error(err),
			zap.String(logger.ISSUE_CATEGORY_ID, request.GetIncident().GetIssueCategoryId()))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &watsonPb.GetTicketDetailsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &watsonPb.GetTicketDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &watsonPb.GetTicketDetailsResponse{
		Status:        rpcPb.StatusOk(),
		TicketDetails: issueConfig.GetConfigPayload().GetWatsonTicketDetails(),
	}, nil
}

func (w *WatsonInfoProvider) GetCommsDetails(ctx context.Context, request *watsonPb.GetCommsDetailsRequest) (*watsonPb.GetCommsDetailsResponse, error) {
	validErr := w.validateGetCommsDetailsRequest(request)
	if validErr != nil {
		logger.Error(ctx, "invalid request while fetching comms details", zap.Error(validErr),
			zap.String(logger.CLIENT_REQUEST_ID, request.GetIncident().GetClientRequestId()))
		return &watsonPb.GetCommsDetailsResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	issueConfig, err := w.issueConfigDao.Get(ctx, request.GetIncident().GetIssueCategoryId(), issueConfigPb.ConfigType_CONFIG_TYPE_INCIDENT_STAGE_BASED_COMMS_DETAILS)
	if err != nil {
		logger.Error(ctx, "failed to fetch comms details from issue config", zap.Error(err),
			zap.String(logger.ISSUE_CATEGORY_ID, request.GetIncident().GetIssueCategoryId()))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &watsonPb.GetCommsDetailsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &watsonPb.GetCommsDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	var commsToSend []*watsonPb.CommsDetail
	switch request.GetCommsType() {
	case watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION:
		commsToSend = issueConfig.GetConfigPayload().GetIncidentStageBasedCommsDetails().GetCreationComms()
	case watsonPb.CommsType_COMMS_TYPE_INCIDENT_RESOLUTION:
		commsToSend = issueConfig.GetConfigPayload().GetIncidentStageBasedCommsDetails().GetResolutionComms()
	case watsonPb.CommsType_COMMS_TYPE_INCIDENT_AUTO_CLOSURE:
		commsToSend = issueConfig.GetConfigPayload().GetIncidentStageBasedCommsDetails().GetAutoClosureComms()
	}
	dynamicFields, err := w.fetchDynamicFields(ctx, request.GetIncident(), request.GetCommsMetadata().GetTicketId())
	if err != nil {
		logger.Error(ctx, "failed to fetch dynamic fields while sending comms", zap.Error(err),
			zap.String(logger.CLIENT_REQUEST_ID, request.GetIncident().GetClientRequestId()))
	}
	if dynamicFields != nil {
		for _, commsTemplate := range commsToSend {
			dynamicFields.populateFields(commsTemplate)
		}
	}

	return &watsonPb.GetCommsDetailsResponse{
		Status:       rpcPb.StatusOk(),
		CommsDetails: commsToSend,
	}, nil
}

func (w *WatsonInfoProvider) validateGetCommsDetailsRequest(request *watsonPb.GetCommsDetailsRequest) error {
	if request.GetIncident().GetIssueCategoryId() == "" {
		return errors.New("issue category id is required")
	}
	switch request.GetCommsType() {
	case watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
		watsonPb.CommsType_COMMS_TYPE_INCIDENT_RESOLUTION,
		watsonPb.CommsType_COMMS_TYPE_INCIDENT_AUTO_CLOSURE:
		return nil
	default:
		return errors.New("comms type can only be one of creation, resolution, or auto-closure")

	}
}
