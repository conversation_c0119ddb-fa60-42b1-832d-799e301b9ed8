package watson_info_provider

import (
	"context"
	"strconv"
	"strings"

	"github.com/pkg/errors"

	watsonPb "github.com/epifi/gamma/api/cx/watson"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

const (
	firstNameKey = "{#first_name}"
	fullNameKey  = "{#full_name}"
	dateKey      = "{#date}"
	ticketIdKey  = "{#ticket_id}"
	DateFormat   = "2 January 2006"
	TimeFormat   = "3:04 PM"
)

type DynamicFields struct {
	FirstName string
	FullName  string
	Date      string
	TicketId  string
}

func (d *DynamicFields) replaceE<PERSON><PERSON><PERSON>(text string) string {
	if d.FirstName != "" {
		text = strings.ReplaceAll(text, firstNameKey, d.FirstName)
	}
	if d.FullName != "" {
		text = strings.ReplaceAll(text, fullName<PERSON>ey, d.FullName)
	}
	if d.Date != "" {
		text = strings.ReplaceAll(text, date<PERSON><PERSON>, d.Date)
	}
	if d.TicketId != "" {
		text = strings.ReplaceAll(text, ticketId<PERSON><PERSON>, d.TicketId)
	}
	return text
}

func (w *WatsonInfoProvider) fetchDynamicFields(ctx context.Context, incident *watsonPb.IncidentDetailsForClient, ticketId int64) (*DynamicFields, error) {
	fields := &DynamicFields{}
	// Date will be in following format: "3 March 1973 at 9:46 AM"
	fields.Date = incident.GetIdentifiedAt().AsTime().Format(DateFormat) + " at " + incident.GetIdentifiedAt().AsTime().Format(TimeFormat)
	if ticketId != 0 {
		fields.TicketId = strconv.Itoa(int(ticketId))
	}

	userResp, userErr := w.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: incident.GetActorId()},
	})
	if te := epifigrpc.RPCError(userResp, userErr); te != nil {
		return fields, errors.Wrap(te, "failed to fetch user details")
	}
	fields.FirstName = userResp.GetUser().GetProfile().GetPanName().GetFirstName()
	fields.FullName = userResp.GetUser().GetProfile().GetPanName().ToFirstNameLastNameString()

	return fields, nil
}

func (d *DynamicFields) populateFields(commsDetail *watsonPb.CommsDetail) {
	switch commsDetail.GetDetail().(type) {
	case *watsonPb.CommsDetail_Notification:
		// replace in SystemTray as well as InAppNotifications

		commsTemplate := commsDetail.GetNotification().GetNotification().GetSystemTrayTemplate().GetCommonTemplateFields()
		if commsTemplate != nil {
			commsTemplate.Title = d.replaceEachKey(commsTemplate.Title)
			commsTemplate.Body = d.replaceEachKey(commsTemplate.Body)
		}

		commsTemplate = commsDetail.GetNotification().GetNotification().GetInAppTemplate().GetCommonTemplateFields()
		if commsTemplate != nil {
			commsTemplate.Title = d.replaceEachKey(commsTemplate.Title)
			commsTemplate.Body = d.replaceEachKey(commsTemplate.Body)
		}
	case *watsonPb.CommsDetail_Email:
		commsDetail.GetEmail().Subject = d.replaceEachKey(commsDetail.GetEmail().GetSubject())
		for _, bodyComponent := range commsDetail.GetEmail().GetBody() {
			bodyComponent.Content = d.replaceEachKey(bodyComponent.GetContent())
		}
	}
}
