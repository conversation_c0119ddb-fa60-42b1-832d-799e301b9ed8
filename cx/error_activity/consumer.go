package error_activity

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	consumerPb "github.com/epifi/gamma/api/cx/error_activity"
	"github.com/epifi/gamma/api/event"
	"github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/error_activity/dao"
	"github.com/epifi/gamma/cx/error_activity/entity"
	factory "github.com/epifi/gamma/cx/error_activity/trigger_processor"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

type Consumer struct {
	eventConfigsDao         dao.EventConfigDao
	genConf                 *genconf.Config
	triggerProcessorFactory factory.TriggerProcessorFactory
}

func NewConsumer(eventConfigsDao dao.EventConfigDao, genConf *genconf.Config, triggerProcessorFactory factory.TriggerProcessorFactory) *Consumer {
	return &Consumer{
		eventConfigsDao:         eventConfigsDao,
		genConf:                 genConf,
		triggerProcessorFactory: triggerProcessorFactory,
	}
}

var _ consumerPb.ConsumerServer = &Consumer{}

func (c *Consumer) ProcessEvent(ctx context.Context, event *event.RudderEvent) (*consumerPb.ProcessEventResponse, error) {
	if !c.genConf.ErrorActivityConfig().IsPipingErrorEventToWatsonEnabled() {
		// TODO: Add handling for message not consumed status on consumer group
		return nil, nil
	}
	logger.Debug(ctx, "processing error activity event", zap.String(logger.EVENT_NAME, event.GetEvent()),
		zap.String(logger.ACTOR_ID_V2, event.GetUserId()), zap.Any(logger.PAYLOAD, event.GetProperties().GetFields()))
	validErr := c.validateEvent(event)
	if validErr != nil {
		logger.Error(ctx, "error while validating received event", zap.Error(validErr), zap.String(logger.MESSAGE_ID, event.GetMessageId()),
			zap.String(logger.EVENT_NAME, event.GetEvent()))
		return permanentFailureResp(rpc.StatusInvalidArgument()), nil
	}

	incidentDetails, err := c.getIncidentDetails(ctx, event)
	if err != nil {
		logger.Error(ctx, "error while fetching incident details for event", zap.Error(err),
			zap.String(logger.EVENT_NAME, event.GetEvent()), zap.String(logger.USER_ID, event.GetUserId()))
		// if config is not found for given event we will discard the event
		// because there is no guarantee we will found config on retrying
		// hence, we are making a decision to discard an event if config is not defined for it
		switch {
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			return permanentFailureResp(rpc.StatusInvalidArgument()), nil
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return permanentFailureResp(rpc.StatusRecordNotFound()), nil
		default:
			return transientFailureResp(rpc.StatusInternal()), nil
		}
	}
	ctx = epificontext.CtxWithActorId(ctx, incidentDetails.ActorId)

	processor, err := c.triggerProcessorFactory.GetTriggerProcessor(incidentDetails.EventTrigger)
	if err != nil {
		logger.Error(ctx, "no processor found for given event trigger", zap.String(logger.TRIGGER, incidentDetails.EventTrigger.String()), zap.Error(err))
		return permanentFailureResp(rpc.StatusInvalidArgument()), nil
	}
	err = processor.ProcessEventTrigger(ctx, incidentDetails)
	if err != nil {
		logger.Error(ctx, "error while processing event trigger", zap.Error(err), zap.String(logger.MESSAGE_ID, event.GetMessageId()),
			zap.String(logger.TRIGGER, incidentDetails.EventTrigger.String()))
		switch {
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			return permanentFailureResp(rpc.StatusInvalidArgument()), nil
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return permanentFailureResp(rpc.StatusRecordNotFound()), nil
		// if there was already an incident triggered for user's issue and cool-off has not yet expired throw already exists
		case errors.Is(err, epifierrors.ErrAlreadyExists):
			return permanentFailureResp(rpc.StatusAlreadyExists()), nil
		default:
			return transientFailureResp(rpc.StatusInternal()), nil
		}
	}
	logger.Info(ctx, "event trigger processed successfully", zap.String(logger.MESSAGE_ID, event.GetMessageId()),
		zap.String(logger.TRIGGER, incidentDetails.EventTrigger.String()))
	return successResp(), nil
}

func (c *Consumer) getEventTrigger(ctx context.Context, event *event.RudderEvent) (consumerPb.EventTrigger, error) {
	isResolutionEventKey := c.genConf.ErrorActivityConfig().IsResolutionEventBooleanEventPayloadKey()
	// try to determine event trigger from event payload
	isResolutionEvent, isResolutionKeyPresent := event.GetProperties().GetFields()[isResolutionEventKey]
	if isResolutionKeyPresent {
		if isResolutionEvent.GetBoolValue() {
			return consumerPb.EventTrigger_EVENT_TRIGGER_RESOLVE_WATSON_INCIDENT, nil
		} else {
			return consumerPb.EventTrigger_EVENT_TRIGGER_CREATE_WATSON_INCIDENT, nil
		}
	}

	// if we cannot determine event trigger from payload, fetch event config
	eventTriggerConfig, err := c.eventConfigsDao.GetByEventNameAndConfigType(ctx, event.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_EVENT_TRIGGER)
	if err != nil {
		return 0, errors.Wrap(err, "cannot fetch event trigger config")
	}
	return eventTriggerConfig.GetConfigPayload().GetEventTrigger(), nil
}

func (c *Consumer) getIssueCategoryId(ctx context.Context, event *event.RudderEvent) (string, error) {
	issueCategoryIdKey := c.genConf.ErrorActivityConfig().IssueCategoryIdEventPayloadKey()
	// try to determine issue category id from event payload
	issueCategoryId := event.GetProperties().GetFields()[issueCategoryIdKey].GetStringValue()
	if issueCategoryId != "" {
		return issueCategoryId, nil
	}

	// if we cannot determine issue category id from payload, fetch event config
	issueCategoryConfig, err := c.eventConfigsDao.GetByEventNameAndConfigType(ctx, event.GetEvent(), consumerPb.ConfigType_CONFIG_TYPE_ISSUE_CATEGORY_ID)
	if err != nil {
		return "", errors.Wrap(err, "error while fetching issue category config")
	}
	return issueCategoryConfig.GetConfigPayload().GetIssueCategoryId(), nil
}

func (c *Consumer) getClientRequestId(event *event.RudderEvent) string {
	clientRequestIdKey := c.genConf.ErrorActivityConfig().ClientRequestIdEventPayloadKey()
	// if clientRequestId is passed in event payload use it, otherwise messageId will be used as clientRequestId
	clientRequestId := event.GetProperties().GetFields()[clientRequestIdKey].GetStringValue()
	if clientRequestId != "" {
		return clientRequestId
	}
	return event.GetMessageId()
}

func (c *Consumer) getIncidentDetails(ctx context.Context, event *event.RudderEvent) (*entity.IncidentDetails, error) {
	eventTrigger, err := c.getEventTrigger(ctx, event)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get event trigger")
	}
	clientRequestId := c.getClientRequestId(event)
	issueCategoryId, err := c.getIssueCategoryId(ctx, event)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get event trigger")
	}

	// not handling error as we already checked timestamp parsing while validating event
	eventReceivedAt, _ := time.Parse(time.RFC3339, event.GetTimestamp())
	return &entity.IncidentDetails{
		ClientRequestId: clientRequestId,
		ActorId:         event.GetUserId(), // userId field in event payload is actorId
		EventTrigger:    eventTrigger,
		IssueCategoryId: issueCategoryId,
		ReceivedAt:      timestamppb.New(eventReceivedAt),
	}, nil
}

func (c *Consumer) validateEvent(rudderEvent *event.RudderEvent) error {
	switch {
	case rudderEvent.GetMessageId() == "":
		return errors.New("message id is required")
	case rudderEvent.GetEvent() == "":
		return errors.New("event name is required")
	case rudderEvent.GetUserId() == "":
		return errors.New("user id is required")
	}

	_, err := time.Parse(time.RFC3339, rudderEvent.GetTimestamp())
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("invalid timestamp provided: %v", rudderEvent.GetTimestamp()))
	}
	return nil
}
