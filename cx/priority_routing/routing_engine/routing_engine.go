package routing_engine

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/pkg/errors"

	priorityPb "github.com/epifi/gamma/api/cx/priority_routing"
	"github.com/epifi/gamma/cx/priority_routing/routing_engine/processor"
)

type IRoutingEngine interface {
	GetUserPriority(ctx context.Context, actorId string) (priorityPb.UserCategory, error)
}
type RoutingEngine struct {
	factory processor.IFactory
}

func NewRoutingEngine(factory processor.IFactory) *RoutingEngine {
	return &RoutingEngine{
		factory: factory,
	}
}

func (r *RoutingEngine) GetUserPriority(ctx context.Context, actorId string) (priorityPb.UserCategory, error) {

	if actorId == "" {
		return priorityPb.UserCategory_CATEGORY_UNSPECIFIED, errors.New("actorID is empty")
	}
	categories := r.getOrderedCategories()

	for _, category := range categories {
		ruleProcessor, err := r.factory.GetRuleProcessor(category)
		if err != nil {
			return priorityPb.UserCategory_CATEGORY_UNSPECIFIED, errors.Wrap(err, "error on fetching RuleProcessor")
		}

		isApplicable, err := ruleProcessor.IsUserCategoryApplicable(ctx, actorId)
		if err != nil {
			return priorityPb.UserCategory_CATEGORY_UNSPECIFIED, errors.Wrap(err, "error while checking user category eligibility")
		}

		if isApplicable == commontypes.BooleanEnum_TRUE {
			return category, nil
		}
	}

	return priorityPb.UserCategory_CATEGORY_UNSPECIFIED, nil
}

func (r *RoutingEngine) getOrderedCategories() []priorityPb.UserCategory {
	return []priorityPb.UserCategory{
		priorityPb.UserCategory_SALARY_PROGRAM_USERS,
		priorityPb.UserCategory_CURRENTLY_ONBOARDING_USERS,
		priorityPb.UserCategory_HIGH_PRIORITY_ACCOUNT_HOLDERS,
		priorityPb.UserCategory_LOW_PRIORITY_ACCOUNT_HOLDERS,
	}
}
