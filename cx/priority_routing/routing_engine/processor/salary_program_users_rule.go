package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"time"

	"github.com/pkg/errors"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/api/salaryprogram"
)

type SalaryProgramUsersRuleProcessor struct {
	salaryProgramClient salaryprogram.SalaryProgramClient
}

func NewSalaryProgramUsersRuleProcessor(salaryProgramClient salaryprogram.SalaryProgramClient) *SalaryProgramUsersRuleProcessor {
	return &SalaryProgramUsersRuleProcessor{
		salaryProgramClient: salaryProgramClient,
	}
}

var _ RuleProcessor = &SalaryProgramUsersRuleProcessor{}

func (s *SalaryProgramUsersRuleProcessor) IsUserCategoryApplicable(ctx context.Context, actorId string) (commontypes.BooleanEnum, error) {
	// get current reg status
	salaryStatusResp, salaryStatusErr := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &salaryprogram.CurrentRegStatusAndNextRegStageRequest{
		ActorId: actorId, FlowType: salaryprogram.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err := epifigrpc.RPCError(salaryStatusResp, salaryStatusErr); err != nil {
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.Wrap(err, "error while fetching salary program details for user")
	}
	// if salary program status is not complete then return false
	if salaryStatusResp.GetRegistrationStatus() != salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		return commontypes.BooleanEnum_FALSE, nil
	}

	// if salary program status is complete
	// check if user is currently active in salary program
	salaryActivationResp, salaryActivationErr := s.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(ctx, &salaryprogram.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: salaryStatusResp.GetRegistrationId(),
		ActiveAtTime:   timestampPb.New(time.Now()),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user.
		ActivationKind: salaryprogram.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	if te := epifigrpc.RPCError(salaryActivationResp, salaryActivationErr); te != nil {
		// if we encounter record not found that means user is not active in salary program
		if salaryActivationResp.GetStatus().IsRecordNotFound() {
			return commontypes.BooleanEnum_FALSE, nil
		}
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.Wrap(te, "error while determining user's salary activation status")
	}
	// return true when user's salary program status is completed and is currently in active state.
	return commontypes.BooleanEnum_TRUE, nil
}
