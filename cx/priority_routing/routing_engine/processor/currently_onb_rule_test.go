package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mockOnb "github.com/epifi/gamma/api/user/onboarding/mocks"
	mockP "github.com/epifi/gamma/cx/test/mocks/priority_routing/priority_routing_helper"
)

func TestCurrentlyOnbRuleProcessor_IsUserCategoryApplicable(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	mockOnbClient := mockOnb.NewMockOnboardingClient(ctr)
	mockHelper := mockP.NewMockIHelper(ctr)

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		actorId	string
	}
	tests := []struct {
		name	string
		args	args
		want	commontypes.BooleanEnum
		wantErr	bool
	}{
		{
			name:	"CustomerIdentifier returning error",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserByActorId(context.Background(), gomock.Any()).
						Return(nil, errors.New("error while fetching User by actor_id")),
				},
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"Currently OnBoarding User",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-2",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserByActorId(context.Background(), gomock.Any()).
						Return(&userPb.User{
							Profile: &userPb.Profile{Email: "email@abc", PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:	91,
								NationalNumber:	1234567890,
							}},
						}, nil),

					mockOnbClient.EXPECT().GetOnboardedUser(context.Background(), gomock.Any()).
						Return(&onbPb.GetOnboardedUserResponse{
							Status: &rpc.Status{Code: uint32(onbPb.GetOnboardedUserResponse_USER_NOT_ONBOARDED)},
						}, nil),
				},
			},
			want:		commontypes.BooleanEnum_TRUE,
			wantErr:	false,
		},
		{
			name:	"OnBoarded User",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-3",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserByActorId(context.Background(), gomock.Any()).
						Return(&userPb.User{
							Profile: &userPb.Profile{Email: "email@abc", PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:	91,
								NationalNumber:	1234567890,
							}},
						}, nil),

					mockOnbClient.EXPECT().GetOnboardedUser(context.Background(), gomock.Any()).
						Return(&onbPb.GetOnboardedUserResponse{
							Status: rpc.StatusOk(),
						}, nil),
				},
			},
			want:		commontypes.BooleanEnum_FALSE,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewCurrentlyOnbRuleProcessor(mockOnbClient, mockHelper)
			got, err := p.IsUserCategoryApplicable(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsUserCategoryApplicable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsUserCategoryApplicable() got = %v, want %v", got, tt.want)
			}
		})
	}
}
