package processor

import (
	"errors"

	priorityPb "github.com/epifi/gamma/api/cx/priority_routing"
)

var (
	NoProcessorFoundForFlowError = errors.New("no rule processor found for given app flow")
)

type IFactory interface {
	GetRuleProcessor(priorityPb.UserCategory) (RuleProcessor, error)
}

type Factory struct {
	highPriorityRuleProcessor       *HighPriorityRuleProcessor
	lowPriorityRuleProcessor        *LowPriorityRuleProcessor
	currentlyOnbRuleProcessor       *CurrentlyOnbRuleProcessor
	salaryProgramUsersRuleProcessor *SalaryProgramUsersRuleProcessor
}

func NewRuleFactory(highPriorityRuleProcessor *HighPriorityRuleProcessor,
	lowPriorityRuleProcessor *LowPriorityRuleProcessor, currentlyOnbRuleProcessor *CurrentlyOnbRuleProcessor,
	salaryProgramUsersRuleProcessor *SalaryProgramUsersRuleProcessor) *Factory {
	return &Factory{
		highPriorityRuleProcessor:       highPriorityRuleProcessor,
		lowPriorityRuleProcessor:        lowPriorityRuleProcessor,
		currentlyOnbRuleProcessor:       currentlyOnbRuleProcessor,
		salaryProgramUsersRuleProcessor: salaryProgramUsersRuleProcessor,
	}
}

var _ IFactory = &Factory{}

func (f *Factory) GetRuleProcessor(userCategory priorityPb.UserCategory) (RuleProcessor, error) {

	switch userCategory {
	case priorityPb.UserCategory_HIGH_PRIORITY_ACCOUNT_HOLDERS:
		return f.highPriorityRuleProcessor, nil
	case priorityPb.UserCategory_LOW_PRIORITY_ACCOUNT_HOLDERS:
		return f.lowPriorityRuleProcessor, nil
	case priorityPb.UserCategory_CURRENTLY_ONBOARDING_USERS:
		return f.currentlyOnbRuleProcessor, nil
	case priorityPb.UserCategory_SALARY_PROGRAM_USERS:
		return f.salaryProgramUsersRuleProcessor, nil
	default:
		return nil, NoProcessorFoundForFlowError
	}
}
