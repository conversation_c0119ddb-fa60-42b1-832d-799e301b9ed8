package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/salaryprogram/mocks"
)

func TestSalaryProgramUsersRuleProcessor_IsUserCategoryApplicable(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockSalaryProgramClient := mocks.NewMockSalaryProgramClient(ctr)

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		actorId	string
	}
	tests := []struct {
		name	string
		args	args
		want	commontypes.BooleanEnum
		wantErr	bool
	}{
		{
			name:	"error while calling salary program rpc",
			args: args{
				mocks: []interface{}{
					mockSalaryProgramClient.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&salaryprogram.CurrentRegStatusAndNextRegStageResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act101",
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"registration status not completed",
			args: args{
				mocks: []interface{}{
					mockSalaryProgramClient.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&salaryprogram.CurrentRegStatusAndNextRegStageResponse{
						Status:			rpcPb.StatusOk(),
						RegistrationStatus:	salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_INITIATED,
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act101",
			},
			want:		commontypes.BooleanEnum_FALSE,
			wantErr:	false,
		},
		{
			name:	"error while calling last active rpc",
			args: args{
				mocks: []interface{}{
					mockSalaryProgramClient.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&salaryprogram.CurrentRegStatusAndNextRegStageResponse{
						Status:			rpcPb.StatusOk(),
						RegistrationStatus:	salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil),
					mockSalaryProgramClient.EXPECT().GetLatestActivationDetailsActiveAtTime(gomock.Any(), gomock.Any()).Return(&salaryprogram.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act101",
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"record not found",
			args: args{
				mocks: []interface{}{
					mockSalaryProgramClient.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&salaryprogram.CurrentRegStatusAndNextRegStageResponse{
						Status:			rpcPb.StatusOk(),
						RegistrationStatus:	salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil),
					mockSalaryProgramClient.EXPECT().GetLatestActivationDetailsActiveAtTime(gomock.Any(), gomock.Any()).Return(&salaryprogram.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act101",
			},
			want:		commontypes.BooleanEnum_FALSE,
			wantErr:	false,
		},
		{
			name:	"salary program active",
			args: args{
				mocks: []interface{}{
					mockSalaryProgramClient.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&salaryprogram.CurrentRegStatusAndNextRegStageResponse{
						Status:			rpcPb.StatusOk(),
						RegistrationStatus:	salaryprogram.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil),
					mockSalaryProgramClient.EXPECT().GetLatestActivationDetailsActiveAtTime(gomock.Any(), gomock.Any()).Return(&salaryprogram.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpcPb.StatusOk(),
					}, nil),
				},
				ctx:		context.Background(),
				actorId:	"act101",
			},
			want:		commontypes.BooleanEnum_TRUE,
			wantErr:	false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewSalaryProgramUsersRuleProcessor(mockSalaryProgramClient)
			got, err := p.IsUserCategoryApplicable(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsUserCategoryApplicable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsUserCategoryApplicable() got = %v, want %v", got, tt.want)
			}
		})
	}
}
