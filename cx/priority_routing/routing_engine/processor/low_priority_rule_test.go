package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	userPriorityDataPb "github.com/epifi/gamma/api/cx/priority_routing"
	"github.com/epifi/gamma/cx/config"
	mockP "github.com/epifi/gamma/cx/test/mocks/priority_routing/priority_routing_helper"
)

type LowPriorityTestSuite struct {
	priorityConfig *config.PriorityRoutingConfig
}

var (
	lpts				LowPriorityTestSuite
	userPriorityDataResponseA	= &userPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days:	0,
		FdAvgBalLast_28Days:		0,
		SmartAvgBalLast_28Days:		0,
		AccountBalance:			0,
		BalancePercentile:		5.25,
		CurrentDepositBalance:		00,
		VkycStatus:			"",
		OpenDateIst:			nil,
	}

	userPriorityDataResponseB	= &userPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days:	1000,
		FdAvgBalLast_28Days:		2000,
		SmartAvgBalLast_28Days:		2000,
		AccountBalance:			100,
		BalancePercentile:		80.50,
		CurrentDepositBalance:		0,
		VkycStatus:			"",
		OpenDateIst:			nil,
	}
)

func TestLowPriorityRuleProcessor_IsUserCategoryApplicable(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockHelper := mockP.NewMockIHelper(ctr)

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		actorId	string
	}
	tests := []struct {
		name	string
		args	args
		want	commontypes.BooleanEnum
		wantErr	bool
	}{
		{
			name:	"priorityRoutingHelper returning error",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserPriorityData(context.Background(), gomock.Any()).
						Return(nil, errors.New("error while fetching UserPriorityData")),
				},
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"User totalBalance more than threshold",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-2",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserPriorityData(context.Background(), gomock.Any()).
						Return(userPriorityDataResponseB, nil),
				},
			},
			want:		commontypes.BooleanEnum_FALSE,
			wantErr:	false,
		},
		{
			name:	"User totalBalance less than threshold",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-3",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserPriorityData(context.Background(), gomock.Any()).
						Return(userPriorityDataResponseA, nil),
				},
			},
			want:		commontypes.BooleanEnum_TRUE,
			wantErr:	false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewLowPriorityRuleProcessor(mockHelper, hpts.priorityConfig)
			got, err := p.IsUserCategoryApplicable(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsUserCategoryApplicable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsUserCategoryApplicable() got = %v, want %v", got, tt.want)
			}
		})
	}
}
