package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"

	UserPriorityDataPb "github.com/epifi/gamma/api/cx/priority_routing"
	"github.com/epifi/gamma/cx/config"
	mockP "github.com/epifi/gamma/cx/test/mocks/priority_routing/priority_routing_helper"
)

type HighPriorityTestSuite struct {
	priorityConfig *config.PriorityRoutingConfig
}

var (
	hpts				HighPriorityTestSuite
	userPriorityDataResponse1	= &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days:	10050,
		FdAvgBalLast_28Days:		510,
		SmartAvgBalLast_28Days:		0,
		AccountBalance:			10050,
		BalancePercentile:		99.25,
		CurrentDepositBalance:		3850,
		VkycStatus:			"",
		OpenDateIst:			nil,
	}

	userPriorityDataResponse2	= &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days:	100,
		FdAvgBalLast_28Days:		0,
		SmartAvgBalLast_28Days:		0,
		AccountBalance:			100,
		BalancePercentile:		80.50,
		CurrentDepositBalance:		0,
		VkycStatus:			"",
		OpenDateIst:			nil,
	}
)

func TestHighPriorityRuleProcessor_IsUserCategoryApplicable(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	mockHelper := mockP.NewMockIHelper(ctr)

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		actorId	string
	}
	tests := []struct {
		name	string
		args	args
		want	commontypes.BooleanEnum
		wantErr	bool
	}{
		{
			name:	"priorityRoutingHelper returning error",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-1",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserPriorityData(context.Background(), gomock.Any()).
						Return(nil, errors.New("error while fetching UserPriorityData")),
				},
			},
			want:		commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr:	true,
		},
		{
			name:	"HighPriority User - TotalBalance greater than threshold",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-2",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserPriorityData(context.Background(), gomock.Any()).
						Return(userPriorityDataResponse1, nil),
				},
			},
			want:		commontypes.BooleanEnum_TRUE,
			wantErr:	false,
		},
		{
			name:	"User totalBalance less than threshold",
			args: args{
				ctx:		context.Background(),
				actorId:	"test-actor-3",
				mocks: []interface{}{
					mockHelper.EXPECT().GetUserPriorityData(context.Background(), gomock.Any()).
						Return(userPriorityDataResponse2, nil),
				},
			},
			want:		commontypes.BooleanEnum_FALSE,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewHighPriorityRuleProcessor(mockHelper, hpts.priorityConfig)
			got, err := p.IsUserCategoryApplicable(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsUserCategoryApplicable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsUserCategoryApplicable() got = %v, want %v", got, tt.want)
			}
		})
	}
}
