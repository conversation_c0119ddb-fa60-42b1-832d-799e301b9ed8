package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/pkg/errors"

	onBoardingPb "github.com/epifi/gamma/api/user/onboarding"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/cx/priority_routing/priority_routing_helper"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type CurrentlyOnbRuleProcessor struct {
	onbClient             onbPb.OnboardingClient
	priorityRoutingHelper priority_routing_helper.IHelper
}

func NewCurrentlyOnbRuleProcessor(onbClient onbPb.OnboardingClient, priorityRoutingHelper priority_routing_helper.IHelper) *CurrentlyOnbRuleProcessor {
	return &CurrentlyOnbRuleProcessor{
		onbClient:             onbClient,
		priorityRoutingHelper: priorityRoutingHelper,
	}
}

var _ RuleProcessor = &CurrentlyOnbRuleProcessor{}

func (p *CurrentlyOnbRuleProcessor) IsUserCategoryApplicable(ctx context.Context, actorId string) (commontypes.BooleanEnum, error) {

	user, err := p.priorityRoutingHelper.GetUserByActorId(ctx, actorId)
	if err != nil {
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.Wrap(err, "error while fetching User by actor_id")
	}

	phoneNumber := user.GetProfile().GetPhoneNumber()

	// check if user has completed onBoarding
	onbResp, err := p.onbClient.GetOnboardedUser(ctx, &onBoardingPb.GetOnboardedUserRequest{
		Identifier: &onBoardingPb.GetOnboardedUserRequest_PhoneNumber{
			PhoneNumber: phoneNumber,
		},
	})

	if err = epifigrpc.RPCError(onbResp, err); err != nil {
		// onBoarding is not complete
		if onbResp.GetStatus().GetCode() == uint32(onBoardingPb.GetOnboardedUserResponse_USER_NOT_ONBOARDED) {
			return commontypes.BooleanEnum_TRUE, nil
		}
	}

	// User exists and is onboarded successfully
	return commontypes.BooleanEnum_FALSE, nil
}
