// nolint:dupl
package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/pkg/errors"

	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/priority_routing/priority_routing_helper"
)

type HighPriorityRuleProcessor struct {
	priorityRoutingHelper priority_routing_helper.IHelper
	priorityConfig        *config.PriorityRoutingConfig
}

func NewHighPriorityRuleProcessor(priorityRoutingHelper priority_routing_helper.IHelper,
	priorityConfig *config.PriorityRoutingConfig) *HighPriorityRuleProcessor {
	return &HighPriorityRuleProcessor{
		priorityRoutingHelper: priorityRoutingHelper,
		priorityConfig:        priorityConfig,
	}
}

var _ RuleProcessor = &HighPriorityRuleProcessor{}

func (p *HighPriorityRuleProcessor) IsUserCategoryApplicable(ctx context.Context, actorId string) (commontypes.BooleanEnum, error) {

	data, err := p.priorityRoutingHelper.GetUserPriorityData(ctx, actorId)
	if err != nil {
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.Wrap(err, "error while fetching userProperties from dao")
	}

	totalBalance := data.GetSavingAvgBalLast_28Days() + data.GetSmartAvgBalLast_28Days() + data.GetFdAvgBalLast_28Days()
	if totalBalance >= float32(p.priorityConfig.ThresholdBalance) {
		return commontypes.BooleanEnum_TRUE, nil
	}
	return commontypes.BooleanEnum_FALSE, nil
}
