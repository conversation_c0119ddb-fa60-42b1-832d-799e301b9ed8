package routing_engine

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	priorityPb "github.com/epifi/gamma/api/cx/priority_routing"
	"github.com/epifi/gamma/cx/test"
	mocks "github.com/epifi/gamma/cx/test/mocks/priority_routing/routing_engine/processor"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, teardown = test.InitTestServer(false)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestRoutingEngine_GetUserPriority(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)

	mockFactory := mocks.NewMockIFactory(ctr)
	mockRuleProcessor := mocks.NewMockRuleProcessor(ctr)

	type args struct {
		mocks   []interface{}
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    priorityPb.UserCategory
		wantErr bool
	}{
		{
			name: "error while getting rule processor",
			args: args{
				ctx:     context.Background(),
				actorId: "test-actor-1",
				mocks: []interface{}{
					mockFactory.EXPECT().GetRuleProcessor(gomock.Any()).
						Return(nil, errors.New("test")),
				},
			},
			want:    priorityPb.UserCategory_CATEGORY_UNSPECIFIED,
			wantErr: true,
		},

		{
			name: "error while processing",
			args: args{
				ctx:     context.Background(),
				actorId: "test-actor-2",
				mocks: []interface{}{
					mockFactory.EXPECT().GetRuleProcessor(gomock.Any()).
						Return(mockRuleProcessor, nil),
					mockRuleProcessor.EXPECT().IsUserCategoryApplicable(context.Background(), gomock.Any()).
						Return(commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.New("test")),
				},
			},
			want:    priorityPb.UserCategory_CATEGORY_UNSPECIFIED,
			wantErr: true,
		},

		{
			name: "Category Applicable",
			args: args{
				ctx:     context.Background(),
				actorId: "test-actor-3",
				mocks: []interface{}{
					mockFactory.EXPECT().GetRuleProcessor(gomock.Any()).
						Return(mockRuleProcessor, nil),
					mockRuleProcessor.EXPECT().IsUserCategoryApplicable(context.Background(), gomock.Any()).
						Return(commontypes.BooleanEnum_TRUE, nil),
				},
			},
			want:    priorityPb.UserCategory_SALARY_PROGRAM_USERS,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := NewRoutingEngine(mockFactory)
			got, err := r.GetUserPriority(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserPriority() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetUserPriority() got = %v, want %v", got, tt.want)
			}
		})
	}
}
