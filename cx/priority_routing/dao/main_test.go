package dao

import (
	"flag"
	"os"
	"testing"

	"github.com/redis/go-redis/v9"

	"github.com/epifi/gamma/cx/priority_routing/test"
)

var (
	redisClient *redis.Client
	teardown    func()
)

func TestMain(m *testing.M) {

	flag.Parse()
	redisClient, teardown = test.InitTestServer()

	// init redis dao test suite
	redisUserPriorityDao := NewUserPriorityPropertiesDao(redisClient)
	rdts = newRedisDaoTestSuite(redisUserPriorityDao, redisClient)

	exitCode := m.Run()
	// os.Exit does not respects deferred functions.
	teardown()
	os.Exit(exitCode)
}
