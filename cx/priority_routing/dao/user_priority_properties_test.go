package dao

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/testing/protocmp"

	UserPriorityDataPb "github.com/epifi/gamma/api/cx/priority_routing"
)

var (
	rdts *redisDaoTestSuite
)

type redisDaoTestSuite struct {
	redisDao    IUserPriorityPropertiesDAO
	redisClient *redis.Client
}

func newRedisDaoTestSuite(redisDao IUserPriorityPropertiesDAO, redisClient *redis.Client) *redisDaoTestSuite {
	return &redisDaoTestSuite{redisDao: redisDao, redisClient: redisClient}
}

var (
	redisDataReq1 = map[string]interface{}{
		"saving_avg_bal_last_28_days": "10500",
		"account_balance":             "10500",
		"balance_percentile":          "50.5",
		"open_date_ist":               "2022-01-19 19:41:06.759",
	}

	redisDataResp1 = &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: float32(10500),
		FdAvgBalLast_28Days:     0,
		SmartAvgBalLast_28Days:  0,
		AccountBalance:          10500,
		BalancePercentile:       50.5,
		CurrentDepositBalance:   0,
		VkycStatus:              "",
		OpenDateIst:             nil,
	}

	redisDataReq2 = map[string]interface{}{
		"saving_avg_bal_last_28_days": "100",
		"account_balance":             "100",
		"smart_avg_bal_last_28_days":  "0",
	}

	redisDataResp2 = &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: 100,
		SmartAvgBalLast_28Days:  0,
		AccountBalance:          100,
	}

	redisDataReq3 = map[string]interface{}{
		"saving_avg_bal_last_28_days": "100",
		"fd_avg_bal_last_28_days":     "0",
		"smart_avg_bal_last_28_days":  "0",
		"account_balance":             "100",
		"balance_percentile":          "55",
		"current_deposit_balance":     "0",
		"vkyc_status":                 "VKYC_SUMMARY_STATUS_REGISTERED",
		"open_date_ist":               "2022-01-23 16:21:40.273",
	}

	redisDataResp3 = &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: 100,
		FdAvgBalLast_28Days:     0,
		SmartAvgBalLast_28Days:  0,
		AccountBalance:          100,
		BalancePercentile:       55,
		CurrentDepositBalance:   0,
		VkycStatus:              "VKYC_SUMMARY_STATUS_REGISTERED",
		OpenDateIst:             nil,
	}

	redisDataReq4 = map[string]interface{}{
		"saving_avg_bal_last_28_days": "100",
		"is_salaried":                 "true",
	}

	redisDataResp4 = &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: 100,
	}

	redisDataReq5 = map[string]interface{}{
		"saving_avg_bal_last_28_days": "100",
		"open_date_ist":               "abc",
	}
	redisDataResp5 = &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: 100,
	}

	redisDataReq6 = map[string]interface{}{
		"saving_avg_bal_last_28_days": "100",
		"open_date_ist":               "2022-01-19 19:41:06.7",
	}
	redisDataResp6 = &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: 100,
	}

	redisDataReq7 = map[string]interface{}{
		"saving_avg_bal_last_28_days": "100",
		"open_date_ist":               "2022-01-19 19:41:06.75",
	}
	redisDataResp7 = &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: 100,
	}
)

func TestUserPriorityPropertiesDao_GetByActorId(t *testing.T) {

	type args struct {
		ctx      context.Context
		actorId  string
		dbFlag   bool
		data     map[string]interface{}
		dateFlag bool
	}
	getDataTcs := []struct {
		name    string
		args    args
		wantErr bool
		want    *UserPriorityDataPb.UserPriorityData
	}{
		{
			name: "Empty Actor Id",
			args: args{
				ctx:     context.Background(),
				actorId: "",
			},
			wantErr: true,
		},
		{
			name: "Record Not Found In Redis",
			args: args{
				ctx:     context.Background(),
				actorId: "test-actor-0",
			},
			wantErr: true,
		},
		{
			name: "Record with open_date_ist Field",
			args: args{
				ctx:      context.Background(),
				actorId:  "test-actor-1",
				dbFlag:   true,
				data:     redisDataReq1,
				dateFlag: true,
			},
			wantErr: false,
			want:    redisDataResp1,
		},
		{
			name: "Record without open_date_ist field",
			args: args{
				ctx:      context.Background(),
				actorId:  "test-actor-2",
				dbFlag:   true,
				data:     redisDataReq2,
				dateFlag: false,
			},
			wantErr: false,
			want:    redisDataResp2,
		},
		{
			name: "Record with all fields",
			args: args{
				ctx:      context.Background(),
				actorId:  "test-actor-3",
				dbFlag:   true,
				data:     redisDataReq3,
				dateFlag: true,
			},
			wantErr: false,
			want:    redisDataResp3,
		},
		{
			name: "Record with extra field",
			args: args{
				ctx:     context.Background(),
				actorId: "test-actor-4",
				dbFlag:  true,
				data:    redisDataReq4,
			},
			wantErr: false,
			want:    redisDataResp4,
		},
		{
			name: "Record with false date field",
			args: args{
				ctx:     context.Background(),
				actorId: "test-actor-5",
				dbFlag:  true,
				data:    redisDataReq5,
			},
			wantErr: false,
			want:    redisDataResp5,
		},
		{
			name: "Record with date field with layout of type 1",
			args: args{
				ctx:      context.Background(),
				actorId:  "test-actor-6",
				dbFlag:   true,
				data:     redisDataReq6,
				dateFlag: true,
			},
			wantErr: false,
			want:    redisDataResp6,
		},
		{
			name: "Record with date field with layout of type 2",
			args: args{
				ctx:      context.Background(),
				actorId:  "test-actor-7",
				dbFlag:   true,
				data:     redisDataReq7,
				dateFlag: true,
			},
			wantErr: false,
			want:    redisDataResp7,
		},
	}

	// nolint: scopelint
	for _, tc := range getDataTcs {
		t.Run(tc.name, func(t *testing.T) {

			if tc.args.dbFlag == true {
				rdts.redisClient.FlushAll(tc.args.ctx)
				rdts.redisClient.HSet(tc.args.ctx, redisKeyMatchPrefix+tc.args.actorId, tc.args.data)
			}

			got, err := rdts.redisDao.GetByActorId(tc.args.ctx, tc.args.actorId)

			if (err != nil) != tc.wantErr {
				t.Errorf("failed on key : %v, err : %v", tc.args.actorId, err)
				return
			}

			if tc.args.dateFlag {
				tc.want.OpenDateIst = got.OpenDateIst
			}

			if diff := cmp.Diff(got, tc.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetByActorId() got = %v, want %v", got, tc.want)
			}

		})
	}

}
