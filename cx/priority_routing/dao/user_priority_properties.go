package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	UserPriorityDataPb "github.com/epifi/gamma/api/cx/priority_routing"
	cxLogger "github.com/epifi/gamma/cx/logger"
	cxTypes "github.com/epifi/gamma/cx/wire/types"
)

type UserPriorityPropertiesDao struct {
	redisClient *redis.Client
}

func NewUserPriorityPropertiesDao(redisClient cxTypes.CxRedisStore) *UserPriorityPropertiesDao {
	return &UserPriorityPropertiesDao{
		redisClient: redisClient,
	}
}

const (
	redisKeyMatchPrefix = "user_priority_agg_metrics:"
)

var (
	SupportedLayouts = []string{
		"2006-01-02 15:04:05.000",
		"2006-01-02 15:04:05.00",
		"2006-01-02 15:04:05.0",
		"2006-01-02 15:04:05.0000",
	}
)

var _ IUserPriorityPropertiesDAO = &UserPriorityPropertiesDao{}

func (u UserPriorityPropertiesDao) GetByActorId(ctx context.Context, actorId string) (*UserPriorityDataPb.UserPriorityData, error) {
	defer metric_util.TrackDuration("cx/priority_routing/dao", "UserPriorityPropertiesDao", "GetByActorId", time.Now())

	if actorId == "" {
		return nil, errors.New("actorId is empty")
	}

	redisKey := GetKeyForRedis(actorId)
	resp, dbErr := u.redisClient.HGetAll(ctx, redisKey).Result()

	if dbErr != nil {
		return nil, errors.Wrap(dbErr, "failed to fetch data")
	} else if len(resp) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	redisRespByteString, marshalErr := json.Marshal(resp)
	if marshalErr != nil {
		return nil, errors.Wrap(marshalErr, "error while marshaling redis response to byteString")
	}

	redisData := &UserPriorityDataPb.UserPriorityDataRedisObject{}
	protoUnMarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err := protoUnMarshaller.Unmarshal(redisRespByteString, redisData); err != nil {
		return nil, errors.Wrap(err, "Error while un-marshaling redisResponse byteString to proto message")
	}

	UserPriorityDataMsg, err := NewUserPriorityDataMessage(ctx, redisData)
	if err != nil {
		return nil, errors.Wrap(err, "Error in converting to priority data proto msg format")
	}
	return UserPriorityDataMsg, nil
}

func GetKeyForRedis(actorId string) string {
	return redisKeyMatchPrefix + actorId
}

func NewUserPriorityDataMessage(ctx context.Context, msg *UserPriorityDataPb.UserPriorityDataRedisObject) (*UserPriorityDataPb.UserPriorityData, error) {
	userPriorityData := &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: msg.SavingAvgBalLast_28Days,
		FdAvgBalLast_28Days:     msg.FdAvgBalLast_28Days,
		SmartAvgBalLast_28Days:  msg.SmartAvgBalLast_28Days,
		AccountBalance:          msg.AccountBalance,
		BalancePercentile:       msg.BalancePercentile,
		CurrentDepositBalance:   msg.CurrentDepositBalance,
		VkycStatus:              msg.VkycStatus,
	}

	if msg.GetOpenDateIst() != "" {
		openDate, err := parseDateTime(msg.GetOpenDateIst())
		if err != nil {
			cxLogger.Error(ctx, "error in parsing open_date_ist", zap.Error(err))
		}
		userPriorityData.OpenDateIst = openDate
	}
	return userPriorityData, nil
}

// parseDateTime parses time from a given string on a best effort basis by checking for all supported layouts
func parseDateTime(value string) (*timestamppb.Timestamp, error) {
	for _, layout := range SupportedLayouts {
		ts, err := datetime.ParseStringTimestampProtoInLocation(layout, value, datetime.IST)
		if err == nil {
			return ts, nil
		}
	}

	return nil, fmt.Errorf("unable to parse timestamp %s layout not supported", value)
}
