package priority_routing_helper

import (
	"context"

	"github.com/pkg/errors"

	actorPb "github.com/epifi/gamma/api/actor"
	UserPriorityDataPb "github.com/epifi/gamma/api/cx/priority_routing"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/cx/priority_routing/dao"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type IHelper interface {
	GetUserPriorityData(ctx context.Context, actorId string) (*UserPriorityDataPb.UserPriorityData, error)
	GetUserByActorId(ctx context.Context, actorId string) (*userPb.User, error)
}

type Helper struct {
	priorityDAO dao.IUserPriorityPropertiesDAO
	userClient  userPb.UsersClient
	actorClient actorPb.ActorClient
}

func NewPriorityDataHelper(priorityDAO dao.IUserPriorityPropertiesDAO, userClient userPb.UsersClient, actorClient actorPb.ActorClient) *Helper {
	return &Helper{
		priorityDAO: priorityDAO,
		userClient:  userClient,
		actorClient: actorClient,
	}
}

var ErrorUserNotFound = errors.New("user not found")
var ErrorActorNotFound = errors.New("actor not found")

var _ IHelper = &Helper{}

func (h *Helper) GetUserPriorityData(ctx context.Context, actorId string) (*UserPriorityDataPb.UserPriorityData, error) {

	if actorId == "" {
		return nil, errors.New("actorID is empty")
	}

	data, err := h.priorityDAO.GetByActorId(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching userProperties from dao")
	}
	return data, nil
}

// this method will fetch user details using actor id
// will return ErrorActorNotFound if we get RecordNotFound status from actor service
// will return ErrorUserNotFound if we get RecordNotFound status from user service
// will return non nil error for other errors
func (h *Helper) GetUserByActorId(ctx context.Context, actorId string) (*userPb.User, error) {
	if actorId == "" {
		return nil, errors.New("actor id is mandatory to get user via actor id")
	}
	// fetch actor details first to get entity id
	actorResp, err := h.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		if actorResp.GetStatus().IsRecordNotFound() {
			return nil, ErrorActorNotFound
		}
		return nil, errors.Wrap(te, "error while fetching actor")
	}

	// Get user by entity id
	userResp, err := h.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{Id: actorResp.GetActor().GetEntityId()},
	})

	if te := epifigrpc.RPCError(userResp, err); te != nil {
		if userResp.GetStatus().IsRecordNotFound() {
			return nil, ErrorUserNotFound
		}
		return nil, errors.Wrap(te, "error while fetching user")
	}
	return userResp.GetUser(), nil
}
