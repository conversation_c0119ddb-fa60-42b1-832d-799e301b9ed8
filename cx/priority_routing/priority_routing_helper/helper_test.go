package priority_routing_helper

import (
	"context"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	mocks2 "github.com/epifi/gamma/api/actor/mocks"
	UserPriorityDataPb "github.com/epifi/gamma/api/cx/priority_routing"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/cx/test"
	mocks "github.com/epifi/gamma/cx/test/mocks/priority_routing/dao"
)

type GetUserPriorityDataTestSuite struct {
}

var (
	userPriorityDataResponse = &UserPriorityDataPb.UserPriorityData{
		SavingAvgBalLast_28Days: 10050,
		FdAvgBalLast_28Days:     510,
		SmartAvgBalLast_28Days:  0,
		AccountBalance:          10050,
		BalancePercentile:       99.25,
		CurrentDepositBalance:   3850,
		VkycStatus:              "",
		OpenDateIst:             nil,
	}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, teardown = test.InitTestServer(false)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestHelper_GetUserPriorityData(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)

	mockDao := mocks.NewMockIUserPriorityPropertiesDAO(ctr)
	mockUserClient := mockUser.NewMockUsersClient(ctr)
	mockActorClient := mocks2.NewMockActorClient(ctr)

	type args struct {
		mocks   []interface{}
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    *UserPriorityDataPb.UserPriorityData
		wantErr bool
	}{
		{
			name: "ActorID is empty",
			args: args{
				ctx:     context.Background(),
				actorId: "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Dao Returning Error",
			args: args{
				ctx:     context.Background(),
				actorId: "test-actor-1",
				mocks: []interface{}{
					mockDao.EXPECT().GetByActorId(context.Background(), gomock.Any()).
						Return(nil, errors.New("Error recieved from dao")),
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Fetch UserPriorityData successfully",
			args: args{
				ctx:     context.Background(),
				actorId: "test-actor-2",
				mocks: []interface{}{
					mockDao.EXPECT().GetByActorId(context.Background(), gomock.Any()).
						Return(userPriorityDataResponse, nil),
				},
			},
			want:    userPriorityDataResponse,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			helper := NewPriorityDataHelper(mockDao, mockUserClient, mockActorClient)
			got, err := helper.GetUserPriorityData(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserPriorityData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserPriorityData() got = %v, want %v", got, tt.want)
			}
		})
	}
}
