package test

import (
	"os"

	"github.com/redis/go-redis/v9"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/storage"
)

var (
	redisTestDbConfig = &redis.Options{
		Addr: "localhost:6379",
		DB:   0,
	}
)

// InitTestServer initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServer() (*redis.Client, func()) {
	// Setup logger for test env
	logger.Init(cfg.TestEnv)

	// update redis if REDIS_HOST env variable is present.
	if val, ok := os.LookupEnv("REDIS_HOST"); ok {
		redisTestDbConfig.Addr = val
	}

	// Init redis connection
	redisClient := storage.NewRedisClient(redisTestDbConfig, false, false)

	return redisClient, func() {
		_ = logger.Log.Sync()
		_ = redisClient.Close()
	}
}
