package issue_resolution_feedback

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"flag"
	"os"
	"reflect"
	"testing"
	"time"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	pb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	cxPayloadPb "github.com/epifi/gamma/api/cx/payload"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	mockTicket "github.com/epifi/gamma/api/cx/ticket/mocks"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"

	"github.com/epifi/be-common/pkg/epifierrors"
	cxNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/cx"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	internalMocks "github.com/epifi/gamma/cx/internal/mocks"
	"github.com/epifi/gamma/cx/test"
	daoMock "github.com/epifi/gamma/cx/test/mocks/issue_resolution_feedback/dao"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, genConf, _, teardown := test.InitTestServer(false)
	txnExecutor := storageV2.NewGormTxnExecutor(nil)
	irfTS = IssueResolutionFeedbackTestSuite{
		conf:        genConf,
		txnExecutor: txnExecutor,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type IssueResolutionFeedbackTestSuite struct {
	conf        *cxGenConf.Config
	txnExecutor storageV2.TxnExecutor
}

var (
	irfTS IssueResolutionFeedbackTestSuite

	feedbackAttempt1 = &pb.IssueResolutionFeedback{
		TicketId:           1,
		ResolutionCategory: pb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
		ClientRequestId:    "test-req-id",
		ProcessStage:       pb.ProcessStage_PROCESS_STAGE_CREATED,
	}
	feedbackAttempt2 = &pb.IssueResolutionFeedback{
		Id:                 "feedback-id-2",
		TicketId:           1,
		ResolutionCategory: pb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
		ClientRequestId:    "test-req-id-2",
		ProcessStage:       pb.ProcessStage_PROCESS_STAGE_CREATED,
		LastTriedAt:        &timestampPb.Timestamp{Seconds: 1659108867},
	}
	feedbackAttemptCallbackSuccess = &pb.IssueResolutionFeedback{
		Id:                 "feedback-id-success",
		TicketId:           1,
		ResolutionCategory: pb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
		ClientRequestId:    "test-req-id-2",
		ProcessStage:       pb.ProcessStage_PROCESS_STAGE_CREATED,
		LastTriedAt:        timestampPb.New(time.Now().Add(-1 * 10000)),
	}
	issuePayload = &pb.IssuePayload{
		TicketId:           1,
		ResolutionCategory: pb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
		ActorId:            "actor-1",
	}
	reqPayload, _ = protojson.Marshal(&cxPayloadPb.IssueResolutionFeedback{IssuePayload: issuePayload})
)

func TestIssueResolutionFeedbackService_IngestTicketForIssueResolutionFeedback(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockFeedbackAttemptDao := daoMock.NewMockIIssueResolutionFeedbackDao(ctr)
	mockCelestial := internalMocks.NewMockICelestialProcessor(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *pb.IngestTicketForIssueResolutionFeedbackRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.IngestTicketForIssueResolutionFeedbackResponse
		wantErr bool
	}{
		{
			name: "invalid argument: no ticket id",
			args: args{
				ctx: context.Background(),
				req: &pb.IngestTicketForIssueResolutionFeedbackRequest{},
			},
			want: &pb.IngestTicketForIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Ticket Id and resolution category are mandatory"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: no resolution category",
			args: args{
				ctx: context.Background(),
				req: &pb.IngestTicketForIssueResolutionFeedbackRequest{IssuePayload: &pb.IssuePayload{TicketId: 1}},
			},
			want: &pb.IngestTicketForIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Ticket Id and resolution category are mandatory"),
			},
			wantErr: false,
		},
		{
			name: "error while creating feedback attempt record",
			args: args{
				ctx: context.Background(),
				req: &pb.IngestTicketForIssueResolutionFeedbackRequest{IssuePayload: issuePayload, ClientRequestId: "test-req-id"},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().Create(gomock.Any(), feedbackAttempt1).Return(nil, errors.New("fail")),
				},
			},
			want: &pb.IngestTicketForIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "already exists: and get feedback attempt record failed",
			args: args{
				ctx: context.Background(),
				req: &pb.IngestTicketForIssueResolutionFeedbackRequest{IssuePayload: issuePayload, ClientRequestId: "test-req-id"},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().Create(gomock.Any(), feedbackAttempt1).Return(nil, epifierrors.ErrAlreadyExists),
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1)).Return(nil, errors.New("fail")),
				},
			},
			want: &pb.IngestTicketForIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error while initiating workflow",
			args: args{
				ctx: context.Background(),
				req: &pb.IngestTicketForIssueResolutionFeedbackRequest{IssuePayload: issuePayload, ClientRequestId: "test-req-id"},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().Create(gomock.Any(), feedbackAttempt1).Return(feedbackAttempt1, nil),
					mockCelestial.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any(), "actor-1", reqPayload, workflowPb.Type_CX_ISSUE_RESOLUTION_FEEDBACK, workflowPb.Version_V0).
						Return(errors.New("fail")),
				},
			},
			want: &pb.IngestTicketForIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "workflow already exists",
			args: args{
				ctx: context.Background(),
				req: &pb.IngestTicketForIssueResolutionFeedbackRequest{IssuePayload: issuePayload, ClientRequestId: "test-req-id"},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().Create(gomock.Any(), feedbackAttempt1).Return(nil, epifierrors.ErrAlreadyExists),
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1)).Return(feedbackAttempt1, nil),
					mockCelestial.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any(), "actor-1", reqPayload, workflowPb.Type_CX_ISSUE_RESOLUTION_FEEDBACK, workflowPb.Version_V0).
						Return(errors.Wrap(epifierrors.ErrAlreadyExists, "fail")),
				},
			},
			want: &pb.IngestTicketForIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusAlreadyExists(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &pb.IngestTicketForIssueResolutionFeedbackRequest{IssuePayload: issuePayload, ClientRequestId: "test-req-id"},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().Create(gomock.Any(), feedbackAttempt1).Return(feedbackAttempt1, nil),
					mockCelestial.EXPECT().InitiateWorkflow(gomock.Any(), gomock.Any(), "actor-1", reqPayload, workflowPb.Type_CX_ISSUE_RESOLUTION_FEEDBACK, workflowPb.Version_V0).
						Return(nil),
				},
			},
			want: &pb.IngestTicketForIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(irfTS.conf, mockFeedbackAttemptDao, nil, mockCelestial, nil)
			got, err := s.IngestTicketForIssueResolutionFeedback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IngestTicketForIssueResolutionFeedback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IngestTicketForIssueResolutionFeedback() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIssueResolutionFeedbackService_PushIssueResolutionFeedback(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockFeedbackAttemptDao := daoMock.NewMockIIssueResolutionFeedbackDao(ctr)
	mockResponseDao := daoMock.NewMockIIssueResolutionUserResponseLogDao(ctr)
	mockCelestial := internalMocks.NewMockICelestialProcessor(ctr)
	mockTicketClient := mockTicket.NewMockTicketClient(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *pb.PushIssueResolutionFeedbackRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.PushIssueResolutionFeedbackResponse
		wantErr bool
	}{
		{
			name: "invalid argument: no ticket id",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket Id is missing"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: no user response",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{
					TicketId: 1,
				},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("feedback is empty"),
			},
			wantErr: false,
		},
		{
			name: "error while getting feedback attempt record",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{
					TicketId: 1,
					Response: "Yes",
				},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1)).Return(nil, errors.New("fail")),
				},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to retrieve ticket from Database"),
			},
			wantErr: false,
		},
		{
			name: "failed to get transaction type",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{
					TicketId: 1234,
					Response: "Yes",
				},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1234)).Return(feedbackAttempt2, nil),
					mockTicketClient.EXPECT().GetSupportTickets(context.Background(), &ticketPb.GetSupportTicketsRequest{
						TicketId: 1234,
					}).Return(nil, errors.New("mock-error")),
				},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to retrieve transaction type"),
			},
			wantErr: false,
		},
		{
			name: "time limit for user response has expired",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{
					TicketId: 1234,
					Response: "Yes",
				},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1234)).Return(feedbackAttempt2, nil),
					mockTicketClient.EXPECT().GetSupportTickets(context.Background(), &ticketPb.GetSupportTicketsRequest{
						TicketId: 1234,
					}).Return(&ticketPb.GetSupportTicketsResponse{Status: rpcPb.StatusOk(), Tickets: []*ticketPb.Ticket{{Id: 1234, CustomFields: &ticketPb.CustomFields{
						SubCategory: &ticketPb.CustomFields_SubCategoryTransactionsDebitedViaFiApp{
							SubCategoryTransactionsDebitedViaFiApp: ticketPb.SubCategoryTransactionsDebitedViaFiApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT,
						}}}}}, nil),
				},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status:               rpcPb.StatusOk(),
				IsFeedbackCommsValid: commontypes.BooleanEnum_FALSE,
				ResponseMeta: &pb.PushIssueResolutionFeedbackResponse_Dispute{
					Dispute: &pb.DisputeMeta{
						TransactionType: pb.TransactionType_TRANSACTION_TYPE_P2M,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "failed at preCondition",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{
					TicketId: 1234,
					Response: "Yes",
				},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1234)).Return(feedbackAttempt2, nil),
					mockTicketClient.EXPECT().GetSupportTickets(context.Background(), &ticketPb.GetSupportTicketsRequest{
						TicketId: 1234,
					}).Return(&ticketPb.GetSupportTicketsResponse{Status: rpcPb.StatusOk(), Tickets: []*ticketPb.Ticket{{Id: 1234, CustomFields: &ticketPb.CustomFields{
						SubCategory: &ticketPb.CustomFields_SubCategoryTransactionsDebitedViaFiApp{
							SubCategoryTransactionsDebitedViaFiApp: ticketPb.SubCategoryTransactionsDebitedViaFiApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_UNSPECIFIED,
						}}}}}, nil),
				},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusFailedPrecondition(),
			},
			wantErr: false,
		},
		{
			name: "feedback already exists",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{
					TicketId: 1234,
					Response: "Yes",
				},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1234)).Return(feedbackAttemptCallbackSuccess, nil),
					mockTicketClient.EXPECT().GetSupportTickets(context.Background(), &ticketPb.GetSupportTicketsRequest{
						TicketId: 1234,
					}).Return(&ticketPb.GetSupportTicketsResponse{Status: rpcPb.StatusOk(), Tickets: []*ticketPb.Ticket{{Id: 1234, CustomFields: &ticketPb.CustomFields{
						SubCategory: &ticketPb.CustomFields_SubCategoryTransactionsDebitedViaFiApp{
							SubCategoryTransactionsDebitedViaFiApp: ticketPb.SubCategoryTransactionsDebitedViaFiApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY,
						}}}}}, nil),
					mockResponseDao.EXPECT().GetLatestFeedbackByIssueResolutionFeedbackId(gomock.Any(), "feedback-id-success").Return(&pb.IssueResolutionUserResponseLog{}, nil),
				},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status:               rpcPb.StatusOk(),
				IsFeedbackCommsValid: commontypes.BooleanEnum_TRUE,
				IsFeedbackExists:     commontypes.BooleanEnum_TRUE,
				ResponseMeta: &pb.PushIssueResolutionFeedbackResponse_Dispute{
					Dispute: &pb.DisputeMeta{
						TransactionType: pb.TransactionType_TRANSACTION_TYPE_P2P,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "failed to send feedback callback signal",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{
					TicketId: 1234,
					Response: "NO",
				},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1234)).Return(feedbackAttemptCallbackSuccess, nil),
					mockTicketClient.EXPECT().GetSupportTickets(context.Background(), &ticketPb.GetSupportTicketsRequest{
						TicketId: 1234,
					}).Return(&ticketPb.GetSupportTicketsResponse{Status: rpcPb.StatusOk(), Tickets: []*ticketPb.Ticket{{Id: 1234, CustomFields: &ticketPb.CustomFields{
						SubCategory: &ticketPb.CustomFields_SubCategoryTransactionsDebitedViaFiApp{
							SubCategoryTransactionsDebitedViaFiApp: ticketPb.SubCategoryTransactionsDebitedViaFiApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT,
						}}}}}, nil),
					mockResponseDao.EXPECT().GetLatestFeedbackByIssueResolutionFeedbackId(gomock.Any(), "feedback-id-success").Return(nil, epifierrors.ErrRecordNotFound),
					mockCelestial.EXPECT().SignalWorkflow(gomock.Any(), gomock.Any(), workflowPb.Client_CX, string(cxNs.IssueResolutionFeedbackCallbackSignal), gomock.Any()).
						Return(errors.New("fail")),
				},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &pb.PushIssueResolutionFeedbackRequest{
					TicketId: 1234,
					Response: "YES",
				},
				mocks: []interface{}{
					mockFeedbackAttemptDao.EXPECT().GetByTicketId(gomock.Any(), int64(1234)).Return(feedbackAttemptCallbackSuccess, nil),
					mockTicketClient.EXPECT().GetSupportTickets(context.Background(), &ticketPb.GetSupportTicketsRequest{
						TicketId: 1234,
					}).Return(&ticketPb.GetSupportTicketsResponse{Status: rpcPb.StatusOk(), Tickets: []*ticketPb.Ticket{{Id: 1234, CustomFields: &ticketPb.CustomFields{
						SubCategory: &ticketPb.CustomFields_SubCategoryTransactionsDebitedFromFiAccountViaOtherApp{
							SubCategoryTransactionsDebitedFromFiAccountViaOtherApp: ticketPb.SubCategoryTransactionsDebitedFromFiAccountViaOtherApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT,
						}}}}}, nil),
					mockResponseDao.EXPECT().GetLatestFeedbackByIssueResolutionFeedbackId(gomock.Any(), "feedback-id-success").Return(nil, epifierrors.ErrRecordNotFound),
					mockCelestial.EXPECT().SignalWorkflow(gomock.Any(), gomock.Any(), workflowPb.Client_CX, string(cxNs.IssueResolutionFeedbackCallbackSignal), gomock.Any()).
						Return(nil),
				},
			},
			want: &pb.PushIssueResolutionFeedbackResponse{
				Status:               rpcPb.StatusOk(),
				IsFeedbackCommsValid: commontypes.BooleanEnum_TRUE,
				IsFeedbackExists:     commontypes.BooleanEnum_FALSE,
				ResponseMeta: &pb.PushIssueResolutionFeedbackResponse_Dispute{
					Dispute: &pb.DisputeMeta{
						TransactionType: pb.TransactionType_TRANSACTION_TYPE_P2M,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(irfTS.conf, mockFeedbackAttemptDao, mockResponseDao, mockCelestial, mockTicketClient)
			got, err := s.PushIssueResolutionFeedback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PushIssueResolutionFeedback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PushIssueResolutionFeedback() got = %v, want %v", got, tt.want)
			}
		})
	}
}
