package issue_resolution_feedback

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"
	"time"

	"github.com/epifi/be-common/api/rpc"
	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	pb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	cxPayloadPb "github.com/epifi/gamma/api/cx/payload"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"

	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/internal"
	"github.com/epifi/gamma/cx/issue_resolution_feedback/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	cxNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/cx"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
)

type Service struct {
	// UnimplementedIssueResolutionFeedbackServer is embedded to have forward compatible implementations
	pb.UnimplementedIssueResolutionFeedbackServiceServer
	conf               *cxGenConf.Config
	feedbackDao        dao.IIssueResolutionFeedbackDao
	responseDao        dao.IIssueResolutionUserResponseLogDao
	celestialProcessor internal.ICelestialProcessor
	ticketClient       ticketPb.TicketClient
}

func NewService(conf *cxGenConf.Config, feedbackDao dao.IIssueResolutionFeedbackDao, responseDao dao.IIssueResolutionUserResponseLogDao,
	celestialProcessor internal.ICelestialProcessor, ticketClient ticketPb.TicketClient) *Service {
	return &Service{
		conf:               conf,
		feedbackDao:        feedbackDao,
		responseDao:        responseDao,
		celestialProcessor: celestialProcessor,
		ticketClient:       ticketClient,
	}
}

var _ pb.IssueResolutionFeedbackServiceServer = &Service{}

var (
	yesNoStringToBooleanEnum = map[string]commontypes.BooleanEnum{
		"yes": commontypes.BooleanEnum_TRUE,
		"no":  commontypes.BooleanEnum_FALSE,
	}
	debitedViaFiAppTransactionTypeMap = map[ticketPb.SubCategoryTransactionsDebitedViaFiApp]pb.TransactionType{
		ticketPb.SubCategoryTransactionsDebitedViaFiApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: pb.TransactionType_TRANSACTION_TYPE_P2P,
		ticketPb.SubCategoryTransactionsDebitedViaFiApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT:    pb.TransactionType_TRANSACTION_TYPE_P2M,
	}
	debitedViaOtherAppTransactionTypeMap = map[ticketPb.SubCategoryTransactionsDebitedFromFiAccountViaOtherApp]pb.TransactionType{
		ticketPb.SubCategoryTransactionsDebitedFromFiAccountViaOtherApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: pb.TransactionType_TRANSACTION_TYPE_P2P,
		ticketPb.SubCategoryTransactionsDebitedFromFiAccountViaOtherApp_SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT:    pb.TransactionType_TRANSACTION_TYPE_P2M,
	}
	transactionTypeMappingNotFoundErr = errors.New("No transaction type mapped for the given subcategory")
)

func (s *Service) IngestTicketForIssueResolutionFeedback(ctx context.Context, req *pb.IngestTicketForIssueResolutionFeedbackRequest) (*pb.IngestTicketForIssueResolutionFeedbackResponse, error) {
	// Feature flag to disable ingestion
	if !s.conf.IssueResolutionFeedbackConfig().IsEnabled() {
		logger.Info(ctx, "Issue Resolution feedback flow disabled", zap.Any(logger.TICKET_ID, req.GetIssuePayload().GetTicketId()))
		return &pb.IngestTicketForIssueResolutionFeedbackResponse{
			Status: rpc.StatusPermissionDeniedWithDebugMsg("feature disabled"),
		}, nil
	}
	// Validate request
	issuePayload := req.GetIssuePayload()
	if issuePayload.GetTicketId() == 0 || issuePayload.GetResolutionCategory() == pb.ResolutionCategory_RESOLUTION_CATEGORY_UNSPECIFIED {
		logger.Error(ctx, "ticket Id and/or Resolution category are empty", zap.Int64(logger.TICKET_ID, issuePayload.GetTicketId()))
		return &pb.IngestTicketForIssueResolutionFeedbackResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("Ticket Id and resolution category are mandatory"),
		}, nil
	}
	// Create or get db entry for the ticket
	entry, err := s.createOrGetFeedbackAttempt(ctx, issuePayload, req.GetClientRequestId())
	if err != nil {
		logger.Error(ctx, "failed to create/get feedback attempt entry", zap.Int64(logger.TICKET_ID, issuePayload.GetTicketId()), zap.Error(err))
		return &pb.IngestTicketForIssueResolutionFeedbackResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	logger.Info(ctx, "initiating issue resolution feedback workflow", zap.Int64(logger.TICKET_ID, issuePayload.GetTicketId()), zap.String(logger.ACTOR_ID, issuePayload.GetActorId()))
	// initiate workflow
	feedbackId := entry.GetId() // we use the generated row Id of the entry as workflow request Id
	err = s.initiateIssueResolutionFeedbackWorkflow(ctx, &celestialPb.ClientReqId{Id: feedbackId, Client: workflowPb.Client_CX}, req)
	if err != nil {
		if errors.Is(err, epifierrors.ErrAlreadyExists) {
			return &pb.IngestTicketForIssueResolutionFeedbackResponse{
				Status: rpc.StatusAlreadyExists(),
			}, nil
		}
		logger.Error(ctx, "failed to initiate issue resolution workflow", zap.Int64(logger.TICKET_ID, issuePayload.GetTicketId()), zap.Error(err))
		return &pb.IngestTicketForIssueResolutionFeedbackResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &pb.IngestTicketForIssueResolutionFeedbackResponse{
		Status: rpc.StatusOk(),
	}, nil
}

//nolint:funlen
func (s *Service) PushIssueResolutionFeedback(ctx context.Context, req *pb.PushIssueResolutionFeedbackRequest) (*pb.PushIssueResolutionFeedbackResponse, error) {
	// Validate request
	ticketId := req.GetTicketId()
	logger.Info(ctx, "Received user feedback for issue resolution feedback workflow", zap.Int64(logger.TICKET_ID, ticketId))
	if err := validateFeedbackRequest(req); err != nil {
		logger.Error(ctx, "user feedback validation failed", zap.Error(err))
		return &pb.PushIssueResolutionFeedbackResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(err.Error()),
		}, nil
	}
	// Retrieve the workflow details for the ticket
	entry, err := s.feedbackDao.GetByTicketId(ctx, ticketId)
	if err != nil {
		logger.Error(ctx, "failed to get feedback attempt entry from db", zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err))
		return &pb.PushIssueResolutionFeedbackResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to retrieve ticket from Database"),
		}, nil
	}
	transactionType, err := s.getTransactionType(ctx, ticketId)
	if err != nil {
		logger.Error(ctx, "failed to get transaction type", zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err))
		if errors.Is(err, transactionTypeMappingNotFoundErr) {
			return &pb.PushIssueResolutionFeedbackResponse{
				Status: rpc.StatusFailedPrecondition(),
			}, nil
		}
		return &pb.PushIssueResolutionFeedbackResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to retrieve transaction type"),
		}, nil
	}

	// Check if the feedback has expired
	if time.Since(entry.GetLastTriedAt().AsTime()) > s.conf.IssueResolutionFeedbackConfig().DisputeConfig().WaitDurationAfterFinalComms() {
		logger.Info(ctx, "time limit for user response has expired", zap.Int64(logger.TICKET_ID, ticketId))
		return &pb.PushIssueResolutionFeedbackResponse{
			Status:               rpc.StatusOk(),
			IsFeedbackCommsValid: commontypes.BooleanEnum_FALSE,
			ResponseMeta: &pb.PushIssueResolutionFeedbackResponse_Dispute{Dispute: &pb.DisputeMeta{
				TransactionType: transactionType,
			}},
		}, nil
	}
	// Check if feedback already exists
	_, err = s.responseDao.GetLatestFeedbackByIssueResolutionFeedbackId(ctx, entry.GetId())
	switch {
	case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "failed to get user feedbacks from db", zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err))
		return &pb.PushIssueResolutionFeedbackResponse{
			Status: rpc.StatusInternalWithDebugMsg("Database error"),
		}, nil
	case err == nil:
		logger.Info(ctx, "user response already exists", zap.Int64(logger.TICKET_ID, ticketId))
		return &pb.PushIssueResolutionFeedbackResponse{
			Status:               rpc.StatusOk(),
			IsFeedbackCommsValid: commontypes.BooleanEnum_TRUE,
			IsFeedbackExists:     commontypes.BooleanEnum_TRUE,
			ResponseMeta: &pb.PushIssueResolutionFeedbackResponse_Dispute{Dispute: &pb.DisputeMeta{
				TransactionType: transactionType,
			}},
		}, nil
	default: // errors.Is(err, epifierrors.ErrRecordNotFound):
		// if there is no earlier feedback, continue the flow and signal the workflow with the feedback received
	}
	// Signal the workflow using the workflow req Id
	wfReqId := entry.GetId()
	feedbackPayload, err := constructFeedbackPayload(entry.GetResolutionCategory(), ticketId, req.GetResponse())
	if err != nil {
		logger.Error(ctx, "failed to construct feedback payload", zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err))
		return &pb.PushIssueResolutionFeedbackResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(err.Error()),
		}, nil
	}
	if err := s.signalFeedbackCallbackToWorkflow(ctx, wfReqId, feedbackPayload); err != nil {
		logger.Error(ctx, "failed to send callback signal to workflow", zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err))
		return &pb.PushIssueResolutionFeedbackResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	logger.Info(ctx, "successfully signaled the workflow with the user response", zap.Int64(logger.TICKET_ID, ticketId), zap.String(logger.WORKFLOW_REQ_ID, wfReqId))
	return &pb.PushIssueResolutionFeedbackResponse{
		Status:               rpc.StatusOk(),
		IsFeedbackCommsValid: commontypes.BooleanEnum_TRUE,
		IsFeedbackExists:     commontypes.BooleanEnum_FALSE,
		ResponseMeta: &pb.PushIssueResolutionFeedbackResponse_Dispute{Dispute: &pb.DisputeMeta{
			TransactionType: transactionType,
		}},
	}, nil
}

func validateFeedbackRequest(req *pb.PushIssueResolutionFeedbackRequest) error {
	switch {
	case req.GetTicketId() == 0:
		return errors.New("ticket Id is missing")
	case req.GetResponse() == "":
		return errors.New("feedback is empty")
	}
	return nil
}

func constructFeedbackPayload(resolutionCategory pb.ResolutionCategory, ticketId int64, response string) (*pb.FeedbackPayload, error) {
	feedbackPayload := &pb.FeedbackPayload{
		TicketId:           ticketId,
		ResolutionFeedback: &pb.ResolutionFeedback{},
	}
	switch resolutionCategory {
	case pb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE:
		resp, ok := yesNoStringToBooleanEnum[strings.ToLower(response)]
		if !ok {
			return nil, errors.New("invalid user response for RESOLUTION_CATEGORY_DISPUTE")
		}
		feedbackPayload.ResolutionFeedback.Feedback = &pb.ResolutionFeedback_DisputeFeedback{
			DisputeFeedback: &pb.DisputeFeedback{
				Response: resp,
			},
		}
	default:
		return nil, errors.New("unknown resolution category")
	}
	return feedbackPayload, nil
}

func (s *Service) createOrGetFeedbackAttempt(ctx context.Context, issuePayload *pb.IssuePayload, clientReqId string) (*pb.IssueResolutionFeedback, error) {
	entry, err := s.feedbackDao.Create(ctx, &pb.IssueResolutionFeedback{
		TicketId:           issuePayload.GetTicketId(),
		ResolutionCategory: issuePayload.GetResolutionCategory(),
		ClientRequestId:    clientReqId,
		ProcessStage:       pb.ProcessStage_PROCESS_STAGE_CREATED,
	})
	if err != nil && !errors.Is(err, epifierrors.ErrAlreadyExists) {
		return nil, errors.Wrap(err, "error creating feedback attempt entry")
	}
	if errors.Is(err, epifierrors.ErrAlreadyExists) {
		entry, err = s.feedbackDao.GetByTicketId(ctx, issuePayload.GetTicketId())
		if err != nil {
			return nil, errors.Wrap(err, "error while fetching feedback attempt entry")
		}
	}
	return entry, nil
}

func (s *Service) initiateIssueResolutionFeedbackWorkflow(ctx context.Context, clientReqId *celestialPb.ClientReqId, req *pb.IngestTicketForIssueResolutionFeedbackRequest) error {
	reqPayload, err := protojson.Marshal(&cxPayloadPb.IssueResolutionFeedback{IssuePayload: req.GetIssuePayload()})
	if err != nil {
		return errors.Wrap(err, "failed to marshal req")
	}
	err = s.celestialProcessor.InitiateWorkflow(ctx, clientReqId, req.GetIssuePayload().GetActorId(), reqPayload, workflowPb.Type_CX_ISSUE_RESOLUTION_FEEDBACK, workflowPb.Version_V0)
	if err != nil {
		return errors.Wrap(err, "failed to initiate workflow")
	}
	return nil
}

func (s *Service) signalFeedbackCallbackToWorkflow(ctx context.Context, wfReqId string, feedbackPayload *pb.FeedbackPayload) error {
	payload, err := protojson.Marshal(&cxPayloadPb.IssueResolutionFeedbackCallbackSignal{
		FeedbackPayload: feedbackPayload,
	})
	if err != nil {
		return errors.Wrap(err, "failed to marshal req")
	}
	signalId := string(cxNs.IssueResolutionFeedbackCallbackSignal)
	err = s.celestialProcessor.SignalWorkflow(ctx, wfReqId, workflowPb.Client_CX, signalId, payload)
	if err != nil {
		return errors.Wrap(err, "failed to signal feedback callback to workflow")
	}
	return nil
}

func (s *Service) getTransactionType(ctx context.Context, ticketId int64) (pb.TransactionType, error) {
	resp, err := s.ticketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketId: ticketId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return pb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, errors.Wrap(rpcErr, "error while fetching ticket from ticket service")
	}

	ticket := resp.GetTickets()[0]
	if ticket.GetIssueCategoryId() != "" {
		txnTypeStr := s.conf.IssueResolutionFeedbackConfig().DisputeConfig().IssueCategoryIdToTransactionType().Get(ticket.GetIssueCategoryId())
		txnType, ok := pb.TransactionType_value[txnTypeStr]
		if !ok {
			return pb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, errors.Wrap(transactionTypeMappingNotFoundErr, fmt.Sprintf("No transaction type mapped for the given issue category Id: %s", ticket.GetIssueCategoryId()))
		}
		return pb.TransactionType(txnType), nil
	}
	var (
		transactionType pb.TransactionType
		found           bool
	)
	switch ticket.GetCustomFields().GetSubCategory().(type) {
	case *ticketPb.CustomFields_SubCategoryTransactionsDebitedViaFiApp:
		transactionType, found = debitedViaFiAppTransactionTypeMap[ticket.GetCustomFields().GetSubCategoryTransactionsDebitedViaFiApp()]
		if !found {
			return pb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, errors.Wrap(transactionTypeMappingNotFoundErr, "No transaction type mapped for the given subcategory for dispute feedback debited via Fi App")
		}
	case *ticketPb.CustomFields_SubCategoryTransactionsDebitedFromFiAccountViaOtherApp:
		transactionType, found = debitedViaOtherAppTransactionTypeMap[ticket.GetCustomFields().GetSubCategoryTransactionsDebitedFromFiAccountViaOtherApp()]
		if !found {
			return pb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, errors.Wrap(transactionTypeMappingNotFoundErr, "No transaction type mapped for the given subcategory for dispute feedback debited from Fi account via other App")
		}
	default:
		return pb.TransactionType_TRANSACTION_TYPE_UNSPECIFIED, errors.New("invalid subcategory type for dispute feedback")
	}
	return transactionType, nil
}
