package dao

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, db, teardown := test.InitTestServer(true)
	ircadts = IssueResolutionFeedbackDAOTestSuite{
		db:   db,
		conf: conf,
		dao:  NewIssueResolutionFeedbackDao(db),
	}
	irufdts = IssueResolutionUserResponseLogDAOTestSuite{
		db:   db,
		conf: conf,
		dao:  NewIssueResolutionUserResponseLogDao(db),
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
