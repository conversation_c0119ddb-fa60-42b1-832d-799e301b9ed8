package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	issueResolutionFeedbackPb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
)

type IssueResolutionFeedback struct {
	Id                 string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	TicketId           int64
	ResolutionCategory issueResolutionFeedbackPb.ResolutionCategory
	ClientRequestId    string
	NumberOfAttempts   int64
	LastTriedAt        time.Time
	ProcessStage       issueResolutionFeedbackPb.ProcessStage
	CreatedAt          time.Time
	UpdatedAt          time.Time
}

type IssueResolutionUserResponseLog struct {
	Id                        string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	IssueResolutionFeedbackId string
	ResolutionFeedback        *issueResolutionFeedbackPb.ResolutionFeedback
	CreatedAt                 time.Time
	UpdatedAt                 time.Time
}

func NewIssueResolutionFeedbackFromProtoMsg(msg *issueResolutionFeedbackPb.IssueResolutionFeedback) *IssueResolutionFeedback {
	return &IssueResolutionFeedback{
		TicketId:           msg.GetTicketId(),
		ResolutionCategory: msg.GetResolutionCategory(),
		ClientRequestId:    msg.GetClientRequestId(),
		NumberOfAttempts:   msg.GetNumberOfAttempts(),
		LastTriedAt:        msg.GetLastTriedAt().AsTime(),
		ProcessStage:       msg.GetProcessStage(),
	}
}

func (i *IssueResolutionFeedback) ToProtoMsg() *issueResolutionFeedbackPb.IssueResolutionFeedback {
	return &issueResolutionFeedbackPb.IssueResolutionFeedback{
		Id:                 i.Id,
		TicketId:           i.TicketId,
		ResolutionCategory: i.ResolutionCategory,
		ClientRequestId:    i.ClientRequestId,
		NumberOfAttempts:   i.NumberOfAttempts,
		LastTriedAt:        timestampPb.New(i.LastTriedAt),
		ProcessStage:       i.ProcessStage,
		CreatedAt:          timestampPb.New(i.CreatedAt),
		UpdatedAt:          timestampPb.New(i.UpdatedAt),
	}
}

func NewIssueResolutionUserResponseLogFromProtoMsg(msg *issueResolutionFeedbackPb.IssueResolutionUserResponseLog) *IssueResolutionUserResponseLog {
	return &IssueResolutionUserResponseLog{
		IssueResolutionFeedbackId: msg.GetIssueResolutionUserFeedbackId(),
		ResolutionFeedback:        msg.GetResolutionFeedback(),
	}
}

func (i *IssueResolutionUserResponseLog) ToProtoMsg() *issueResolutionFeedbackPb.IssueResolutionUserResponseLog {
	return &issueResolutionFeedbackPb.IssueResolutionUserResponseLog{
		Id:                            i.Id,
		IssueResolutionUserFeedbackId: i.IssueResolutionFeedbackId,
		ResolutionFeedback:            i.ResolutionFeedback,
		CreatedAt:                     timestampPb.New(i.CreatedAt),
		UpdatedAt:                     timestampPb.New(i.UpdatedAt),
	}
}
