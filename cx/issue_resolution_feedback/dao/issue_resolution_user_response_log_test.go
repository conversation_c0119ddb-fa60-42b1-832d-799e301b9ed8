package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/protobuf/proto"
	gormV2 "gorm.io/gorm"

	issueResolutionFeedbackPb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/test"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

type IssueResolutionUserResponseLogDAOTestSuite struct {
	db   *gormV2.DB
	conf *config.Config
	dao  *IssueResolutionUserResponseLogDao
}

var (
	irufdts  IssueResolutionUserResponseLogDAOTestSuite
	feedback = &issueResolutionFeedbackPb.ResolutionFeedback{
		Feedback: &issueResolutionFeedbackPb.ResolutionFeedback_DisputeFeedback{
			DisputeFeedback: &issueResolutionFeedbackPb.DisputeFeedback{
				Response: commontypes.BooleanEnum_TRUE,
			},
		},
	}
)

func TestIssueResolutionUserResponseLogDao_Create(t *testing.T) {
	type args struct {
		ctx                    context.Context
		resolutionUserFeedback *issueResolutionFeedbackPb.IssueResolutionUserResponseLog
	}
	tests := []struct {
		name    string
		args    args
		want    *issueResolutionFeedbackPb.IssueResolutionUserResponseLog
		wantErr bool
	}{
		{
			name: "invalid arg",
			args: args{
				ctx: context.Background(),
				resolutionUserFeedback: &issueResolutionFeedbackPb.IssueResolutionUserResponseLog{
					IssueResolutionUserFeedbackId: "",
					ResolutionFeedback:            nil,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successful",
			args: args{
				ctx: context.Background(),
				resolutionUserFeedback: &issueResolutionFeedbackPb.IssueResolutionUserResponseLog{
					IssueResolutionUserFeedbackId: "f123",
					ResolutionFeedback:            feedback,
				},
			},
			want: &issueResolutionFeedbackPb.IssueResolutionUserResponseLog{
				IssueResolutionUserFeedbackId: "f123",
				ResolutionFeedback:            feedback,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ircadts.db, ircadts.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := irufdts.dao.Create(tt.args.ctx, tt.args.resolutionUserFeedback)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("Create() got: %v, want: %v", got, tt.want)
			}
		})
	}
}

func TestIssueResolutionUserResponseLogDao_GetLatestFeedbackByIssueResolutionFeedbackId(t *testing.T) {
	type args struct {
		ctx        context.Context
		feedbackId string
	}
	tests := []struct {
		name    string
		args    args
		want    *issueResolutionFeedbackPb.IssueResolutionUserResponseLog
		wantErr bool
	}{
		{
			name: "invalid arg",
			args: args{
				ctx:        context.Background(),
				feedbackId: "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				ctx:        context.Background(),
				feedbackId: "xyz",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successful",
			args: args{
				ctx:        context.Background(),
				feedbackId: "3645a4b6-6c02-40b5-a76a-ac9d21b97582",
			},
			want: &issueResolutionFeedbackPb.IssueResolutionUserResponseLog{
				IssueResolutionUserFeedbackId: "3645a4b6-6c02-40b5-a76a-ac9d21b97582",
				ResolutionFeedback:            feedback,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ircadts.db, ircadts.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := irufdts.dao.GetLatestFeedbackByIssueResolutionFeedbackId(tt.args.ctx, tt.args.feedbackId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLatestFeedbackByIssueResolutionFeedbackId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetLatestFeedbackByIssueResolutionFeedbackId() got: %v, want: %v", got, tt.want)
			}
		})
	}
}
