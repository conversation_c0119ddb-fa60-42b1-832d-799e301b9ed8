package dao

import (
	"context"
	"time"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	issueResolutionFeedbackPb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	model "github.com/epifi/gamma/cx/issue_resolution_feedback/dao/model"
)

type IssueResolutionFeedbackDao struct {
	db *gormv2.DB
}

func NewIssueResolutionFeedbackDao(db types.SherlockPGDB) *IssueResolutionFeedbackDao {
	return &IssueResolutionFeedbackDao{
		db: db,
	}
}

var IssueResolutionFeedbackColumnMap = map[issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask]string{
	issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_TICKET_ID:           "ticket_id",
	issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_RESOLUTION_CATEGORY: "resolution_category",
	issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_CLIENT_REQUEST_ID:   "client_request_id",
	issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_NUMBER_OF_ATTEMPTS:  "number_of_attempts",
	issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_LAST_TRIED_AT:       "last_tried_at",
	issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_PROCESS_STAGE:       "process_stage",
}

func (i *IssueResolutionFeedbackDao) Create(ctx context.Context, resolutionFeedback *issueResolutionFeedbackPb.IssueResolutionFeedback) (*issueResolutionFeedbackPb.IssueResolutionFeedback, error) {
	defer metric_util.TrackDuration("cx/issue_resolution_feedback/dao", "IssueResolutionFeedbackDao", "Create", time.Now())
	if resolutionFeedback.GetTicketId() == 0 || resolutionFeedback.GetResolutionCategory() == issueResolutionFeedbackPb.ResolutionCategory_RESOLUTION_CATEGORY_UNSPECIFIED {
		return nil, errors.New("ticket id and resolution category cannot be empty")
	}

	issueModel := model.NewIssueResolutionFeedbackFromProtoMsg(resolutionFeedback)
	db := gormctxv2.FromContextOrDefault(ctx, i.db)
	if err := db.Create(issueModel).Error; err != nil {
		if storageV2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrAlreadyExists
		}
		return nil, errors.Wrap(err, "error while creating issue resolution feedback attempt record in db")
	}
	return issueModel.ToProtoMsg(), nil
}

func (i *IssueResolutionFeedbackDao) GetByTicketId(ctx context.Context, ticketId int64) (*issueResolutionFeedbackPb.IssueResolutionFeedback, error) {
	defer metric_util.TrackDuration("cx/issue_resolution_feedback/dao", "IssueResolutionFeedbackDao", "GetByTicketId", time.Now())
	if ticketId == 0 {
		return nil, errors.New("ticket id is mandatory")
	}
	db := gormctxv2.FromContextOrDefault(ctx, i.db)
	query := db.Model(&model.IssueResolutionFeedback{}).Where("ticket_id = ?", ticketId)
	var issueModel model.IssueResolutionFeedback
	if err := query.First(&issueModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while fetching record from db")
	}
	return issueModel.ToProtoMsg(), nil
}

func (i *IssueResolutionFeedbackDao) GetByClientRequestId(ctx context.Context, clientRequestId string) ([]*issueResolutionFeedbackPb.IssueResolutionFeedback, error) {
	defer metric_util.TrackDuration("cx/issue_resolution_feedback/dao", "IssueResolutionFeedbackDao", "GetByClientRequestId", time.Now())
	if clientRequestId == "" {
		return nil, errors.New("client request id is mandatory")
	}
	db := gormctxv2.FromContextOrDefault(ctx, i.db)
	query := db.Model(&model.IssueResolutionFeedback{}).Where("client_request_id = ?", clientRequestId)
	var issueModelList []*model.IssueResolutionFeedback
	if err := query.Find(&issueModelList).Error; err != nil {
		return nil, errors.Wrap(err, "error while fetching record from db")
	}
	if len(issueModelList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	var issueProtoList []*issueResolutionFeedbackPb.IssueResolutionFeedback
	for _, issue := range issueModelList {
		issueProtoList = append(issueProtoList, issue.ToProtoMsg())
	}
	return issueProtoList, nil
}

func (i *IssueResolutionFeedbackDao) UpdateByTicketId(ctx context.Context, ticketId int64, resolutionFeedbackAttempt *issueResolutionFeedbackPb.IssueResolutionFeedback, updateMask []issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask) error {
	defer metric_util.TrackDuration("cx/issue_resolution_feedback/dao", "IssueResolutionFeedbackDao", "UpdateByTicketId", time.Now())
	if ticketId == 0 || len(updateMask) == 0 {
		return errors.New("ticket id and update mask are mandatory")
	}
	db := gormctxv2.FromContextOrDefault(ctx, i.db)
	issueModel := model.NewIssueResolutionFeedbackFromProtoMsg(resolutionFeedbackAttempt)
	query := db.Model(&model.IssueResolutionFeedback{}).Where("ticket_id = ?", ticketId).Select(getIssueResolutionFeedbackColumnsForUpdate(updateMask)).
		Updates(issueModel)
	if query.Error != nil {
		return errors.Wrap(query.Error, "error while updating issue resolution feedback attempt record")
	}
	if query.RowsAffected == 0 {
		return epifierrors.ErrRecordNotFound
	}
	return nil
}

func getIssueResolutionFeedbackColumnsForUpdate(updateMask []issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMask {
		selectColumns = append(selectColumns, IssueResolutionFeedbackColumnMap[field])
	}
	return selectColumns
}
