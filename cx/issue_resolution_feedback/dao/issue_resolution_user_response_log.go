package dao

import (
	"context"
	"time"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	issueResolutionFeedbackPb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	"github.com/epifi/gamma/cx/issue_resolution_feedback/dao/model"
)

type IssueResolutionUserResponseLogDao struct {
	db *gormv2.DB
}

func NewIssueResolutionUserResponseLogDao(db types.SherlockPGDB) *IssueResolutionUserResponseLogDao {
	return &IssueResolutionUserResponseLogDao{
		db: db,
	}
}

func (i *IssueResolutionUserResponseLogDao) Create(ctx context.Context, userResponseLog *issueResolutionFeedbackPb.IssueResolutionUserResponseLog) (*issueResolutionFeedbackPb.IssueResolutionUserResponseLog, error) {
	defer metric_util.TrackDuration("cx/issue_resolution_feedback/dao", "IssueResolutionUserResponseLogDao", "Create", time.Now())
	if userResponseLog.GetResolutionFeedback() == nil || userResponseLog.GetIssueResolutionUserFeedbackId() == "" {
		return nil, errors.New("feedback or feedback id cannot be empty")
	}
	issueModel := model.NewIssueResolutionUserResponseLogFromProtoMsg(userResponseLog)
	db := gormctxv2.FromContextOrDefault(ctx, i.db)
	if err := db.Create(issueModel).Error; err != nil {
		return nil, errors.Wrap(err, "error while creating issue resolution user feedback record in db")
	}
	return issueModel.ToProtoMsg(), nil
}

func (i *IssueResolutionUserResponseLogDao) GetLatestFeedbackByIssueResolutionFeedbackId(ctx context.Context, feedbackId string) (*issueResolutionFeedbackPb.IssueResolutionUserResponseLog, error) {
	defer metric_util.TrackDuration("cx/issue_resolution_feedback/dao", "IssueResolutionUserResponseLogDao", "GetLatestFeedbackByIssueResolutionFeedbackId", time.Now())
	if feedbackId == "" {
		return nil, errors.New("feedback id is mandatory")
	}
	db := gormctxv2.FromContextOrDefault(ctx, i.db)
	query := db.Where("issue_resolution_feedback_id = ?", feedbackId)
	// fetch latest record
	latestRecord := &model.IssueResolutionUserResponseLog{}
	if err := query.Order("created_at desc").First(latestRecord).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while fetching issue resolution user feedback from db")
	}
	return latestRecord.ToProtoMsg(), nil
}
