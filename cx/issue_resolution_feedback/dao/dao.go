//go:generate dao_metrics_gen .
package dao

import (
	"context"

	issueResolutionFeedbackPb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
)

type IIssueResolutionFeedbackDao interface {
	Create(ctx context.Context, resolutionFeedback *issueResolutionFeedbackPb.IssueResolutionFeedback) (*issueResolutionFeedbackPb.IssueResolutionFeedback, error)
	GetByTicketId(ctx context.Context, ticketId int64) (*issueResolutionFeedbackPb.IssueResolutionFeedback, error)
	GetByClientRequestId(ctx context.Context, clientRequestId string) ([]*issueResolutionFeedbackPb.IssueResolutionFeedback, error)
	UpdateByTicketId(ctx context.Context, ticketId int64, resolutionFeedbackAttempt *issueResolutionFeedbackPb.IssueResolutionFeedback, updateMask []issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask) error
}

type IIssueResolutionUserResponseLogDao interface {
	Create(ctx context.Context, userResponseLog *issueResolutionFeedbackPb.IssueResolutionUserResponseLog) (*issueResolutionFeedbackPb.IssueResolutionUserResponseLog, error)
	GetLatestFeedbackByIssueResolutionFeedbackId(ctx context.Context, feedbackId string) (*issueResolutionFeedbackPb.IssueResolutionUserResponseLog, error)
}
