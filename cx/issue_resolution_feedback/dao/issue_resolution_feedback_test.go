package dao

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/golang/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"

	issueResolutionFeedbackPb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/test"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

type IssueResolutionFeedbackDAOTestSuite struct {
	db   *gormV2.DB
	conf *config.Config
	dao  *IssueResolutionFeedbackDao
}

var (
	ircadts IssueResolutionFeedbackDAOTestSuite
)

func TestIssueResolutionFeedbackDao_Create(t *testing.T) {
	type args struct {
		ctx                       context.Context
		resolutionFeedbackAttempt *issueResolutionFeedbackPb.IssueResolutionFeedback
	}
	tests := []struct {
		name    string
		args    args
		want    *issueResolutionFeedbackPb.IssueResolutionFeedback
		wantErr bool
	}{
		{
			name: "invalid args",
			args: args{
				ctx: context.Background(),
				resolutionFeedbackAttempt: &issueResolutionFeedbackPb.IssueResolutionFeedback{
					TicketId:           0,
					ResolutionCategory: 0,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "already exists",
			args: args{
				ctx: context.Background(),
				resolutionFeedbackAttempt: &issueResolutionFeedbackPb.IssueResolutionFeedback{
					TicketId:           1000,
					ResolutionCategory: issueResolutionFeedbackPb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successful",
			args: args{
				ctx: context.Background(),
				resolutionFeedbackAttempt: &issueResolutionFeedbackPb.IssueResolutionFeedback{
					TicketId:           100,
					ResolutionCategory: issueResolutionFeedbackPb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
					ProcessStage:       issueResolutionFeedbackPb.ProcessStage_PROCESS_STAGE_CREATED,
				},
			},
			want: &issueResolutionFeedbackPb.IssueResolutionFeedback{
				TicketId:           100,
				ResolutionCategory: issueResolutionFeedbackPb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
				ProcessStage:       issueResolutionFeedbackPb.ProcessStage_PROCESS_STAGE_CREATED,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ircadts.db, ircadts.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := ircadts.dao.Create(tt.args.ctx, tt.args.resolutionFeedbackAttempt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.LastTriedAt = got.GetLastTriedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("Create() got: %v, want: %v", got, tt.want)
			}
		})
	}
}

func TestIssueResolutionFeedbackDao_GetByTicketId(t *testing.T) {
	type args struct {
		ctx      context.Context
		ticketId int64
	}
	tests := []struct {
		name    string
		args    args
		want    *issueResolutionFeedbackPb.IssueResolutionFeedback
		wantErr bool
	}{
		{
			name: "invalid args",
			args: args{
				ctx:      context.Background(),
				ticketId: 0,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				ctx:      context.Background(),
				ticketId: 9000,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successful 1",
			args: args{
				ctx:      context.Background(),
				ticketId: 1000,
			},
			want: &issueResolutionFeedbackPb.IssueResolutionFeedback{
				TicketId:           1000,
				ResolutionCategory: issueResolutionFeedbackPb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
				ProcessStage:       issueResolutionFeedbackPb.ProcessStage_PROCESS_STAGE_CREATED,
				NumberOfAttempts:   0,
			},
			wantErr: false,
		},
		{
			name: "successful 2",
			args: args{
				ctx:      context.Background(),
				ticketId: 2000,
			},
			want: &issueResolutionFeedbackPb.IssueResolutionFeedback{
				TicketId:           2000,
				ResolutionCategory: issueResolutionFeedbackPb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
				ProcessStage:       issueResolutionFeedbackPb.ProcessStage_PROCESS_STAGE_SENT_TO_COMMS,
				NumberOfAttempts:   1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ircadts.db, ircadts.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := ircadts.dao.GetByTicketId(tt.args.ctx, tt.args.ticketId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByTicketId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.LastTriedAt = got.GetLastTriedAt()
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetByTicketId() got: %v,  want: %v", got, tt.want)
			}
		})
	}
}

func TestIssueResolutionFeedbackDao_GetByClientRequestId(t *testing.T) {
	type args struct {
		ctx             context.Context
		clientRequestId string
	}
	tests := []struct {
		name    string
		args    args
		want    []*issueResolutionFeedbackPb.IssueResolutionFeedback
		wantErr bool
	}{
		{
			name: "invalid args",
			args: args{
				ctx:             context.Background(),
				clientRequestId: "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				ctx:             context.Background(),
				clientRequestId: "ninja1000",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successful",
			args: args{
				ctx:             context.Background(),
				clientRequestId: "dis100",
			},
			want: []*issueResolutionFeedbackPb.IssueResolutionFeedback{
				{
					TicketId:           3000,
					ResolutionCategory: issueResolutionFeedbackPb.ResolutionCategory_RESOLUTION_CATEGORY_DISPUTE,
					ProcessStage:       issueResolutionFeedbackPb.ProcessStage_PROCESS_STAGE_SENT_TO_COMMS,
					NumberOfAttempts:   2,
					ClientRequestId:    "dis100",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ircadts.db, ircadts.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := ircadts.dao.GetByClientRequestId(tt.args.ctx, tt.args.clientRequestId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			err = compareIssueResolutionFeedbackProtoList(got, tt.want)
			if err != nil {
				t.Errorf("GetByClientRequestId() got and want is not equal, err: %v", err)
			}
		})
	}
}

func compareIssueResolutionFeedbackProtoList(got []*issueResolutionFeedbackPb.IssueResolutionFeedback, want []*issueResolutionFeedbackPb.IssueResolutionFeedback) error {
	if len(got) != len(want) {
		return errors.New("got and want len is not equal")
	}
	for index := range got {
		want[index].Id = got[index].GetId()
		want[index].CreatedAt = got[index].GetCreatedAt()
		want[index].UpdatedAt = got[index].GetUpdatedAt()
		want[index].LastTriedAt = got[index].GetLastTriedAt()
		if !proto.Equal(got[index], want[index]) {
			return fmt.Errorf("got = %v, want %v", got, want)
		}
	}
	return nil
}

func TestIssueResolutionFeedbackDao_UpdateByTicketId(t *testing.T) {
	type args struct {
		ctx                       context.Context
		ticketId                  int64
		resolutionFeedbackAttempt *issueResolutionFeedbackPb.IssueResolutionFeedback
		updateMask                []issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "invalid args",
			args: args{
				ctx:        context.Background(),
				ticketId:   0,
				updateMask: nil,
			},
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				ctx:        context.Background(),
				ticketId:   9000,
				updateMask: []issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask{issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_NUMBER_OF_ATTEMPTS},
			},
			wantErr: true,
		},
		{
			name: "successful",
			args: args{
				ctx:      context.Background(),
				ticketId: 1000,
				resolutionFeedbackAttempt: &issueResolutionFeedbackPb.IssueResolutionFeedback{
					NumberOfAttempts: 1,
					LastTriedAt:      timestampPb.New(time.Now()),
					ProcessStage:     issueResolutionFeedbackPb.ProcessStage_PROCESS_STAGE_SENT_TO_COMMS,
				},
				updateMask: []issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask{
					issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_NUMBER_OF_ATTEMPTS,
					issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_LAST_TRIED_AT,
					issueResolutionFeedbackPb.IssueResolutionFeedbackFieldMask_ISSUE_RESOLUTION_FEEDBACK_FIELD_MASK_PROCESS_STAGE,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, ircadts.db, ircadts.conf.EpifiDb.GetName(), test.AffectedTestTables)
			err := ircadts.dao.UpdateByTicketId(tt.args.ctx, tt.args.ticketId, tt.args.resolutionFeedbackAttempt, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
