package dao_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/dao"
	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, db, teardown := test.InitTestServer(true)
	citmdTS = CrmToIssueTrackerMappingDAOTestSuite{
		db:      db,
		conf:    conf,
		citmDao: dao.NewCrmToIssueTrackerMappingDao(db),
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
