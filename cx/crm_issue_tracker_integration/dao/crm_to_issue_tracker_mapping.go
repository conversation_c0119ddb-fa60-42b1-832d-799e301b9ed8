package dao

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/dao/model"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"
)

type CrmToIssueTrackerMappingDao struct {
	db *gormv2.DB
}

func NewCrmToIssueTrackerMappingDao(db types.SherlockPGDB) *CrmToIssueTrackerMappingDao {
	return &CrmToIssueTrackerMappingDao{db: db}
}

var _ ICrmToIssueTrackerMappingDao = &CrmToIssueTrackerMappingDao{}

func (c *CrmToIssueTrackerMappingDao) CreateBatch(ctx context.Context, mappings []*citPb.CrmToIssueTrackerMapping) ([]*citPb.CrmToIssueTrackerMapping, error) {
	defer metric_util.TrackDuration("cx/crm_issue_tracker_integration/dao", "CrmToIssueTrackerMappingDao", "CreateBatch", time.Now())
	err := validateMappingListForCreateBatch(mappings)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, err.Error())
	}
	mappingModelsList := model.NewCrmToIssueTrackerMappingModelsList(mappings)
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := db.Create(mappingModelsList).Error; err != nil {
		if storagev2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, errors.Wrap(err, "failed to insert crm<>issueTracker ticket mappings into db")
	}
	return model.CrmToIssueTrackerMappingModelsListToProtosList(mappingModelsList), nil
}

func (c *CrmToIssueTrackerMappingDao) GetByCrmTicketId(ctx context.Context, crmTool citPb.CrmTool, crmTicketId int64) ([]*citPb.CrmToIssueTrackerMapping, error) {
	defer metric_util.TrackDuration("cx/crm_issue_tracker_integration/dao", "CrmToIssueTrackerMappingDao", "GetByCrmTicketId", time.Now())
	var mappingModels []*model.CrmToIssueTrackerMapping

	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	query := db.Where("crm_tool = ? AND crm_ticket_id = ?", crmTool, crmTicketId)
	if err := query.Order("created_at desc").Find(&mappingModels).Error; err != nil {
		return nil, errors.Wrap(err, "error while fetching crm<>issueTracker ticket mapping from db")
	}
	if len(mappingModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return model.CrmToIssueTrackerMappingModelsListToProtosList(mappingModels), nil
}

func (c *CrmToIssueTrackerMappingDao) GetByIssueTrackerTicketId(ctx context.Context, issueTrackerTool citPb.IssueTrackerTool, issueTrackerTicketId int64) ([]*citPb.CrmToIssueTrackerMapping, error) {
	defer metric_util.TrackDuration("cx/crm_issue_tracker_integration/dao", "CrmToIssueTrackerMappingDao", "GetByIssueTrackerTicketId", time.Now())
	var mappingModels []*model.CrmToIssueTrackerMapping

	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	query := db.Where("issue_tracker_tool = ? AND issue_tracker_ticket_id = ?", issueTrackerTool, issueTrackerTicketId)
	if err := query.Order("created_at desc").Find(&mappingModels).Error; err != nil {
		return nil, errors.Wrap(err, "error while fetching crm<>issueTracker ticket mapping from db")
	}
	if len(mappingModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return model.CrmToIssueTrackerMappingModelsListToProtosList(mappingModels), nil
}

// TODO: should we move this validation to service layer ?
func validateMappingListForCreateBatch(mappings []*citPb.CrmToIssueTrackerMapping) error {
	if len(mappings) == 0 {
		return errors.New("mapping list cannot be empty")
	}
	for _, mapping := range mappings {
		switch {
		case mapping.GetCrmTool() == citPb.CrmTool_CRM_TOOL_UNSPECIFIED:
			return errors.New("CRM tool cannot be unspecified")
		case mapping.GetCrmTicketId() == 0:
			return errors.New("CRM ticket ID cannot be zero")
		case mapping.GetIssueTrackerTool() == citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_UNSPECIFIED:
			return errors.New("Issue tracker tool cannot be unspecified")
		case mapping.GetIssueTrackerTicketId() == 0:
			return errors.New("Issue tracker ticket ID cannot be zero")
		default: // continue to validate next mapping
		}
	}
	return nil
}
