package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
)

type CrmToIssueTrackerMapping struct {
	// unique identifier of the row
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key"`
	// CRM tool associated with this row. eg: Freshdesk
	CrmTool citPb.CrmTool
	// Id of the Ticket created on CRM tool like Freshdesk
	CrmTicketId int64
	// Issue tracker tool associated with this row. eg: Monorail
	IssueTrackerTool citPb.IssueTrackerTool
	// Id of the Issue/Ticket created on Issue tracker tool like Monorail
	IssueTrackerTicketId int64
	// timestamp at which this row was created in DB
	CreatedAt time.Time
	// timestamp at which this row was last updated
	UpdatedAt time.Time
}

func NewCrmToIssueTrackerMappingModel(msg *citPb.CrmToIssueTrackerMapping) *CrmToIssueTrackerMapping {
	modelMsg := &CrmToIssueTrackerMapping{
		CrmTool:              msg.GetCrmTool(),
		CrmTicketId:          msg.GetCrmTicketId(),
		IssueTrackerTool:     msg.GetIssueTrackerTool(),
		IssueTrackerTicketId: msg.GetIssueTrackerTicketId(),
	}
	return modelMsg
}
func NewCrmToIssueTrackerMappingModelsList(mappings []*citPb.CrmToIssueTrackerMapping) []*CrmToIssueTrackerMapping {
	var mappingModels []*CrmToIssueTrackerMapping
	for _, mappingProto := range mappings {
		mappingModels = append(mappingModels, NewCrmToIssueTrackerMappingModel(mappingProto))
	}
	return mappingModels
}

func (c *CrmToIssueTrackerMapping) ToProtoMessage() *citPb.CrmToIssueTrackerMapping {
	protoMsg := &citPb.CrmToIssueTrackerMapping{
		Id:                   c.Id,
		CrmTool:              c.CrmTool,
		CrmTicketId:          c.CrmTicketId,
		IssueTrackerTool:     c.IssueTrackerTool,
		IssueTrackerTicketId: c.IssueTrackerTicketId,
		CreatedAt:            timestamppb.New(c.CreatedAt),
		UpdatedAt:            timestamppb.New(c.UpdatedAt),
	}
	return protoMsg
}

func CrmToIssueTrackerMappingModelsListToProtosList(mappingModels []*CrmToIssueTrackerMapping) []*citPb.CrmToIssueTrackerMapping {
	var mappings []*citPb.CrmToIssueTrackerMapping
	for _, mappingModel := range mappingModels {
		mappings = append(mappings, mappingModel.ToProtoMessage())
	}
	return mappings
}
