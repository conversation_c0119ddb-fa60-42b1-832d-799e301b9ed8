package dao_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/ptypes/timestamp"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	sbPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/dao"
	"github.com/epifi/gamma/cx/test"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"

	gormV2 "gorm.io/gorm"
)

type CrmToIssueTrackerMappingDAOTestSuite struct {
	db      *gormV2.DB
	conf    *config.Config
	citmDao dao.ICrmToIssueTrackerMappingDao
}

const (
	actorId1 = "actor-id-1"
)

var (
	citmdTS CrmToIssueTrackerMappingDAOTestSuite

	citMapping1 = &citPb.CrmToIssueTrackerMapping{
		Id:                   "de34d771-2c76-44e3-81f9-48c730feef1d",
		CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
		CrmTicketId:          1234,
		IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
		IssueTrackerTicketId: 4321,
		CreatedAt:            &timestamp.Timestamp{Seconds: 1685943000},
		UpdatedAt:            &timestamp.Timestamp{Seconds: 1685943000},
	}
	citMapping2 = &citPb.CrmToIssueTrackerMapping{
		Id:                   "de34d771-2c76-44e3-81f9-48c730feef1e",
		CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
		CrmTicketId:          1234,
		IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
		IssueTrackerTicketId: 1,
		CreatedAt:            &timestamp.Timestamp{Seconds: 1685946600},
		UpdatedAt:            &timestamp.Timestamp{Seconds: 1685946600},
	}
	citMapping3 = &citPb.CrmToIssueTrackerMapping{
		Id:                   "de34d771-2c76-44e3-81f9-48c730feef1c",
		CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
		CrmTicketId:          1732,
		IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
		IssueTrackerTicketId: 1,
		CreatedAt:            &timestamp.Timestamp{Seconds: 1685950200},
		UpdatedAt:            &timestamp.Timestamp{Seconds: 1685950200},
	}
)

func TestCrmToIssueTrackerMappingDao_CreateBatch(t *testing.T) {
	type args struct {
		ctx     context.Context
		mapping []*sbPb.CrmToIssueTrackerMapping
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "empty mapping list",
			args: args{
				ctx:     context.Background(),
				mapping: []*sbPb.CrmToIssueTrackerMapping{},
			},
			wantErr: true,
		},
		{
			name: "invalid CRM tool name",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.CrmToIssueTrackerMapping{{
					CrmTicketId: 1,
				}},
			},
			wantErr: true,
		},
		{
			name: "invalid CRM ticket Id",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.CrmToIssueTrackerMapping{{
					CrmTool: citPb.CrmTool_CRM_TOOL_FRESHDESK,
				}},
			},
			wantErr: true,
		},
		{
			name: "invalid Issue Tracker tool name",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.CrmToIssueTrackerMapping{{
					CrmTool:     citPb.CrmTool_CRM_TOOL_FRESHDESK,
					CrmTicketId: 1,
				}},
			},
			wantErr: true,
		},
		{
			name: "invalid Issue Tracker ticket Id",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.CrmToIssueTrackerMapping{{
					CrmTool:          citPb.CrmTool_CRM_TOOL_FRESHDESK,
					CrmTicketId:      1,
					IssueTrackerTool: citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
				}},
			},
			wantErr: true,
		},
		{
			name: "duplicate entry",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.CrmToIssueTrackerMapping{{
					CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
					CrmTicketId:          1234,
					IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
					IssueTrackerTicketId: 1,
				}},
			},
			wantErr: true,
		},
		{
			name: "success: one entry",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.CrmToIssueTrackerMapping{{
					CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
					CrmTicketId:          2,
					IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
					IssueTrackerTicketId: 2,
				}},
			},
			wantErr: false,
		},
		{
			name: "success: multiple entries",
			args: args{
				ctx: context.Background(),
				mapping: []*sbPb.CrmToIssueTrackerMapping{{
					CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
					CrmTicketId:          2,
					IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
					IssueTrackerTicketId: 2,
				}, {
					CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
					CrmTicketId:          1234,
					IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
					IssueTrackerTicketId: 1111,
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, citmdTS.db, citmdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			_, err := citmdTS.citmDao.CreateBatch(tt.args.ctx, tt.args.mapping)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCrmToIssueTrackerMappingDao_GetByCrmTicketId(t *testing.T) {
	type args struct {
		ctx         context.Context
		crmTool     citPb.CrmTool
		crmTicketId int64
	}
	tests := []struct {
		name    string
		args    args
		want    []*citPb.CrmToIssueTrackerMapping
		wantErr bool
	}{
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "success: one record",
			args: args{
				ctx:         context.Background(),
				crmTool:     citPb.CrmTool_CRM_TOOL_FRESHDESK,
				crmTicketId: 1732,
			},
			want:    []*citPb.CrmToIssueTrackerMapping{citMapping3},
			wantErr: false,
		},
		{
			name: "success: multiple records",
			args: args{
				ctx:         context.Background(),
				crmTool:     citPb.CrmTool_CRM_TOOL_FRESHDESK,
				crmTicketId: 1234,
			},
			want:    []*citPb.CrmToIssueTrackerMapping{citMapping2, citMapping1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, citmdTS.db, citmdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := citmdTS.citmDao.GetByCrmTicketId(tt.args.ctx, tt.args.crmTool, tt.args.crmTicketId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByCrmTicketId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isCrmToIssueTrackerMappingListEqual(got, tt.want) {
				t.Errorf("GetByCrmTicketId() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func TestCrmToIssueTrackerMappingDao_GetByIssueTrackerTicketId(t *testing.T) {
	type args struct {
		ctx                  context.Context
		issueTrackerTool     citPb.IssueTrackerTool
		issueTrackerTicketId int64
	}
	tests := []struct {
		name    string
		args    args
		want    []*citPb.CrmToIssueTrackerMapping
		wantErr bool
	}{
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "success: one record",
			args: args{
				ctx:                  context.Background(),
				issueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
				issueTrackerTicketId: 4321,
			},
			want:    []*citPb.CrmToIssueTrackerMapping{citMapping1},
			wantErr: false,
		},
		{
			name: "success: multiple records",
			args: args{
				ctx:                  context.Background(),
				issueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
				issueTrackerTicketId: 1,
			},
			want:    []*citPb.CrmToIssueTrackerMapping{citMapping3, citMapping2},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, citmdTS.db, citmdTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := citmdTS.citmDao.GetByIssueTrackerTicketId(tt.args.ctx, tt.args.issueTrackerTool, tt.args.issueTrackerTicketId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByIssueTrackerTicketId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isCrmToIssueTrackerMappingListEqual(got, tt.want) {
				t.Errorf("GetByIssueTrackerTicketId() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func isCrmToIssueTrackerMappingListEqual(got []*sbPb.CrmToIssueTrackerMapping, want []*sbPb.CrmToIssueTrackerMapping) bool {
	if len(got) != len(want) {
		return false
	}
	for idx, _ := range got {
		if !isCrmToIssueTrackerMappingEqual(got[idx], want[idx]) {
			return false
		}
	}
	return true
}

func isCrmToIssueTrackerMappingEqual(got *sbPb.CrmToIssueTrackerMapping, want *sbPb.CrmToIssueTrackerMapping) bool {
	if got.GetCrmTool() != want.GetCrmTool() || got.GetCrmTicketId() != want.GetCrmTicketId() ||
		got.GetIssueTrackerTool() != want.GetIssueTrackerTool() || got.GetIssueTrackerTicketId() != want.GetIssueTrackerTicketId() {
		return false
	}
	return true
}
