//go:generate dao_metrics_gen .
//go:generate mockgen -source=dao.go -destination=../../test/mocks/crm_issue_tracker_integration/dao/mock_dao.go
package dao

import (
	"context"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
)

// ICrmToIssueTrackerMappingDao -  Interface which exposes DAO methods on top of CrmToIssueTrackerMapping table
type ICrmToIssueTrackerMappingDao interface {
	// CreateBatch - method to bulk insert mapping records in db
	// accepts list of mapping objects to be created and returns list of created entries in the db
	CreateBatch(ctx context.Context, mappings []*citPb.CrmToIssueTrackerMapping) ([]*citPb.CrmToIssueTrackerMapping, error)
	// GetByCrmTicketId - method to retrieve all mappings for the given ticket Id on the CRM tool.
	GetByCrmTicketId(ctx context.Context, crmTool citPb.CrmTool, crmTicketId int64) ([]*citPb.CrmToIssueTrackerMapping, error)
	// GetByIssueTrackerTicketId - method to retrieve all mappings for the given ticket Id on the issue tracker tool.
	GetByIssueTrackerTicketId(ctx context.Context, issueTrackerTool citPb.IssueTrackerTool, issueTrackerTicketId int64) ([]*citPb.CrmToIssueTrackerMapping, error)
}
