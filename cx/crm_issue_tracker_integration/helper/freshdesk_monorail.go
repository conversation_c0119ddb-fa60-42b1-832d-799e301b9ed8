package helper

import (
	"context"

	"github.com/pkg/errors"

	monorailPb "github.com/epifi/be-common/api/pkg/monorail"
	"github.com/epifi/be-common/pkg/epifigrpc"
	monorailApi "github.com/epifi/be-common/pkg/monorail/api_wrapper"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	cxTicketPb "github.com/epifi/gamma/api/cx/ticket"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
)

type FreshdeskMonorailApiHelper struct {
	conf               *cxGenConf.Config
	monorailApiWrapper monorailApi.IMonorailApiWrapper
	fdCxTicketClient   cxTicketPb.TicketClient
}

func NewFreshdeskMonorailApiHelper(conf *cxGenConf.Config, monorailApiWrapper monorailApi.IMonorailApiWrapper,
	fdCxTicketClient cxTicketPb.TicketClient) *FreshdeskMonorailApiHelper {
	return &FreshdeskMonorailApiHelper{
		conf:               conf,
		monorailApiWrapper: monorailApiWrapper,
		fdCxTicketClient:   fdCxTicketClient,
	}
}

var _ CrmIssueTrackerIntegrationHelper = &FreshdeskMonorailApiHelper{}

func (f *FreshdeskMonorailApiHelper) InvokeIssueTrackerCreateApis(ctx context.Context, issueTrackerTicket *citPb.IssueTrackerTicket) (*citPb.IssueTrackerTicket, error) {
	createdTicket := &citPb.IssueTrackerTicket{}
	monorailResp, createErr := f.monorailApiWrapper.CreateIssue(ctx, &monorailPb.CreateIssueRequest{Issue: issueTrackerTicket.GetMonorailIssue()})
	if createErr != nil {
		return nil, errors.Wrap(createErr, "failed to create monorail ticket")
	}
	createdTicket.Ticket = &citPb.IssueTrackerTicket_MonorailIssue{MonorailIssue: monorailResp.GetIssue()}
	return createdTicket, nil
}

func (f *FreshdeskMonorailApiHelper) InvokeIssueTrackerUpdateApis(ctx context.Context, issueTrackerTicket *citPb.IssueTrackerTicket, comment string) error {
	var monorailComment *monorailPb.Comment
	if comment != "" {
		monorailComment = &monorailPb.Comment{
			Author:  &monorailPb.Person{Email: f.conf.MonorailConfig().AuthorEmail},
			Content: comment,
		}
	}
	_, updateErr := f.monorailApiWrapper.UpdateIssue(ctx, &monorailPb.UpdateIssueRequest{Issue: issueTrackerTicket.GetMonorailIssue(), Comment: monorailComment})
	if updateErr != nil {
		return errors.Wrap(updateErr, "failed to update monorail ticket")
	}
	return nil
}

func (f *FreshdeskMonorailApiHelper) InvokeCrmUpdateApis(ctx context.Context, crmTickets []*citPb.CrmTicket, comment string, isTicketAttributeUpdate bool) error {
	if isTicketAttributeUpdate {
		for _, crmTicket := range crmTickets {
			fdTicketResp, updateErr := f.fdCxTicketClient.UpdateTicketAsync(ctx, &cxTicketPb.UpdateTicketAsyncRequest{Ticket: crmTicket.GetFreshdeskTicket()})
			if te := epifigrpc.RPCError(fdTicketResp, updateErr); te != nil {
				return errors.Wrap(te, "failed to update freshdesk ticket")
			}
		}
	}
	if comment != "" {
		for _, crmTicket := range crmTickets {
			resp, err := f.fdCxTicketClient.AddPrivateNoteAsync(ctx, &cxTicketPb.AddPrivateNoteAsyncRequest{
				TicketId: crmTicket.GetFreshdeskTicket().GetId(),
				Body:     comment,
			})
			if te := epifigrpc.RPCError(resp, err); te != nil {
				return errors.Wrap(te, "error while adding private note to ticket on freshdesk")
			}
		}
	}
	return nil
}

func (f *FreshdeskMonorailApiHelper) UpdateIssueTrackerTicketIdInCrmTicket(ctx context.Context, crmTicket *citPb.CrmTicket, issueTrackerTicketId int64) error {
	fdTicketId := crmTicket.GetFreshdeskTicket().GetId()
	fdTicketResp, updateErr := f.fdCxTicketClient.UpdateTicketAsync(ctx, &cxTicketPb.UpdateTicketAsyncRequest{Ticket: &cxTicketPb.Ticket{
		Id: fdTicketId,
		CustomFields: &cxTicketPb.CustomFields{
			MonorailRaised:   cxTicketPb.MonorailRaised_MONORAIL_RAISED_YES,
			MonorailTicketId: issueTrackerTicketId,
		},
	}})
	if te := epifigrpc.RPCError(fdTicketResp, updateErr); te != nil {
		return errors.Wrap(te, "failed to update freshdesk ticket with monorail Id")
	}
	return nil
}

func (f *FreshdeskMonorailApiHelper) GetCrmTicketId(ticket *citPb.CrmTicket) int64 {
	return ticket.GetFreshdeskTicket().GetId()
}

func (f *FreshdeskMonorailApiHelper) GetIssueTrackerTicketId(ticket *citPb.IssueTrackerTicket) int64 {
	return ticket.GetMonorailIssue().GetId()
}
