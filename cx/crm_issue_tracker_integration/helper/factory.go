package helper

import (
	"errors"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
)

type ICrmIssueTrackerIntegrationHelperFactory interface {
	GetCrmIssueTrackerIntegrationHelperImplementation(crmTool citPb.CrmTool, issueTrackerTool citPb.IssueTrackerTool) (CrmIssueTrackerIntegrationHelper, error)
}

type CrmIssueTrackerIntegrationHelperFactory struct {
	freshdeskMonorailApiHelper *FreshdeskMonorailApiHelper
}

func NewCrmIssueTrackerIntegrationHelperFactory(freshdeskMonorailApiHelper *FreshdeskMonorailApiHelper) *CrmIssueTrackerIntegrationHelperFactory {
	return &CrmIssueTrackerIntegrationHelperFactory{
		freshdeskMonorailApiHelper: freshdeskMonorailApiHelper,
	}
}

var _ ICrmIssueTrackerIntegrationHelperFactory = &CrmIssueTrackerIntegrationHelperFactory{}

func (i *CrmIssueTrackerIntegrationHelperFactory) GetCrmIssueTrackerIntegrationHelperImplementation(crmTool citPb.CrmTool, issueTrackerTool citPb.IssueTrackerTool) (CrmIssueTrackerIntegrationHelper, error) {
	switch {
	case crmTool == citPb.CrmTool_CRM_TOOL_FRESHDESK && issueTrackerTool == citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL:
		return i.freshdeskMonorailApiHelper, nil
	default:
		return nil, errors.New("no Crm<>IssueTracker integration helper implementation found for the given tools")
	}
}
