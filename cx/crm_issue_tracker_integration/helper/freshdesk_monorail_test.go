package helper

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"

	monorailPb "github.com/epifi/be-common/api/pkg/monorail"
	"github.com/epifi/be-common/api/rpc"
	mockApiWrapper "github.com/epifi/be-common/pkg/monorail/mocks/api_wrapper"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	cxTicketPb "github.com/epifi/gamma/api/cx/ticket"
	mockCxTicket "github.com/epifi/gamma/api/cx/ticket/mocks"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
)

type FreshdeskMonorailHelperTestSuite struct {
	GenConf *cxGenConf.Config
}

var (
	fmhTS               *FreshdeskMonorailHelperTestSuite
	monorailIssue1      = &monorailPb.Issue{Id: 1, Summary: "summary 1", Description: "description 1"}
	issueTrackerTicket1 = &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: monorailIssue1,
		},
	}
	monorailIssueWithoutId1      = &monorailPb.Issue{Summary: "summary 1", Description: "description 1"}
	issueTrackerTicketWithoutId1 = &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: monorailIssueWithoutId1,
		},
	}
	monorailCommentContent1 = "comment on monorail issue 1"

	freshdeskTicket1 = &cxTicketPb.Ticket{
		Id:     1,
		Status: cxTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
	}
	crmTicket1 = &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: freshdeskTicket1,
		},
	}
	freshdeskCommentContent1 = "comment on freshdesk issue 1"
)

func TestFreshdeskMonorail_InvokeIssueTrackerCreateApis(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockMonorailApi := mockApiWrapper.NewMockIMonorailApiWrapper(ctr)
	type args struct {
		mocks              []interface{}
		ctx                context.Context
		issueTrackerTicket *citPb.IssueTrackerTicket
	}
	tests := []struct {
		name    string
		args    args
		want    *citPb.IssueTrackerTicket
		wantErr bool
	}{
		{
			name: "error invoking create issue API",
			args: args{
				mocks: []interface{}{
					mockMonorailApi.EXPECT().CreateIssue(gomock.Any(), &monorailPb.CreateIssueRequest{Issue: monorailIssueWithoutId1}).Return(nil, errors.New("mock err")),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicketWithoutId1,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockMonorailApi.EXPECT().CreateIssue(gomock.Any(), &monorailPb.CreateIssueRequest{Issue: monorailIssueWithoutId1}).Return(&monorailPb.CreateIssueResponse{Issue: monorailIssue1}, nil),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicketWithoutId1,
			},
			want:    issueTrackerTicket1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailApiHelper(nil, mockMonorailApi, nil)
			got, err := s.InvokeIssueTrackerCreateApis(tt.args.ctx, tt.args.issueTrackerTicket)
			if (err != nil) != tt.wantErr {
				t.Errorf("InvokeIssueTrackerCreateApis() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("InvokeIssueTrackerCreateApis() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFreshdeskMonorail_InvokeIssueTrackerUpdateApis(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockMonorailApi := mockApiWrapper.NewMockIMonorailApiWrapper(ctr)
	type args struct {
		mocks              []interface{}
		ctx                context.Context
		issueTrackerTicket *citPb.IssueTrackerTicket
		comment            string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "error invoking update issue API",
			args: args{
				mocks: []interface{}{
					mockMonorailApi.EXPECT().UpdateIssue(gomock.Any(), &monorailPb.UpdateIssueRequest{Issue: monorailIssue1}).Return(nil, errors.New("mock err")),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicket1,
			},
			wantErr: true,
		},
		{
			name: "success without comment",
			args: args{
				mocks: []interface{}{
					mockMonorailApi.EXPECT().UpdateIssue(gomock.Any(), &monorailPb.UpdateIssueRequest{
						Issue: monorailIssue1,
					}).Return(&monorailPb.UpdateIssueResponse{Issue: monorailIssue1}, nil),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicket1,
			},
			wantErr: false,
		},
		{
			name: "success with comment",
			args: args{
				mocks: []interface{}{
					mockMonorailApi.EXPECT().UpdateIssue(gomock.Any(), &monorailPb.UpdateIssueRequest{
						Issue:   monorailIssue1,
						Comment: &monorailPb.Comment{Author: &monorailPb.Person{Email: fmhTS.GenConf.MonorailConfig().AuthorEmail}, Content: monorailCommentContent1},
					}).Return(&monorailPb.UpdateIssueResponse{Issue: monorailIssue1}, nil)},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicket1,
				comment:            monorailCommentContent1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailApiHelper(fmhTS.GenConf, mockMonorailApi, nil)
			err := s.InvokeIssueTrackerUpdateApis(tt.args.ctx, tt.args.issueTrackerTicket, tt.args.comment)
			if (err != nil) != tt.wantErr {
				t.Errorf("InvokeIssueTrackerUpdateApis() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestFreshdeskMonorail_InvokeCrmUpdateApis(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockCxFdTicketClient := mockCxTicket.NewMockTicketClient(ctr)
	type args struct {
		mocks                   []interface{}
		ctx                     context.Context
		crmTickets              []*citPb.CrmTicket
		comment                 string
		isTicketAttributeUpdate bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "error invoking update Freshdesk API",
			args: args{
				mocks: []interface{}{
					mockCxFdTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), &cxTicketPb.UpdateTicketAsyncRequest{Ticket: freshdeskTicket1}).Return(nil, errors.New("mock err")),
				},
				ctx:                     context.Background(),
				crmTickets:              []*citPb.CrmTicket{crmTicket1},
				isTicketAttributeUpdate: true,
			},
			wantErr: true,
		},
		{
			name: "success: update ticket attribute without comment",
			args: args{
				mocks: []interface{}{
					mockCxFdTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), &cxTicketPb.UpdateTicketAsyncRequest{Ticket: freshdeskTicket1}).Return(&cxTicketPb.UpdateTicketAsyncResponse{Status: rpc.StatusOk()}, nil),
				},
				ctx:                     context.Background(),
				crmTickets:              []*citPb.CrmTicket{crmTicket1},
				isTicketAttributeUpdate: true,
			},
			wantErr: false,
		},
		{
			name: "error invoking Freshdesk add private note API",
			args: args{
				mocks: []interface{}{
					mockCxFdTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), &cxTicketPb.UpdateTicketAsyncRequest{Ticket: freshdeskTicket1}).Return(&cxTicketPb.UpdateTicketAsyncResponse{Status: rpc.StatusOk()}, nil),
					mockCxFdTicketClient.EXPECT().AddPrivateNoteAsync(gomock.Any(), &cxTicketPb.AddPrivateNoteAsyncRequest{
						TicketId: freshdeskTicket1.GetId(),
						Body:     freshdeskCommentContent1,
					}).Return(&cxTicketPb.AddPrivateNoteAsyncResponse{Status: rpc.StatusInternalWithDebugMsg("mock failure")}, nil),
				},
				ctx:                     context.Background(),
				crmTickets:              []*citPb.CrmTicket{crmTicket1},
				comment:                 freshdeskCommentContent1,
				isTicketAttributeUpdate: true,
			},
			wantErr: true,
		},
		{
			name: "success: add comment, no ticket attribute update",
			args: args{
				mocks: []interface{}{
					mockCxFdTicketClient.EXPECT().AddPrivateNoteAsync(gomock.Any(), &cxTicketPb.AddPrivateNoteAsyncRequest{
						TicketId: freshdeskTicket1.GetId(),
						Body:     freshdeskCommentContent1,
					}).Return(&cxTicketPb.AddPrivateNoteAsyncResponse{Status: rpc.StatusOk()}, nil),
				},
				ctx:                     context.Background(),
				crmTickets:              []*citPb.CrmTicket{crmTicket1},
				comment:                 freshdeskCommentContent1,
				isTicketAttributeUpdate: false,
			},
			wantErr: false,
		},
		{
			name: "success: ticket attribute update and comment",
			args: args{
				mocks: []interface{}{
					mockCxFdTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), &cxTicketPb.UpdateTicketAsyncRequest{Ticket: freshdeskTicket1}).Return(&cxTicketPb.UpdateTicketAsyncResponse{Status: rpc.StatusOk()}, nil),
					mockCxFdTicketClient.EXPECT().AddPrivateNoteAsync(gomock.Any(), &cxTicketPb.AddPrivateNoteAsyncRequest{
						TicketId: freshdeskTicket1.GetId(),
						Body:     freshdeskCommentContent1,
					}).Return(&cxTicketPb.AddPrivateNoteAsyncResponse{Status: rpc.StatusOk()}, nil),
				},
				ctx:                     context.Background(),
				crmTickets:              []*citPb.CrmTicket{crmTicket1},
				comment:                 freshdeskCommentContent1,
				isTicketAttributeUpdate: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailApiHelper(fmhTS.GenConf, nil, mockCxFdTicketClient)
			err := s.InvokeCrmUpdateApis(tt.args.ctx, tt.args.crmTickets, tt.args.comment, tt.args.isTicketAttributeUpdate)
			if (err != nil) != tt.wantErr {
				t.Errorf("InvokeCrmUpdateApis() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestFreshdeskMonorail_UpdateIssueTrackerTicketIdInCrmTicket(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockCxFdTicketClient := mockCxTicket.NewMockTicketClient(ctr)
	type args struct {
		mocks                []interface{}
		ctx                  context.Context
		crmTicket            *citPb.CrmTicket
		issueTrackerTicketId int64
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "error invoking update Freshdesk API",
			args: args{
				mocks: []interface{}{
					mockCxFdTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), &cxTicketPb.UpdateTicketAsyncRequest{
						Ticket: &cxTicketPb.Ticket{
							Id: freshdeskTicket1.GetId(),
							CustomFields: &cxTicketPb.CustomFields{
								MonorailRaised:   cxTicketPb.MonorailRaised_MONORAIL_RAISED_YES,
								MonorailTicketId: monorailIssue1.GetId(),
							},
						}}).Return(nil, errors.New("mock err")),
				},
				ctx:                  context.Background(),
				crmTicket:            crmTicket1,
				issueTrackerTicketId: monorailIssue1.GetId(),
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockCxFdTicketClient.EXPECT().UpdateTicketAsync(gomock.Any(), &cxTicketPb.UpdateTicketAsyncRequest{
						Ticket: &cxTicketPb.Ticket{
							Id: freshdeskTicket1.GetId(),
							CustomFields: &cxTicketPb.CustomFields{
								MonorailRaised:   cxTicketPb.MonorailRaised_MONORAIL_RAISED_YES,
								MonorailTicketId: monorailIssue1.GetId(),
							},
						}}).Return(&cxTicketPb.UpdateTicketAsyncResponse{Status: rpc.StatusOk()}, nil),
				},
				ctx:                  context.Background(),
				crmTicket:            crmTicket1,
				issueTrackerTicketId: monorailIssue1.GetId(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailApiHelper(fmhTS.GenConf, nil, mockCxFdTicketClient)
			err := s.UpdateIssueTrackerTicketIdInCrmTicket(tt.args.ctx, tt.args.crmTicket, tt.args.issueTrackerTicketId)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateIssueTrackerTicketIdInCrmTicket() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestFreshdeskMonorail_GetCrmTicketId(t *testing.T) {
	t.Parallel()
	type args struct {
		crmTicket *citPb.CrmTicket
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "success",
			args: args{
				crmTicket: crmTicket1,
			},
			want: crmTicket1.GetFreshdeskTicket().GetId(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailApiHelper(fmhTS.GenConf, nil, nil)
			got := s.GetCrmTicketId(tt.args.crmTicket)
			if got != tt.want {
				t.Errorf("GetCrmTicketId() got = %v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestFreshdeskMonorail_GetIssueTrackerTicketId(t *testing.T) {
	t.Parallel()
	type args struct {
		issueTrackerTicket *citPb.IssueTrackerTicket
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "success",
			args: args{
				issueTrackerTicket: issueTrackerTicket1,
			},
			want: issueTrackerTicket1.GetMonorailIssue().GetId(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailApiHelper(fmhTS.GenConf, nil, nil)
			got := s.GetIssueTrackerTicketId(tt.args.issueTrackerTicket)
			if got != tt.want {
				t.Errorf("GetIssueTrackerTicketId() got = %v, want: %v", got, tt.want)
				return
			}
		})
	}
}
