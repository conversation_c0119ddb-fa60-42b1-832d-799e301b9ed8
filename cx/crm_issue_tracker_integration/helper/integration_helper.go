package helper

import (
	"context"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
)

type CrmIssueTrackerIntegrationHelper interface {
	InvokeIssueTrackerCreateApis(ctx context.Context, issueTrackerTicket *citPb.IssueTrackerTicket) (*citPb.IssueTrackerTicket, error)
	InvokeIssueTrackerUpdateApis(ctx context.Context, issueTrackerTicket *citPb.IssueTrackerTicket, comment string) error
	InvokeCrmUpdateApis(ctx context.Context, crmTickets []*citPb.CrmTicket, comment string, isTicketAttributeUpdate bool) error
	UpdateIssueTrackerTicketIdInCrmTicket(ctx context.Context, crmTicket *citPb.CrmTicket, issueTrackerTicketId int64) error

	GetCrmTicketId(ticket *citPb.CrmTicket) int64
	GetIssueTrackerTicketId(ticket *citPb.IssueTrackerTicket) int64
}
