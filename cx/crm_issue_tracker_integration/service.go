package crm_issue_tracker_integration

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/helper"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/ticket_translator"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/dao"
)

type Service struct {
	// UnimplementedIssueResolutionFeedbackServer is embedded to have forward compatible implementations
	citPb.UnimplementedCrmIssueTrackerIntegrationServer
	conf                     *cxGenConf.Config
	mappingDao               dao.ICrmToIssueTrackerMappingDao
	ticketTranslatorFactory  ticket_translator.ITicketTranslatorFactory
	integrationHelperFactory helper.ICrmIssueTrackerIntegrationHelperFactory
}

func NewService(conf *cxGenConf.Config, mappingDao dao.ICrmToIssueTrackerMappingDao, ticketTranslatorFactory ticket_translator.ITicketTranslatorFactory,
	integrationHelperFactory helper.ICrmIssueTrackerIntegrationHelperFactory) *Service {
	return &Service{
		conf:                     conf,
		mappingDao:               mappingDao,
		ticketTranslatorFactory:  ticketTranslatorFactory,
		integrationHelperFactory: integrationHelperFactory,
	}
}

var _ citPb.CrmIssueTrackerIntegrationServer = &Service{}

// CreateIssueTrackerTicket RPC implementation for creating an issue tracker ticket
func (s *Service) CreateIssueTrackerTicket(ctx context.Context, req *citPb.CreateIssueTrackerTicketRequest) (*citPb.CreateIssueTrackerTicketResponse, error) {
	if validateErr := validateCreateIssueTrackerTicket(req); validateErr != nil {
		logger.Error(ctx, "create issue tracker ticket request validation failed", zap.Error(validateErr))
		return &citPb.CreateIssueTrackerTicketResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(validateErr.Error()),
		}, nil
	}
	helperImpl, factoryErr := s.integrationHelperFactory.GetCrmIssueTrackerIntegrationHelperImplementation(req.GetCrmTool(), req.GetIssueTrackerTool())
	if factoryErr != nil {
		logger.Error(ctx, "failed to get integration helper implementation", zap.Error(factoryErr), zap.String("CrmTool", req.GetCrmTool().String()),
			zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		return &citPb.CreateIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(factoryErr.Error()),
		}, nil
	}
	crmTicketId := helperImpl.GetCrmTicketId(req.GetCrmTicket())
	// TODO: Need to handle this better to avoid duplicate ticket creation. Eg: two different threads can succeed here and go on to create two separate tickets
	// As of now, we are not having unique constraint on CRM ticket Id to keep track of any duplicates created due to this
	mapping, dbErr := s.mappingDao.GetByCrmTicketId(ctx, req.GetCrmTool(), crmTicketId)
	switch {
	case dbErr == nil && len(mapping) != 0:
		logger.Info(ctx, "A monorail issue already exists for this FD ticket", zap.Int64(logger.MONORAIL_ISSUE_ID, mapping[0].GetIssueTrackerTicketId()))
		// If the ticket already exists in our db, we probably failed to update monorail ID on freshdesk in previous attempt.
		// So try to update Freshdesk ticket with monorail ID present in the DB
		updateCrmErr := helperImpl.UpdateIssueTrackerTicketIdInCrmTicket(ctx, req.GetCrmTicket(), mapping[0].GetIssueTrackerTicketId())
		if updateCrmErr != nil {
			logger.Error(ctx, "failed to update issue tracker ticket Id in crm ticket on retry", zap.Error(updateCrmErr))
			return &citPb.CreateIssueTrackerTicketResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		return &citPb.CreateIssueTrackerTicketResponse{
			// already exists status will avoid retries
			Status: rpc.StatusAlreadyExists(),
		}, nil
	case !errors.Is(dbErr, epifierrors.ErrRecordNotFound):
		// if it is a db error other than record not found
		logger.Error(ctx, "failed to get ticket mapping from db", zap.Error(dbErr), zap.Int64(logger.TICKET_ID, crmTicketId),
			zap.String("CrmTool", req.GetCrmTool().String()), zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		return &citPb.CreateIssueTrackerTicketResponse{
			Status: rpc.StatusInternal(),
		}, nil
	default:
		// continue with issue tracker ticket creation
	}
	issueTrackerTicket, translateErr := s.getIssueTrackerTicketFromCrmTicketForCreate(ctx, req.GetCrmTool(), req.GetCrmTicket(), req.GetIssueTrackerTool())
	if translateErr != nil {
		logger.Error(ctx, "failed to translate CRM ticket to issue tracker ticket", zap.Error(translateErr), zap.Int64(logger.TICKET_ID, crmTicketId),
			zap.String("CrmTool", req.GetCrmTool().String()), zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		if errors.Is(translateErr, epifierrors.ErrInvalidArgument) {
			return &citPb.CreateIssueTrackerTicketResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg(translateErr.Error()),
			}, nil
		}
		if errors.Is(translateErr, epifierrors.ErrRecordNotFound) {
			return &citPb.CreateIssueTrackerTicketResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg(translateErr.Error()),
			}, nil
		}
		return &citPb.CreateIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(translateErr.Error()),
		}, nil
	}
	createdTicket, apiErr := helperImpl.InvokeIssueTrackerCreateApis(ctx, issueTrackerTicket)
	if apiErr != nil {
		logger.Error(ctx, "create issue tracker API failed", zap.Error(apiErr), zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		return &citPb.CreateIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(apiErr.Error()),
		}, nil
	}
	// add entry in the DB
	issueTrackerTicketId := helperImpl.GetIssueTrackerTicketId(createdTicket)
	if _, mapCreateErr := s.mappingDao.CreateBatch(ctx, []*citPb.CrmToIssueTrackerMapping{{
		CrmTool:              req.GetCrmTool(),
		CrmTicketId:          crmTicketId,
		IssueTrackerTool:     req.GetIssueTrackerTool(),
		IssueTrackerTicketId: issueTrackerTicketId,
	}}); mapCreateErr != nil {
		logger.Error(ctx, "failed to create CRM to issue tracker ticket mapping in db", zap.Error(mapCreateErr))
		return &citPb.CreateIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(mapCreateErr.Error()),
		}, nil
	}
	// update Issue tracker Id in CRM ticket
	updateCrmErr := helperImpl.UpdateIssueTrackerTicketIdInCrmTicket(ctx, req.GetCrmTicket(), issueTrackerTicketId)
	if updateCrmErr != nil {
		logger.Error(ctx, "failed to update issue tracker ticket Id in crm ticket", zap.Error(updateCrmErr))
		return &citPb.CreateIssueTrackerTicketResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &citPb.CreateIssueTrackerTicketResponse{
		Status:             rpc.StatusOk(),
		IssueTrackerTicket: createdTicket,
	}, nil
}

func validateCreateIssueTrackerTicket(req *citPb.CreateIssueTrackerTicketRequest) error {
	if req.GetIssueTrackerTool() == citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_UNSPECIFIED {
		return errors.New("issue tracker tool is unspecified")
	}
	return validateCrmTicketRequest(req.GetCrmTool(), req.GetCrmTicket())
}

func (s *Service) getIssueTrackerTicketFromCrmTicketForCreate(ctx context.Context, crmTool citPb.CrmTool, crmTicket *citPb.CrmTicket,
	issueTrackerTool citPb.IssueTrackerTool) (*citPb.IssueTrackerTicket, error) {
	translator, factoryErr := s.ticketTranslatorFactory.GetTicketTranslatorImplementation(crmTool, issueTrackerTool)
	if factoryErr != nil {
		return nil, errors.Wrap(factoryErr, "failed to get ticket translator implementation")
	}
	issueTrackerTicket, translateErr := translator.TranslateCrmToIssueTrackerTicketForCreate(ctx, crmTicket)
	if translateErr != nil {
		return nil, errors.Wrap(translateErr, "failed to translate CRM ticket to Issue tracker ticket")
	}
	return issueTrackerTicket, nil
}

func (s *Service) getIssueTrackerTicketFromCrmTicketForUpdate(ctx context.Context, crmTool citPb.CrmTool, crmTicket *citPb.CrmTicket,
	issueTrackerTool citPb.IssueTrackerTool) (*citPb.IssueTrackerTicket, string, error) {
	translator, factoryErr := s.ticketTranslatorFactory.GetTicketTranslatorImplementation(crmTool, issueTrackerTool)
	if factoryErr != nil {
		return nil, "", errors.Wrap(factoryErr, "failed to get ticket translator implementation")
	}
	issueTrackerTicket, comment, translateErr := translator.TranslateCrmToIssueTrackerTicketForUpdate(ctx, crmTicket)
	if translateErr != nil {
		return nil, "", errors.Wrap(translateErr, "failed to translate CRM ticket to Issue tracker ticket")
	}
	return issueTrackerTicket, comment, nil
}

// UpdateIssueTrackerTicket RPC implementation for updating an issue tracker ticket
func (s *Service) UpdateIssueTrackerTicket(ctx context.Context, req *citPb.UpdateIssueTrackerTicketRequest) (*citPb.UpdateIssueTrackerTicketResponse, error) {
	if validateErr := validateUpdateIssueTrackerTicket(req); validateErr != nil {
		logger.Error(ctx, "update issue tracker ticket request validation failed", zap.Error(validateErr))
		return &citPb.UpdateIssueTrackerTicketResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(validateErr.Error()),
		}, nil
	}
	helperImpl, factoryErr := s.integrationHelperFactory.GetCrmIssueTrackerIntegrationHelperImplementation(req.GetCrmTool(), req.GetIssueTrackerTool())
	if factoryErr != nil {
		logger.Error(ctx, "failed to get integration helper implementation", zap.Error(factoryErr), zap.String("CrmTool", req.GetCrmTool().String()),
			zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		return &citPb.UpdateIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(factoryErr.Error()),
		}, nil
	}
	crmTicketId := helperImpl.GetCrmTicketId(req.GetCrmTicket())
	issueTrackerTicket, comment, translateErr := s.getIssueTrackerTicketFromCrmTicketForUpdate(ctx, req.GetCrmTool(), req.GetCrmTicket(), req.GetIssueTrackerTool())
	if translateErr != nil {
		logger.Error(ctx, "failed to translate CRM ticket to issue tracker ticket", zap.Error(translateErr), zap.Int64(logger.TICKET_ID, crmTicketId),
			zap.String("CrmTool", req.GetCrmTool().String()), zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		if errors.Is(translateErr, epifierrors.ErrRecordNotFound) {
			return &citPb.UpdateIssueTrackerTicketResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg(translateErr.Error()),
			}, nil
		}
		return &citPb.UpdateIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(translateErr.Error()),
		}, nil
	}
	apiErr := helperImpl.InvokeIssueTrackerUpdateApis(ctx, issueTrackerTicket, comment)
	if apiErr != nil {
		logger.Error(ctx, "update issue tracker API failed", zap.Error(apiErr), zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		return &citPb.UpdateIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(apiErr.Error()),
		}, nil
	}
	return &citPb.UpdateIssueTrackerTicketResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func validateUpdateIssueTrackerTicket(req *citPb.UpdateIssueTrackerTicketRequest) error {
	if req.GetIssueTrackerTool() == citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_UNSPECIFIED {
		return errors.New("issue tracker tool is unspecified")
	}
	return validateCrmTicketRequest(req.GetCrmTool(), req.GetCrmTicket())
}

// UpdateCrmTicket RPC implementation for updating a crm ticket based on updates on CRDB
// The updates can include ticket attributes and/or comments on the ticket
func (s *Service) UpdateCrmTicket(ctx context.Context, req *citPb.UpdateCrmTicketRequest) (*citPb.UpdateCrmTicketResponse, error) {
	if validateErr := validateUpdateCrmTicket(req); validateErr != nil {
		logger.Error(ctx, "update CRM ticket request validation failed", zap.Error(validateErr))
		return &citPb.UpdateCrmTicketResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(validateErr.Error()),
		}, nil
	}
	helperImpl, factoryErr := s.integrationHelperFactory.GetCrmIssueTrackerIntegrationHelperImplementation(req.GetCrmTool(), req.GetIssueTrackerTool())
	if factoryErr != nil {
		logger.Error(ctx, "failed to get integration helper implementation", zap.Error(factoryErr), zap.String("CrmTool", req.GetCrmTool().String()),
			zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		return &citPb.UpdateCrmTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(factoryErr.Error()),
		}, nil
	}
	issueTrackerTicketId := helperImpl.GetIssueTrackerTicketId(req.GetIssueTrackerTicket())
	crmTickets, isTicketUpdateRequiredAfterTranslation, translateErr := s.getCrmTicketFromIssueTrackerTicketForUpdate(ctx, req.GetCrmTool(), req.GetIssueTrackerTool(), req.GetIssueTrackerTicket())
	if translateErr != nil {
		logger.Error(ctx, "failed to translate issue tracker ticket to CRM ticket", zap.Error(translateErr), zap.Int64(logger.MONORAIL_ISSUE_ID, issueTrackerTicketId),
			zap.String("CrmTool", req.GetCrmTool().String()), zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		if errors.Is(translateErr, epifierrors.ErrRecordNotFound) {
			return &citPb.UpdateCrmTicketResponse{
				Status: rpc.StatusRecordNotFoundWithDebugMsg(translateErr.Error()),
			}, nil
		}
		return &citPb.UpdateCrmTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(translateErr.Error()),
		}, nil
	}
	// We are pushing updates to freshdesk using a scheduled job.
	// req.GetIsTicketAttributeUpdate() will represent if ticket attributes were actually updated since the last run of the job
	// isTicketUpdateRequiredAfterTranslation will represent if the translation logic requires ticket update
	isTicketAttributeUpdate := req.GetIsTicketAttributeUpdate() && isTicketUpdateRequiredAfterTranslation
	apiErr := helperImpl.InvokeCrmUpdateApis(ctx, crmTickets, req.GetNote(), isTicketAttributeUpdate)
	if apiErr != nil {
		logger.Error(ctx, "failed to invoke crm update ticket APIs", zap.Error(apiErr), zap.String("CrmTool", req.GetCrmTool().String()))
		return &citPb.UpdateCrmTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(apiErr.Error()),
		}, nil
	}
	return &citPb.UpdateCrmTicketResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func validateUpdateCrmTicket(req *citPb.UpdateCrmTicketRequest) error {
	if req.GetCrmTool() == citPb.CrmTool_CRM_TOOL_UNSPECIFIED {
		return errors.New("CRM tool is unspecified")
	}
	return validateIssueTrackerTicketRequest(req.GetIssueTrackerTool(), req.GetIssueTrackerTicket())
}

func (s *Service) getCrmTicketFromIssueTrackerTicketForUpdate(ctx context.Context, crmTool citPb.CrmTool, issueTrackerTool citPb.IssueTrackerTool, issueTrackerTicket *citPb.IssueTrackerTicket) ([]*citPb.CrmTicket, bool, error) {
	translator, factoryErr := s.ticketTranslatorFactory.GetTicketTranslatorImplementation(crmTool, issueTrackerTool)
	if factoryErr != nil {
		return nil, false, errors.Wrap(factoryErr, "failed to get ticket translator implementation")
	}
	crmTickets, isTicketAttributeUpdate, translateErr := translator.TranslateIssueTrackerToCrmTicketsForUpdate(ctx, issueTrackerTicket)
	if translateErr != nil {
		return nil, false, errors.Wrap(translateErr, "failed to translate issue tracker ticket to crm ticket")
	}
	return crmTickets, isTicketAttributeUpdate, nil
}

func (s *Service) AppendCrmTicketToIssueTrackerTicket(ctx context.Context, req *citPb.AppendCrmTicketToIssueTrackerTicketRequest) (*citPb.AppendCrmTicketToIssueTrackerTicketResponse, error) {
	if validateErr := validateAppendCrmTicketToIssueTrackerTicketRequest(req); validateErr != nil {
		logger.Error(ctx, "append crm ticket to issue tracker ticket request validation failed", zap.Error(validateErr))
		return &citPb.AppendCrmTicketToIssueTrackerTicketResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(validateErr.Error()),
		}, nil
	}
	helperImpl, factoryErr := s.integrationHelperFactory.GetCrmIssueTrackerIntegrationHelperImplementation(req.GetCrmTool(), req.GetIssueTrackerTool())
	if factoryErr != nil {
		logger.Error(ctx, "failed to get integration helper implementation", zap.Error(factoryErr), zap.String("CrmTool", req.GetCrmTool().String()),
			zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		return &citPb.AppendCrmTicketToIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(factoryErr.Error()),
		}, nil
	}
	crmTicketId := helperImpl.GetCrmTicketId(req.GetCrmTicket())
	issueTrackerTicket, comment, translateErr := s.getIssueTrackerTicketAndCommentForAppendingCrmTicket(ctx, req.GetCrmTool(), req.GetCrmTicket(), req.GetIssueTrackerTool())
	if translateErr != nil {
		logger.Error(ctx, "failed to translate CRM ticket to issue tracker ticket", zap.Error(translateErr), zap.Int64(logger.TICKET_ID, crmTicketId),
			zap.String("CrmTool", req.GetCrmTool().String()), zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		if errors.Is(translateErr, epifierrors.ErrAlreadyExists) {
			return &citPb.AppendCrmTicketToIssueTrackerTicketResponse{
				Status: rpc.StatusAlreadyExistsWithDebugMsg(translateErr.Error()),
			}, nil
		}
		return &citPb.AppendCrmTicketToIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(translateErr.Error()),
		}, nil
	}
	issueTrackerTicketId := helperImpl.GetIssueTrackerTicketId(issueTrackerTicket)
	apiErr := helperImpl.InvokeIssueTrackerUpdateApis(ctx, issueTrackerTicket, comment)
	if apiErr != nil {
		logger.Error(ctx, "update issue tracker API failed", zap.Error(apiErr), zap.String("IssueTrackerTool", req.GetIssueTrackerTool().String()))
		return &citPb.AppendCrmTicketToIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(apiErr.Error()),
		}, nil
	}
	_, mapCreateErr := s.mappingDao.CreateBatch(ctx, []*citPb.CrmToIssueTrackerMapping{{
		CrmTool:              req.GetCrmTool(),
		CrmTicketId:          crmTicketId,
		IssueTrackerTool:     req.GetIssueTrackerTool(),
		IssueTrackerTicketId: issueTrackerTicketId,
	}})
	if mapCreateErr != nil {
		logger.Error(ctx, "failed to create CRM to issue tracker ticket mapping for append", zap.Error(mapCreateErr))
		return &citPb.AppendCrmTicketToIssueTrackerTicketResponse{
			Status: rpc.StatusInternalWithDebugMsg(mapCreateErr.Error()),
		}, nil
	}
	return &citPb.AppendCrmTicketToIssueTrackerTicketResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func validateAppendCrmTicketToIssueTrackerTicketRequest(req *citPb.AppendCrmTicketToIssueTrackerTicketRequest) error {
	if req.GetIssueTrackerTool() == citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_UNSPECIFIED {
		return errors.New("issue tracker tool is unspecified")
	}
	if req.GetCrmTicket().GetFreshdeskTicket().GetCustomFields().GetMonorailTicketId() == 0 {
		return errors.New("monorail id is not populated")
	}
	return validateCrmTicketRequest(req.GetCrmTool(), req.GetCrmTicket())
}

func (s *Service) getIssueTrackerTicketAndCommentForAppendingCrmTicket(ctx context.Context, crmTool citPb.CrmTool, crmTicket *citPb.CrmTicket,
	issueTrackerTool citPb.IssueTrackerTool) (*citPb.IssueTrackerTicket, string, error) {
	translator, factoryErr := s.ticketTranslatorFactory.GetTicketTranslatorImplementation(crmTool, issueTrackerTool)
	if factoryErr != nil {
		return nil, "", errors.Wrap(factoryErr, "failed to get ticket translator implementation")
	}
	issueTrackerTicket, comment, translateErr := translator.TranslateCrmToIssueTrackerTicketForAppend(ctx, crmTicket)
	if translateErr != nil {
		return nil, "", errors.Wrap(translateErr, "failed to translate crm to issue tracker ticket for append")
	}
	return issueTrackerTicket, comment, nil
}

func validateCrmTicketRequest(crmTool citPb.CrmTool, crmTicket *citPb.CrmTicket) error {
	switch crmTool {
	case citPb.CrmTool_CRM_TOOL_UNSPECIFIED:
		return errors.New("CRM tool is unspecified")
	case citPb.CrmTool_CRM_TOOL_FRESHDESK:
		if crmTicket.GetFreshdeskTicket().GetId() == 0 {
			return errors.New("freshdesk ticket Id is empty for type CRM_TOOL_FRESHDESK")
		}
	default:
		return errors.New("the given CRM tool is not supported yet")
	}
	return nil
}

func validateIssueTrackerTicketRequest(issueTrackerTool citPb.IssueTrackerTool, issueTrackerTicket *citPb.IssueTrackerTicket) error {
	switch issueTrackerTool {
	case citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_UNSPECIFIED:
		return errors.New("issue tracker tool is unspecified")
	case citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL:
		if issueTrackerTicket.GetMonorailIssue().GetId() == 0 {
			return errors.New("monorail ticket Id is empty for type CRM_TOOL_FRESHDESK")
		}
	default:
		return errors.New("the given issue tracker tool is not supported yet")
	}
	return nil
}
