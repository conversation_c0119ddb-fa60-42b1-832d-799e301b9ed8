package ticket_translator

import (
	"errors"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
)

type ITicketTranslatorFactory interface {
	GetTicketTranslatorImplementation(crmTool citPb.CrmTool, issueTrackerTool citPb.IssueTrackerTool) (TicketTranslator, error)
}

type TicketTranslatorFactory struct {
	freshdeskMonorailTranslator *FreshdeskMonorailTranslator
}

func NewTicketTranslatorFactory(freshdeskMonorailTranslator *FreshdeskMonorailTranslator) *TicketTranslatorFactory {
	return &TicketTranslatorFactory{
		freshdeskMonorailTranslator: freshdeskMonorailTranslator,
	}
}

var _ ITicketTranslatorFactory = &TicketTranslatorFactory{}

func (i *TicketTranslatorFactory) GetTicketTranslatorImplementation(crmTool citPb.CrmTool, issueTrackerTool citPb.IssueTrackerTool) (TicketTranslator, error) {
	switch {
	case crmTool == citPb.CrmTool_CRM_TOOL_FRESHDESK && issueTrackerTool == citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL:
		return i.freshdeskMonorailTranslator, nil
	default:
		return nil, errors.New("no translator implementation found for the given tools")
	}
}
