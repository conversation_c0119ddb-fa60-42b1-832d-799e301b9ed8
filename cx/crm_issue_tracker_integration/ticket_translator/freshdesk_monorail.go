package ticket_translator

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"golang.org/x/oauth2/google"
	"golang.org/x/oauth2/jwt"
	"google.golang.org/api/drive/v3"
	"google.golang.org/api/option"
	"google.golang.org/protobuf/encoding/protojson"

	monorailPb "github.com/epifi/be-common/api/pkg/monorail"
	monorailPayload "github.com/epifi/be-common/api/pkg/monorail/payload"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	monorailApi "github.com/epifi/be-common/pkg/monorail/api_wrapper"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	cxFdTicketPb "github.com/epifi/gamma/api/cx/ticket"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/cx/config"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/dao"
	monorailDescription "github.com/epifi/gamma/cx/crm_issue_tracker_integration/ticket_translator/monorail_description"
	cxHelper "github.com/epifi/gamma/cx/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"
	ticketDao "github.com/epifi/gamma/cx/ticket/dao"
	mapUtils "github.com/epifi/gamma/pkg/map"
)

type FreshdeskMonorailTranslator struct {
	conf                           *cxGenConf.Config
	ticketDetailsTransformationDao ticketDao.ITicketDetailsTransformationDao
	crmIssueTrackerMappingDao      dao.ICrmToIssueTrackerMappingDao
	fdVgClient                     freshdeskPb.FreshdeskClient
	monorailApiWrapper             monorailApi.IMonorailApiWrapper
	monorailDescriptionFactory     monorailDescription.IMonorailDescriptionFactory
}

const (
	needMoreInfoFromCxLabel     = "NeedMoreInfoFromCX"
	freshdeskIdsCustomFieldName = "FreshdeskIDs"
	// Ref: https://developers.freshdesk.com/api/#list_all_ticket_notes
	freshdeskMaxConversationsPerPage = 30

	// Regex to match a <img > block in html, with a capturing group(https://www.regular-expressions.info/brackets.html) defined for src url
	regexToParseImageInHtml = `<img[^>]+\bsrc=["']([^"']+)["'][^>]*>`

	// Ref: https://developers.google.com/drive/api/guides/mime-types
	mimeTypeGoogleDriveFolder = "application/vnd.google-apps.folder"
	mineTypePng               = "image/png"

	// file name format for storing images in Google Drive: <ticket-id>_<conversation-id>_image_<number>.png
	googleDriveFileNameFormatForImages = "%d_%d_image_%d.png"
	// file name format for storing attachments in Google Drive: <ticket-id>_<conversation-id>_<attachment-name>
	googleDriveFileNameFormatForAttachments = "%d_%d_%s"
)

var (
	// These mappings are per the sheet shared by Product & PE : https://docs.google.com/spreadsheets/d/1R4KpUp1-Pse1N2LLz4fShAXDlQhi2dWR8m0m31bYnq8/edit#gid=1609351389
	freshdeskToMonorailStatusMap = map[cxFdTicketPb.Status]monorailPb.MonorailIssueStatus{
		cxFdTicketPb.Status_STATUS_SEND_TO_PRODUCT: monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_NEW,
		cxFdTicketPb.Status_STATUS_REOPEN:          monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_REOPENED,
	}
	monorailToFreshdeskStatusMap = map[monorailPb.MonorailIssueStatus]cxFdTicketPb.Status{
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_NEW:                cxFdTicketPb.Status_STATUS_SEND_TO_PRODUCT,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_ACCEPTED:           cxFdTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_STARTED:            cxFdTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_IN_REVIEW:          cxFdTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_RESOLVING_COMMENTS: cxFdTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_BLOCKED:            cxFdTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_FIXED:              cxFdTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_VERIFIED:           cxFdTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_INVALID:            cxFdTicketPb.Status_STATUS_OPEN,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_WONT_FIX:           cxFdTicketPb.Status_STATUS_OPEN,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_DEPLOYED:           cxFdTicketPb.Status_STATUS_RESOLVED,
		monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_DONE:               cxFdTicketPb.Status_STATUS_OPEN,
	}
	// The label to be used in monorail for the given freshdesk priority level
	freshdeskToMonorailPriorityMap = map[cxFdTicketPb.Priority]string{
		cxFdTicketPb.Priority_PRIORITY_URGENT: "Priority-Critical",
		cxFdTicketPb.Priority_PRIORITY_HIGH:   "Priority-High",
		cxFdTicketPb.Priority_PRIORITY_MEDIUM: "Priority-Medium",
		cxFdTicketPb.Priority_PRIORITY_LOW:    "Priority-Low",
	}
	// labels as mentioned in https://docs.google.com/document/d/1ykqPcXa4AiGubvEGqPJAUF6IyXZ_QBVudG5MB3WvmwE/edit#
	freshdeskToMonorailAppPlatformMap = map[commontypes.Platform]string{
		commontypes.Platform_ANDROID: "Area-Android",
		commontypes.Platform_IOS:     "Area-iOS",
	}
)

func NewFreshdeskMonorailTranslator(conf *cxGenConf.Config, ticketDetailsTransformationDao ticketDao.ITicketDetailsTransformationDao,
	crmIssueTrackerMappingDao dao.ICrmToIssueTrackerMappingDao, fdVgClient freshdeskPb.FreshdeskClient, monorailApiWrapper monorailApi.IMonorailApiWrapper,
	monorailDescriptionFactory monorailDescription.IMonorailDescriptionFactory) *FreshdeskMonorailTranslator {
	return &FreshdeskMonorailTranslator{
		conf:                           conf,
		ticketDetailsTransformationDao: ticketDetailsTransformationDao,
		crmIssueTrackerMappingDao:      crmIssueTrackerMappingDao,
		fdVgClient:                     fdVgClient,
		monorailApiWrapper:             monorailApiWrapper,
		monorailDescriptionFactory:     monorailDescriptionFactory,
	}
}

var _ TicketTranslator = &FreshdeskMonorailTranslator{}

func (f *FreshdeskMonorailTranslator) TranslateCrmToIssueTrackerTicketForCreate(ctx context.Context, crmTicket *citPb.CrmTicket) (*citPb.IssueTrackerTicket, error) {
	fdTicket := crmTicket.GetFreshdeskTicket()
	title := fmt.Sprintf(f.conf.FreshdeskMonorailIntegrationConfig().MonorailIssueSubjectFormat, fdTicket.GetId(), fdTicket.GetSubject())
	components, err := f.getMonorailComponents(ctx, fdTicket.GetCustomFieldWithValue())
	if err != nil {
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			return nil, errors.Wrap(err, "invalid argument to fetch Monorail components for given L1, L2, L3")
		}
		return nil, errors.Wrap(err, "failed to fetch Monorail components for the given L1, L2, L3")
	}
	owners, err := f.getMonorailIssueOwnersFromComponents(components)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch Monorail issue owners for the the components: "+strings.Join(components, ","))
	}
	if len(owners) == 0 {
		return nil, errors.New("monorail issue owners list is empty")
	}
	// 1. Fetch private notes existing on the Freshdesk ticket,
	// 2. Get component specific additional info to be populated on monorail ticket
	// and build the description for Monorail issue
	description := f.buildMonorailIssueDescription(fdTicket, f.getPrivateNotes(ctx, fdTicket.GetId(), owners),
		f.getComponentSpecificAdditionalInfoForDescription(ctx, components, fdTicket))
	return &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Title:       title,
				Summary:     title,
				Description: description,
				Status:      freshdeskToMonorailStatusMap[fdTicket.GetStatus()],
				Components:  components,
				Owner:       owners[0],
				Cc:          owners,
				Labels:      f.getMonorailIssueLabels(fdTicket),
				CustomFieldValues: []*monorailPb.CustomFieldValue{{
					FieldName:  freshdeskIdsCustomFieldName,
					FieldValue: strconv.FormatInt(fdTicket.GetId(), 10),
				}},
			},
		},
	}, nil
}

func (f *FreshdeskMonorailTranslator) TranslateCrmToIssueTrackerTicketForUpdate(ctx context.Context, crmTicket *citPb.CrmTicket) (*citPb.IssueTrackerTicket, string, error) {
	fdTicket := crmTicket.GetFreshdeskTicket()
	mapping, dbErr := f.crmIssueTrackerMappingDao.GetByCrmTicketId(ctx, citPb.CrmTool_CRM_TOOL_FRESHDESK, fdTicket.GetId())
	switch {
	case errors.Is(dbErr, epifierrors.ErrRecordNotFound): // this includes len(mapping) == 0
		return nil, "", errors.Wrap(epifierrors.ErrRecordNotFound, "no monorail issue mapping exists for this FD ticket")
	case dbErr != nil:
		return nil, "", errors.Wrap(dbErr, "failed to fetch monorail issue mapping for this FD ticket")
	default:
		// continue with translating other fields
	}
	monorailIssueId := mapping[0].GetIssueTrackerTicketId()
	monorailIssue := &monorailPb.Issue{
		// There will be only one ticket for a given Freshdesk Ticket, hence using 0. NOTE: This is not enforced on DB constraints to capture any duplicate monorail tickets due to race conditions
		Id:     monorailIssueId,
		Status: freshdeskToMonorailStatusMap[fdTicket.GetStatus()],
	}
	if fdTicket.GetSubject() != "" {
		monorailIssue.Summary = fmt.Sprintf(f.conf.FreshdeskMonorailIntegrationConfig().MonorailIssueSubjectFormat, fdTicket.GetId(), fdTicket.GetSubject())
	}
	comment, err := f.getCommentFromFreshdeskConversationForUpdate(ctx, crmTicket.GetFreshdeskTicketConversations(), monorailIssueId)
	if err != nil {
		return nil, "", errors.Wrap(err, "failed to get monorail comment from freshdesk private note")
	}
	return &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: monorailIssue,
		},
	}, comment, nil
}

func (f *FreshdeskMonorailTranslator) getCommentFromFreshdeskConversationForUpdate(ctx context.Context, conversations *citPb.FreshdeskTicketConversations, monorailIssueId int64) (string, error) {
	if len(conversations.GetConversations()) == 0 {
		return "", nil
	}
	conversation := conversations.GetConversations()[0]
	commentPrefix := fmt.Sprintf(f.conf.FreshdeskMonorailIntegrationConfig().FreshdeskToMonorailCommentPrefix, conversation.GetTicketId())
	monorailIssue, err := f.monorailApiWrapper.GetIssue(ctx, &monorailPb.GetIssueRequest{IssueId: monorailIssueId})
	if err != nil {
		return "", errors.Wrap(err, "failed to get monorail issue by Id")
	}
	monorailParticipants := []*monorailPb.Person{monorailIssue.GetIssue().GetOwner()}
	// We need to give file permissions to all the persons who are part of the monorail.
	for _, ccOwner := range monorailIssue.GetIssue().GetCc() {
		// Owner can be part of cc. Avoid adding duplicate to avoid an extra API call for adding Google Drive file permissions
		if ccOwner.GetEmail() != monorailIssue.GetIssue().GetOwner().GetEmail() {
			monorailParticipants = append(monorailParticipants, ccOwner)
		}
	}
	return commentPrefix + f.ConstructMonorailCommentForFreshdeskConversation(ctx, conversation, monorailParticipants), nil
}

func (f *FreshdeskMonorailTranslator) TranslateCrmToIssueTrackerTicketForAppend(ctx context.Context, crmTicket *citPb.CrmTicket) (*citPb.IssueTrackerTicket, string, error) {
	fdTicket := crmTicket.GetFreshdeskTicket()
	monorailIssueId := fdTicket.GetCustomFields().GetMonorailTicketId()
	if fdTicket.GetCustomFields().GetMonorailTicketId() == 0 {
		return nil, "", errors.New("Monorail Ticket Id has not been populated in freshdesk ticket")
	}
	// First we should check if this freshdesk ticket is already mapped to some monorail ticket
	_, getByFdErr := f.crmIssueTrackerMappingDao.GetByCrmTicketId(ctx, citPb.CrmTool_CRM_TOOL_FRESHDESK, fdTicket.GetId())
	switch {
	case errors.Is(getByFdErr, epifierrors.ErrRecordNotFound): // this includes len(mapping) == 0
		// continue since there is no entry for this FD ticket
	case getByFdErr != nil:
		return nil, "", errors.Wrap(getByFdErr, fmt.Sprintf("failed to get mapping for FD ticket- %d and Monorail Issue- %d", fdTicket.GetId(), monorailIssueId))
	case getByFdErr == nil:
		return nil, "", errors.Wrap(epifierrors.ErrAlreadyExists, fmt.Sprintf("mapping already exists for FD ticket- %d and Monorail Issue- %d", fdTicket.GetId(), monorailIssueId))
	}
	// check if the given monorail Id is already created by this FD<>Monorail service.
	// This check is to disallow appending a FD ticket to a Monorail issue which is not created by this service.
	_, getByMonorailErr := f.crmIssueTrackerMappingDao.GetByIssueTrackerTicketId(ctx, citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL, monorailIssueId)
	switch {
	case errors.Is(getByMonorailErr, epifierrors.ErrRecordNotFound): // this check includes len(mapping) == 0
		return nil, "", errors.Wrap(epifierrors.ErrRecordNotFound, fmt.Sprintf("given Monorail issue Id - %d has not been created by this service", monorailIssueId))
	case getByMonorailErr != nil:
		// if it is a db error other than record not found
		return nil, "", errors.Wrap(getByMonorailErr, fmt.Sprintf("failed to fetch records for the Monorail issue Id - %d", monorailIssueId))
	default:
		// continue with other translations
	}
	monorailIssue := &monorailPb.Issue{
		Id: monorailIssueId,
		CustomFieldValues: []*monorailPb.CustomFieldValue{{
			FieldName:  freshdeskIdsCustomFieldName,
			FieldValue: strconv.FormatInt(fdTicket.GetId(), 10),
		}},
	}
	comment, err := f.getMonorailCommentForAppendingFreshdeskTicket(ctx, fdTicket)
	if err != nil {
		logger.Error(ctx, "failed to build monorail comment for appending freshdesk ticket", zap.Int64(logger.TICKET_ID, fdTicket.GetId()))
		comment += "\n\n<" + err.Error() + ">\n"
	}
	return &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: monorailIssue,
		},
	}, comment, nil
}

func (f *FreshdeskMonorailTranslator) getMonorailCommentForAppendingFreshdeskTicket(ctx context.Context, fdTicket *cxFdTicketPb.Ticket) (string, error) {
	comment := "<b>Appending a related FD ticket</b>\n"
	comment += getCommonAdditionalInfo(fdTicket)
	components, err := f.getMonorailComponents(ctx, fdTicket.GetCustomFieldWithValue())
	if err != nil {
		// return the comment build so far
		return comment, errors.Wrap(err, "failed to fetch Monorail components for the given L1, L2, L3")
	}
	comment += f.getComponentSpecificAdditionalInfoForDescription(ctx, components, fdTicket)
	return comment, nil
}

func (f *FreshdeskMonorailTranslator) TranslateIssueTrackerToCrmTicketsForUpdate(ctx context.Context, issueTrackerTicket *citPb.IssueTrackerTicket) ([]*citPb.CrmTicket, bool, error) {
	monorailIssue := issueTrackerTicket.GetMonorailIssue()
	mappings, dbErr := f.crmIssueTrackerMappingDao.GetByIssueTrackerTicketId(ctx, citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL, monorailIssue.GetId())
	switch {
	case errors.Is(dbErr, epifierrors.ErrRecordNotFound): // this check includes len(mapping) == 0
		return nil, false, errors.Wrap(epifierrors.ErrRecordNotFound, "no Freshdesk ticket exists for this Monorail issue")
	case dbErr != nil:
		// if it is a db error other than record not found
		return nil, false, errors.Wrap(dbErr, "failed to fetch Freshdesk ticket for this Monorail issue")
	default:
		// continue with other translations
	}
	var crmTicketList []*citPb.CrmTicket
	// we should call freshdesk update API only if there is an update involved here
	isUpdateTicketRequired := false
	status := getFreshdeskStatus(issueTrackerTicket.GetMonorailIssue())
	if status != cxFdTicketPb.Status_STATUS_UNSPECIFIED {
		isUpdateTicketRequired = true
	}
	for _, mapping := range mappings {
		crmTicketList = append(crmTicketList, &citPb.CrmTicket{
			Ticket: &citPb.CrmTicket_FreshdeskTicket{
				FreshdeskTicket: &cxFdTicketPb.Ticket{
					Id:     mapping.GetCrmTicketId(),
					Status: status,
				},
			},
		})
	}
	return crmTicketList, isUpdateTicketRequired, nil
}

func getFreshdeskStatus(monorailIssue *monorailPb.Issue) cxFdTicketPb.Status {
	// Only if the label for needMoreInfoFromCxLabel, we map to the Freshdesk status STATUS_NEEDS_CLARIFICATION_FROM_CX
	// If ticket on monorail is set to BLOCKED status for any other reason, the state would still be STATUS_WAITING_ON_PRODUCT
	if monorailIssue.GetStatus() == monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_BLOCKED &&
		lo.Contains(monorailIssue.GetLabels(), needMoreInfoFromCxLabel) {
		return cxFdTicketPb.Status_STATUS_NEEDS_CLARIFICATION_FROM_CX
	}
	return monorailToFreshdeskStatusMap[monorailIssue.GetStatus()]
}

// getMonorailComponents - utility method to get the Monorail components mapped to the L1, L2, L3 of the ticket
func (f *FreshdeskMonorailTranslator) getMonorailComponents(ctx context.Context, customFieldsWithValue *cxFdTicketPb.CustomFieldsWithValue) ([]string, error) {
	l1 := f.getProductCategoryEnum(customFieldsWithValue.GetProductCategory())
	if l1 == cxFdTicketPb.ProductCategory_PRODUCT_CATEGORY_UNSPECIFIED {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "product category cannot be unspecified")
	}
	l2 := customFieldsWithValue.GetProductCategoryDetails()
	l3 := customFieldsWithValue.GetSubCategory()
	transformations, dbErr := f.ticketDetailsTransformationDao.GetAllByFilters(ctx,
		ticketDao.WithProdCatProdCatDetailsSubcatList([]*cxFdTicketPb.TicketDetailsTransformation{{ProductCategory: l1, ProductCategoryDetails: l2, Subcategory: l3}}),
		ticketDao.WithTransformationTypeList([]cxFdTicketPb.TicketTransformationType{cxFdTicketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_MONORAIL_COMPONENTS}))
	if dbErr != nil {
		logger.Error(ctx, "failed to fetch monorail components mapping from db", zap.String(logger.PRODUCT_CATEGORY, l1.String()),
			zap.String(logger.PRODUCT_CATEGORY_DETAILS, l2), zap.String(logger.PRODUCT_SUB_CATEGORY, l3))
		return nil, errors.Wrap(dbErr, "failed to fetch monorail components mapping from db")
	}
	var components []string
	for _, transformation := range transformations {
		components = append(components, transformation.GetTransformationValue().GetMonorailComponents()...)
	}
	return components, nil
}

func (f *FreshdeskMonorailTranslator) getProductCategoryEnum(productCategoryStr string) cxFdTicketPb.ProductCategory {
	productCatValToEnumMap := mapUtils.ReverseMap(f.conf.SupportTicketFreshdeskConfig().ProductCategoryEnumToValueMapping)
	productCategoryEnumStr := strings.ToUpper(productCatValToEnumMap[productCategoryStr])
	productCategoryEnum := cxFdTicketPb.ProductCategory(cxFdTicketPb.ProductCategory_value[productCategoryEnumStr])
	return productCategoryEnum
}

// getMonorailIssueOwnersFromComponents - utility method to get list of owners from a list of monorail components
func (f *FreshdeskMonorailTranslator) getMonorailIssueOwnersFromComponents(components []string) ([]*monorailPb.Person, error) {
	var owners []*monorailPb.Person
	for _, component := range components {
		ownerEmail, ok := f.conf.FreshdeskMonorailIntegrationConfig().MonorailComponentOwnerMap[strings.ToLower(component)]
		if !ok {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "failed to get owner for the monorail component: "+component)
		}
		for _, email := range ownerEmail {
			owners = append(owners, &monorailPb.Person{Email: email})
		}
	}
	return owners, nil
}

func (f *FreshdeskMonorailTranslator) buildMonorailIssueDescription(fdTicket *cxFdTicketPb.Ticket, privateNotes []string, componentSpecificAdditionalInfo string) string {
	description := ""
	// For chat-sourced tickets the description contains the chat messages.
	// This has to be skipped as confirmed by CX-PE team
	if fdTicket.GetSource() != cxFdTicketPb.Source_SOURCE_CHAT {
		description += cxHelper.HtmlToTextForFreshdeskTicket(fdTicket.GetDescription())
	}
	// add all the private notes to description
	// NOTE: Only <b> (bold) formatting is working on Monorail UI i.e. other html formatting are not working as expected
	for idx, privateNote := range privateNotes {
		description += "\r\n\r\n<b>Private Note " + strconv.FormatInt(int64(idx+1), 10) + ":</b>\r\n"
		description += privateNote
	}
	// add additional info to the description
	// TODO: move this to config with dynamic fields based on the component/ L1,L2,L3
	description += "\r\n\r\n<b>**Additional Info**</b>"
	description += getCommonAdditionalInfo(fdTicket)
	description += componentSpecificAdditionalInfo
	return description
}

func getCommonAdditionalInfo(fdTicket *cxFdTicketPb.Ticket) string {
	commonInfo := ""
	commonInfo += "\n<b>Actor Id:</b> " + fdTicket.GetActorId()
	commonInfo += "\n<b>FD Ticket Id:</b> " + strconv.FormatInt(fdTicket.GetId(), 10)
	commonInfo += "\n<b>FD Ticket Status:</b> " + fdTicket.GetStatus().String()
	commonInfo += "\n<b>Product Category (L1):</b> " + fdTicket.GetCustomFieldWithValue().GetProductCategory()
	commonInfo += "\n<b>Product Category Details (L2):</b> " + fdTicket.GetCustomFieldWithValue().GetProductCategoryDetails()
	commonInfo += "\n<b>Subcategory (L3):</b> " + fdTicket.GetCustomFieldWithValue().GetSubCategory()
	return commonInfo
}

func (f *FreshdeskMonorailTranslator) getComponentSpecificAdditionalInfoForDescription(ctx context.Context, components []string, fdTicket *cxFdTicketPb.Ticket) string {
	additionalInfo := ""
	for _, component := range components {
		info, err := f.getAdditionalInfoForComponent(ctx, component, fdTicket)
		if err != nil {
			cxLogger.Error(ctx, "Failed to fetch additional info for the component for monorail description", zap.Error(err),
				zap.String(logger.MONORAIL_COMPONENT, component), zap.Int64(logger.TICKET_ID, fdTicket.GetId()))
			// We are adding info on these failed-to-fetch fields for the component to the Monorail description for clarity
			additionalInfo += "\n\n<" + err.Error() + ">\n"
		} else {
			additionalInfo += info
		}
	}
	return additionalInfo
}

func (f *FreshdeskMonorailTranslator) getAdditionalInfoForComponent(ctx context.Context, monorailComponent string, fdTicket *cxFdTicketPb.Ticket) (string, error) {
	descriptionProcessor, processorErr := f.monorailDescriptionFactory.GetMonorailDescriptionProcessor(monorailComponent)
	if processorErr != nil {
		return "", errors.Wrap(processorErr, fmt.Sprintf("failed to get additional info for the monorail component: %s", monorailComponent))
	}
	info, infoErr := descriptionProcessor.GetAdditionalInfoForMonorailDescription(ctx, fdTicket)
	if infoErr != nil {
		return "", errors.Wrap(infoErr, fmt.Sprintf("failed to get additional info for the monorail component: %s", monorailComponent))
	}
	return info, nil
}

func (f *FreshdeskMonorailTranslator) getMonorailIssueLabels(fdTicket *cxFdTicketPb.Ticket) []string {
	labels := f.conf.FreshdeskMonorailIntegrationConfig().MonorailIssueDefaultLabels
	labels = append(labels, freshdeskToMonorailPriorityMap[fdTicket.GetPriority()])
	osType := fdTicket.GetCustomFields().GetOsType()
	if osType != commontypes.Platform_PLATFORM_UNSPECIFIED {
		labels = append(labels, freshdeskToMonorailAppPlatformMap[osType])
	}
	return labels
}

func (f *FreshdeskMonorailTranslator) getPrivateNotes(ctx context.Context, ticketId int64, monorailParticipants []*monorailPb.Person) []string {
	var privateNotes []string
	conversationsExist := true
	pageNum := int64(1)
	for conversationsExist {
		fdConversationsResp, fdConversationsErr := f.fdVgClient.FetchTicketConversations(ctx,
			&freshdeskPb.FetchTicketConversationsRequest{
				Header: &commonvgpb.RequestHeader{
					Vendor: commonvgpb.Vendor_FRESHDESK,
				},
				TicketId:   ticketId,
				PageNumber: pageNum,
			})
		if te := epifigrpc.RPCError(fdConversationsResp, fdConversationsErr); te != nil {
			cxLogger.Error(ctx, "Failed to fetch conversations for the ticket", zap.Int64(logger.TICKET_ID, ticketId), zap.Error(te))
		}
		for _, conversation := range fdConversationsResp.GetTicketConversationList() {
			if conversation.GetPrivate() {
				privateNotes = append(privateNotes, f.ConstructMonorailCommentForFreshdeskConversation(ctx, conversation, monorailParticipants))
			}
		}
		if len(fdConversationsResp.GetTicketConversationList()) < freshdeskMaxConversationsPerPage {
			conversationsExist = false
		}
		pageNum++
	}
	return privateNotes
}

// ConstructMonorailCommentForFreshdeskConversation will construct a Comment to be posted on Monorail issue for a given Freshdesk Conversation / Note.
// If there are any embedded images or attachments in the Freshdesk conversation, those files will be uploaded to a Google Drive folder,
// and the corresponding file links will be added to the Monorail Comment.
// monorailOwners will be given the permissions for the uploaded files
// Returns the final Monorail Comment. Errors are just logged because images/attachments are provided on best effort basis
func (f *FreshdeskMonorailTranslator) ConstructMonorailCommentForFreshdeskConversation(ctx context.Context, conversation *freshdeskPb.TicketConversation, monorailParticipants []*monorailPb.Person) string {
	body := conversation.GetBody()
	// Parse the embedded images in html, Upload them to google drive and append the file url to the comment
	imageRegExp := regexp.MustCompile(regexToParseImageInHtml)
	// Find all the images in the Body. -1 means no limit on number of matches
	imageMatchSlice := imageRegExp.FindAllStringSubmatch(body, -1)
	for imageIdx, imageMatch := range imageMatchSlice {
		// imageMatch[0] contains the exact match i.e. the entire <img > block and
		// imageMatch[1] contains the URL [based on the capturing group defined for the URL in the regexToParseImageInHtml].
		if len(imageMatch) != 2 {
			logger.Error(ctx, "improper image match in html", zap.Any("imageMatch", imageMatch),
				zap.Int64(logger.TICKET_ID, conversation.GetTicketId()), zap.Int64("conversationId", conversation.GetId()))
		}
		imageUrl := imageMatch[1]
		// file name format for storing file in Google Drive
		fileName := fmt.Sprintf(googleDriveFileNameFormatForImages, conversation.GetTicketId(), conversation.GetId(), imageIdx)
		driveUrl, err := f.UploadFileToGoogleDriveAndGetUrl(ctx, imageUrl, &drive.File{
			Name:     fileName,
			MimeType: mineTypePng,
		}, monorailParticipants, conversation.GetTicketId())
		if err != nil {
			logger.Error(ctx, "failed to upload attachment to google drive", zap.Error(err),
				zap.Int64(logger.TICKET_ID, conversation.GetTicketId()), zap.String("fileName", fileName))
		}
		imageStr := fmt.Sprintf("{Image %d: %s} ", imageIdx+1, driveUrl)
		// Replace the <img > block with {Image %d: %s} format. Using curl brackets so that it won't be replaced when
		// we remove all the html tags from this body string
		body = strings.Replace(body, imageMatch[0], imageStr, 1)
	}

	// Upload the attachments to google drive and append the file urls to the comment
	attachmentString := ""
	if len(conversation.GetAttachments()) > 0 {
		attachmentString += "\n\nAttachments:\n"
	}
	for attachmentIdx, attachment := range conversation.GetAttachments() {
		attachmentString += fmt.Sprintf("Attachment %d - %s: ", attachmentIdx+1, attachment.GetName())
		fileName := fmt.Sprintf(googleDriveFileNameFormatForAttachments, conversation.GetTicketId(), conversation.GetId(), attachment.GetName())
		driveUrl, err := f.UploadFileToGoogleDriveAndGetUrl(ctx, attachment.GetAttachmentUrl(), &drive.File{
			Name:     fileName,
			MimeType: attachment.GetContentType(),
		}, monorailParticipants, conversation.GetTicketId())
		if err != nil {
			logger.Error(ctx, "failed to upload attachment to google drive", zap.Error(err),
				zap.Int64(logger.TICKET_ID, conversation.GetTicketId()), zap.Int64("AttachmentId", attachment.GetId()))
		}
		attachmentString += driveUrl + "\n"
	}
	bodyWithoutHtmlTags := cxHelper.HtmlToTextForFreshdeskTicket(body)
	return bodyWithoutHtmlTags + attachmentString
}

// UploadFileToGoogleDriveAndGetUrl will download the file with the given fileUrl and upload it to Google Drive and give permissions to monorailParticipants
// Returns the URL to the uploaded file and any errors
// TODO: check if context deadline exceeds for larger files
func (f *FreshdeskMonorailTranslator) UploadFileToGoogleDriveAndGetUrl(ctx context.Context, fileUrl string, fileMeta *drive.File,
	monorailParticipants []*monorailPb.Person, freshdeskTicketId int64) (string, error) {
	getFileResp, getFileErr := http.Get(fileUrl) //nolint
	if getFileErr != nil {
		return "", errors.Wrap(getFileErr, "failed to get file: "+fileUrl)
	}

	// Not having it in server.go because it can be initialized with different credentials based on use case.
	driveSrv, err := f.initializeDriveService(ctx)
	if err != nil {
		return "", errors.Wrap(err, "failed to initialize drive service")
	}

	rootFolderId := f.conf.FreshdeskMonorailIntegrationConfig().GoogleDriveFolderIdForAttachments
	folderId, getOrCreateErr := f.getOrCreateFolder(ctx, driveSrv, rootFolderId, freshdeskTicketId)
	if getOrCreateErr != nil || folderId == "" {
		logger.Error(ctx, fmt.Sprintf("Failed to create or get folder under %s for freshdesk ticket %d", rootFolderId, freshdeskTicketId), zap.Error(getOrCreateErr))
		// If we couldn't create or get folder, then create the file in root folder itself since we don't want to block on this failure
		folderId = rootFolderId
	}
	fileMeta.Parents = []string{folderId}
	// Upload the file to google drive
	fileCreateResp, fileCreateErr := driveSrv.Files.Create(fileMeta).Media(getFileResp.Body).Do()
	if fileCreateErr != nil {
		return "", errors.Wrap(err, "failed to update freshdesk ticket with monorail Id")
	}
	// Give permissions to the owners (Use goroutine to avoid blocking on this call)
	goroutine.Run(ctx, 2*time.Minute, func(ctx context.Context) {
		for _, owner := range monorailParticipants {
			_, permissionsErr := driveSrv.Permissions.Create(fileCreateResp.Id, &drive.Permission{
				EmailAddress: owner.GetEmail(),
				Type:         "user",
				Role:         "writer",
			}).Do()
			if permissionsErr != nil {
				logger.Error(ctx, fmt.Sprintf("Failed to give permissions to %s for %s", owner.GetEmail(), fileMeta.Name))
			}
		}
	})
	return fmt.Sprintf("https://drive.google.com/open?id=%s", fileCreateResp.Id), nil
}

func (f *FreshdeskMonorailTranslator) initializeDriveService(ctx context.Context) (*drive.Service, error) {
	keyJson := f.conf.Secrets().Ids[config.MonorailServiceAccountKey]
	key := &monorailPayload.ServiceAccountKey{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(keyJson), key)
	if err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal monorail service account key")
	}
	jwtConf := &jwt.Config{
		Email:        f.conf.MonorailConfig().ServiceAccountEmail,
		PrivateKey:   []byte(key.GetPrivateKey()),
		PrivateKeyID: key.GetPrivateKeyId(),
		Scopes:       []string{drive.DriveScope},
		TokenURL:     google.JWTTokenURL,
	}
	httpClient := jwtConf.Client(ctx)
	return drive.NewService(ctx, option.WithHTTPClient(httpClient))
}

func (f *FreshdeskMonorailTranslator) getOrCreateFolder(ctx context.Context, driveSrv *drive.Service, rootFolderId string, freshdeskTicketId int64) (string, error) {
	folderName := strconv.FormatInt(freshdeskTicketId, 10)
	queryString := fmt.Sprintf("mimeType='%s' and name='%s' and '%s' in parents", mimeTypeGoogleDriveFolder, folderName, rootFolderId)
	getFolderIdResp, getFolderErr := driveSrv.Files.List().Q(queryString).Do()
	if getFolderErr == nil && getFolderIdResp != nil && len(getFolderIdResp.Files) > 0 {
		// If the folder is found then return the Id
		return getFolderIdResp.Files[0].Id, nil
	}
	if getFolderErr != nil {
		// log the error and continue to try and create a folder
		logger.Error(ctx, fmt.Sprintf("failed to get folder using query %s", queryString), zap.Error(getFolderErr))
	}
	folderMeta := &drive.File{
		Name:     folderName,
		Parents:  []string{rootFolderId},
		MimeType: mimeTypeGoogleDriveFolder,
	}
	subFolderResp, createFolderErr := driveSrv.Files.Create(folderMeta).Do()
	if createFolderErr != nil || subFolderResp == nil {
		return "", errors.Wrap(createFolderErr, fmt.Sprintf("failed to create folder %s under %s", folderName, rootFolderId))
	}
	return subFolderResp.Id, nil
}
