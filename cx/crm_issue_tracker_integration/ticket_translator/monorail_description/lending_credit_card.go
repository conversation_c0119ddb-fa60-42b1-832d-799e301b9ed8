package monorail_description

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	cxFdTicketPb "github.com/epifi/gamma/api/cx/ticket"
	ffPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type LendingCreditCardProcessor struct {
	ffClient ffPb.FireflyClient
}

func NewLendingCreditCardProcessor(ffClient ffPb.FireflyClient) *LendingCreditCardProcessor {
	return &LendingCreditCardProcessor{
		ffClient: ffClient,
	}
}

func (l *LendingCreditCardProcessor) GetAdditionalInfoForMonorailDescription(ctx context.Context, freshdeskTicket *cxFdTicketPb.Ticket) (string, error) {
	additionalInfo := ""

	latestCard, err := l.ffClient.GetCreditCard(ctx, &ffPb.GetCreditCardRequest{
		GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: freshdeskTicket.GetActorId()},
	})
	if te := epifigrpc.RPCError(latestCard, err); te != nil {
		logger.Error(ctx, "Error in fetching credit card data details", zap.Error(te))
		return "", errors.Wrap(te, "failed to fetch credit card details")
	}
	additionalInfo += "\n<b>Masked Credit Card No:</b> " + latestCard.GetCreditCard().GetBasicInfo().GetMaskedCardNumber()
	additionalInfo += "\n<b>Kit Number:</b> " + latestCard.GetCreditCard().GetVendorIdentifier()
	return additionalInfo, nil
}
