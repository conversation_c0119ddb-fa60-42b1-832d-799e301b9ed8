package monorail_description

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	cxFdTicketPb "github.com/epifi/gamma/api/cx/ticket"
	ffPb "github.com/epifi/gamma/api/firefly"
	fireflyMocks "github.com/epifi/gamma/api/firefly/mocks"
)

const (
	actor1 = "actor-1"
)

var (
	creditCard1 = &ffPb.CreditCard{
		VendorIdentifier:	"kit-number-1",
		BasicInfo: &ffPb.BasicInfo{
			MaskedCardNumber: "masked-card-number-1",
		},
	}
)

func TestLendingCreditCard_GetAdditionalInfoForMonorailDescription(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockFireflyClient := fireflyMocks.NewMockFireflyClient(ctr)
	type args struct {
		mocks		[]interface{}
		ctx		context.Context
		fdTicket	*cxFdTicketPb.Ticket
	}
	tests := []struct {
		name	string
		args	args
		want	string
		wantErr	bool
	}{
		{
			name:	"error fetching credit card details",
			args: args{
				mocks: []interface{}{
					mockFireflyClient.EXPECT().GetCreditCard(gomock.Any(), &ffPb.GetCreditCardRequest{
						GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: actor1},
					}).Return(nil, errors.New("mock err")),
				},
				ctx:	context.Background(),
				fdTicket: &cxFdTicketPb.Ticket{
					ActorId: actor1,
				},
			},
			want:		"",
			wantErr:	true,
		},
		{
			name:	"success",
			args: args{
				mocks: []interface{}{
					mockFireflyClient.EXPECT().GetCreditCard(gomock.Any(), &ffPb.GetCreditCardRequest{
						GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: actor1},
					}).Return(&ffPb.GetCreditCardResponse{
						Status:		rpc.StatusOk(),
						CreditCard:	creditCard1,
					}, nil),
				},
				ctx:	context.Background(),
				fdTicket: &cxFdTicketPb.Ticket{
					ActorId: actor1,
				},
			},
			want:		"\n<b>Masked Credit Card No:</b> masked-card-number-1\n<b>Kit Number:</b> kit-number-1",
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewLendingCreditCardProcessor(mockFireflyClient)
			got, err := s.GetAdditionalInfoForMonorailDescription(tt.args.ctx, tt.args.fdTicket)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAdditionalInfoForMonorailDescription() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetAdditionalInfoForMonorailDescription() got = %v, want %v", got, tt.want)
			}
		})
	}
}
