//go:generate mockgen -source=processor.go -destination=../../../test/mocks/crm_issue_tracker_integration/ticket_translator/monorail_description/mock_processor.go
package monorail_description

import (
	"context"

	cxFdTicketPb "github.com/epifi/gamma/api/cx/ticket"
)

type MonorailDescriptionProcessor interface {
	// Get the additional info specific to a monorail component requried to build the description for monorail issue
	GetAdditionalInfoForMonorailDescription(ctx context.Context, freshdeskTicket *cxFdTicketPb.Ticket) (string, error)
}
