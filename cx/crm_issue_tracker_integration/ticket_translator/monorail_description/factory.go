//go:generate mockgen -source=factory.go -destination=../../../test/mocks/crm_issue_tracker_integration/ticket_translator/monorail_description/mock_factory.go
package monorail_description

import (
	"errors"
)

type IMonorailDescriptionFactory interface {
	GetMonorailDescriptionProcessor(monorailComponent string) (MonorailDescriptionProcessor, error)
}

type MonorailDescriptionFactory struct {
	lendingCreditCardProcessor *LendingCreditCardProcessor
}

const (
	monorailComponentLendingCreditCard = "Lending>CreditCard"
)

func NewMonorailDescriptionFactory(lendingCreditCardProcessor *LendingCreditCardProcessor) *MonorailDescriptionFactory {
	return &MonorailDescriptionFactory{
		lendingCreditCardProcessor: lendingCreditCardProcessor,
	}
}

var _ IMonorailDescriptionFactory = &MonorailDescriptionFactory{}

func (i *MonorailDescriptionFactory) GetMonorailDescriptionProcessor(monorailComponent string) (MonorailDescriptionProcessor, error) {
	switch monorailComponent {
	case monorailComponentLendingCreditCard:
		return i.lendingCreditCardProcessor, nil
	default:
		return nil, errors.New("no monorail description processor found for the component")
	}
}
