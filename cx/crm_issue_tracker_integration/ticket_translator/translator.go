package ticket_translator

import (
	"context"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
)

type TicketTranslator interface {
	// Translation for Creating a ticket throws errors if any of the required fields are not obtainable
	TranslateCrmToIssueTrackerTicketForCreate(ctx context.Context, crmTicket *citPb.CrmTicket) (*citPb.IssueTrackerTicket, error)
	// Transaltion for updating a ticket
	TranslateCrmToIssueTrackerTicketForUpdate(ctx context.Context, crmTicket *citPb.CrmTicket) (*citPb.IssueTrackerTicket, string, error)
	// TranslateCrmToIssueTrackerTicketForAppend - For appending a duplicate CRM ticket to an already existing Issue tracker ticket
	// Returns IssueTrackerTicket object with fields to be updated along a with a comment which has to be added to the Issue Tracker ticket
	TranslateCrmToIssueTrackerTicketForAppend(ctx context.Context, crmTicket *citPb.CrmTicket) (*citPb.IssueTrackerTicket, string, error)
	TranslateIssueTrackerToCrmTicketsForUpdate(ctx context.Context, issueTrackerTicket *citPb.IssueTrackerTicket) ([]*citPb.CrmTicket, bool, error)
}
