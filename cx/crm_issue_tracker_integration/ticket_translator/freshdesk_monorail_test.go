package ticket_translator

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	monorailPb "github.com/epifi/be-common/api/pkg/monorail"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	mockApiWrapper "github.com/epifi/be-common/pkg/monorail/mocks/api_wrapper"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	cxTicketPb "github.com/epifi/gamma/api/cx/ticket"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	mockVgFreshdesk "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk/mocks"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	mockCrmIssueTrackerDao "github.com/epifi/gamma/cx/test/mocks/crm_issue_tracker_integration/dao"
	mockMonorailDesc "github.com/epifi/gamma/cx/test/mocks/crm_issue_tracker_integration/ticket_translator/monorail_description"
	mockTicketDao "github.com/epifi/gamma/cx/test/mocks/ticket/dao"
)

type FreshdeskMonorailTranslatorTestSuite struct {
	genConf *cxGenConf.Config
}

const (
	monorailComponent1 = "Area1>SubArea1"
	monorailEmail1     = "<EMAIL>"
	defaultLabel       = "Freshdesk-escalation"
	priorityLowLabel   = "Priority-Low"
	osAndroidLabel     = "Area-Android"
	additionalInfo1    = "\nadditional-info-1"

	descriptionHtml1 = "<div style=\"font-family:-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif; font-size:14px\">\n<div dir=\"ltr\">Description line 1</div>\n<div dir=\"ltr\">Description line 2</div>\n</div>"
	// Freshdesk APIs give multi line values in DescriptionText field in this format (corresponding to descriptionHtml1 in Description)
	descriptionText1 = "Description line 1 Description line 2"
	privateNoteHtml1 = "<div style=\"font-family:-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif; font-size:14px\"><div dir=\"ltr\">private-note-line-1<br>private-note-line-2</div></div>"
	// Freshdesk APIs give multi line values in BodyText field in this format (corresponding to privateNoteHtml1 in Body)
	privateNoteText1 = "private-note-line-1 private-note-line-2"
)

var (
	fmtTS                     *FreshdeskMonorailTranslatorTestSuite
	mockErr                   = errors.New("mock err")
	crmToIssueTrackerMapping1 = &citPb.CrmToIssueTrackerMapping{
		CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
		CrmTicketId:          1234,
		IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
		IssueTrackerTicketId: 9876,
	}
	freshdeskTicket1 = &cxTicketPb.Ticket{
		Subject:         "subject-1",
		Description:     descriptionHtml1,
		DescriptionText: descriptionText1,
		Id:              1,
		Priority:        cxTicketPb.Priority_PRIORITY_LOW,
		Status:          cxTicketPb.Status_STATUS_SEND_TO_PRODUCT,
		CustomFieldWithValue: &cxTicketPb.CustomFieldsWithValue{
			ProductCategory:        "Credit Card",
			ProductCategoryDetails: "Eligibility",
			SubCategory:            "Enquiry",
		},
	}
	crmTicket1 = &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: freshdeskTicket1,
		},
	}
	freshdeskTicket2 = &cxTicketPb.Ticket{
		Subject:         "subject-1",
		Description:     "description-1",
		DescriptionText: "description-1",
		Id:              1,
		Priority:        cxTicketPb.Priority_PRIORITY_LOW,
		Status:          cxTicketPb.Status_STATUS_SEND_TO_PRODUCT,
		CustomFieldWithValue: &cxTicketPb.CustomFieldsWithValue{
			ProductCategory:        "Credit Card",
			ProductCategoryDetails: "Eligibility",
			SubCategory:            "Enquiry",
		},
		CustomFields: &cxTicketPb.CustomFields{
			OsType: commontypes.Platform_ANDROID,
		},
	}
	crmTicket2 = &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: freshdeskTicket2,
		},
	}
	ticketDetailsTransformation1 = &cxTicketPb.TicketDetailsTransformation{
		TransformationType: cxTicketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_MONORAIL_COMPONENTS,
		TransformationValue: &cxTicketPb.TicketDetailsTransformation_TransformationValue{
			MonorailComponents: []string{monorailComponent1},
		},
	}
	issueTrackerTicket1 = &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Title:       "FD - 1 : subject-1",
				Summary:     "FD - 1 : subject-1",
				Description: "\nDescription line 1\nDescription line 2\n\r\n\r\n<b>**Additional Info**</b>\n<b>Actor Id:</b> \n<b>FD Ticket Id:</b> 1\n<b>FD Ticket Status:</b> STATUS_SEND_TO_PRODUCT\n<b>Product Category (L1):</b> Credit Card\n<b>Product Category Details (L2):</b> Eligibility\n<b>Subcategory (L3):</b> Enquiry\n\n<failed to get additional info for the monorail component: Area1>SubArea1: mock err>\n",
				Status:      monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_NEW,
				Components:  []string{monorailComponent1},
				Owner:       &monorailPb.Person{Email: monorailEmail1},
				Cc:          []*monorailPb.Person{{Email: monorailEmail1}},
				Labels:      []string{defaultLabel, priorityLowLabel},
				CustomFieldValues: []*monorailPb.CustomFieldValue{{
					FieldName:  freshdeskIdsCustomFieldName,
					FieldValue: "1",
				}},
			},
		},
	}
	issueTrackerTicket2 = &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Title:       "FD - 1 : subject-1",
				Summary:     "FD - 1 : subject-1",
				Description: "\nDescription line 1\nDescription line 2\n\r\n\r\n<b>Private Note 1:</b>\r\nprivate-note-line-1\nprivate-note-line-2\r\n\r\n<b>**Additional Info**</b>\n<b>Actor Id:</b> \n<b>FD Ticket Id:</b> 1\n<b>FD Ticket Status:</b> STATUS_SEND_TO_PRODUCT\n<b>Product Category (L1):</b> Credit Card\n<b>Product Category Details (L2):</b> Eligibility\n<b>Subcategory (L3):</b> Enquiry\n\n<failed to get additional info for the monorail component: Area1>SubArea1: mock err>\n",
				Status:      monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_NEW,
				Components:  []string{monorailComponent1},
				Owner:       &monorailPb.Person{Email: monorailEmail1},
				Cc:          []*monorailPb.Person{{Email: monorailEmail1}},
				Labels:      []string{defaultLabel, priorityLowLabel},
				CustomFieldValues: []*monorailPb.CustomFieldValue{{
					FieldName:  freshdeskIdsCustomFieldName,
					FieldValue: "1",
				}},
			},
		},
	}
	issueTrackerTicket3 = &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Title:       "FD - 1 : subject-1",
				Summary:     "FD - 1 : subject-1",
				Description: "\nDescription line 1\nDescription line 2\n\r\n\r\n<b>Private Note 1:</b>\r\nprivate-note-line-1\nprivate-note-line-2\r\n\r\n<b>**Additional Info**</b>\n<b>Actor Id:</b> \n<b>FD Ticket Id:</b> 1\n<b>FD Ticket Status:</b> STATUS_SEND_TO_PRODUCT\n<b>Product Category (L1):</b> Credit Card\n<b>Product Category Details (L2):</b> Eligibility\n<b>Subcategory (L3):</b> Enquiry\n\n<failed to get additional info for the monorail component: Area1>SubArea1: mock err>\n",
				Status:      monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_NEW,
				Components:  []string{monorailComponent1},
				Owner:       &monorailPb.Person{Email: monorailEmail1},
				Cc:          []*monorailPb.Person{{Email: monorailEmail1}},
				Labels:      []string{defaultLabel, priorityLowLabel},
				CustomFieldValues: []*monorailPb.CustomFieldValue{{
					FieldName:  freshdeskIdsCustomFieldName,
					FieldValue: "1",
				}},
			},
		},
	}
	issueTrackerTicket4 = &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Title:       "FD - 1 : subject-1",
				Summary:     "FD - 1 : subject-1",
				Description: "\nDescription line 1\nDescription line 2\n\r\n\r\n<b>**Additional Info**</b>\n<b>Actor Id:</b> \n<b>FD Ticket Id:</b> 1\n<b>FD Ticket Status:</b> STATUS_SEND_TO_PRODUCT\n<b>Product Category (L1):</b> Credit Card\n<b>Product Category Details (L2):</b> Eligibility\n<b>Subcategory (L3):</b> Enquiry\nadditional-info-1",
				Status:      monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_NEW,
				Components:  []string{monorailComponent1},
				Owner:       &monorailPb.Person{Email: monorailEmail1},
				Cc:          []*monorailPb.Person{{Email: monorailEmail1}},
				Labels:      []string{defaultLabel, priorityLowLabel},
				CustomFieldValues: []*monorailPb.CustomFieldValue{{
					FieldName:  freshdeskIdsCustomFieldName,
					FieldValue: "1",
				}},
			},
		},
	}
	issueTrackerTicket5 = &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Title:       "FD - 1 : subject-1",
				Summary:     "FD - 1 : subject-1",
				Description: "description-1\r\n\r\n<b>Private Note 1:</b>\r\nprivate-note-line-1\nprivate-note-line-2\r\n\r\n<b>**Additional Info**</b>\n<b>Actor Id:</b> \n<b>FD Ticket Id:</b> 1\n<b>FD Ticket Status:</b> STATUS_SEND_TO_PRODUCT\n<b>Product Category (L1):</b> Credit Card\n<b>Product Category Details (L2):</b> Eligibility\n<b>Subcategory (L3):</b> Enquiry\nadditional-info-1",
				Status:      monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_NEW,
				Components:  []string{monorailComponent1},
				Owner:       &monorailPb.Person{Email: monorailEmail1},
				Cc:          []*monorailPb.Person{{Email: monorailEmail1}},
				Labels:      []string{defaultLabel, priorityLowLabel, osAndroidLabel},
				CustomFieldValues: []*monorailPb.CustomFieldValue{{
					FieldName:  freshdeskIdsCustomFieldName,
					FieldValue: "1",
				}},
			},
		},
	}
)

func TestFreshdeskMonorailTranslator_TranslateCrmToIssueTrackerTicketForCreate(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockTransformationDao := mockTicketDao.NewMockITicketDetailsTransformationDao(ctr)
	mockMonorailDescriptionProcessor := mockMonorailDesc.NewMockMonorailDescriptionProcessor(ctr)
	mockMonorailDescriptionFactory := mockMonorailDesc.NewMockIMonorailDescriptionFactory(ctr)
	mockVgFdClient := mockVgFreshdesk.NewMockFreshdeskClient(ctr)
	mockMonorailApi := mockApiWrapper.NewMockIMonorailApiWrapper(ctr)

	type args struct {
		mocks     []interface{}
		ctx       context.Context
		crmTicket *citPb.CrmTicket
	}
	tests := []struct {
		name    string
		args    args
		want    *citPb.IssueTrackerTicket
		wantErr bool
	}{
		{
			name: "failed to fetch Monorail components: unspecified product category",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed to fetch Monorail components from db",
			args: args{
				mocks: []interface{}{
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return(nil, mockErr),
				},
				ctx:       context.Background(),
				crmTicket: crmTicket1,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed to fetch Monorail issue owners for the the components",
			args: args{
				mocks: []interface{}{
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return([]*cxTicketPb.TicketDetailsTransformation{{TransformationValue: &cxTicketPb.TicketDetailsTransformation_TransformationValue{
						MonorailComponents: []string{"random-component"},
					}}}, nil),
				},
				ctx:       context.Background(),
				crmTicket: crmTicket1,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "monorail issue owners list is empty",
			args: args{
				mocks: []interface{}{
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return([]*cxTicketPb.TicketDetailsTransformation{}, nil),
				},
				ctx:       context.Background(),
				crmTicket: crmTicket1,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Success: no private notes, no additional info - couldn't get conversations for the ticket",
			args: args{
				mocks: []interface{}{
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return([]*cxTicketPb.TicketDetailsTransformation{ticketDetailsTransformation1}, nil),
					mockVgFdClient.EXPECT().FetchTicketConversations(context.Background(), gomock.Any()).Return(nil, mockErr),
					mockMonorailDescriptionFactory.EXPECT().GetMonorailDescriptionProcessor(monorailComponent1).Return(nil, mockErr),
				},
				ctx:       context.Background(),
				crmTicket: crmTicket1,
			},
			want:    issueTrackerTicket1,
			wantErr: false,
		},
		{
			name: "Success: with private notes, no additional info - no monorail description processor implementation",
			args: args{
				mocks: []interface{}{
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return([]*cxTicketPb.TicketDetailsTransformation{ticketDetailsTransformation1}, nil),
					mockVgFdClient.EXPECT().FetchTicketConversations(context.Background(), gomock.Any()).Return(&freshdeskPb.FetchTicketConversationsResponse{
						Status:                 rpc.StatusOk(),
						TicketConversationList: []*freshdeskPb.TicketConversation{{Private: true, Body: privateNoteHtml1, BodyText: privateNoteText1}},
					}, nil),
					mockMonorailDescriptionFactory.EXPECT().GetMonorailDescriptionProcessor(monorailComponent1).Return(nil, mockErr),
				},
				ctx:       context.Background(),
				crmTicket: crmTicket1,
			},
			want:    issueTrackerTicket2,
			wantErr: false,
		},
		{
			name: "Success: with private notes, no additional info - couldn't get data from monorail description processor",
			args: args{
				mocks: []interface{}{
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return([]*cxTicketPb.TicketDetailsTransformation{ticketDetailsTransformation1}, nil),
					mockVgFdClient.EXPECT().FetchTicketConversations(context.Background(), gomock.Any()).Return(&freshdeskPb.FetchTicketConversationsResponse{
						Status:                 rpc.StatusOk(),
						TicketConversationList: []*freshdeskPb.TicketConversation{{Private: true, Body: privateNoteHtml1, BodyText: privateNoteText1}},
					}, nil),
					mockMonorailDescriptionFactory.EXPECT().GetMonorailDescriptionProcessor(monorailComponent1).Return(mockMonorailDescriptionProcessor, nil),
					mockMonorailDescriptionProcessor.EXPECT().GetAdditionalInfoForMonorailDescription(context.Background(), gomock.Any()).Return("", mockErr),
				},
				ctx:       context.Background(),
				crmTicket: crmTicket1,
			},
			want:    issueTrackerTicket3,
			wantErr: false,
		},
		{
			name: "Success: no private notes, with additional info",
			args: args{
				mocks: []interface{}{
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return([]*cxTicketPb.TicketDetailsTransformation{ticketDetailsTransformation1}, nil),
					mockVgFdClient.EXPECT().FetchTicketConversations(context.Background(), gomock.Any()).Return(&freshdeskPb.FetchTicketConversationsResponse{Status: rpc.StatusRecordNotFound()}, nil),
					mockMonorailDescriptionFactory.EXPECT().GetMonorailDescriptionProcessor(monorailComponent1).Return(mockMonorailDescriptionProcessor, nil),
					mockMonorailDescriptionProcessor.EXPECT().GetAdditionalInfoForMonorailDescription(context.Background(), gomock.Any()).Return(additionalInfo1, nil),
				},
				ctx:       context.Background(),
				crmTicket: crmTicket1,
			},
			want:    issueTrackerTicket4,
			wantErr: false,
		},
		{
			name: "Success: with private notes, with additional info",
			args: args{
				mocks: []interface{}{
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return([]*cxTicketPb.TicketDetailsTransformation{ticketDetailsTransformation1}, nil),
					mockVgFdClient.EXPECT().FetchTicketConversations(context.Background(), gomock.Any()).Return(&freshdeskPb.FetchTicketConversationsResponse{
						Status:                 rpc.StatusOk(),
						TicketConversationList: []*freshdeskPb.TicketConversation{{Private: true, Body: privateNoteHtml1, BodyText: privateNoteText1}},
					}, nil),
					mockMonorailDescriptionFactory.EXPECT().GetMonorailDescriptionProcessor(monorailComponent1).Return(mockMonorailDescriptionProcessor, nil),
					mockMonorailDescriptionProcessor.EXPECT().GetAdditionalInfoForMonorailDescription(context.Background(), gomock.Any()).Return(additionalInfo1, nil),
				},
				ctx:       context.Background(),
				crmTicket: crmTicket2,
			},
			want:    issueTrackerTicket5,
			wantErr: false,
		},
		// TODO: https://monorail.pointz.in/p/fi-app/issues/detail?id=55884. Add mocks for google drive APIs
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailTranslator(fmtTS.genConf, mockTransformationDao, nil, mockVgFdClient, mockMonorailApi, mockMonorailDescriptionFactory)
			got, err := s.TranslateCrmToIssueTrackerTicketForCreate(tt.args.ctx, tt.args.crmTicket)
			if (err != nil) != tt.wantErr {
				t.Errorf("TranslateCrmToIssueTrackerTicketForCreate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("TranslateCrmToIssueTrackerTicketForCreate() got = %v, want %v, \n diff \n %s ", got, tt.want, diff)
				return
			}
		})
	}
}

func TestFreshdeskMonorailTranslator_TranslateCrmToIssueTrackerTicketForUpdate(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockCrmIssueTrackerMappingDao := mockCrmIssueTrackerDao.NewMockICrmToIssueTrackerMappingDao(ctr)
	mockMonorailApi := mockApiWrapper.NewMockIMonorailApiWrapper(ctr)

	issueTrackerTicketForUpdate := &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Id:      1,
				Summary: "FD - 1234 : subject-1",
				Status:  monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_REOPENED,
			},
		},
	}
	crmTicketForUpdate := &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: &cxTicketPb.Ticket{
				Id:      1234,
				Subject: "subject-1",
				Status:  cxTicketPb.Status_STATUS_REOPEN,
			},
		},
	}
	type args struct {
		mocks     []interface{}
		ctx       context.Context
		crmTicket *citPb.CrmTicket
	}
	tests := []struct {
		name     string
		args     args
		want     *citPb.IssueTrackerTicket
		wantNote string
		wantErr  bool
	}{
		{
			name: "no monorail issue mapping exists",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForUpdate,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed to fetch monorail issue mapping from db",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), gomock.Any(), gomock.Any()).Return(nil, mockErr),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForUpdate,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), gomock.Any(), gomock.Any()).Return([]*citPb.CrmToIssueTrackerMapping{
						{
							CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
							CrmTicketId:          1234,
							IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
							IssueTrackerTicketId: 1,
						},
					}, nil),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForUpdate,
			},
			want:    issueTrackerTicketForUpdate,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailTranslator(fmtTS.genConf, nil, mockCrmIssueTrackerMappingDao, nil, mockMonorailApi, nil)
			got, gotNote, err := s.TranslateCrmToIssueTrackerTicketForUpdate(tt.args.ctx, tt.args.crmTicket)
			if (err != nil) != tt.wantErr {
				t.Errorf("TranslateCrmToIssueTrackerTicketForUpdate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotNote != tt.wantNote {
				t.Errorf("TranslateCrmToIssueTrackerTicketForUpdate() gotNote = %s, wantNote = %s", gotNote, tt.wantNote)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("TranslateCrmToIssueTrackerTicketForUpdate() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFreshdeskMonorailTranslator_TranslateIssueTrackerToCrmTicketsForUpdate(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockCrmIssueTrackerMappingDao := mockCrmIssueTrackerDao.NewMockICrmToIssueTrackerMappingDao(ctr)
	mockMonorailApi := mockApiWrapper.NewMockIMonorailApiWrapper(ctr)

	issueTrackerTicketForUpdate1 := &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Id:     1,
				Status: monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_STARTED,
				Labels: []string{needMoreInfoFromCxLabel},
			},
		},
	}
	issueTrackerTicketForUpdate2 := &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Id:     1,
				Status: monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_BLOCKED,
				Labels: []string{needMoreInfoFromCxLabel},
			},
		},
	}
	issueTrackerTicketForUpdate3 := &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Id:     1,
				Status: monorailPb.MonorailIssueStatus_MONORAIL_ISSUE_STATUS_REOPENED,
			},
		},
	}
	crmTicketForUpdate1 := &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: &cxTicketPb.Ticket{
				Id:     1234,
				Status: cxTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
			},
		},
	}
	crmTicketForUpdate2 := &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: &cxTicketPb.Ticket{
				Id:     1234,
				Status: cxTicketPb.Status_STATUS_NEEDS_CLARIFICATION_FROM_CX,
			},
		},
	}
	crmTicketForUpdate3 := &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: &cxTicketPb.Ticket{
				Id: 1234,
			},
		},
	}
	type args struct {
		mocks              []interface{}
		ctx                context.Context
		issueTrackerTicket *citPb.IssueTrackerTicket
	}
	tests := []struct {
		name                        string
		args                        args
		wantTickets                 []*citPb.CrmTicket
		wantIsTicketAttributeUpdate bool
		wantErr                     bool
	}{
		{
			name: "no monorail issue mapping exists",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicketForUpdate1,
			},
			wantTickets:                 nil,
			wantIsTicketAttributeUpdate: false,
			wantErr:                     true,
		},
		{
			name: "failed to fetch monorail issue mapping from db",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), gomock.Any(), gomock.Any()).Return(nil, mockErr),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicketForUpdate1,
			},
			wantTickets:                 nil,
			wantIsTicketAttributeUpdate: false,
			wantErr:                     true,
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), gomock.Any(), gomock.Any()).Return([]*citPb.CrmToIssueTrackerMapping{
						{
							CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
							CrmTicketId:          1234,
							IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
							IssueTrackerTicketId: 1,
						},
					}, nil),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicketForUpdate1,
			},
			wantTickets:                 []*citPb.CrmTicket{crmTicketForUpdate1},
			wantIsTicketAttributeUpdate: true,
			wantErr:                     false,
		},
		{
			name: "success: based on label",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), gomock.Any(), gomock.Any()).Return([]*citPb.CrmToIssueTrackerMapping{
						{
							CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
							CrmTicketId:          1234,
							IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
							IssueTrackerTicketId: 1,
						},
					}, nil),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicketForUpdate2,
			},
			wantTickets:                 []*citPb.CrmTicket{crmTicketForUpdate2},
			wantIsTicketAttributeUpdate: true,
			wantErr:                     false,
		},
		{
			name: "success: ticket attribute update not required",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), gomock.Any(), gomock.Any()).Return([]*citPb.CrmToIssueTrackerMapping{
						{
							CrmTool:              citPb.CrmTool_CRM_TOOL_FRESHDESK,
							CrmTicketId:          1234,
							IssueTrackerTool:     citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
							IssueTrackerTicketId: 1,
						},
					}, nil),
				},
				ctx:                context.Background(),
				issueTrackerTicket: issueTrackerTicketForUpdate3,
			},
			wantTickets:                 []*citPb.CrmTicket{crmTicketForUpdate3},
			wantIsTicketAttributeUpdate: false,
			wantErr:                     false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailTranslator(fmtTS.genConf, nil, mockCrmIssueTrackerMappingDao, nil, mockMonorailApi, nil)
			gotTickets, gotIsTicketAttributeUpdate, err := s.TranslateIssueTrackerToCrmTicketsForUpdate(tt.args.ctx, tt.args.issueTrackerTicket)
			if (err != nil) != tt.wantErr {
				t.Errorf("TranslateIssueTrackerToCrmTicketsForUpdate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotIsTicketAttributeUpdate != tt.wantIsTicketAttributeUpdate {
				t.Errorf("TranslateIssueTrackerToCrmTicketsForUpdate() gotIsTicketAttributeUpdate = %v, wantIsTicketAttributeUpdate %v", gotIsTicketAttributeUpdate, tt.wantIsTicketAttributeUpdate)
				return
			}
			if !reflect.DeepEqual(gotTickets, tt.wantTickets) {
				t.Errorf("TranslateIssueTrackerToCrmTicketsForUpdate() gotTicket = %v, wantTicket %v", gotTickets, tt.wantTickets)
			}
		})
	}
}

func TestFreshdeskMonorailTranslator_TranslateCrmToIssueTrackerTicketForAppend(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	mockCrmIssueTrackerMappingDao := mockCrmIssueTrackerDao.NewMockICrmToIssueTrackerMappingDao(ctr)
	mockTransformationDao := mockTicketDao.NewMockITicketDetailsTransformationDao(ctr)
	mockMonorailDescriptionProcessor := mockMonorailDesc.NewMockMonorailDescriptionProcessor(ctr)
	mockMonorailDescriptionFactory := mockMonorailDesc.NewMockIMonorailDescriptionFactory(ctr)
	mockMonorailApi := mockApiWrapper.NewMockIMonorailApiWrapper(ctr)

	issueTrackerTicketForAppend := &citPb.IssueTrackerTicket{
		Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
			MonorailIssue: &monorailPb.Issue{
				Id:                9876,
				CustomFieldValues: []*monorailPb.CustomFieldValue{{FieldName: freshdeskIdsCustomFieldName, FieldValue: "1234"}},
			},
		},
	}
	crmTicketForAppend1 := &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: &cxTicketPb.Ticket{
				Id:      1234,
				Subject: "subject-1",
				Status:  cxTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
				CustomFields: &cxTicketPb.CustomFields{
					MonorailRaised:   cxTicketPb.MonorailRaised_MONORAIL_RAISED_YES,
					MonorailTicketId: 9876,
				},
			},
		},
	}
	crmTicketForAppend2 := &citPb.CrmTicket{
		Ticket: &citPb.CrmTicket_FreshdeskTicket{
			FreshdeskTicket: &cxTicketPb.Ticket{
				Id:      1234,
				Subject: "subject-1",
				Status:  cxTicketPb.Status_STATUS_WAITING_ON_PRODUCT,
				CustomFields: &cxTicketPb.CustomFields{
					MonorailRaised:   cxTicketPb.MonorailRaised_MONORAIL_RAISED_YES,
					MonorailTicketId: 9876,
				},
				CustomFieldWithValue: &cxTicketPb.CustomFieldsWithValue{
					ProductCategory:        "Credit Card",
					ProductCategoryDetails: "Eligibility",
					SubCategory:            "Enquiry",
				},
			},
		},
	}
	type args struct {
		mocks     []interface{}
		ctx       context.Context
		crmTicket *citPb.CrmTicket
	}
	tests := []struct {
		name        string
		args        args
		wantTicket  *citPb.IssueTrackerTicket
		wantComment string
		wantErr     bool
	}{
		{
			name: "monorail ticket is not populated in FD ticket",
			args: args{
				ctx:       context.Background(),
				crmTicket: crmTicket1,
			},
			wantTicket:  nil,
			wantComment: "",
			wantErr:     true,
		},
		{
			name: "failed to get mapping for fd ticket",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), citPb.CrmTool_CRM_TOOL_FRESHDESK, int64(1234)).Return(nil, mockErr),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForAppend1,
			},
			wantTicket:  nil,
			wantComment: "",
			wantErr:     true,
		},
		{
			name: "mapping already exists for this FD ticket",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), citPb.CrmTool_CRM_TOOL_FRESHDESK, int64(1234)).Return([]*citPb.CrmToIssueTrackerMapping{crmToIssueTrackerMapping1}, nil),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForAppend1,
			},
			wantTicket:  nil,
			wantComment: "",
			wantErr:     true,
		},
		{
			name: "failed to get mapping for monorail ticket",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), citPb.CrmTool_CRM_TOOL_FRESHDESK, int64(1234)).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL, int64(9876)).Return(nil, mockErr),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForAppend1,
			},
			wantTicket:  nil,
			wantComment: "",
			wantErr:     true,
		},
		{
			name: "monorail ticket has not been created by this service",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), citPb.CrmTool_CRM_TOOL_FRESHDESK, int64(1234)).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL, int64(9876)).Return(nil, epifierrors.ErrRecordNotFound),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForAppend1,
			},
			wantTicket:  nil,
			wantComment: "",
			wantErr:     true,
		},
		{
			name: "success: partial comment due to failed to get monorail component for Li, L2, L3",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), citPb.CrmTool_CRM_TOOL_FRESHDESK, int64(1234)).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL, int64(9876)).Return([]*citPb.CrmToIssueTrackerMapping{crmToIssueTrackerMapping1}, nil),
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return(nil, mockErr),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForAppend2,
			},
			wantTicket:  issueTrackerTicketForAppend,
			wantComment: "<b>Appending a related FD ticket</b>\n\n<b>Actor Id:</b> \n<b>FD Ticket Id:</b> 1234\n<b>FD Ticket Status:</b> STATUS_WAITING_ON_PRODUCT\n<b>Product Category (L1):</b> Credit Card\n<b>Product Category Details (L2):</b> Eligibility\n<b>Subcategory (L3):</b> Enquiry\n\n<failed to fetch Monorail components for the given L1, L2, L3: failed to fetch monorail components mapping from db: mock err>\n",
			wantErr:     false,
		},
		{
			name: "success: with full comment",
			args: args{
				mocks: []interface{}{
					mockCrmIssueTrackerMappingDao.EXPECT().GetByCrmTicketId(context.Background(), citPb.CrmTool_CRM_TOOL_FRESHDESK, int64(1234)).Return(nil, epifierrors.ErrRecordNotFound),
					mockCrmIssueTrackerMappingDao.EXPECT().GetByIssueTrackerTicketId(context.Background(), citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL, int64(9876)).Return([]*citPb.CrmToIssueTrackerMapping{crmToIssueTrackerMapping1}, nil),
					mockTransformationDao.EXPECT().GetAllByFilters(gomock.Any(), gomock.Any()).Return([]*cxTicketPb.TicketDetailsTransformation{ticketDetailsTransformation1}, nil),
					mockMonorailDescriptionFactory.EXPECT().GetMonorailDescriptionProcessor(monorailComponent1).Return(mockMonorailDescriptionProcessor, nil),
					mockMonorailDescriptionProcessor.EXPECT().GetAdditionalInfoForMonorailDescription(context.Background(), gomock.Any()).Return(additionalInfo1, nil),
				},
				ctx:       context.Background(),
				crmTicket: crmTicketForAppend2,
			},
			wantTicket:  issueTrackerTicketForAppend,
			wantComment: "<b>Appending a related FD ticket</b>\n\n<b>Actor Id:</b> \n<b>FD Ticket Id:</b> 1234\n<b>FD Ticket Status:</b> STATUS_WAITING_ON_PRODUCT\n<b>Product Category (L1):</b> Credit Card\n<b>Product Category Details (L2):</b> Eligibility\n<b>Subcategory (L3):</b> Enquiry\nadditional-info-1",
			wantErr:     false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshdeskMonorailTranslator(fmtTS.genConf, mockTransformationDao, mockCrmIssueTrackerMappingDao, nil, mockMonorailApi, mockMonorailDescriptionFactory)
			gotTicket, gotComment, err := s.TranslateCrmToIssueTrackerTicketForAppend(tt.args.ctx, tt.args.crmTicket)
			if (err != nil) != tt.wantErr {
				t.Errorf("TranslateCrmToIssueTrackerTicketForAppend() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.wantComment, gotComment, protocmp.Transform()); diff != "" {
				t.Errorf("TranslateCrmToIssueTrackerTicketForAppend() gotComment = %v\n wantComment %v, \n diff \n %s ", gotComment, tt.wantComment, diff)
				return
			}
			if !proto.Equal(gotTicket, tt.wantTicket) {
				t.Errorf("TranslateCrmToIssueTrackerTicketForAppend() gotTicket = %v, wantTicket %v", gotTicket, tt.wantTicket)
			}
		})
	}
}
