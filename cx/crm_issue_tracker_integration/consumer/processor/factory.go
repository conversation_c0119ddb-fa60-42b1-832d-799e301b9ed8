package processor

import (
	"context"
	"errors"

	citConsumerPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
)

type ICrmIssueTrackerEventProcessorFactory interface {
	GetEventProcessor(ctx context.Context, eventType citConsumerPb.EventType) (CrmIssueTrackerEventProcessor, error)
}

type CrmIssueTrackerEventProcessorFactory struct {
	freshdeskTicketChangeProcessor       *FreshdeskTicketChangeProcessor
	freshdeskTicketConversationProcessor *FreshdeskTicketConversationProcessor
	monorailUpdatesCommentsProcessor     *MonorailUpdatesCommentsProcessor
}

func NewCrmIssueTrackerEventProcessorFactory(freshdeskTicketChangeProcessor *FreshdeskTicketChangeProcessor,
	freshdeskTicketConversationProcessor *FreshdeskTicketConversationProcessor, monorailUpdatesCommentsProcessor *MonorailUpdatesCommentsProcessor) *CrmIssueTrackerEventProcessorFactory {
	return &CrmIssueTrackerEventProcessorFactory{
		freshdeskTicketChangeProcessor:       freshdeskTicketChangeProcessor,
		freshdeskTicketConversationProcessor: freshdeskTicketConversationProcessor,
		monorailUpdatesCommentsProcessor:     monorailUpdatesCommentsProcessor,
	}
}

var _ ICrmIssueTrackerEventProcessorFactory = &CrmIssueTrackerEventProcessorFactory{}

// GetFreshdeskTicketEnricher returns processor for given entity
func (c *CrmIssueTrackerEventProcessorFactory) GetEventProcessor(ctx context.Context, eventType citConsumerPb.EventType) (CrmIssueTrackerEventProcessor, error) {
	switch eventType {
	case citConsumerPb.EventType_EVENT_TYPE_FRESHDESK_TICKET_CHANGE:
		return c.freshdeskTicketChangeProcessor, nil
	case citConsumerPb.EventType_EVENT_TYPE_FRESHDESK_TICKET_CONVERSATION:
		return c.freshdeskTicketConversationProcessor, nil
	case citConsumerPb.EventType_EVENT_TYPE_MONORAIL_ISSUE_UPDATES_COMMENTS:
		return c.monorailUpdatesCommentsProcessor, nil
	default:
		return nil, errors.New("invalid or unspecified event type")
	}
}
