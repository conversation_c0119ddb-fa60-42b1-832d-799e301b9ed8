package processor

import (
	"context"

	citConsumerPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
)

// CrmIssueTrackerEventProcessor defines abstract method to process freshdesk events
type CrmIssueTrackerEventProcessor interface {
	// ProcessCrmIssueTrackerEvent - processes a specific event published to cx-crm-freshdesk-integration-queue
	ProcessCrmIssueTrackerEvent(ctx context.Context, eventReq *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventRequest) (*citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse, error)
}
