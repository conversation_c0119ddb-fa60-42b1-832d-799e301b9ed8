package processor

import (
	"context"

	"go.uber.org/zap"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	citConsumerPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer/helper"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type FreshdeskTicketChangeProcessor struct {
	crmIssueTrackerClient citPb.CrmIssueTrackerIntegrationClient
}

func NewFreshdeskTicketChangeProcessor(crmIssueTrackerClient citPb.CrmIssueTrackerIntegrationClient) *FreshdeskTicketChangeProcessor {
	return &FreshdeskTicketChangeProcessor{
		crmIssueTrackerClient: crmIssueTrackerClient,
	}
}

func (f *FreshdeskTicketChangeProcessor) ProcessCrmIssueTrackerEvent(ctx context.Context, eventReq *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventRequest) (*citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse, error) {
	existingTicket := eventReq.GetFreshdeskTicketChangePayload().GetExistingTicketDetails()
	newTicket := eventReq.GetFreshdeskTicketChangePayload().GetNewTicketDetails()
	if existingTicket.GetStatus() == newTicket.GetStatus() {
		logger.Info(ctx, "no status change for the Freshdesk ticket, hence ignoring the event", zap.Int64(logger.TICKET_ID, newTicket.GetId()))
		return helper.CrmIssueTrackerIntegrationSuccessResp(), nil
	}
	switch newTicket.GetStatus() {
	case ticketPb.Status_STATUS_SEND_TO_PRODUCT:
		createResp, err := f.crmIssueTrackerClient.CreateIssueTrackerTicket(ctx, &citPb.CreateIssueTrackerTicketRequest{
			IssueTrackerTool: citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
			CrmTool:          citPb.CrmTool_CRM_TOOL_FRESHDESK,
			CrmTicket: &citPb.CrmTicket{
				Ticket: &citPb.CrmTicket_FreshdeskTicket{
					FreshdeskTicket: newTicket.GetTicket(),
				},
			},
		})
		if te := epifigrpc.RPCError(createResp, err); te != nil {
			logger.Error(ctx, "failed to create monorail ticket for freshdesk ticket", zap.Error(te), zap.Int64(logger.TICKET_ID, newTicket.GetId()))
			return helper.GetRespForRpcFailureStatus(createResp.GetStatus()), nil
		}
		logger.Info(ctx, "successfully created Monorail ticket for Freshdesk Ticket", zap.Int64(logger.TICKET_ID, newTicket.GetId()),
			zap.Int64(logger.MONORAIL_ISSUE_ID, createResp.GetIssueTrackerTicket().GetMonorailIssue().GetId()))
	case ticketPb.Status_STATUS_REOPEN:
		resp, err := f.crmIssueTrackerClient.UpdateIssueTrackerTicket(ctx, &citPb.UpdateIssueTrackerTicketRequest{
			IssueTrackerTool: citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
			CrmTool:          citPb.CrmTool_CRM_TOOL_FRESHDESK,
			CrmTicket: &citPb.CrmTicket{
				Ticket: &citPb.CrmTicket_FreshdeskTicket{
					FreshdeskTicket: newTicket.GetTicket(),
				},
			},
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			logger.Error(ctx, "failed to update monorail ticket for freshdesk ticket", zap.Error(te), zap.Int64(logger.TICKET_ID, newTicket.GetId()))
			return helper.GetRespForRpcFailureStatus(resp.GetStatus()), nil
		}
	case ticketPb.Status_STATUS_WAITING_ON_PRODUCT:
		resp, err := f.crmIssueTrackerClient.AppendCrmTicketToIssueTrackerTicket(ctx, &citPb.AppendCrmTicketToIssueTrackerTicketRequest{
			IssueTrackerTool: citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
			CrmTool:          citPb.CrmTool_CRM_TOOL_FRESHDESK,
			CrmTicket: &citPb.CrmTicket{
				Ticket: &citPb.CrmTicket_FreshdeskTicket{
					FreshdeskTicket: newTicket.GetTicket(),
				},
			},
		})
		if te := epifigrpc.RPCError(resp, err); te != nil {
			logger.Error(ctx, "failed to append freshdesk ticket to monorail ticket", zap.Error(te), zap.Int64(logger.TICKET_ID, newTicket.GetId()))
			return helper.GetRespForRpcFailureStatus(resp.GetStatus()), nil
		}
	default:
	}
	return helper.CrmIssueTrackerIntegrationSuccessResp(), nil
}
