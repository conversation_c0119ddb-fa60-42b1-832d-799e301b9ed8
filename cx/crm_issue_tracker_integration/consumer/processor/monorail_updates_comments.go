package processor

import (
	"context"
	"strconv"
	"strings"

	"go.uber.org/zap"

	monorailPb "github.com/epifi/be-common/api/pkg/monorail"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	citConsumerPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer/helper"
)

type MonorailUpdatesCommentsProcessor struct {
	conf                  *cxGenConf.Config
	crmIssueTrackerClient citPb.CrmIssueTrackerIntegrationClient
}

func NewMonorailUpdatesCommentsProcessor(conf *cxGenConf.Config, crmIssueTrackerClient citPb.CrmIssueTrackerIntegrationClient) *MonorailUpdatesCommentsProcessor {
	return &MonorailUpdatesCommentsProcessor{
		conf:                  conf,
		crmIssueTrackerClient: crmIssueTrackerClient,
	}
}

func (f *MonorailUpdatesCommentsProcessor) ProcessCrmIssueTrackerEvent(ctx context.Context, eventReq *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventRequest) (*citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse, error) {
	issue := eventReq.GetMonorailIssueUpdatesCommentsPayload().GetMonorailIssue()

	privateNoteContent := f.buildFreshdeskPrivateNoteContent(issue.GetId(), eventReq.GetMonorailIssueUpdatesCommentsPayload().GetIssueComments())

	resp, err := f.crmIssueTrackerClient.UpdateCrmTicket(ctx, &citPb.UpdateCrmTicketRequest{
		IssueTrackerTool: citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
		CrmTool:          citPb.CrmTool_CRM_TOOL_FRESHDESK,
		IssueTrackerTicket: &citPb.IssueTrackerTicket{
			Ticket: &citPb.IssueTrackerTicket_MonorailIssue{
				MonorailIssue: issue,
			},
		},
		// Currently, we want to update Freshdesk ticket attributes only if there is status update on monorail
		IsTicketAttributeUpdate: eventReq.GetMonorailIssueUpdatesCommentsPayload().GetIsStatusUpdate(),
		// Freshdesk Add private note API will be invoked only if privateNoteContent is a non-empty string
		Note: privateNoteContent,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to update freshdesk ticket for the given monorail ticket", zap.Error(te), zap.Int64(logger.MONORAIL_ISSUE_ID, issue.GetId()))
		return helper.GetRespForRpcFailureStatus(resp.GetStatus()), nil
	}
	return helper.CrmIssueTrackerIntegrationSuccessResp(), nil
}

func (f *MonorailUpdatesCommentsProcessor) buildFreshdeskPrivateNoteContent(monorailIssueId int64, monorailComments []*monorailPb.Comment) string {
	var fdCommentList []string
	// filter out the comments which need to be forwarded to Freshdesk
	fdCommentPrefix := f.conf.FreshdeskMonorailIntegrationConfig().PrefixForForwardingMonorailComment
	for _, comment := range monorailComments {
		if strings.HasPrefix(comment.GetContent(), fdCommentPrefix) {
			fdCommentList = append(fdCommentList, strings.TrimPrefix(comment.GetContent(), fdCommentPrefix))
		}
	}
	if len(fdCommentList) == 0 {
		return ""
	}
	// NOTE: All the private notes should start with this prefix. This is to identify if a given private note
	// has been published from monorail while forwarding private notes from Freshdesk to Monorail (i.e. on a Freshdesk Conversation event)
	fdPrivateNote := f.conf.FreshdeskMonorailIntegrationConfig().MonorailToFreshdeskPrivateNotePrefix
	// Adding monorail Id info to the private note just in case we failed to update Monorail Id field of the ticket earlier
	fdPrivateNote += " [Monorail Id: " + strconv.FormatInt(monorailIssueId, 10) + "]"
	for _, fdComment := range fdCommentList {
		// Use <br> for new line i.e. formatting each comment in a new line
		fdPrivateNote += "<br><br>" + fdComment
	}
	return fdPrivateNote
}
