package processor

import (
	"context"
	"strings"

	"go.uber.org/zap"

	citPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	citConsumerPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer/helper"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type FreshdeskTicketConversationProcessor struct {
	conf                  *cxGenConf.Config
	crmIssueTrackerClient citPb.CrmIssueTrackerIntegrationClient
}

func NewFreshdeskTicketConversationProcessor(conf *cxGenConf.Config, crmIssueTrackerClient citPb.CrmIssueTrackerIntegrationClient) *FreshdeskTicketConversationProcessor {
	return &FreshdeskTicketConversationProcessor{
		conf:                  conf,
		crmIssueTrackerClient: crmIssueTrackerClient,
	}
}

func (f *FreshdeskTicketConversationProcessor) ProcessCrmIssueTrackerEvent(ctx context.Context, eventReq *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventRequest) (*citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse, error) {
	conversation := eventReq.GetFreshdeskConversationPayload().GetConversation()
	// if it is not a private note then ignore
	if !conversation.GetPrivate() {
		logger.Info(ctx, "the ticket conversation object is not a private note, hence ignoring", zap.Int64(logger.TICKET_ID, conversation.GetTicketId()),
			zap.Int64("conversationId", conversation.GetId()))
		return helper.CrmIssueTrackerIntegrationSuccessResp(), nil
	}
	privateNoteText := conversation.GetBodyText()
	// if this private note was published from monorail, then do not trigger an update to monorail
	if strings.HasPrefix(privateNoteText, f.conf.FreshdeskMonorailIntegrationConfig().MonorailToFreshdeskPrivateNotePrefix) {
		logger.Info(ctx, "this private note has been added from monorail issue, hence ignoring", zap.Int64(logger.TICKET_ID, conversation.GetTicketId()),
			zap.Int64("conversationId", conversation.GetId()))
		return helper.CrmIssueTrackerIntegrationSuccessResp(), nil
	}
	resp, err := f.crmIssueTrackerClient.UpdateIssueTrackerTicket(ctx, &citPb.UpdateIssueTrackerTicketRequest{
		IssueTrackerTool: citPb.IssueTrackerTool_ISSUE_TRACKER_TOOL_MONORAIL,
		CrmTool:          citPb.CrmTool_CRM_TOOL_FRESHDESK,
		CrmTicket: &citPb.CrmTicket{
			Ticket: &citPb.CrmTicket_FreshdeskTicket{
				FreshdeskTicket: &ticketPb.Ticket{Id: conversation.GetTicketId()},
			},
			Notes: &citPb.CrmTicket_FreshdeskTicketConversations{
				FreshdeskTicketConversations: &citPb.FreshdeskTicketConversations{
					Conversations: []*freshdeskPb.TicketConversation{freshdeskPb.FromVendorTicketConversation(conversation)},
				},
			},
		},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to update monorail ticket for freshdesk ticket", zap.Error(te), zap.Int64(logger.TICKET_ID, conversation.GetTicketId()),
			zap.Int64("conversationId", conversation.GetId()))
		return helper.GetRespForRpcFailureStatus(resp.GetStatus()), nil
	}
	return helper.CrmIssueTrackerIntegrationSuccessResp(), nil
}
