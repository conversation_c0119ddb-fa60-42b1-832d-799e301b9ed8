package consumer

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	citConsumerPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer/helper"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer/processor"
	"github.com/epifi/be-common/pkg/logger"
)

type Service struct {
	eventProcessorFactory processor.ICrmIssueTrackerEventProcessorFactory
}

func NewService(eventProcessorFactory processor.ICrmIssueTrackerEventProcessorFactory) *Service {
	return &Service{
		eventProcessorFactory: eventProcessorFactory,
	}
}

var _ citConsumerPb.CrmIssueTrackerIntegrationConsumerServer = &Service{}

func (s *Service) ProcessCrmIssueTrackerIntegrationEvent(ctx context.Context, req *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventRequest) (*citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse, error) {
	eventProcessor, err := s.eventProcessorFactory.GetEventProcessor(ctx, req.GetEventType())
	if err != nil {
		logger.Error(ctx, "failed to get crm issue tracker processor implementation", zap.Error(err), zap.String(logger.EVENT_TYPE, req.GetEventType().String()))
		return helper.CrmIssueTrackerIntegrationPermanentErr(rpc.StatusUnimplemented()), nil
	}
	return eventProcessor.ProcessCrmIssueTrackerEvent(ctx, req)
}
