package helper

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	citConsumerPb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
)

func CrmIssueTrackerIntegrationTransErr(statusCode *rpc.Status) *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse {
	return &citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, GrpcStatusCode: statusCode},
	}
}

func CrmIssueTrackerIntegrationSuccessResp() *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse {
	return &citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS, GrpcStatusCode: rpc.StatusOk()},
	}
}

func CrmIssueTrackerIntegrationPermanentErr(statusCode *rpc.Status) *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse {
	return &citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE, GrpcStatusCode: statusCode},
	}
}

func GetRespForRpcFailureStatus(rpcStatus *rpc.Status) *citConsumerPb.ProcessCrmIssueTrackerIntegrationEventResponse {
	if rpcStatus.IsAlreadyExists() || rpcStatus.IsRecordNotFound() || rpcStatus.IsInvalidArgument() {
		return CrmIssueTrackerIntegrationPermanentErr(rpcStatus)
	}
	return CrmIssueTrackerIntegrationTransErr(rpcStatus)
}
